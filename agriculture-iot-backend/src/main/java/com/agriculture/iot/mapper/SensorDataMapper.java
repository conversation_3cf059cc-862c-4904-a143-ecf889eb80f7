package com.agriculture.iot.mapper;

import com.agriculture.iot.entity.SensorData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 传感器数据Mapper接口
 *
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface SensorDataMapper extends BaseMapper<SensorData> {

    /**
     * 根据设备ID和传感器类型查找最新数据
     */
    @Select("SELECT * FROM sensor_data " +
            "WHERE device_id = #{deviceId} AND sensor_type_id = #{sensorTypeId} " +
            "ORDER BY created_at DESC LIMIT 1")
    SensorData findLatestByType(@Param("deviceId") Long deviceId, 
                               @Param("sensorTypeId") Long sensorTypeId);

    /**
     * 删除指定时间之前的数据
     */
    @Select("DELETE FROM sensor_data WHERE created_at < #{cutoffDate}")
    int deleteBefore(@Param("cutoffDate") LocalDateTime cutoffDate);
}