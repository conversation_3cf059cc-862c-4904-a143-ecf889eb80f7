package com.agriculture.iot.mapper;

import com.agriculture.iot.entity.IrrigationRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;

/**
 * 灌溉记录Mapper接口
 *
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface IrrigationRecordMapper extends BaseMapper<IrrigationRecord> {

    /**
     * 删除指定时间之前的记录
     */
    @Select("DELETE FROM irrigation_record WHERE created_at < #{cutoffDate}")
    int deleteBefore(@Param("cutoffDate") LocalDateTime cutoffDate);
}