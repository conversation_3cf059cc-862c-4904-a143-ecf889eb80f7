/* 监测历史分析页面样式 */

/* ECharts 图表样式 */
.echarts-wrapper {
  margin: 20rpx 0;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
}

.comparison-chart {
  margin-top: 30rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.chart-subtitle {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

/* 查询表单 */
.query-form {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.date-picker {
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: white;
  font-size: 26rpx;
  color: #333;
  text-align: center;
}

.date-separator {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin: 0 10rpx;
}

/* 指标选择器 */
.indicator-selector {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.indicator-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: white;
  transition: all 0.2s;
  cursor: pointer;
}

.indicator-item.selected {
  border-color: #2E7D32;
  background-color: #E8F5E8;
}

.indicator-item:active {
  transform: scale(0.95);
}

.indicator-icon {
  font-size: 32rpx;
}

.indicator-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.form-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.form-actions .btn {
  flex: 1;
}

/* 统计数据 */
.statistics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 25rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #2E7D32;
}

.stat-icon {
  font-size: 40rpx;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.stat-name {
  font-size: 24rpx;
  color: #666;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #2E7D32;
}

.stat-desc {
  font-size: 22rpx;
  color: #999;
}

/* 图表样式 */
.chart-container {
  position: relative;
  height: 400rpx;
  margin: 20rpx 0;
}

.simple-chart {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.chart-line {
  position: relative;
  width: 100%;
  height: 100%;
}

.line-point {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  transform: translate(-50%, 50%);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.chart-x-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 15rpx;
  padding: 0 20rpx;
}

.x-label {
  font-size: 22rpx;
  color: #666;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  margin-top: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.legend-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

.legend-label {
  font-size: 24rpx;
  color: #333;
}

/* 数据表格 */
.data-table {
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: white;
}

.table-header {
  display: flex;
  background-color: #f5f5f5;
  border-bottom: 1rpx solid #e0e0e0;
}

.header-cell {
  flex: 1;
  padding: 20rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  border-right: 1rpx solid #e0e0e0;
}

.header-cell:last-child {
  border-right: none;
}

.header-cell.time {
  flex: 1.5;
}

.table-body {
  max-height: 600rpx;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 20rpx;
  font-size: 24rpx;
  color: #333;
  text-align: center;
  border-right: 1rpx solid #f0f0f0;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell.time {
  flex: 1.5;
  font-weight: 500;
  color: #666;
}

/* 无数据状态 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 20rpx;
  color: #999;
}

.no-data-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.no-data-text {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.no-data-desc {
  font-size: 24rpx;
  text-align: center;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .statistics-grid {
    grid-template-columns: 1fr;
  }
  
  .indicator-selector {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .chart-legend {
    flex-wrap: wrap;
    gap: 15rpx;
  }
  
  .table-body {
    max-height: 400rpx;
  }
}