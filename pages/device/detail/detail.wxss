/* 设备详情页面样式 */

/* 快速控制样式 */
.quick-controls {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.control-item {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #e0e0e0;
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.control-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.control-details {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 20rpx;
}

.control-param {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.param-label {
  font-size: 26rpx;
  color: #666;
  min-width: 80rpx;
}

.param-value {
  font-size: 26rpx;
  color: #08C160;
  font-weight: 600;
  min-width: 60rpx;
}

.picker-view {
  padding: 15rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  background-color: white;
  font-size: 26rpx;
  color: #333;
  min-width: 120rpx;
}

/* 滑块样式调整 */
.control-param slider {
  flex: 1;
  margin: 0 15rpx;
}

/* 输入框样式 */
.control-param input {
  padding: 15rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  background-color: white;
  font-size: 26rpx;
  min-width: 120rpx;
}

/* 设备信息 */
.device-info {
  padding: 0;
}

.device-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #E0E0E0;
}

.device-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #08C160 0%, #4DD582 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.device-icon {
  font-size: 60rpx;
}

.device-basic {
  flex: 1;
}

.device-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 10rpx;
}

.device-type {
  font-size: 26rpx;
  color: #666666;
  display: block;
  margin-bottom: 15rpx;
}

.device-status {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #08C160;
}

.device-status.offline .status-dot {
  background-color: #F44336;
}

.device-status.warning .status-dot {
  background-color: #FF9800;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #08C160;
}

.device-status.offline .status-text {
  color: #F44336;
}

.device-status.warning .status-text {
  color: #FF9800;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rpx;
  background-color: #E0E0E0;
}

.info-item {
  background-color: white;
  padding: 25rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-label {
  font-size: 24rpx;
  color: #666666;
}

.info-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

/* 实时状态 */
.status-grid {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.status-value {
  font-size: 32rpx;
  color: #08C160;
  font-weight: 600;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
  max-width: 300rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background-color: #E0E0E0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #2196F3;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-fill.fertilizer {
  background-color: #08C160;
}

.progress-text {
  font-size: 24rpx;
  color: #666666;
  min-width: 60rpx;
  text-align: right;
}

/* 运行统计 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.stat-item {
  text-align: center;
  padding: 30rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #08C160;
  display: block;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

/* 日志列表 */
.log-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  gap: 20rpx;
}

.log-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.log-icon.irrigation {
  background-color: #E3F2FD;
}

.log-icon.fertilizer {
  background-color: #E8F8EC;
}

.log-icon.maintenance {
  background-color: #FFF3E0;
}

.log-icon.alert {
  background-color: #FFEBEE;
}

.log-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.log-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.log-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.log-time {
  font-size: 22rpx;
  color: #999999;
}

.log-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.log-status.success {
  background-color: #E8F8EC;
  color: #08C160;
}

.log-status.warning {
  background-color: #FFF3E0;
  color: #FF9800;
}

.log-status.error {
  background-color: #FFEBEE;
  color: #F44336;
}

.view-more {
  font-size: 26rpx;
  color: #08C160;
  font-weight: 500;
}

/* 快捷操作 */
.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1rpx solid rgba(8, 193, 96, 0.1);
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #333333;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #08C160, #08C160);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.action-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
}

.action-btn:active::before {
  transform: scaleX(1);
}

.btn-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  background: linear-gradient(135deg, #08C160, #08C160);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(8, 193, 96, 0.3);
}

.btn-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
}

/* 维护信息 */
.maintenance-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.maintenance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.maintenance-item:last-child {
  border-bottom: none;
}

.maintenance-label {
  font-size: 28rpx;
  color: #333333;
}

.maintenance-value {
  font-size: 26rpx;
  color: #666666;
}