# 生产环境配置
spring:
  # 数据源配置
  datasource:
    url: **********************************************************************************************************************
    username: agriculture_user
    password: ${DB_PASSWORD:agriculture_password}
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
      auto-commit: true
      idle-timeout: 30000
      pool-name: AgricultureHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: -1ms

# 日志配置
logging:
  level:
    root: INFO
    com.agriculture.iot: INFO
    org.springframework.web: WARN
    com.baomidou.mybatisplus: WARN
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/agriculture-iot-backend.log
    max-size: 100MB
    max-history: 30

# 自定义配置
agriculture:
  # 文件上传路径
  upload:
    path: /data/uploads/
    url-prefix: /uploads/
  
  # 设备配置
  device:
    heartbeat-interval: 30000 # 心跳间隔（毫秒）
    offline-timeout: 60000 # 离线超时（毫秒）
    command-timeout: 5000 # 命令超时（毫秒）
  
  # 传感器数据配置
  sensor:
    # 数据保留天数
    data-retention-days: 365
    # 批量插入大小
    batch-size: 5000
    # 数据采集间隔（毫秒）
    data-collection-interval: 60000
    # 告警检查间隔（毫秒）
    alert-check-interval: 300000
  
  # 灌溉配置
  irrigation:
    default-duration: 30 # 默认灌溉时长（分钟）
    max-duration: 120 # 最大灌溉时长（分钟）
    default-water-amount: 50.0 # 默认用水量（升）
    safety-check-interval: 60000 # 安全检查间隔（毫秒）
  
  # 施肥配置
  fertilizer:
    default-amount: 10.0 # 默认施肥量（升）
    max-amount: 50.0 # 最大施肥量（升）
    default-concentration: 0.5 # 默认浓度（%）
    safety-check-interval: 60000 # 安全检查间隔（毫秒）
  
  # 报警配置
  alarm:
    notification-methods: email,sms,push # 通知方式
    high-priority-check-interval: 60000 # 高优先级检查间隔（毫秒）
    cleanup-days: 90 # 清理天数
  
  # 定时任务配置
  schedule:
    # 传感器数据清理任务
    data-cleanup:
      enabled: true
      cron: "0 0 2 * * ?"  # 每天凌晨2点执行
    # 设备状态检查任务
    device-status-check:
      enabled: true
      cron: "0 */5 * * * ?"  # 每5分钟执行一次
    # 计划执行检查任务
    schedule-execution-check:
      enabled: true
      cron: "0 * * * * ?"  # 每分钟执行一次
    # 逾期计划检查任务
    overdue-schedule-check:
      enabled: true
      cron: "0 0 * * * ?"  # 每小时执行一次
    # 每日报告生成任务
    daily-report:
      enabled: true
      cron: "0 0 8 * * ?"  # 每天早上8点执行
    # 线程池配置
    thread-pool:
      core-size: 10
      max-size: 20
      queue-capacity: 1000
      keep-alive-seconds: 300

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true