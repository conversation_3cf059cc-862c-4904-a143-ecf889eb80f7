<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriculture.iot.mapper.SensorDataMapper">

    <!-- 根据设备ID和传感器类型查找最新数据 -->
    <select id="findLatestByType" resultType="com.agriculture.iot.entity.SensorData">
        SELECT 
            id,
            device_id as deviceId,
            sensor_type_id as sensorTypeId,
            value,
            unit,
            created_at as createdAt
        FROM sensor_data
        WHERE device_id = #{deviceId} 
        AND sensor_type_id = #{sensorTypeId}
        ORDER BY created_at DESC
        LIMIT 1
    </select>

    <!-- 删除指定时间之前的数据 -->
    <delete id="deleteBefore">
        DELETE FROM sensor_data 
        WHERE created_at < #{cutoffDate}
    </delete>

</mapper>