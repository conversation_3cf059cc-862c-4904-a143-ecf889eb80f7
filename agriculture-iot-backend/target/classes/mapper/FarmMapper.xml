<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agriculture.iot.mapper.FarmMapper">

    <!-- 分页查询农场列表 -->
    <select id="selectFarmPage" resultType="com.agriculture.iot.entity.Farm">
        SELECT *
        FROM farms
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 查询农场统计数据 -->
    <select id="selectFarmStatistics" resultType="com.agriculture.iot.entity.Farm">
        SELECT f.*,
            (SELECT COUNT(*) FROM plots p WHERE p.farm_id = f.id) AS plot_count,
            (SELECT COUNT(*) FROM devices d JOIN plots p ON d.plot_id = p.id WHERE p.farm_id = f.id) AS device_count
        FROM farms f
        WHERE f.id = #{farmId}
    </select>

</mapper>