<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agriculture.iot.mapper.PlotMapper">

    <!-- 分页查询地块列表 -->
    <select id="selectPlotPage" resultType="com.agriculture.iot.entity.Plot">
        SELECT *
        FROM plots
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="farmId != null">
                AND farm_id = #{farmId}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 查询地块统计数据 -->
    <select id="selectPlotStatistics" resultType="com.agriculture.iot.entity.Plot">
        SELECT p.*,
            (SELECT COUNT(*) FROM devices d WHERE d.plot_id = p.id) AS device_count
        FROM plots p
        WHERE p.id = #{plotId}
    </select>

    <!-- 获取地块绑定的设备列表 -->
    <select id="getPlotDevices" resultType="java.lang.Object">
        SELECT 
            d.id,
            d.name,
            d.device_type,
            d.model,
            d.status,
            d.online_status,
            d.battery_level,
            dt.name as type_name,
            dt.description as type_description
        FROM devices d
        LEFT JOIN device_types dt ON d.device_type_id = dt.id
        WHERE d.plot_id = #{plotId}
        ORDER BY d.created_at DESC
    </select>

    <!-- 为地块绑定设备 -->
    <update id="bindDeviceToPlot">
        UPDATE devices 
        SET plot_id = #{plotId}, updated_at = NOW()
        WHERE id = #{deviceId}
    </update>

    <!-- 解绑设备 -->
    <update id="unbindDeviceFromPlot">
        UPDATE devices 
        SET plot_id = NULL, updated_at = NOW()
        WHERE id = #{deviceId} AND plot_id = #{plotId}
    </update>

    <!-- 获取地块设备数量 -->
    <select id="getDeviceCountByPlotId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM devices WHERE plot_id = #{plotId}
    </select>

    <!-- 获取地块在线设备数量 -->
    <select id="getOnlineDeviceCountByPlotId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM devices 
        WHERE plot_id = #{plotId} AND online_status = 1
    </select>

    <!-- 获取地块传感器数量 -->
    <select id="getSensorCountByPlotId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM devices d
        LEFT JOIN device_types dt ON d.device_type_id = dt.id
        WHERE d.plot_id = #{plotId} AND dt.category = 'sensor'
    </select>

    <!-- 获取地块本月灌溉次数 -->
    <select id="getIrrigationCountByPlotId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM irrigation_records ir
        LEFT JOIN devices d ON ir.device_id = d.id
        WHERE d.plot_id = #{plotId} 
        AND ir.created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')
        AND ir.created_at &lt; DATE_ADD(DATE_FORMAT(NOW(), '%Y-%m-01'), INTERVAL 1 MONTH)
    </select>

    <!-- 获取地块本月施肥次数 -->
    <select id="getFertilizerCountByPlotId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM fertilizer_records fr
        LEFT JOIN devices d ON fr.device_id = d.id
        WHERE d.plot_id = #{plotId} 
        AND fr.created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')
        AND fr.created_at &lt; DATE_ADD(DATE_FORMAT(NOW(), '%Y-%m-01'), INTERVAL 1 MONTH)
    </select>

    <!-- 获取地块本月报警次数 -->
    <select id="getAlarmCountByPlotId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM alarms a
        LEFT JOIN devices d ON a.device_id = d.id
        WHERE d.plot_id = #{plotId} 
        AND a.created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')
        AND a.created_at &lt; DATE_ADD(DATE_FORMAT(NOW(), '%Y-%m-01'), INTERVAL 1 MONTH)
    </select>

    <!-- 获取地块最新土壤湿度 -->
    <select id="getLatestSoilHumidity" resultType="java.math.BigDecimal">
        SELECT sd.value FROM sensor_data sd
        LEFT JOIN devices d ON sd.device_id = d.id
        LEFT JOIN sensor_types st ON sd.sensor_type_id = st.id
        WHERE d.plot_id = #{plotId} 
        AND st.type_code = 'soil_humidity'
        ORDER BY sd.created_at DESC
        LIMIT 1
    </select>

    <!-- 获取地块最新土壤温度 -->
    <select id="getLatestSoilTemperature" resultType="java.math.BigDecimal">
        SELECT sd.value FROM sensor_data sd
        LEFT JOIN devices d ON sd.device_id = d.id
        LEFT JOIN sensor_types st ON sd.sensor_type_id = st.id
        WHERE d.plot_id = #{plotId} 
        AND st.type_code = 'soil_temperature'
        ORDER BY sd.created_at DESC
        LIMIT 1
    </select>

    <!-- 获取地块最新pH值 -->
    <select id="getLatestPhValue" resultType="java.math.BigDecimal">
        SELECT sd.value FROM sensor_data sd
        LEFT JOIN devices d ON sd.device_id = d.id
        LEFT JOIN sensor_types st ON sd.sensor_type_id = st.id
        WHERE d.plot_id = #{plotId} 
        AND st.type_code = 'ph_value'
        ORDER BY sd.created_at DESC
        LIMIT 1
    </select>

</mapper>