/* 设备选择页面样式 */

.page-container {
  background-color: #F5F5F5;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 操作提示头部 */
.action-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  color: white;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

.action-icon .icon-text {
  font-size: 48rpx;
  color: white;
}

.action-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.action-title {
  font-size: 32rpx;
  font-weight: 600;
}

.action-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 可用设备计数 */
.available-count {
  font-size: 24rpx;
  color: #666666;
  background-color: #E8F8EC;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* 设备列表 */
.device-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: white;
  border-radius: 12rpx;
  border: 1px solid #E0E0E0;
  transition: all 0.3s ease;
}

.device-item:active {
  transform: scale(0.98);
  background-color: #F8F9FA;
}

.device-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  background-color: #08C160;
}

.device-icon .icon-text {
  font-size: 40rpx;
  color: white;
}

.device-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.device-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.device-location {
  font-size: 24rpx;
  color: #666666;
}

.device-status {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 8rpx;
}

.status-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #08C160;
}

.status-indicator.offline {
  background-color: #F44336;
}

.status-text {
  font-size: 22rpx;
  color: #08C160;
}

.capacity-text {
  font-size: 20rpx;
  color: #999999;
}

.device-action {
  margin-left: 20rpx;
}

.action-btn {
  font-size: 26rpx;
  color: #08C160;
  padding: 12rpx 24rpx;
  border: 1px solid #08C160;
  border-radius: 20rpx;
}

/* 无设备提示 */
.no-device {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.no-device-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.no-device-text {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
}

.no-device-desc {
  font-size: 24rpx;
  color: #666666;
}

/* 批量操作选项 */
.batch-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.batch-option {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: white;
  border-radius: 12rpx;
  border: 1px solid #E0E0E0;
  transition: all 0.3s ease;
}

.batch-option:active {
  transform: scale(0.98);
  background-color: #F8F9FA;
}

.batch-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.batch-icon .icon-text {
  font-size: 32rpx;
  color: white;
}

.batch-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.batch-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.batch-desc {
  font-size: 24rpx;
  color: #666666;
}

/* 区域选择 */
.zone-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 30rpx;
}

.zone-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background-color: white;
  border: 2rpx solid #E0E0E0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.zone-item.selected {
  border-color: #08C160;
  background-color: #E8F8EC;
}

.zone-item:active {
  transform: scale(0.95);
}

.zone-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.zone-devices {
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.zone-status {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  color: white;
}

.zone-status.online {
  background-color: #08C160;
}

.zone-status.partial {
  background-color: #FF9800;
}

.zone-actions {
  display: flex;
  gap: 20rpx;
}

.zone-actions .btn {
  flex: 1;
}

/* 操作底部 */
.action-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background-color: white;
  border-top: 1px solid #E0E0E0;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-footer .btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

/* 背景色类 */
.primary-bg {
  background-color: #08C160;
}

.secondary-bg {
  background-color: #1976D2;
}

.warning-bg {
  background-color: #FF9800;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .zone-grid {
    grid-template-columns: 1fr;
  }
  
  .action-header {
    flex-direction: column;
    text-align: center;
  }
  
  .action-icon {
    margin-right: 0;
    margin-bottom: 16rpx;
  }
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #08C160;
  color: white;
}

.btn-primary:disabled {
  background-color: #CCCCCC;
  color: #666666;
}

.btn-outline {
  background-color: white;
  color: #08C160;
  border: 1px solid #08C160;
}

.btn:active {
  transform: scale(0.95);
}