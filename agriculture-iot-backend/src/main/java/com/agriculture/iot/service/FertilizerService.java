package com.agriculture.iot.service;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.FertilizerRecord;
import com.agriculture.iot.entity.FertilizerSchedule;
import com.agriculture.iot.entity.FertilizerType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 施肥服务接口
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface FertilizerService extends IService<FertilizerRecord> {

    /**
     * 执行施肥
     */
    Map<String, Object> applyFertilizer(FertilizerRecord.ApplyRequest request);

    /**
     * 停止施肥
     */
    Result<Void> stopFertilizer(FertilizerRecord.StopRequest request);

    /**
     * 获取肥料配方列表
     */
    List<FertilizerRecord.Formula> getFertilizerFormulas(String cropType, String growthStage);

    /**
     * 分页获取施肥记录
     */
    IPage<FertilizerRecord> getFertilizerRecords(
            Page<FertilizerRecord> page, Long plotId, Long deviceId, 
            LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取施肥计划列表
     */
    IPage<FertilizerSchedule> getFertilizerSchedules(
            Page<FertilizerSchedule> page, Long plotId, Long deviceId, Integer status);

    /**
     * 创建施肥计划
     */
    FertilizerSchedule createFertilizerSchedule(FertilizerSchedule schedule);

    /**
     * 更新施肥计划
     */
    FertilizerSchedule updateFertilizerSchedule(FertilizerSchedule schedule);

    /**
     * 删除施肥计划
     */
    boolean deleteFertilizerSchedule(Long scheduleId);

    /**
     * 获取肥料类型列表
     */
    List<FertilizerType> getFertilizerTypes();

    /**
     * 获取施肥状态
     */
    Result<FertilizerRecord.StatusInfo> getFertilizerStatus(Long recordId);

    /**
     * 获取施肥统计
     */
    Result<FertilizerRecord.Statistics> getFertilizerStatistics(
            Long plotId, Long deviceId, Integer days);

    /**
     * 获取效果分析
     */
    List<FertilizerRecord.EffectivenessData> getFertilizerEffectiveness(
            Long plotId, Long deviceId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 创建自定义配方
     */
    Result<FertilizerRecord.Formula> createCustomFormula(FertilizerRecord.Formula formula);

    /**
     * 获取推荐配方
     */
    List<FertilizerRecord.Formula> getRecommendedFormulas(
            String cropType, String growthStage, String soilType, Long plotId);

    /**
     * 获取施肥报告
     */
    Result<FertilizerRecord.ReportData> getFertilizerReport(
            String reportType, Long deviceId, Long plotId, 
            LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 批量执行施肥
     */
    Map<String, Object> batchApplyFertilizer(FertilizerRecord.BatchApplyRequest request);

    /**
     * 获取营养元素分析
     */
    FertilizerRecord.NutrientAnalysis getNutrientAnalysis(Long plotId, Integer days);

    /**
     * 获取施肥建议
     */
    List<FertilizerRecord.Suggestion> getFertilizerSuggestions(Long deviceId, Long plotId);

    /**
     * 设置施肥提醒
     */
    boolean setFertilizerReminder(FertilizerRecord.ReminderConfig config);

    /**
     * 获取施肥提醒配置
     */
    List<FertilizerRecord.ReminderConfig> getFertilizerReminders(Long deviceId, Long plotId);

    /**
     * 获取成本分析
     */
    FertilizerRecord.CostAnalysis getFertilizerCostAnalysis(
            Long plotId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取施肥计划列表
     */
    Result<IPage<FertilizerRecord.Plan>> getPlans(
            Integer current, Integer size, Long plotId, String status);

    /**
     * 创建施肥计划
     */
    Result<FertilizerRecord.Plan> createPlan(FertilizerRecord.Plan plan);

    /**
     * 更新施肥计划
     */
    Result<FertilizerRecord.Plan> updatePlan(Long id, FertilizerRecord.Plan plan);

    /**
     * 删除施肥计划
     */
    Result<Void> deletePlan(Long id);

    /**
     * 执行施肥计划
     */
    Result<FertilizerRecord> executePlan(Long planId);

    /**
     * 获取施肥模板列表
     */
    Result<IPage<FertilizerRecord.Template>> getTemplates(
            Integer current, Integer size, String cropType);

    /**
     * 创建施肥模板
     */
    Result<FertilizerRecord.Template> createTemplate(FertilizerRecord.Template template);

    /**
     * 更新施肥模板
     */
    Result<FertilizerRecord.Template> updateTemplate(Long id, FertilizerRecord.Template template);

    /**
     * 删除施肥模板
     */
    Result<Void> deleteTemplate(Long id);

    /**
     * 获取施肥提醒列表
     */
    Result<IPage<FertilizerRecord.Reminder>> getReminders(
            Integer current, Integer size, String status);

    /**
     * 创建施肥提醒
     */
    Result<FertilizerRecord.Reminder> createReminder(FertilizerRecord.Reminder reminder);

    /**
     * 更新施肥提醒
     */
    Result<FertilizerRecord.Reminder> updateReminder(Long id, FertilizerRecord.Reminder reminder);

    /**
     * 删除施肥提醒
     */
    Result<Void> deleteReminder(Long id);
}