Page({
  data: {
    startDate: '',
    endDate: '',
    indicators: [
      { type: 'humidity', name: '土壤湿度', icon: '💧', selected: true },
      { type: 'temperature', name: '土壤温度', icon: '🌡️', selected: false },
      { type: 'ph', name: 'PH值', icon: '🧪', selected: false },
      { type: 'ec', name: 'EC值', icon: '⚡', selected: false }
    ],
    statisticsData: [],
    chartData: {
      datasets: [],
      xLabels: []
    },
    echartsData: [],
    tableData: [],
    tableHeaders: [],
    noData: false
  },

  onLoad(options) {
    this.initDefaultDates();
    this.queryData();
  },

  // 初始化默认日期（最近7天）
  initDefaultDates() {
    const now = new Date();
    const endDate = this.formatDate(now);
    const startDate = this.formatDate(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000));
    
    this.setData({
      startDate: startDate,
      endDate: endDate
    });
  },

  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  onStartDateChange(e) {
    this.setData({
      startDate: e.detail.value
    });
  },

  onEndDateChange(e) {
    this.setData({
      endDate: e.detail.value
    });
  },

  toggleIndicator(e) {
    const type = e.currentTarget.dataset.type;
    const indicators = this.data.indicators.map(item => {
      if (item.type === type) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });
    
    this.setData({
      indicators: indicators
    });
  },

  queryData() {
    const selectedIndicators = this.data.indicators.filter(item => item.selected);
    
    if (selectedIndicators.length === 0) {
      wx.showToast({
        title: '请至少选择一个监测指标',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.startDate || !this.data.endDate) {
      wx.showToast({
        title: '请选择查询时间范围',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '查询中...'
    });

    // 模拟数据查询
    setTimeout(() => {
      this.generateMockData(selectedIndicators);
      wx.hideLoading();
    }, 1500);
  },

  generateMockData(selectedIndicators) {
    // 生成统计数据
    const statisticsData = selectedIndicators.map(indicator => {
      const values = this.generateRandomValues(indicator.type);
      return {
        type: indicator.type,
        icon: indicator.icon,
        name: indicator.name,
        value: values.avg.toFixed(1) + this.getUnit(indicator.type),
        description: `最高: ${values.max.toFixed(1)} 最低: ${values.min.toFixed(1)}`
      };
    });

    // 生成图表数据
    const chartData = this.generateChartData(selectedIndicators);
    
    // 生成ECharts数据
    const echartsData = this.generateEChartsData(selectedIndicators);
    
    // 生成表格数据
    const tableData = this.generateTableData(selectedIndicators);
    
    this.setData({
      statisticsData: statisticsData,
      chartData: chartData,
      echartsData: echartsData,
      tableData: tableData.data,
      tableHeaders: tableData.headers,
      noData: false
    });
  },

  generateRandomValues(type) {
    const ranges = {
      humidity: { min: 30, max: 80 },
      temperature: { min: 15, max: 30 },
      ph: { min: 6.0, max: 7.5 },
      ec: { min: 0.5, max: 2.0 }
    };
    
    const range = ranges[type];
    const values = [];
    
    for (let i = 0; i < 20; i++) {
      values.push(range.min + Math.random() * (range.max - range.min));
    }
    
    return {
      min: Math.min(...values),
      max: Math.max(...values),
      avg: values.reduce((a, b) => a + b, 0) / values.length
    };
  },

  getUnit(type) {
    const units = {
      humidity: '%',
      temperature: '°C',
      ph: '',
      ec: 'mS/cm'
    };
    return units[type] || '';
  },

  generateChartData(selectedIndicators) {
    const colors = ['#2E7D32', '#1976D2', '#F57C00', '#7B1FA2'];
    const datasets = selectedIndicators.map((indicator, index) => {
      const data = [];
      for (let i = 0; i < 7; i++) {
        data.push({
          x: (i * 100) / 6,
          y: 20 + Math.random() * 60
        });
      }
      
      return {
        label: indicator.name,
        color: colors[index % colors.length],
        data: data
      };
    });
    
    const xLabels = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000);
      xLabels.push(`${date.getMonth() + 1}/${date.getDate()}`);
    }
    
    return {
      datasets: datasets,
      xLabels: xLabels
    };
  },

  // 生成ECharts格式的数据
  generateEChartsData(selectedIndicators) {
    const chartData = [];
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000);
      const dayData = {
        name: `${date.getMonth() + 1}/${date.getDate()}`,
        humidity: 50 + Math.random() * 30,
        temperature: 18 + Math.random() * 12,
        ph: 6.0 + Math.random() * 1.5,
        ec: 0.8 + Math.random() * 1.2
      };
      chartData.push(dayData);
    }
    
    return chartData;
  },

  // 获取多指标对比图表数据
  getMultiIndicatorChartData() {
    const selectedIndicators = this.data.indicators.filter(item => item.selected);
    const chartData = this.generateEChartsData(selectedIndicators);
    
    return chartData.map(item => ({
      name: item.name,
      humidity: item.humidity,
      temperature: item.temperature,
      ph: item.ph,
      ec: item.ec
    }));
  },

  // 获取单个指标的趋势数据
  getSingleIndicatorData(type) {
    const chartData = this.generateEChartsData([{ type }]);
    return chartData.map(item => ({
      name: item.name,
      value: item[type]
    }));
  },

  generateTableData(selectedIndicators) {
    const headers = selectedIndicators.map(item => item.name);
    const data = [];
    
    for (let i = 0; i < 10; i++) {
      const date = new Date(Date.now() - i * 60 * 60 * 1000);
      const timeStr = `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      
      const values = selectedIndicators.map(indicator => {
        const mockValue = this.generateRandomValues(indicator.type).avg;
        return mockValue.toFixed(1) + this.getUnit(indicator.type);
      });
      
      data.push({
        time: timeStr,
        values: values
      });
    }
    
    return {
      headers: headers,
      data: data
    };
  },

  resetQuery() {
    this.setData({
      indicators: this.data.indicators.map(item => ({
        ...item,
        selected: item.type === 'humidity'
      })),
      statisticsData: [],
      chartData: { datasets: [], xLabels: [] },
      tableData: [],
      tableHeaders: [],
      noData: false
    });
    
    this.initDefaultDates();
  },

  exportChart() {
    wx.showToast({
      title: '正在导出图表...',
      icon: 'loading'
    });
    
    setTimeout(() => {
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      });
    }, 2000);
  },

  // 图表导出事件
  onChartExport(e) {
    wx.showToast({
      title: '图表导出功能',
      icon: 'none'
    });
  },

  // 图表全屏事件
  onChartFullscreen(e) {
    wx.showToast({
      title: '图表全屏功能',
      icon: 'none'
    });
  },

  exportTableData() {
    wx.showToast({
      title: '正在导出数据...',
      icon: 'loading'
    });
    
    setTimeout(() => {
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      });
    }, 2000);
  },

  onPullDownRefresh() {
    this.queryData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  }
})