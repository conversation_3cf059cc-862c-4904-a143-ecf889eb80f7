<!--个人中心页面 - 现代简约风格-->
<view class="page-container">
  <view class="container">

    <!-- 用户信息区域 - 直接在渐变背景上 -->
    <view class="user-profile-section">
      <!-- 头像区域 -->
      <view class="user-avatar-wrapper">
        <view class="user-avatar">
          <view class="avatar-placeholder">👤</view>
        </view>
      </view>
      
      <!-- 用户信息区域 -->
      <view class="user-info-wrapper">
        <view class="user-main-info">
          <text class="user-name">{{userInfo.name || '请设置昵称'}}</text>
          <view class="user-role-section">
            <text class="user-role">{{userInfo.role || '农场主'}}</text>
          </view>
        </view>
        
        <!-- 编辑按钮 -->
        <view class="profile-actions">
          <button class="btn btn-small btn-outline" bindtap="editProfile">
            编辑资料
          </button>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="card grid-menu-card">
      <view class="menu-grid-simple">
        <view class="grid-item active" bindtap="navigateToSettings">
          <view class="grid-icon"><text class="icon-text">⚙️</text></view>
          <text class="grid-title">系统设置</text>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="card logout-card">
      <button class="btn btn-large logout-btn" bindtap="logout">
        退出登录
      </button>
    </view>

  </view>
</view>