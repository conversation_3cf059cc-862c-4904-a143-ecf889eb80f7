<!--运行记录页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 标签导航 -->
    <view class="tab-nav">
      <view class="tab-item {{activeTab === 'operation' ? 'active' : ''}}" 
            bindtap="switchTab" data-tab="operation">
        操作日志
      </view>
      <view class="tab-item {{activeTab === 'reports' ? 'active' : ''}}" 
            bindtap="switchTab" data-tab="reports">
        运行报告
      </view>
      <view class="tab-item {{activeTab === 'alerts' ? 'active' : ''}}" 
            bindtap="switchTab" data-tab="alerts">
        异常记录
      </view>
    </view>

    <!-- 筛选器 -->
    <view class="filter-bar">
      <picker mode="date" value="{{filters.startDate}}" 
              bindchange="onStartDateChange" class="date-picker">
        <view class="picker-btn">
          <text class="picker-icon">📅</text>
          <text class="picker-text">{{filters.startDate}}</text>
        </view>
      </picker>
      
      <text class="date-separator">至</text>
      
      <picker mode="date" value="{{filters.endDate}}" 
              bindchange="onEndDateChange" class="date-picker">
        <view class="picker-btn">
          <text class="picker-icon">📅</text>
          <text class="picker-text">{{filters.endDate}}</text>
        </view>
      </picker>
      
      <button class="filter-btn" bindtap="applyFilters">筛选</button>
    </view>

    <!-- 操作日志标签页 -->
    <view wx:if="{{activeTab === 'operation'}}">
      
      <!-- 统计卡片 -->
      <view class="stats-cards">
        <view class="stat-card">
          <text class="stat-value">{{operationStats.totalOperations}}</text>
          <text class="stat-label">总操作次数</text>
        </view>
        <view class="stat-card">
          <text class="stat-value">{{operationStats.successRate}}%</text>
          <text class="stat-label">成功率</text>
        </view>
        <view class="stat-card">
          <text class="stat-value">{{operationStats.todayOperations}}</text>
          <text class="stat-label">今日操作</text>
        </view>
      </view>

      <!-- 操作类型筛选 -->
      <view class="operation-filter">
        <scroll-view scroll-x class="filter-scroll">
          <view class="filter-tags">
            <view class="filter-tag {{operationFilter === 'all' ? 'active' : ''}}" 
                  bindtap="filterOperations" data-type="all">
              全部
            </view>
            <view class="filter-tag {{operationFilter === 'irrigation' ? 'active' : ''}}" 
                  bindtap="filterOperations" data-type="irrigation">
              灌溉
            </view>
            <view class="filter-tag {{operationFilter === 'fertilizer' ? 'active' : ''}}" 
                  bindtap="filterOperations" data-type="fertilizer">
              施肥
            </view>
            <view class="filter-tag {{operationFilter === 'maintenance' ? 'active' : ''}}" 
                  bindtap="filterOperations" data-type="maintenance">
              维护
            </view>
            <view class="filter-tag {{operationFilter === 'config' ? 'active' : ''}}" 
                  bindtap="filterOperations" data-type="config">
              配置
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 操作日志列表 -->
      <view class="log-list">
        <view class="log-item" wx:for="{{filteredOperationLogs}}" wx:key="id">
          <view class="log-header">
            <view class="log-icon {{item.type}}">
              <text>{{item.icon}}</text>
            </view>
            <view class="log-info">
              <text class="log-title">{{item.title}}</text>
              <text class="log-time">{{item.time}}</text>
            </view>
            <view class="log-status {{item.status}}">
              <text>{{item.statusText}}</text>
            </view>
          </view>
          <view class="log-content">
            <text class="log-description">{{item.description}}</text>
            <view class="log-details" wx:if="{{item.details}}">
              <view class="detail-item" wx:for="{{item.details}}" wx:key="label" wx:for-item="detail">
                <text class="detail-label">{{detail.label}}:</text>
                <text class="detail-value">{{detail.value}}</text>
              </view>
            </view>
          </view>
          <view class="log-actions" wx:if="{{item.canRetry || item.canExport}}">
            <button class="btn btn-small btn-outline" wx:if="{{item.canRetry}}" 
                    bindtap="retryOperation" data-id="{{item.id}}">
              重试
            </button>
            <button class="btn btn-small btn-outline" wx:if="{{item.canExport}}" 
                    bindtap="exportLog" data-id="{{item.id}}">
              导出
            </button>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMoreOperationLogs}}">
        <button class="btn btn-outline" bindtap="loadMoreOperationLogs">
          加载更多
        </button>
      </view>

    </view>

    <!-- 运行报告标签页 -->
    <view wx:if="{{activeTab === 'reports'}}">
      
      <!-- 报告类型选择 -->
      <view class="report-types">
        <view class="type-item {{reportType === 'daily' ? 'active' : ''}}" 
              bindtap="selectReportType" data-type="daily">
          <text class="type-icon">📊</text>
          <text class="type-text">日报</text>
        </view>
        <view class="type-item {{reportType === 'weekly' ? 'active' : ''}}" 
              bindtap="selectReportType" data-type="weekly">
          <text class="type-icon">📈</text>
          <text class="type-text">周报</text>
        </view>
        <view class="type-item {{reportType === 'monthly' ? 'active' : ''}}" 
              bindtap="selectReportType" data-type="monthly">
          <text class="type-icon">📋</text>
          <text class="type-text">月报</text>
        </view>
      </view>

      <!-- 运行统计 -->
      <view class="card">
        <view class="card-title">运行统计</view>
        <view class="report-stats">
          <view class="stat-row">
            <text class="stat-name">总运行时间</text>
            <text class="stat-data">{{reportData.totalRunTime}}</text>
          </view>
          <view class="stat-row">
            <text class="stat-name">灌溉次数</text>
            <text class="stat-data">{{reportData.irrigationCount}}次</text>
          </view>
          <view class="stat-row">
            <text class="stat-name">施肥次数</text>
            <text class="stat-data">{{reportData.fertilizerCount}}次</text>
          </view>
          <view class="stat-row">
            <text class="stat-name">用水量</text>
            <text class="stat-data">{{reportData.waterUsage}}L</text>
          </view>
          <view class="stat-row">
            <text class="stat-name">肥料用量</text>
            <text class="stat-data">{{reportData.fertilizerUsage}}kg</text>
          </view>
          <view class="stat-row">
            <text class="stat-name">平均效率</text>
            <text class="stat-data">{{reportData.efficiency}}%</text>
          </view>
        </view>
      </view>

      <!-- 使用量图表 -->
      <view class="card">
        <view class="card-title">使用量趋势</view>
        <view class="chart-container">
          <view class="chart-placeholder">
            <text class="chart-icon">📊</text>
            <text class="chart-text">图表区域</text>
            <text class="chart-note">用水量和肥料使用量趋势图</text>
          </view>
        </view>
      </view>

      <!-- 效率分析 -->
      <view class="card">
        <view class="card-title">效率分析</view>
        <view class="efficiency-list">
          <view class="efficiency-item">
            <view class="efficiency-name">
              <text class="item-icon">💧</text>
              <text class="item-text">灌溉效率</text>
            </view>
            <view class="efficiency-bar">
              <view class="bar-fill" style="width: {{reportData.irrigationEfficiency}}%"></view>
            </view>
            <text class="efficiency-value">{{reportData.irrigationEfficiency}}%</text>
          </view>
          
          <view class="efficiency-item">
            <view class="efficiency-name">
              <text class="item-icon">🌱</text>
              <text class="item-text">施肥效率</text>
            </view>
            <view class="efficiency-bar">
              <view class="bar-fill fertilizer" style="width: {{reportData.fertilizerEfficiency}}%"></view>
            </view>
            <text class="efficiency-value">{{reportData.fertilizerEfficiency}}%</text>
          </view>
          
          <view class="efficiency-item">
            <view class="efficiency-name">
              <text class="item-icon">⚙️</text>
              <text class="item-text">设备运行率</text>
            </view>
            <view class="efficiency-bar">
              <view class="bar-fill system" style="width: {{reportData.systemUptime}}%"></view>
            </view>
            <text class="efficiency-value">{{reportData.systemUptime}}%</text>
          </view>
        </view>
      </view>

      <!-- 导出报告 -->
      <view class="export-section">
        <button class="btn btn-primary" bindtap="exportReport">
          📄 导出报告
        </button>
        <button class="btn btn-outline" bindtap="shareReport">
          📤 分享报告
        </button>
      </view>

    </view>

    <!-- 异常记录标签页 -->
    <view wx:if="{{activeTab === 'alerts'}}">
      
      <!-- 异常统计 -->
      <view class="alert-stats">
        <view class="alert-stat">
          <text class="alert-count warning">{{alertStats.warnings}}</text>
          <text class="alert-label">警告</text>
        </view>
        <view class="alert-stat">
          <text class="alert-count error">{{alertStats.errors}}</text>
          <text class="alert-label">错误</text>
        </view>
        <view class="alert-stat">
          <text class="alert-count resolved">{{alertStats.resolved}}</text>
          <text class="alert-label">已解决</text>
        </view>
      </view>

      <!-- 异常级别筛选 -->
      <view class="alert-filter">
        <view class="filter-tag {{alertFilter === 'all' ? 'active' : ''}}" 
              bindtap="filterAlerts" data-level="all">
          全部
        </view>
        <view class="filter-tag {{alertFilter === 'error' ? 'active' : ''}}" 
              bindtap="filterAlerts" data-level="error">
          错误
        </view>
        <view class="filter-tag {{alertFilter === 'warning' ? 'active' : ''}}" 
              bindtap="filterAlerts" data-level="warning">
          警告
        </view>
        <view class="filter-tag {{alertFilter === 'resolved' ? 'active' : ''}}" 
              bindtap="filterAlerts" data-level="resolved">
          已解决
        </view>
      </view>

      <!-- 异常记录列表 -->
      <view class="alert-list">
        <view class="alert-item {{item.level}}" wx:for="{{filteredAlerts}}" wx:key="id">
          <view class="alert-header">
            <view class="alert-icon">
              <text>{{item.icon}}</text>
            </view>
            <view class="alert-info">
              <text class="alert-title">{{item.title}}</text>
              <text class="alert-time">{{item.time}}</text>
            </view>
            <view class="alert-level {{item.level}}">
              <text>{{item.levelText}}</text>
            </view>
          </view>
          <view class="alert-content">
            <text class="alert-description">{{item.description}}</text>
            <view class="alert-solution" wx:if="{{item.solution}}">
              <text class="solution-label">建议解决方案:</text>
              <text class="solution-text">{{item.solution}}</text>
            </view>
          </view>
          <view class="alert-actions">
            <button class="btn btn-small btn-outline" wx:if="{{!item.resolved}}" 
                    bindtap="resolveAlert" data-id="{{item.id}}">
              标记为已解决
            </button>
            <button class="btn btn-small btn-outline" bindtap="viewAlertDetail" data-id="{{item.id}}">
              查看详情
            </button>
          </view>
        </view>
      </view>

    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{showEmptyState}}">
      <text class="empty-icon">📝</text>
      <text class="empty-text">暂无记录</text>
      <text class="empty-desc">选择不同的时间范围查看更多记录</text>
    </view>

  </view>
</view>