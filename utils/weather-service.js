// 天气服务 - 使用和风天气API
class WeatherService {
  constructor() {
    // 和风天气API配置
    // 注意：当前使用公共API地址，建议申请专用API Host
    this.apiKey = '90cfbc1071e14649b74ece39c22c665e'; // 测试用key，请申请正式key
    this.baseUrl = 'https://n36x87rhfk.re.qweatherapi.com';
    
    // 错误重试配置
    this.maxRetries = 2;
    this.retryDelay = 1000;
  }

  // 获取当前天气
  async getCurrentWeather(location) {
    console.log('开始获取天气数据，位置:', location);
    
    return new Promise((resolve, reject) => {
      // 使用API KEY方式请求
      wx.request({
        url: `${this.baseUrl}/v7/weather/now`,
        data: {
          location: `${location.longitude},${location.latitude}`,
          key: this.apiKey
        },
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('和风天气API响应:', res);
          
          // 检查HTTP状态码
          if (res.statusCode === 200) {
            // 检查和风天气API返回的业务状态码
            if (res.data && res.data.code === '200') {
              const formattedData = this.formatWeatherData(res.data.now);
              console.log('天气数据格式化完成:', formattedData);
              resolve(formattedData);
              return;
            } else {
              const code = res.data ? res.data.code : 'unknown';
              console.error('和风天气API业务错误:', code, res.data);
              
              // 根据错误码提供具体错误信息
              let errorMsg = '天气数据获取失败';
              switch (code) {
                case '401':
                  errorMsg = 'API认证失败，请检查API密钥';
                  break;
                case '402':
                  errorMsg = 'API请求超出限制';
                  break;
                case '403':
                  errorMsg = 'API访问被拒绝';
                  break;
                case '404':
                  errorMsg = '查询位置不存在';
                  break;
                case '429':
                  errorMsg = 'API请求过于频繁';
                  break;
                default:
                  errorMsg = `API错误: ${code}`;
              }
              reject(new Error(errorMsg));
            }
          } else {
            const errorMsg = `HTTP错误: ${res.statusCode}`;
            console.error(errorMsg);
            
            if (res.statusCode === 403) {
              reject(new Error('API访问被拒绝，请检查API密钥或配额'));
            } else if (res.statusCode === 401) {
              reject(new Error('API认证失败'));
            } else {
              reject(new Error(errorMsg));
            }
          }
        },
        fail: (error) => {
          console.error('网络请求失败:', error);
          
          if (error.errMsg && error.errMsg.includes('request:fail')) {
            if (error.errMsg.includes('timeout')) {
              reject(new Error('网络请求超时'));
            } else if (error.errMsg.includes('network error')) {
              reject(new Error('网络连接错误'));
            } else {
              reject(new Error('网络请求失败'));
            }
          } else {
            reject(new Error(`请求失败: ${error.errMsg || '未知错误'}`));
          }
        }
      });
    });
  }

  // 获取天气预报
  async getWeatherForecast(location, days = 3) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}/v7/weather/${days}d`,
        data: {
          location: `${location.longitude},${location.latitude}`,
          key: this.apiKey
        },
        success: (res) => {
          if (res.data.code === '200') {
            resolve(res.data.daily);
          } else {
            reject(new Error('获取天气预报失败'));
          }
        },
        fail: reject
      });
    });
  }

  // 获取生活指数（包含农业指数）
  async getLifeIndex(location) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}/v7/indices/1d`,
        data: {
          location: `${location.longitude},${location.latitude}`,
          type: '1,2,3,16', // 运动、洗车、穿衣、农业指数
          key: this.apiKey
        },
        success: (res) => {
          if (res.data.code === '200') {
            resolve(res.data.daily);
          } else {
            reject(new Error('获取生活指数失败'));
          }
        },
        fail: reject
      });
    });
  }


  // 格式化天气数据
  formatWeatherData(rawData) {
    return {
      temperature: parseInt(rawData.temp),
      weather: rawData.text,
      humidity: parseInt(rawData.humidity),
      windLevel: rawData.windScale,
      windDir: rawData.windDir,
      feelsLike: parseInt(rawData.feelsLike),
      visibility: rawData.vis,
      pressure: rawData.pressure,
      updateTime: rawData.obsTime
    };
  }

  // 生成模拟数据（开发测试用）
  getMockWeatherData() {
    const weatherTypes = ['晴', '多云', '阴', '小雨', '中雨'];
    const randomWeather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];
    
    return {
      temperature: Math.floor(Math.random() * 20) + 15, // 15-35度
      weather: randomWeather,
      humidity: Math.floor(Math.random() * 40) + 40, // 40-80%
      windLevel: Math.floor(Math.random() * 5) + 1, // 1-5级
      windDir: '东南风',
      feelsLike: Math.floor(Math.random() * 20) + 15,
      visibility: Math.floor(Math.random() * 10) + 5, // 5-15km
      pressure: Math.floor(Math.random() * 50) + 1000, // 1000-1050hPa
      updateTime: new Date().toISOString()
    };
  }

  // 获取农业建议
  getAgricultureAdvice(weatherData) {
    const { temperature, humidity, weather } = weatherData;
    const advices = [];

    // 温度建议
    if (temperature > 35) {
      advices.push('🌡️ 高温警告：及时遮阳降温，增加灌溉频次');
    } else if (temperature < 5) {
      advices.push('❄️ 低温预警：做好防冻保温措施');
    } else if (temperature >= 20 && temperature <= 28) {
      advices.push('✅ 温度适宜：适合进行各项农事活动');
    }

    // 湿度建议
    if (humidity > 80) {
      advices.push('💧 湿度偏高：注意通风，预防病害');
    } else if (humidity < 30) {
      advices.push('🌵 湿度偏低：增加喷雾，保持土壤湿润');
    }

    // 天气建议
    if (weather.includes('雨')) {
      advices.push('🌧️ 降雨天气：暂停施肥，注意排水');
    } else if (weather === '晴') {
      advices.push('☀️ 晴好天气：适合晾晒、施肥作业');
    }

    return advices.length > 0 ? advices : ['🌱 当前天气条件良好'];
  }

  // 地址转经纬度（地理编码）
  async geocodeAddress(address) {
    // 示例：使用和风天气地理编码API
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}/v7/geocoding/geo`,
        data: {
          address,
          key: this.apiKey
        },
        success: (res) => {
          if (res.data.code === '200' && res.data.location && res.data.location.length > 0) {
            const loc = res.data.location[0];
            resolve({
              longitude: parseFloat(loc.lon),
              latitude: parseFloat(loc.lat),
              name: loc.name,
              adm: loc.adm
            });
          } else {
            reject(new Error('地址转经纬度失败'));
          }
        },
        fail: reject
      });
    });
  }

  // 智能获取天气数据（支持经纬度或地址，带降级机制）
  async getWeatherWithFallback(locationOrAddress) {
    console.log('智能获取天气数据，位置或地址:', locationOrAddress);
    let location = null;
    if (typeof locationOrAddress === 'string') {
      // 如果传入的是地址字符串，先地理编码
      try {
        location = await this.geocodeAddress(locationOrAddress);
      } catch (err) {
        console.warn('地址转经纬度失败，降级到模拟数据:', err.message);
        const mockData = this.getMockWeatherData();
        return {
          ...mockData,
          source: 'mock',
          success: false,
          error: err.message
        };
      }
    } else {
      location = locationOrAddress;
    }
    try {
      // 首先尝试获取真实天气数据
      const weatherData = await this.getCurrentWeather(location);
      console.log('成功获取真实天气数据');
      return {
        ...weatherData,
        source: 'api',
        success: true,
        location: location.name || location.adm || undefined // 附加地名
      };
    } catch (error) {
      console.warn('API获取失败，降级到模拟数据:', error.message);
      // API失败时使用模拟数据
      const mockData = this.getMockWeatherData();
      return {
        ...mockData,
        source: 'mock',
        success: false,
        error: error.message
      };
    }
  }

  // 测试API连接
  async testAPIConnection() {
    console.log('开始测试和风天气API连接...');
    
    // 使用重庆坐标进行测试
    const testLocation = {
      longitude: 106.5507,
      latitude: 29.5647
    };
    
    try {
      const result = await this.getCurrentWeather(testLocation);
      console.log('API测试成功:', result);
      return {
        success: true,
        message: 'API连接正常',
        data: result
      };
    } catch (error) {
      console.error('API测试失败:', error);
      return {
        success: false,
        message: error.message,
        data: null
      };
    }
  }

  // 获取API状态信息
  getAPIInfo() {
    return {
      apiKey: this.apiKey.substring(0, 8) + '...',
      baseUrl: this.baseUrl,
      maxRetries: this.maxRetries,
      retryDelay: this.retryDelay
    };
  }
}

module.exports = WeatherService;
