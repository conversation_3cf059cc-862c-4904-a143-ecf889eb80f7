<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agriculture.iot.mapper.IrrigationLogMapper">

    <!-- 分页查询灌溉记录 -->
    <select id="selectIrrigationLogPage" resultType="com.agriculture.iot.entity.IrrigationRecord">
        SELECT *
        FROM irrigation_logs
        <where>
            <if test="deviceId != null">
                AND device_id = #{deviceId}
            </if>
            <if test="plotId != null">
                AND plot_id = #{plotId}
            </if>
            <if test="startTime != null">
                AND start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND end_time <= #{endTime}
            </if>
        </where>
        ORDER BY start_time DESC
    </select>

</mapper>