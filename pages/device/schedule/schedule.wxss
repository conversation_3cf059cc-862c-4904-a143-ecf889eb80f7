/* 高级灌溉调度页面样式 */

/* 表单样式优化 */
.form-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #e8f5e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 添加时段按钮样式 */
.section-title .btn {
  flex: none;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  margin-left: 16rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #2c3e50;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #08C160;
  background-color: #fff;
  box-shadow: 0 0 0 6rpx rgba(8, 193, 96, 0.1);
}

.form-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 12rpx;
  line-height: 1.5;
}

/* 重复模式选择器样式 */
.repeat-options {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}

.repeat-option {
  flex: 1;
  padding: 20rpx;
  background-color: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.repeat-option:active {
  transform: scale(0.98);
}

.repeat-option.selected {
  background-color: #E8F8EC;
  border-color: #08C160;
  color: #08C160;
}

.repeat-option .option-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 星期选择器样式 */
.weekday-selector {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16rpx;
  margin-top: 20rpx;
  padding: 0 8rpx;
}

.weekday-item {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.weekday-item:active {
  transform: scale(0.96);
}

.weekday-item.selected {
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  border-color: #08C160;
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(8, 193, 96, 0.3);
}

.weekday-text {
  font-size: 24rpx;
  font-weight: 600;
}

/* 时间段编辑卡片样式 */
.time-slot-edit {
  background-color: #fff;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.time-slot-edit:hover {
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
}

.slot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f1f3f4;
}

.slot-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.slot-form {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.time-inputs {
  display: flex;
  gap: 16rpx;
}

.time-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.input-label {
  font-size: 24rpx;
  color: #6c757d;
  font-weight: 500;
}

.picker-view {
  padding: 20rpx 16rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #2c3e50;
  text-align: center;
  transition: all 0.3s ease;
}

.picker-view:active {
  background-color: #e9ecef;
  border-color: #08C160;
}

.param-inputs {
  display: flex;
  gap: 16rpx;
}

.param-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.param-input {
  padding: 20rpx 16rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #2c3e50;
  text-align: center;
  transition: all 0.3s ease;
}

.param-input:focus {
  border-color: #08C160;
  background-color: #fff;
  box-shadow: 0 0 0 4rpx rgba(8, 193, 96, 0.1);
}

.zone-selector {
  margin-top: 8rpx;
}

.zone-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
  margin-top: 12rpx;
}

.zone-option {
  padding: 16rpx 12rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.zone-option:active {
  transform: scale(0.96);
}

.zone-option.selected {
  background-color: #E8F8EC;
  border-color: #08C160;
  color: #08C160;
}

.zone-name {
  font-size: 24rpx;
  font-weight: 500;
}

/* 弹窗样式优化 */
.modal-overlay {
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8rpx);
}

.modal-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f1f3f4;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: rgba(108, 117, 125, 0.1);
  border: none;
  font-size: 28rpx;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  max-height: 1000rpx;
  padding: 32rpx;
}

.modal-footer {
  padding: 24rpx 32rpx 40rpx;
  display: flex;
  gap: 16rpx;
  background-color: #f8f9fa;
  justify-content: center;
}

.btn {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
  min-width: 180rpx;
}

.modal-footer .btn {
  flex: 1;
  max-width: 200rpx;
}

.btn-outline {
  background-color: #fff;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.btn-outline:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

.btn-primary {
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(8, 193, 96, 0.3);
}

.btn-primary:active {
  box-shadow: 0 2rpx 8rpx rgba(8, 193, 96, 0.4);
  transform: scale(0.98);
}

.btn-mini {
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
}

.btn-small {
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  border-radius: 10rpx;
}

.btn-danger {
  background-color: #fff;
  color: #dc3545;
  border: 2rpx solid #dc3545;
}

.btn-danger:active {
  background-color: #dc3545;
  color: #fff;
  transform: scale(0.96);
}

/* 时间段删除按钮特殊样式 */
.slot-header .btn-danger {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
}

.slot-header .btn-danger:active {
  background-color: #dc3545;
  color: #fff;
  border-color: #dc3545;
}

/* 日历头部 */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
}

.month-selector {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.nav-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  border: none;
  font-size: 24rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:active {
  background-color: #e0e0e0;
}

.month-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  min-width: 160rpx;
  text-align: center;
}

.view-toggle {
  display: flex;
  border-radius: 8rpx;
  overflow: hidden;
  border: 1rpx solid #e0e0e0;
}

.toggle-btn {
  padding: 15rpx 30rpx;
  font-size: 26rpx;
  background-color: white;
  color: #666;
  border: none;
  border-radius: 0;
}

.toggle-btn.active {
  background-color: #08C160;
  color: white;
}

/* 日历网格 */
.calendar-grid {
  padding: 20rpx;
}

.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.week-day {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  padding: 15rpx 0;
  font-weight: 600;
}

.calendar-body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 10rpx;
}

.date-cell {
  position: relative;
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.2s;
}

.date-cell.current-month {
  background-color: white;
  color: #333;
}

.date-cell.prev-month,
.date-cell.next-month {
  color: #ccc;
  background-color: #f9f9f9;
}

.date-cell.today {
  background-color: #E8F8EC;
  color: #08C160;
  font-weight: 600;
}

.date-cell.selected {
  background-color: #08C160;
  color: white;
}

.date-cell:active {
  transform: scale(0.95);
}

.date-number {
  font-size: 26rpx;
  margin-bottom: 5rpx;
}

.schedule-indicators {
  display: flex;
  gap: 4rpx;
  flex-wrap: wrap;
  justify-content: center;
}

.indicator {
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background-color: #08C160;
}

.indicator.irrigation {
  background-color: #2196F3;
}

.indicator.fertilizer {
  background-color: #4CAF50;
}

/* 日期详情 */
.date-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.selected-date {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.day-schedules {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.schedule-time-slot {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
  border-left: 4rpx solid #08C160;
}

.time-slot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.slot-time {
  font-size: 28rpx;
  font-weight: 600;
  color: #08C160;
}

.slot-actions {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.slot-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  min-width: 80rpx;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.no-schedules {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

/* 计划列表 */
.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.schedule-item {
  background-color: white;
  border-radius: 12rpx;
  padding: 25rpx;
  border: 1rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.schedule-info {
  flex: 1;
}

.schedule-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.schedule-time {
  font-size: 24rpx;
  color: #08C160;
  font-weight: 500;
}

.schedule-details {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.schedule-actions {
  display: flex;
  gap: 15rpx;
  justify-content: flex-end;
}

/* 季节设置 */
.seasonal-settings {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-top: 20rpx;
}

.season-item {
  padding: 25rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.season-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.season-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.season-period {
  font-size: 24rpx;
  color: #666;
}

.season-adjustments {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.adjustment-item {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.adjustment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.adjustment-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.adjustment-display {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  background-color: #08C160;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.adjustment-value {
  font-size: 28rpx;
  font-weight: 600;
}

.adjustment-unit {
  font-size: 20rpx;
  opacity: 0.9;
}

/* ======= 自定义调整滑块样式 ======= */

/* 自定义调整滑块容器 */
.custom-adjustment-slider {
  margin: 20rpx 0;
}

.custom-adjustment-slider .slider-track {
  position: relative;
  height: 8rpx;
  background-color: #E0E0E0;
  border-radius: 4rpx;
  margin: 0 20rpx;
}

.custom-adjustment-slider .slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.1s ease;
}

.custom-adjustment-slider .slider-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 40rpx;
  height: 40rpx;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.custom-adjustment-slider .thumb-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
}

/* 时长调整滑块 - 绿色 */
.custom-adjustment-slider.duration .slider-fill {
  background: linear-gradient(90deg, #08C160 0%, #08C160 100%);
}

.custom-adjustment-slider.duration .thumb-inner {
  background-color: #08C160;
}

/* 频次调整滑块 - 蓝色 */
.custom-adjustment-slider.frequency .slider-fill {
  background: linear-gradient(90deg, #2196F3 0%, #1976D2 100%);
}

.custom-adjustment-slider.frequency .thumb-inner {
  background-color: #2196F3;
}

/* 滑块范围标签 */
.custom-adjustment-slider .slider-range {
  display: flex;
  justify-content: space-between;
  margin: 15rpx 20rpx 0;
}

.custom-adjustment-slider .range-min,
.custom-adjustment-slider .range-max {
  font-size: 22rpx;
  color: #666666;
}

/* 快速模板 */
.template-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 20rpx;
}

.template-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
  padding: 25rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s;
  cursor: pointer;
}

.template-item:active {
  transform: scale(0.95);
  border-color: #08C160;
  background-color: #E8F8EC;
}

.template-icon {
  font-size: 48rpx;
}

.template-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.template-desc {
  font-size: 22rpx;
  color: #666;
  text-align: center;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 90vw;
  max-width: 600rpx;
  max-height: 80vh;
  background-color: white;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  border: none;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #e0e0e0;
}

.modal-footer .btn {
  flex: 1;
}

/* 表单区域 */
.form-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 25rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: white;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #08C160;
  box-shadow: 0 0 0 2rpx rgba(8, 193, 96, 0.2);
}

.form-desc {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 重复模式选项 */
.repeat-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.repeat-option {
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.repeat-option.selected {
  background-color: #08C160;
  border-color: #08C160;
  color: white;
}

.repeat-option:not(.selected):active {
  background-color: #f5f5f5;
}

.option-text {
  font-size: 26rpx;
}

/* 工作日选择器 */
.weekday-selector {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 10rpx;
}

.weekday-item {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.2s;
}

.weekday-item.selected {
  background-color: #08C160;
  border-color: #08C160;
  color: white;
}

.weekday-item:not(.selected):active {
  background-color: #f5f5f5;
}

.weekday-text {
  font-size: 24rpx;
}

/* 时间段编辑 */
.time-slots {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.time-slot-edit {
  padding: 25rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
}

.slot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.slot-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.slot-form {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.time-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.time-input-group {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.input-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.picker-view {
  padding: 15rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  background-color: white;
  font-size: 26rpx;
  color: #333;
}

.param-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.param-input-group {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.param-input {
  padding: 15rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  font-size: 26rpx;
  background-color: white;
}

.zone-selector {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.zone-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10rpx;
}

.zone-option {
  padding: 15rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.zone-option.selected {
  background-color: #08C160;
  border-color: #08C160;
  color: white;
}

.zone-option:not(.selected):active {
  background-color: #f5f5f5;
}

.zone-name {
  font-size: 24rpx;
}

.seasonal-form {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  margin-top: 15rpx;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .template-grid {
    grid-template-columns: 1fr;
  }
  
  .repeat-options {
    grid-template-columns: 1fr;
  }
  
  .time-inputs,
  .param-inputs {
    grid-template-columns: 1fr;
  }
  
  .slot-details {
    grid-template-columns: 1fr;
  }
  
  .zone-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .weekday-selector {
    gap: 5rpx;
  }
}