<!--关于我们页面-->
<view class="page-container">
  <!-- 应用信息 -->
  <view class="app-info-section">
    <view class="app-header">
      <view class="app-logo">🌱</view>
      <view class="app-details">
        <text class="app-name">{{appInfo.name}}</text>
        <text class="app-version">版本 {{appInfo.version}}</text>
        <text class="app-build">构建号 {{appInfo.buildNumber}}</text>
      </view>
    </view>
    
    <view class="app-description">
      <text class="description-text">{{appInfo.description}}</text>
    </view>
  </view>

  <!-- 核心功能 -->
  <view class="features-section">
    <view class="section-title">
      <text class="title-text">核心功能</text>
    </view>
    
    <view class="features-grid">
      <view wx:for="{{features}}" wx:key="title" class="feature-item">
        <view class="feature-icon">{{item.icon}}</view>
        <text class="feature-title">{{item.title}}</text>
        <text class="feature-desc">{{item.desc}}</text>
      </view>
    </view>
  </view>

  <!-- 开发团队 -->
  <view class="team-section">
    <view class="section-title">
      <text class="title-text">开发团队</text>
    </view>
    
    <view class="team-list">
      <view wx:for="{{teamMembers}}" wx:key="name" class="team-item">
        <view class="team-avatar">👥</view>
        <view class="team-info">
          <text class="team-name">{{item.name}}</text>
          <text class="team-role">{{item.role}}</text>
          <text class="team-desc">{{item.desc}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-section">
    <view class="section-title">
      <text class="title-text">版本历史</text>
    </view>
    
    <view class="changelog-list">
      <view wx:for="{{changelog}}" wx:key="version" class="changelog-item">
        <view class="changelog-header">
          <text class="changelog-version">{{item.version}}</text>
          <text class="changelog-date">{{item.date}}</text>
        </view>
        <view class="changelog-content">
          <view wx:for="{{item.changes}}" wx:for-item="change" wx:key="*this" class="change-item">
            <text class="change-bullet">•</text>
            <text class="change-text">{{change}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系方式 */
  <view class="contact-section">
    <view class="section-title">
      <text class="title-text">联系我们</text>
    </view>
    
    <view class="contact-list">
      <view class="contact-item" bindtap="copyContact">
        <view class="contact-icon">📞</view>
        <view class="contact-info">
          <text class="contact-label">客服电话</text>
          <text class="contact-value">{{appInfo.contact}}</text>
        </view>
        <view class="contact-action">
          <text class="action-text">点击复制</text>
        </view>
      </view>
      
      <view class="contact-item" bindtap="copyEmail">
        <view class="contact-icon">📧</view>
        <view class="contact-info">
          <text class="contact-label">客服邮箱</text>
          <text class="contact-value">{{appInfo.email}}</text>
        </view>
        <view class="contact-action">
          <text class="action-text">点击复制</text>
        </view>
      </view>
      
      <view class="contact-item" bindtap="contactSupport">
        <view class="contact-icon">💬</view>
        <view class="contact-info">
          <text class="contact-label">技术支持</text>
          <text class="contact-value">在线客服</text>
        </view>
        <view class="contact-action">
          <text class="action-text">联系客服</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions-section">
    <view class="action-buttons">
      <button class="btn btn-outline" bindtap="checkUpdate">
        🔄 检查更新
      </button>
      <button class="btn btn-outline" bindtap="shareApp">
        📤 分享应用
      </button>
    </view>
  </view>

  <!-- 法律信息 -->
  <view class="legal-section">
    <view class="legal-links">
      <text class="legal-link" bindtap="viewPrivacy">隐私政策</text>
      <text class="legal-separator">|</text>
      <text class="legal-link" bindtap="viewTerms">用户协议</text>
    </view>
    
    <view class="copyright">
      <text class="copyright-text">© 2024 {{appInfo.developer}}</text>
      <text class="copyright-text">保留所有权利</text>
    </view>
  </view>
</view>