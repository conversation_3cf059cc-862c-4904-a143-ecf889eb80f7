Page({
  data: {
    deviceInfo: {
      name: '水肥一体机-01',
      type: '智能灌溉设备',
      status: 'online',
      statusText: '在线运行'
    },
    deviceDetails: [
      { key: 'model', label: '设备型号', value: 'SF-2000Pro' },
      { key: 'serial', label: '序列号', value: 'SF202401001' },
      { key: 'ip', label: 'IP地址', value: '*************' },
      { key: 'mac', label: 'MAC地址', value: '00:1B:44:11:3A:B7' },
      { key: 'version', label: '固件版本', value: 'v2.1.3' },
      { key: 'location', label: '安装位置', value: '1号大棚东侧' },
      { key: 'install_date', label: '安装日期', value: '2024-01-15' },
      { key: 'uptime', label: '运行时间', value: '156天12小时' }
    ],
    realtimeData: {
      waterLevel: 85,
      fertilizerA: 75,
      fertilizerB: 60,
      pressure: 2.3,
      flowRate: 45,
      temperature: 23
    },
    runStats: {
      totalRunTime: '1,240小时',
      waterUsed: '12,500L',
      fertilizerUsed: '280kg',
      taskCount: '1,856次'
    },
    recentLogs: [
      {
        id: 1,
        type: 'irrigation',
        icon: '💧',
        title: '灌溉任务完成',
        description: '1号区域灌溉15分钟，用水75L',
        time: '2小时前',
        status: 'success',
        statusText: '成功'
      },
      {
        id: 2,
        type: 'fertilizer',
        icon: '🌱',
        title: '自动施肥',
        description: '番茄生长期配方，浓度80%，时长12分钟',
        time: '5小时前',
        status: 'success',
        statusText: '成功'
      },
      {
        id: 3,
        type: 'maintenance',
        icon: '🔧',
        title: '系统自检',
        description: '传感器校准，系统运行正常',
        time: '1天前',
        status: 'success',
        statusText: '正常'
      },
      {
        id: 4,
        type: 'alert',
        icon: '⚠️',
        title: '肥料余量不足',
        description: '肥料B余量低于30%，建议及时补充',
        time: '2天前',
        status: 'warning',
        statusText: '警告'
      }
    ],
    maintenanceInfo: {
      lastMaintenance: '2024-06-15',
      nextMaintenance: '2024-09-15',
      warrantyExpiry: '2025-01-15'
    },
    // 快速控制数据
    irrigationRunning: false,
    irrigationFlow: 60,
    irrigationDuration: 30,
    fertilizerRunning: false,
    fertilizerConcentration: 80,
    selectedFormula: 0,
    formulaOptions: ['通用配方', '番茄专用', '黄瓜专用', '草莓专用']
  },

  onLoad(options) {
    const deviceId = options.id;
    this.loadDeviceDetail(deviceId);
    this.startRealtimeUpdate();
  },

  onShow() {
    this.refreshData();
  },

  onUnload() {
    this.stopRealtimeUpdate();
  },

  loadDeviceDetail(deviceId) {
    console.log('加载设备详情:', deviceId);
    // 模拟API调用
    wx.showLoading({
      title: '加载中...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
    }, 1000);
  },

  refreshData() {
    console.log('刷新设备数据');
    // 刷新实时数据
  },

  startRealtimeUpdate() {
    this.realtimeTimer = setInterval(() => {
      this.updateRealtimeData();
    }, 5000);
  },

  stopRealtimeUpdate() {
    if (this.realtimeTimer) {
      clearInterval(this.realtimeTimer);
    }
  },

  updateRealtimeData() {
    // 模拟实时数据更新
    const realtimeData = this.data.realtimeData;
    const updates = {};
    
    // 确保数据类型为数字，模拟数据波动
    const currentPressure = parseFloat(realtimeData.pressure) || 2.3;
    const currentFlowRate = parseFloat(realtimeData.flowRate) || 45;
    const currentTemperature = parseFloat(realtimeData.temperature) || 23;
    
    updates['realtimeData.pressure'] = (currentPressure + (Math.random() - 0.5) * 0.2).toFixed(1);
    updates['realtimeData.flowRate'] = Math.max(0, currentFlowRate + (Math.random() - 0.5) * 5).toFixed(0);
    updates['realtimeData.temperature'] = (currentTemperature + (Math.random() - 0.5) * 2).toFixed(0);
    
    this.setData(updates);
  },

  // 导航到相关页面
  navigateToIrrigation() {
    wx.navigateTo({
      url: '/pages/device/irrigation/irrigation'
    });
  },

  navigateToFertilizer() {
    wx.navigateTo({
      url: '/pages/device/fertilizer/fertilizer'
    });
  },

  navigateToConfig() {
    wx.navigateTo({
      url: '/pages/device/config/config'
    });
  },

  navigateToLogs() {
    wx.navigateTo({
      url: '/pages/device/logs/logs'
    });
  },

  viewAllLogs() {
    wx.navigateTo({
      url: '/pages/device/logs/logs'
    });
  },

  scheduleMaintenance() {
    wx.showModal({
      title: '预约维护',
      content: '是否要预约设备维护服务？我们将安排专业技术人员联系您。',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '预约成功，我们将尽快联系您',
            icon: 'success',
            duration: 2000
          });
        }
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 快速控制方法
  onIrrigationSwitch(e) {
    const checked = e.detail.value;
    this.setData({
      irrigationRunning: checked
    });
    
    if (checked) {
      wx.showToast({
        title: '灌溉系统已启动',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '灌溉系统已停止',
        icon: 'success'
      });
    }
  },

  onFertilizerSwitch(e) {
    const checked = e.detail.value;
    this.setData({
      fertilizerRunning: checked
    });
    
    if (checked) {
      wx.showToast({
        title: '施肥系统已启动',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '施肥系统已停止',
        icon: 'success'
      });
    }
  },

  onFlowChange(e) {
    this.setData({
      irrigationFlow: e.detail.value
    });
  },

  onDurationInput(e) {
    this.setData({
      irrigationDuration: e.detail.value
    });
  },

  onConcentrationChange(e) {
    this.setData({
      fertilizerConcentration: e.detail.value
    });
  },

  onFormulaChange(e) {
    this.setData({
      selectedFormula: e.detail.value
    });
  }
})