package com.agriculture.iot.service.impl;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.Schedule;
import com.agriculture.iot.mapper.ScheduleMapper;
import com.agriculture.iot.service.ScheduleService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 调度服务实现类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduleServiceImpl extends ServiceImpl<ScheduleMapper, Schedule> implements ScheduleService {

    private final ScheduleMapper scheduleMapper;

    @Override
    public IPage<Schedule.Detail> getSchedules(Integer current, Integer size, String type, String status, 
                                               Long plotId, Long deviceId) {
        Page<Schedule.Detail> page = new Page<>(current, size);
        return scheduleMapper.selectScheduleDetailsPage(page, type, status, plotId, deviceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Schedule.Detail> createSchedule(Schedule.CreateRequest request) {
        try {
            Schedule schedule = new Schedule();
            schedule.setName(request.getName());
            schedule.setDescription(request.getDescription());
            schedule.setType(request.getType());
            schedule.setPlotId(request.getPlotId());
            schedule.setDeviceId(request.getDeviceId());
            schedule.setStartDate(request.getStartDate());
            schedule.setEndDate(request.getEndDate());
            schedule.setExecutionTime(request.getExecutionTime());
            schedule.setRepeatPattern(request.getRepeatPattern());
            schedule.setRepeatInterval(request.getRepeatInterval());
            schedule.setStatus("active");
            schedule.setPriority(request.getPriority());
            schedule.setEnabled(true);
            schedule.setParameters(request.getParameters());
            schedule.setCreatedAt(LocalDateTime.now());
            schedule.setUpdatedAt(LocalDateTime.now());
            
            // 计算下次执行时间
            LocalDateTime nextExecution = calculateNextExecution(schedule);
            schedule.setNextExecution(nextExecution);
            
            boolean saved = this.save(schedule);
            if (saved) {
                Schedule.Detail detail = scheduleMapper.selectScheduleDetailById(schedule.getId());
                return Result.success(detail);
            } else {
                return Result.error("创建计划失败");
            }
        } catch (Exception e) {
            log.error("创建计划失败", e);
            return Result.error("创建计划失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Schedule.Detail> updateSchedule(Long id, Schedule.UpdateRequest request) {
        try {
            Schedule schedule = this.getById(id);
            if (schedule == null) {
                return Result.error("计划不存在");
            }
            
            schedule.setName(request.getName());
            schedule.setDescription(request.getDescription());
            schedule.setType(request.getType());
            schedule.setPlotId(request.getPlotId());
            schedule.setDeviceId(request.getDeviceId());
            schedule.setStartDate(request.getStartDate());
            schedule.setEndDate(request.getEndDate());
            schedule.setExecutionTime(request.getExecutionTime());
            schedule.setRepeatPattern(request.getRepeatPattern());
            schedule.setRepeatInterval(request.getRepeatInterval());
            schedule.setPriority(request.getPriority());
            schedule.setParameters(request.getParameters());
            schedule.setUpdatedAt(LocalDateTime.now());
            
            // 重新计算下次执行时间
            LocalDateTime nextExecution = calculateNextExecution(schedule);
            schedule.setNextExecution(nextExecution);
            
            boolean updated = this.updateById(schedule);
            if (updated) {
                Schedule.Detail detail = scheduleMapper.selectScheduleDetailById(id);
                return Result.success(detail);
            } else {
                return Result.error("更新计划失败");
            }
        } catch (Exception e) {
            log.error("更新计划失败", e);
            return Result.error("更新计划失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteSchedule(Long id) {
        try {
            boolean deleted = this.removeById(id);
            return deleted ? Result.success() : Result.error("删除计划失败");
        } catch (Exception e) {
            log.error("删除计划失败", e);
            return Result.error("删除计划失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Schedule.Detail> getScheduleDetail(Long id) {
        try {
            Schedule.Detail detail = scheduleMapper.selectScheduleDetailById(id);
            return detail != null ? Result.success(detail) : Result.error("计划不存在");
        } catch (Exception e) {
            log.error("获取计划详情失败", e);
            return Result.error("获取计划详情失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> toggleSchedule(Long id, Boolean enabled) {
        try {
            Schedule schedule = this.getById(id);
            if (schedule == null) {
                return Result.error("计划不存在");
            }
            
            schedule.setEnabled(enabled);
            schedule.setUpdatedAt(LocalDateTime.now());
            
            boolean updated = this.updateById(schedule);
            return updated ? Result.success() : Result.error("更新状态失败");
        } catch (Exception e) {
            log.error("更新计划状态失败", e);
            return Result.error("更新计划状态失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Schedule.ExecutionResult> executeSchedule(Long id) {
        try {
            Schedule schedule = this.getById(id);
            if (schedule == null) {
                return Result.error("计划不存在");
            }
            
            if (!schedule.getEnabled()) {
                return Result.error("计划已禁用");
            }
            
            // 创建执行结果
            Schedule.ExecutionResult result = new Schedule.ExecutionResult();
            result.setScheduleId(id);
            result.setExecutionTime(LocalDateTime.now());
            result.setStatus("success");
            result.setMessage("计划执行成功");
            
            // 更新计划的执行信息
            schedule.setLastExecution(LocalDateTime.now());
            schedule.setExecutionCount(schedule.getExecutionCount() + 1);
            schedule.setNextExecution(calculateNextExecution(schedule));
            this.updateById(schedule);
            
            // TODO: 实际执行计划的业务逻辑
            // 这里应该根据计划类型执行相应的操作，如灌溉、施肥等
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("执行计划失败", e);
            return Result.error("执行计划失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<Schedule.ExecutionResult>> batchExecuteSchedules(List<Long> scheduleIds) {
        try {
            List<Schedule.ExecutionResult> results = new ArrayList<>();
            
            for (Long scheduleId : scheduleIds) {
                Result<Schedule.ExecutionResult> result = executeSchedule(scheduleId);
                if (result.getCode() == 200) {
                    results.add(result.getData());
                } else {
                    Schedule.ExecutionResult failResult = new Schedule.ExecutionResult();
                    failResult.setScheduleId(scheduleId);
                    failResult.setExecutionTime(LocalDateTime.now());
                    failResult.setStatus("failed");
                    failResult.setMessage(result.getMessage());
                    results.add(failResult);
                }
            }
            
            return Result.success(results);
        } catch (Exception e) {
            log.error("批量执行计划失败", e);
            return Result.error("批量执行计划失败：" + e.getMessage());
        }
    }

    @Override
    public IPage<Schedule.ExecutionRecord> getExecutionRecords(Integer current, Integer size, Long scheduleId, String status,
                                                               LocalDateTime startTime, LocalDateTime endTime) {
        Page<Schedule.ExecutionRecord> page = new Page<>(current, size);
        return scheduleMapper.selectExecutionRecordsPage(page, scheduleId, status, startTime, endTime);
    }

    @Override
    public Result<Schedule.Statistics> getScheduleStatistics(LocalDate startDate, LocalDate endDate, String type, Long plotId) {
        try {
            Schedule.Statistics statistics = scheduleMapper.selectScheduleStatistics(startDate, endDate, type, plotId);
            return statistics != null ? Result.success(statistics) : Result.error("统计数据不存在");
        } catch (Exception e) {
            log.error("获取计划统计失败", e);
            return Result.error("获取计划统计失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Schedule.Template>> getScheduleTemplates(String type, String category) {
        try {
            List<Schedule.Template> templates = scheduleMapper.selectScheduleTemplates(type, category);
            return Result.success(templates);
        } catch (Exception e) {
            log.error("获取计划模板失败", e);
            return Result.error("获取计划模板失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Schedule.Template> createScheduleTemplate(Schedule.Template template) {
        try {
            template.setCreatedAt(LocalDateTime.now());
            int result = scheduleMapper.insertScheduleTemplate(template);
            return result > 0 ? Result.success(template) : Result.error("创建模板失败");
        } catch (Exception e) {
            log.error("创建计划模板失败", e);
            return Result.error("创建计划模板失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Schedule.Detail> createScheduleFromTemplate(Long templateId, Long plotId, Long deviceId, LocalDate startDate) {
        try {
            Schedule.Template template = scheduleMapper.selectScheduleTemplateById(templateId);
            if (template == null) {
                return Result.error("模板不存在");
            }
            
            Schedule.CreateRequest request = new Schedule.CreateRequest();
            request.setName(template.getName() + " - " + startDate);
            request.setDescription(template.getDescription());
            request.setType(template.getType());
            request.setPlotId(plotId);
            request.setDeviceId(deviceId);
            request.setStartDate(startDate);
            request.setRepeatPattern(template.getRepeatPattern());
            request.setRepeatInterval(template.getRepeatInterval());
            request.setPriority("medium");
            request.setParameters(template.getParameters());
            
            return createSchedule(request);
        } catch (Exception e) {
            log.error("从模板创建计划失败", e);
            return Result.error("从模板创建计划失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Schedule.ConflictInfo> checkScheduleConflict(Long plotId, Long deviceId, LocalDateTime startTime, 
                                                               LocalDateTime endTime, Long excludeId) {
        try {
            Schedule.ConflictInfo conflictInfo = scheduleMapper.selectScheduleConflict(plotId, deviceId, startTime, endTime, excludeId);
            return Result.success(conflictInfo);
        } catch (Exception e) {
            log.error("检查计划冲突失败", e);
            return Result.error("检查计划冲突失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Schedule.CalendarView>> getCalendarView(Integer year, Integer month, Long plotId, String type) {
        try {
            List<Schedule.CalendarView> calendarViews = scheduleMapper.selectCalendarView(year, month, plotId, type);
            return Result.success(calendarViews);
        } catch (Exception e) {
            log.error("获取日历视图失败", e);
            return Result.error("获取日历视图失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Schedule.CalendarItem>> getTodaySchedules(Long plotId, String type) {
        try {
            List<Schedule.CalendarItem> schedules = scheduleMapper.selectTodaySchedules(plotId, type);
            return Result.success(schedules);
        } catch (Exception e) {
            log.error("获取今日计划失败", e);
            return Result.error("获取今日计划失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Schedule.CalendarItem>> getUpcomingSchedules(Integer hours, Long plotId, String type) {
        try {
            List<Schedule.CalendarItem> schedules = scheduleMapper.selectUpcomingSchedules(hours, plotId, type);
            return Result.success(schedules);
        } catch (Exception e) {
            log.error("获取即将到期计划失败", e);
            return Result.error("获取即将到期计划失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Schedule.CalendarItem>> getOverdueSchedules(Long plotId, String type) {
        try {
            List<Schedule.CalendarItem> schedules = scheduleMapper.selectOverdueSchedules(plotId, type);
            return Result.success(schedules);
        } catch (Exception e) {
            log.error("获取逾期计划失败", e);
            return Result.error("获取逾期计划失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Schedule.Detail> duplicateSchedule(Long id, Schedule.CreateRequest modifications) {
        try {
            Schedule originalSchedule = this.getById(id);
            if (originalSchedule == null) {
                return Result.error("原计划不存在");
            }
            
            // 创建新的计划请求
            Schedule.CreateRequest request = new Schedule.CreateRequest();
            request.setName(modifications.getName() != null ? modifications.getName() : originalSchedule.getName() + " - 副本");
            request.setDescription(modifications.getDescription() != null ? modifications.getDescription() : originalSchedule.getDescription());
            request.setType(modifications.getType() != null ? modifications.getType() : originalSchedule.getType());
            request.setPlotId(modifications.getPlotId() != null ? modifications.getPlotId() : originalSchedule.getPlotId());
            request.setDeviceId(modifications.getDeviceId() != null ? modifications.getDeviceId() : originalSchedule.getDeviceId());
            request.setStartDate(modifications.getStartDate() != null ? modifications.getStartDate() : originalSchedule.getStartDate());
            request.setEndDate(modifications.getEndDate() != null ? modifications.getEndDate() : originalSchedule.getEndDate());
            request.setExecutionTime(modifications.getExecutionTime() != null ? modifications.getExecutionTime() : originalSchedule.getExecutionTime());
            request.setRepeatPattern(modifications.getRepeatPattern() != null ? modifications.getRepeatPattern() : originalSchedule.getRepeatPattern());
            request.setRepeatInterval(modifications.getRepeatInterval() != null ? modifications.getRepeatInterval() : originalSchedule.getRepeatInterval());
            request.setPriority(modifications.getPriority() != null ? modifications.getPriority() : originalSchedule.getPriority());
            request.setParameters(modifications.getParameters() != null ? modifications.getParameters() : originalSchedule.getParameters());
            
            return createSchedule(request);
        } catch (Exception e) {
            log.error("复制计划失败", e);
            return Result.error("复制计划失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> pauseSchedule(Long id) {
        return updateScheduleStatus(id, "paused");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> resumeSchedule(Long id) {
        return updateScheduleStatus(id, "active");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> completeSchedule(Long id) {
        return updateScheduleStatus(id, "completed");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cancelSchedule(Long id) {
        return updateScheduleStatus(id, "cancelled");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchUpdateScheduleStatus(List<Long> scheduleIds, String status) {
        try {
            int result = scheduleMapper.batchUpdateStatus(scheduleIds, status);
            return result > 0 ? Result.success() : Result.error("批量更新状态失败");
        } catch (Exception e) {
            log.error("批量更新计划状态失败", e);
            return Result.error("批量更新计划状态失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Schedule.CalendarItem>> getNextExecutionSchedules(Integer limit) {
        try {
            List<Schedule.CalendarItem> schedules = scheduleMapper.selectNextExecutionSchedules(limit);
            return Result.success(schedules);
        } catch (Exception e) {
            log.error("获取下次执行计划失败", e);
            return Result.error("获取下次执行计划失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateNextExecution(Long id, LocalDateTime nextExecution) {
        try {
            int result = scheduleMapper.updateNextExecution(id, nextExecution);
            return result > 0 ? Result.success() : Result.error("更新执行时间失败");
        } catch (Exception e) {
            log.error("更新下次执行时间失败", e);
            return Result.error("更新下次执行时间失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> recordExecutionResult(Long scheduleId, Schedule.ExecutionResult result) {
        try {
            // 更新计划的执行统计
            Schedule schedule = this.getById(scheduleId);
            if (schedule != null) {
                schedule.setExecutionCount(schedule.getExecutionCount() + 1);
                if ("success".equals(result.getStatus())) {
                    schedule.setSuccessCount(schedule.getSuccessCount() + 1);
                }
                schedule.setLastExecution(result.getExecutionTime());
                schedule.setNextExecution(calculateNextExecution(schedule));
                this.updateById(schedule);
            }
            
            // TODO: 保存执行记录到执行记录表
            
            return Result.success();
        } catch (Exception e) {
            log.error("记录执行结果失败", e);
            return Result.error("记录执行结果失败：" + e.getMessage());
        }
    }

    /**
     * 更新计划状态
     */
    private Result<Void> updateScheduleStatus(Long id, String status) {
        try {
            Schedule schedule = this.getById(id);
            if (schedule == null) {
                return Result.error("计划不存在");
            }
            
            schedule.setStatus(status);
            schedule.setUpdatedAt(LocalDateTime.now());
            
            boolean updated = this.updateById(schedule);
            return updated ? Result.success() : Result.error("更新状态失败");
        } catch (Exception e) {
            log.error("更新计划状态失败", e);
            return Result.error("更新计划状态失败：" + e.getMessage());
        }
    }

    /**
     * 计算下次执行时间
     */
    private LocalDateTime calculateNextExecution(Schedule schedule) {
        if (schedule.getRepeatPattern() == null || "none".equals(schedule.getRepeatPattern())) {
            return null;
        }
        
        LocalDateTime baseTime = schedule.getLastExecution() != null 
            ? schedule.getLastExecution() 
            : LocalDateTime.now();
        
        switch (schedule.getRepeatPattern()) {
            case "daily":
                return baseTime.plusDays(schedule.getRepeatInterval() != null ? schedule.getRepeatInterval() : 1);
            case "weekly":
                return baseTime.plusWeeks(schedule.getRepeatInterval() != null ? schedule.getRepeatInterval() : 1);
            case "monthly":
                return baseTime.plusMonths(schedule.getRepeatInterval() != null ? schedule.getRepeatInterval() : 1);
            default:
                return null;
        }
    }
}