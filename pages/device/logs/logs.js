const api = require('../../../utils/api.js');

Page({
  data: {
    activeTab: 'operation',
    
    // 筛选器
    filters: {
      startDate: '',
      endDate: ''
    },
    
    // 操作日志相关
    operationFilter: 'all',
    operationStats: {
      totalOperations: 1856,
      successRate: 98.5,
      todayOperations: 12
    },
    
    operationLogs: [
      {
        id: 1,
        type: 'irrigation',
        icon: '💧',
        title: '自动灌溉完成',
        description: '1号区域定时灌溉任务执行完成',
        time: '2024-07-07 15:30:25',
        status: 'success',
        statusText: '成功',
        details: [
          { label: '灌溉时长', value: '15分钟' },
          { label: '用水量', value: '85L' },
          { label: '流量', value: '5.7L/min' }
        ],
        canExport: true
      },
      {
        id: 2,
        type: 'fertilizer',
        icon: '🌱',
        title: '施肥任务执行',
        description: '番茄生长期配方施肥完成',
        time: '2024-07-07 12:15:10',
        status: 'success',
        statusText: '成功',
        details: [
          { label: '配方名称', value: '番茄生长期配方' },
          { label: '施肥时长', value: '12分钟' },
          { label: '肥料用量', value: '2.3kg' },
          { label: '浓度', value: '80%' }
        ],
        canExport: true
      },
      {
        id: 3,
        type: 'maintenance',
        icon: '🔧',
        title: '系统自检',
        description: '每日系统自检完成，所有组件运行正常',
        time: '2024-07-07 08:00:00',
        status: 'success',
        statusText: '正常',
        details: [
          { label: '检查项目', value: '传感器、阀门、水泵' },
          { label: '检查结果', value: '全部正常' },
          { label: '下次检查', value: '2024-07-08 08:00' }
        ]
      },
      {
        id: 4,
        type: 'irrigation',
        icon: '💧',
        title: '灌溉任务失败',
        description: '2号区域灌溉失败，压力不足',
        time: '2024-07-06 18:45:32',
        status: 'error',
        statusText: '失败',
        details: [
          { label: '失败原因', value: '系统压力不足' },
          { label: '当前压力', value: '1.2 bar' },
          { label: '要求压力', value: '2.0 bar' }
        ],
        canRetry: true
      },
      {
        id: 5,
        type: 'config',
        icon: '⚙️',
        title: '配置更新',
        description: '灌溉区域配置已更新',
        time: '2024-07-06 14:20:15',
        status: 'success',
        statusText: '成功',
        details: [
          { label: '修改项目', value: '3号区域面积' },
          { label: '原值', value: '150㎡' },
          { label: '新值', value: '180㎡' }
        ]
      }
    ],
    
    filteredOperationLogs: [],
    hasMoreOperationLogs: true,
    
    // 运行报告相关
    reportType: 'daily',
    reportData: {
      totalRunTime: '24小时30分钟',
      irrigationCount: 8,
      fertilizerCount: 3,
      waterUsage: 450,
      fertilizerUsage: 12.5,
      efficiency: 95.2,
      irrigationEfficiency: 92,
      fertilizerEfficiency: 88,
      systemUptime: 99
    },
    
    // 异常记录相关
    alertFilter: 'all',
    alertStats: {
      warnings: 3,
      errors: 1,
      resolved: 15
    },
    
    alerts: [
      {
        id: 1,
        level: 'error',
        icon: '🚨',
        title: '水压异常',
        description: '系统水压持续低于安全阈值，可能影响正常灌溉',
        time: '2024-07-07 16:45:00',
        levelText: '严重',
        solution: '检查水泵运行状态，确认水源供应是否正常',
        resolved: false
      },
      {
        id: 2,
        level: 'warning',
        icon: '⚠️',
        title: '肥料余量不足',
        description: '肥料B储量低于20%，建议及时补充',
        time: '2024-07-07 10:30:00',
        levelText: '警告',
        solution: '联系供应商补充肥料B，或手动添加肥料',
        resolved: false
      },
      {
        id: 3,
        level: 'warning',
        icon: '⚠️',
        title: 'pH值偏高',
        description: '2号区域土壤pH值连续3天超过7.5',
        time: '2024-07-06 14:20:00',
        levelText: '警告',
        solution: '调整灌溉水pH值，或使用酸性肥料降低土壤pH',
        resolved: false
      },
      {
        id: 4,
        level: 'resolved',
        icon: '✅',
        title: '传感器通信恢复',
        description: '3号区域湿度传感器通信已恢复正常',
        time: '2024-07-06 09:15:00',
        levelText: '已解决',
        solution: '重启传感器模块',
        resolved: true
      }
    ],
    
    filteredAlerts: [],
    showEmptyState: false
  },

  onLoad() {
    this.initializePage();
  },

  initializePage() {
    // 初始化日期
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    
    this.setData({
      'filters.startDate': this.formatDate(yesterday),
      'filters.endDate': this.formatDate(today)
    });
    
    // 加载API数据
    this.loadOperationLogs();
    this.loadReports();
    this.loadAlerts();
  },

  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 标签切换
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  // 日期筛选
  onStartDateChange(e) {
    this.setData({
      'filters.startDate': e.detail.value
    });
  },

  onEndDateChange(e) {
    this.setData({
      'filters.endDate': e.detail.value
    });
  },

  applyFilters() {
    wx.showLoading({
      title: '筛选中...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      this.filterOperationLogs();
      this.filterAlerts();
      
      wx.showToast({
        title: '筛选完成',
        icon: 'success'
      });
    }, 1000);
  },

  // 操作日志相关方法
  filterOperations(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      operationFilter: type
    });
    this.loadOperationLogs(); // 重新加载API数据
  },

  filterOperationLogs() {
    // 方法保留用于兼容，但实际由loadOperationLogs处理
    this.loadOperationLogs();
  },

  async retryOperation(e) {
    const logId = e.currentTarget.dataset.id;
    const log = this.data.operationLogs.find(item => item.id === logId);
    
    wx.showModal({
      title: '重试操作',
      content: `确定要重试"${log.title}"吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '重试中...' });
            
            const response = await api.retryOperation(logId);
            
            if (response.code === 200) {
              wx.showToast({
                title: response.data.success ? '重试成功' : '重试失败',
                icon: response.data.success ? 'success' : 'none'
              });
              
              // 重新加载日志
              this.loadOperationLogs();
            }
          } catch (error) {
            console.error('重试操作失败:', error);
            wx.showToast({
              title: '重试失败',
              icon: 'none'
            });
          } finally {
            wx.hideLoading();
          }
        }
      }
    });
  },

  async exportLog(e) {
    try {
      wx.showLoading({ title: '准备导出...' });
      
      const params = {
        type: this.data.operationFilter,
        startDate: this.data.filters.startDate,
        endDate: this.data.filters.endDate,
        format: 'excel'
      };
      
      const response = await api.exportLogs(params);
      
      if (response.code === 200) {
        wx.showToast({
          title: response.message,
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('导出失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  loadMoreOperationLogs() {
    wx.showLoading({
      title: '加载中...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟加载更多数据
      this.setData({
        hasMoreOperationLogs: false
      });
      
      wx.showToast({
        title: '已加载全部记录',
        icon: 'none'
      });
    }, 1500);
  },

  // 运行报告相关方法
  selectReportType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      reportType: type
    });
    this.loadReports();
  },

  exportReport() {
    wx.showActionSheet({
      itemList: ['导出为PDF', '导出为Excel', '导出为图片'],
      success: (res) => {
        const formats = ['PDF', 'Excel', '图片'];
        wx.showToast({
          title: `导出${formats[res.tapIndex]}功能开发中`,
          icon: 'none'
        });
      }
    });
  },

  shareReport() {
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    });
  },

  // 异常记录相关方法
  filterAlerts(e) {
    const level = e ? e.currentTarget.dataset.level : this.data.alertFilter;
    this.setData({
      alertFilter: level
    });
    
    this.loadAlerts(); // 重新从API加载数据
  },

  async resolveAlert(e) {
    const alertId = e.currentTarget.dataset.id;
    const alert = this.data.alerts.find(item => item.id === alertId);
    
    wx.showModal({
      title: '标记为已解决',
      content: `确定要将"${alert.title}"标记为已解决吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '处理中...' });
            
            const response = await api.resolveAlert(alertId, '手动标记为已解决');
            
            if (response.code === 200) {
              wx.showToast({
                title: '已标记为已解决',
                icon: 'success'
              });
              
              // 重新加载异常记录
              this.loadAlerts();
            }
          } catch (error) {
            console.error('标记异常失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          } finally {
            wx.hideLoading();
          }
        }
      }
    });
  },

  viewAlertDetail(e) {
    const alertId = e.currentTarget.dataset.id;
    const alert = this.data.alerts.find(item => item.id === alertId);
    
    wx.showModal({
      title: alert.title,
      content: `${alert.description}\n\n建议解决方案:\n${alert.solution}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // API数据加载方法
  async loadOperationLogs() {
    try {
      wx.showLoading({ title: '加载中...' });
      
      const params = {
        type: this.data.operationFilter === 'all' ? undefined : this.data.operationFilter,
        startDate: this.data.filters.startDate,
        endDate: this.data.filters.endDate,
        page: 1,
        limit: 50
      };
      
      const response = await api.getOperationLogs(params);
      
      if (response.code === 200) {
        this.setData({
          operationLogs: response.data.logs,
          filteredOperationLogs: response.data.logs,
          operationStats: response.data.stats,
          hasMoreOperationLogs: response.data.hasMore
        });
      }
    } catch (error) {
      console.error('加载操作日志失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  async loadReports() {
    try {
      const response = await api.getReports({ 
        type: this.data.reportType 
      });
      
      if (response.code === 200) {
        this.setData({
          reportData: response.data
        });
      }
    } catch (error) {
      console.error('加载运行报告失败:', error);
    }
  },

  async loadAlerts() {
    try {
      const params = {
        level: this.data.alertFilter === 'all' ? undefined : this.data.alertFilter
      };
      
      const response = await api.getAlerts(params);
      
      if (response.code === 200) {
        this.setData({
          alerts: response.data.alerts,
          filteredAlerts: response.data.alerts,
          alertStats: response.data.stats
        });
      }
    } catch (error) {
      console.error('加载异常记录失败:', error);
    }
  }
})