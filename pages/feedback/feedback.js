Page({
  data: {
    feedbackType: 'bug',
    feedbackTypes: [
      { value: 'bug', label: 'Bug反馈', icon: '🐛' },
      { value: 'feature', label: '功能建议', icon: '💡' },
      { value: 'ui', label: '界面体验', icon: '🎨' },
      { value: 'performance', label: '性能问题', icon: '⚡' },
      { value: 'other', label: '其他问题', icon: '💬' }
    ],
    title: '',
    content: '',
    contact: '',
    images: [],
    maxImages: 4,
    submitting: false
  },

  onLoad() {
    // 页面加载
  },

  selectType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      feedbackType: type
    });
  },

  onTitleInput(e) {
    this.setData({
      title: e.detail.value
    });
  },

  onContentInput(e) {
    this.setData({
      content: e.detail.value
    });
  },

  onContactInput(e) {
    this.setData({
      contact: e.detail.value
    });
  },

  chooseImage() {
    const { images, maxImages } = this.data;
    const remaining = maxImages - images.length;
    
    if (remaining <= 0) {
      wx.showToast({
        title: '最多上传4张图片',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: remaining,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newImages = [...images, ...res.tempFilePaths];
        this.setData({
          images: newImages
        });
      }
    });
  },

  previewImage(e) {
    const { current } = e.currentTarget.dataset;
    const { images } = this.data;
    
    wx.previewImage({
      current: current,
      urls: images
    });
  },

  removeImage(e) {
    const { index } = e.currentTarget.dataset;
    const { images } = this.data;
    
    wx.showModal({
      title: '删除图片',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          images.splice(index, 1);
          this.setData({
            images: [...images]
          });
        }
      }
    });
  },

  validateForm() {
    const { title, content, feedbackType } = this.data;
    
    if (!title.trim()) {
      wx.showToast({
        title: '请输入反馈标题',
        icon: 'none'
      });
      return false;
    }
    
    if (title.length < 5) {
      wx.showToast({
        title: '标题至少5个字符',
        icon: 'none'
      });
      return false;
    }
    
    if (!content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return false;
    }
    
    if (content.length < 10) {
      wx.showToast({
        title: '内容至少10个字符',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  async submitFeedback() {
    if (this.data.submitting) return;
    
    if (!this.validateForm()) return;
    
    this.setData({ submitting: true });
    
    try {
      wx.showLoading({
        title: '提交中...'
      });
      
      const { feedbackType, title, content, contact, images } = this.data;
      
      // 模拟提交反馈
      await this.uploadFeedback({
        type: feedbackType,
        title: title.trim(),
        content: content.trim(),
        contact: contact.trim(),
        images: images,
        timestamp: new Date().getTime()
      });
      
      wx.hideLoading();
      
      wx.showModal({
        title: '提交成功',
        content: '感谢您的反馈！我们会认真处理您的建议，并在1-3个工作日内回复。',
        showCancel: false,
        confirmText: '确定',
        success: () => {
          // 清空表单
          this.resetForm();
          
          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 500);
        }
      });
      
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'error'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  async uploadFeedback(data) {
    // 模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Feedback submitted:', data);
        // 保存到本地存储（实际应用中应该发送到服务器）
        const feedbacks = wx.getStorageSync('feedbacks') || [];
        feedbacks.unshift(data);
        wx.setStorageSync('feedbacks', feedbacks);
        resolve();
      }, 2000);
    });
  },

  resetForm() {
    this.setData({
      feedbackType: 'bug',
      title: '',
      content: '',
      contact: '',
      images: []
    });
  },

  saveDraft() {
    const { feedbackType, title, content, contact, images } = this.data;
    
    if (!title.trim() && !content.trim()) {
      wx.showToast({
        title: '没有内容可保存',
        icon: 'none'
      });
      return;
    }
    
    const draft = {
      feedbackType,
      title: title.trim(),
      content: content.trim(),
      contact: contact.trim(),
      images,
      timestamp: new Date().getTime()
    };
    
    wx.setStorageSync('feedbackDraft', draft);
    
    wx.showToast({
      title: '草稿已保存',
      icon: 'success'
    });
  },

  loadDraft() {
    const draft = wx.getStorageSync('feedbackDraft');
    
    if (!draft) {
      wx.showToast({
        title: '没有保存的草稿',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '加载草稿',
      content: '确定要加载之前保存的草稿吗？当前内容将被覆盖。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            feedbackType: draft.feedbackType || 'bug',
            title: draft.title || '',
            content: draft.content || '',
            contact: draft.contact || '',
            images: draft.images || []
          });
          
          wx.showToast({
            title: '草稿已加载',
            icon: 'success'
          });
        }
      }
    });
  }
})