/* 图表组件样式 */
.chart-container {
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.chart-actions {
  display: flex;
  gap: 20rpx;
}

.chart-action {
  font-size: 24rpx;
  color: #2E7D32;
  padding: 10rpx 20rpx;
  border: 1rpx solid #2E7D32;
  border-radius: 8rpx;
  background-color: transparent;
}

.chart-action:active {
  background-color: #2E7D32;
  color: #ffffff;
}

.chart-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-canvas {
  display: block;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 28rpx;
  color: #666666;
}

.chart-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.chart-empty image {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.5;
}

.chart-empty text {
  font-size: 28rpx;
  color: #999999;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #E0E0E0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.legend-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
}

.legend-text {
  font-size: 24rpx;
  color: #666666;
}