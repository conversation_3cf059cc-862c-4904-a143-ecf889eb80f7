# 智慧农业物联网后端系统

## 项目简介

智慧农业物联网后端系统是基于SpringBoot3 + MyBatis-Plus + JDK17开发的现代化农业管理系统，为智慧农业小程序提供完整的后端API支持。

## 技术栈

- **框架**: SpringBoot 3.2.1
- **ORM**: MyBatis-Plus 3.5.5
- **JDK**: 17
- **数据库**: MySQL 8.0+
- **缓存**: Redis
- **文档**: Swagger/OpenAPI 3.0
- **构建工具**: Maven

## 项目结构

```
agriculture-iot-backend/
├── src/main/java/com/agriculture/iot/
│   ├── AgricultureIotApplication.java    # 启动类
│   ├── entity/                           # 实体类
│   ├── mapper/                           # 数据访问层
│   ├── service/                          # 业务逻辑层
│   ├── controller/                       # 控制器层
│   ├── dto/                              # 数据传输对象
│   ├── config/                           # 配置类
│   └── common/                           # 公共类
├── src/main/resources/
│   ├── application.yml                   # 配置文件
│   └── mapper/                           # MyBatis XML映射文件
├── pom.xml                               # Maven配置
└── README.md                            # 项目说明
```

## 环境要求

- **Java**: JDK 17+
- **MySQL**: 8.0+
- **Redis**: 5.0+
- **Maven**: 3.6+

## 快速开始

### 1. 数据库初始化

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS agriculture_iot DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行数据库脚本
mysql -u root -p agriculture_iot < database-schema.sql
```

### 2. 配置文件修改

编辑 `src/main/resources/application.yml` 文件：

```yaml
spring:
  datasource:
    url: **********************************************************************************************************************
    username: your_username
    password: your_password
  
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
```

### 3. 项目构建

```bash
# 进入项目目录
cd agriculture-iot-backend

# 安装依赖
mvn clean install

# 运行项目
mvn spring-boot:run
```

### 4. 访问API文档

项目启动后，访问以下地址查看API文档：
- Swagger UI: http://localhost:8080/api/swagger-ui.html
- OpenAPI JSON: http://localhost:8080/api/v3/api-docs

## API接口

### 农场管理
- `GET /api/farms` - 获取农场列表
- `GET /api/farms/{id}` - 获取农场详情
- `POST /api/farms` - 创建农场
- `PUT /api/farms/{id}` - 更新农场
- `DELETE /api/farms/{id}` - 删除农场

### 地块管理
- `GET /api/plots` - 获取地块列表
- `GET /api/plots/{id}` - 获取地块详情
- `POST /api/plots` - 创建地块
- `PUT /api/plots/{id}` - 更新地块
- `DELETE /api/plots/{id}` - 删除地块

### 设备管理
- `GET /api/devices` - 获取设备列表
- `GET /api/devices/{id}` - 获取设备详情
- `POST /api/devices` - 创建设备
- `PUT /api/devices/{id}` - 更新设备
- `DELETE /api/devices/{id}` - 删除设备

### 传感器数据
- `GET /api/sensor-data` - 获取传感器数据
- `POST /api/sensor-data` - 上传传感器数据
- `GET /api/sensor-data/latest/{deviceId}` - 获取最新数据

### 灌溉管理
- `GET /api/irrigation-records` - 获取灌溉记录
- `POST /api/irrigation-records` - 创建灌溉记录
- `GET /api/irrigation-schedules` - 获取灌溉计划
- `POST /api/irrigation-schedules` - 创建灌溉计划

### 施肥管理
- `GET /api/fertilizer-records` - 获取施肥记录
- `POST /api/fertilizer-records` - 创建施肥记录

### 告警管理
- `GET /api/alarms` - 获取告警列表
- `PUT /api/alarms/{id}/status` - 更新告警状态

## 数据库设计

### 主要表结构
- `farms` - 农场信息表
- `plots` - 地块信息表
- `devices` - 设备信息表
- `device_types` - 设备类型表
- `sensor_data` - 传感器数据表
- `sensor_types` - 传感器类型表
- `irrigation_records` - 灌溉记录表
- `fertilizer_records` - 施肥记录表
- `fertilizer_types` - 肥料类型表
- `irrigation_schedules` - 灌溉计划表
- `alarms` - 告警记录表

## 部署说明

### 开发环境
1. 确保已安装JDK 17、MySQL 8.0、Redis
2. 创建数据库并执行初始化脚本
3. 修改配置文件中的数据库连接信息
4. 运行 `mvn spring-boot:run`

### 生产环境
1. 使用Docker部署：
```bash
# 构建镜像
docker build -t agriculture-iot-backend .

# 运行容器
docker run -d \
  --name agriculture-iot-backend \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e SPRING_DATASOURCE_URL=*************************************** \
  -e SPRING_DATASOURCE_USERNAME=root \
  -e SPRING_DATASOURCE_PASSWORD=password \
  -e SPRING_REDIS_HOST=redis \
  -e SPRING_REDIS_PORT=6379 \
  agriculture-iot-backend
```

### 性能优化
1. **数据库优化**:
   - 为频繁查询的字段添加索引
   - 使用分区表存储传感器历史数据
   - 定期清理过期数据

2. **缓存策略**:
   - 使用Redis缓存热点数据
   - 设置合理的缓存过期时间
   - 实现缓存预热机制

3. **连接池配置**:
   - 根据服务器配置调整连接池大小
   - 监控连接池使用情况

## 监控与日志

### 日志配置
- 日志文件路径: `logs/agriculture-iot.log`
- 日志级别: DEBUG (开发环境) / INFO (生产环境)
- 日志轮转: 每天生成新文件，保留30天

### 健康检查
- 健康检查端点: http://localhost:8080/api/actuator/health
- 详细信息端点: http://localhost:8080/api/actuator/info

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库连接配置正确
- 检查防火墙设置

### 2. Redis连接失败
- 检查Redis服务是否启动
- 确认Redis配置正确
- 检查Redis密码是否正确

### 3. 端口占用
- 检查8080端口是否被占用
- 修改application.yml中的server.port配置

### 4. 依赖下载失败
- 检查Maven配置和镜像源
- 清理本地Maven仓库缓存
- 使用 `mvn clean install -U` 强制更新

## 开发规范

### 代码规范
- 遵循阿里巴巴Java开发规范
- 使用Lombok简化代码
- 统一异常处理
- 统一响应格式

### API规范
- RESTful API设计
- 统一返回格式
- 使用HTTP状态码
- 提供详细的API文档

### 数据库规范
- 使用下划线命名法
- 添加表和字段注释
- 合理使用索引
- 避免使用保留字

## 技术支持

如有问题，请联系：
- 邮箱: <EMAIL>
- 文档: https://docs.agriculture-iot.com
- 问题反馈: https://github.com/agriculture-iot/backend/issues