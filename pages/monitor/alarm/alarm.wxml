<!--预警设置页面-->
<view class="page-container">
  
  <!-- 标签切换 -->
  <view class="tab-bar">
    <view 
      class="tab-item {{currentTab === 'settings' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="settings"
    >
      <text class="tab-text">预警设置</text>
    </view>
    <view 
      class="tab-item {{currentTab === 'history' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="history"
    >
      <text class="tab-text">历史记录</text>
    </view>
    <view 
      class="tab-item {{currentTab === 'notification' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="notification"
    >
      <text class="tab-text">通知设置</text>
    </view>
  </view>

  <!-- 预警设置 -->
  <view wx:if="{{currentTab === 'settings'}}" class="settings-content">
    <view class="parameters-list">
      <view 
        wx:for="{{parameterKeys}}" 
        wx:for-item="paramKey"
        wx:key="*this"
        class="parameter-item"
      >
        <view class="parameter-header">
          <view class="parameter-info">
            <view class="parameter-icon">{{displayData[paramKey].paramIcon}}</view>
            <view class="parameter-details">
              <text class="parameter-name">{{displayData[paramKey].paramName}}</text>
              <text class="parameter-desc">{{displayData[paramKey].paramDesc}}</text>
            </view>
          </view>
          
          <switch 
            class="parameter-switch"
            checked="{{displayData[paramKey].enabled}}"
            bindchange="toggleParameter"
            data-parameter="{{paramKey}}"
            color="#2E7D32"
          />
        </view>
        
        <view wx:if="{{displayData[paramKey].enabled}}" class="parameter-settings">
          <view class="threshold-display">
            <view class="threshold-item">
              <text class="threshold-label">最小值</text>
              <text class="threshold-value">{{displayData[paramKey].min}}{{displayData[paramKey].paramUnit}}</text>
            </view>
            <view class="threshold-item">
              <text class="threshold-label">最大值</text>
              <text class="threshold-value">{{displayData[paramKey].max}}{{displayData[paramKey].paramUnit}}</text>
            </view>
            <view class="threshold-item">
              <text class="threshold-label">预警级别</text>
              <view class="level-badge level-{{displayData[paramKey].level}}">
                {{displayData[paramKey].levelLabel}}
              </view>
            </view>
          </view>
          
          <button 
            class="btn btn-outline btn-small"
            bindtap="editParameter"
            data-parameter="{{paramKey}}"
          >
            ⚙️ 编辑
          </button>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="settings-actions">
      <button class="btn btn-secondary" bindtap="testAlarm">
        🧪 测试预警
      </button>
      <button class="btn btn-outline" bindtap="resetSettings">
        🔄 恢复默认
      </button>
    </view>
  </view>

  <!-- 历史记录 -->
  <view wx:if="{{currentTab === 'history'}}" class="history-content">
    <view wx:if="{{alarmHistory.length === 0}}" class="empty-state">
      <view class="empty-icon">📋</view>
      <text class="empty-title">暂无预警记录</text>
      <text class="empty-desc">当参数超出设定阈值时，会在这里显示预警信息</text>
    </view>
    
    <view wx:else class="history-list">
      <view 
        wx:for="{{alarmHistory}}" 
        wx:key="id"
        class="history-item"
        bindtap="viewAlarmDetail"
        data-id="{{item.id}}"
      >
        <view class="history-icon">
          {{parameterInfo[item.type].icon}}
        </view>
        
        <view class="history-info">
          <text class="history-title">{{item.message}}</text>
          <text class="history-detail">
            当前值: {{item.value}}{{parameterInfo[item.type].unit}} | 
            阈值: {{item.threshold}}{{parameterInfo[item.type].unit}}
          </text>
          <text class="history-time">{{item.timestamp}}</text>
        </view>
        
        <view class="history-actions">
          <view class="level-badge level-{{item.level}}">
            <text wx:if="{{item.level === 'low'}}">低级预警</text>
            <text wx:elif="{{item.level === 'medium'}}">中级预警</text>
            <text wx:else>高级预警</text>
          </view>
          
          <button 
            wx:if="{{item.status === 'pending'}}"
            class="btn btn-small btn-primary"
            bindtap="handleAlarm"
            data-id="{{item.id}}"
            catchtap="true"
          >
            处理
          </button>
          
          <view wx:else class="status-badge handled">
            已处理
          </view>
        </view>
      </view>
    </view>
    
    <view wx:if="{{alarmHistory.length > 0}}" class="history-actions">
      <button class="btn btn-outline" bindtap="clearHistory">
        🗑️ 清空历史
      </button>
    </view>
  </view>

  <!-- 通知设置 -->
  <view wx:if="{{currentTab === 'notification'}}" class="notification-content">
    <view class="notification-list">
      <view class="notification-item">
        <view class="notification-info">
          <view class="notification-icon">📱</view>
          <view class="notification-details">
            <text class="notification-name">小程序推送</text>
            <text class="notification-desc">在小程序内显示预警消息</text>
          </view>
        </view>
        <switch 
          checked="{{notificationSettings.push}}"
          bindchange="toggleNotification"
          data-type="push"
          color="#2E7D32"
        />
      </view>
      
      <view class="notification-item">
        <view class="notification-info">
          <view class="notification-icon">💬</view>
          <view class="notification-details">
            <text class="notification-name">微信消息</text>
            <text class="notification-desc">通过微信服务通知发送消息</text>
          </view>
        </view>
        <switch 
          checked="{{notificationSettings.wechat}}"
          bindchange="toggleNotification"
          data-type="wechat"
          color="#2E7D32"
        />
      </view>
      
      <view class="notification-item">
        <view class="notification-info">
          <view class="notification-icon">📱</view>
          <view class="notification-details">
            <text class="notification-name">短信通知</text>
            <text class="notification-desc">重要预警通过短信发送（付费功能）</text>
          </view>
        </view>
        <switch 
          checked="{{notificationSettings.sms}}"
          bindchange="toggleNotification"
          data-type="sms"
          color="#2E7D32"
        />
      </view>
      
      <view class="notification-item">
        <view class="notification-info">
          <view class="notification-icon">📧</view>
          <view class="notification-details">
            <text class="notification-name">邮件通知</text>
            <text class="notification-desc">定期发送预警汇总邮件</text>
          </view>
        </view>
        <switch 
          checked="{{notificationSettings.email}}"
          bindchange="toggleNotification"
          data-type="email"
          color="#2E7D32"
        />
      </view>
    </view>
    
    <view class="notification-tip">
      <view class="tip-icon">💡</view>
      <view class="tip-content">
        <text class="tip-title">通知说明</text>
        <text class="tip-text">• 小程序推送：免费，需要用户授权</text>
        <text class="tip-text">• 微信消息：免费，通过微信服务号发送</text>
        <text class="tip-text">• 短信通知：收费功能，紧急情况使用</text>
        <text class="tip-text">• 邮件通知：免费，需要绑定邮箱</text>
      </view>
    </view>
  </view>

  <!-- 参数编辑弹窗 -->
  <view wx:if="{{editingParameter}}" class="edit-modal">
    <view class="modal-backdrop" bindtap="cancelEdit"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">编辑 {{parameterInfo[editingParameter].name}}</text>
        <view class="modal-close" bindtap="cancelEdit">×</view>
      </view>
      
      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">最小值 ({{parameterInfo[editingParameter].unit}})</text>
          <input 
            class="form-input"
            type="digit"
            value="{{tempSettings.min}}"
            bindinput="onMinInput"
            placeholder="请输入最小值"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">最大值 ({{parameterInfo[editingParameter].unit}})</text>
          <input 
            class="form-input"
            type="digit"
            value="{{tempSettings.max}}"
            bindinput="onMaxInput"
            placeholder="请输入最大值"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">预警级别</text>
          <view class="level-selector">
            <view 
              wx:for="{{levelOptions}}" 
              wx:key="value"
              class="level-option {{tempSettings.level === item.value ? 'selected' : ''}}"
              bindtap="selectLevel"
              data-level="{{item.value}}"
            >
              <view class="level-color" style="background-color: {{item.color}}"></view>
              <text class="level-label">{{item.label}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="btn btn-secondary" bindtap="cancelEdit">取消</button>
        <button class="btn btn-primary" bindtap="confirmEdit">确认</button>
      </view>
    </view>
  </view>
</view>