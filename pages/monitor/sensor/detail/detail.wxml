<!--传感器详情页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 传感器基本信息 -->
    <view class="card">
      <view class="device-header">
        <view class="device-avatar">
          <text class="device-icon">{{sensorInfo.icon}}</text>
        </view>
        <view class="device-basic">
          <text class="device-name">{{sensorInfo.name}}</text>
          <text class="device-type">{{sensorInfo.type}}</text>
          <view class="device-status {{sensorInfo.status}}">
            <text class="status-dot"></text>
            <text class="status-text">{{sensorInfo.statusText}}</text>
          </view>
        </view>
        <view class="signal-indicator">
          <view class="signal-bars {{sensorInfo.signalLevel}}">
            <view class="signal-bar"></view>
            <view class="signal-bar"></view>
            <view class="signal-bar"></view>
            <view class="signal-bar"></view>
          </view>
          <text class="signal-text">{{sensorInfo.signalStrength}}dBm</text>
        </view>
      </view>
      
      <view class="info-grid">
        <view class="info-item" wx:for="{{deviceDetails}}" wx:key="key">
          <text class="info-label">{{item.label}}</text>
          <text class="info-value">{{item.value}}</text>
        </view>
      </view>
    </view>

    <!-- 实时数据 -->
    <view class="card">
      <view class="card-title">
        <text>实时数据</text>
        <text class="update-time">更新时间: {{lastUpdateTime}}</text>
      </view>
      
      <view class="realtime-data">
        <view class="data-item {{item.status}}" wx:for="{{realtimeData}}" wx:key="type">
          <view class="data-header">
            <text class="data-icon">{{item.icon}}</text>
            <text class="data-name">{{item.name}}</text>
          </view>
          <view class="data-value">
            <text class="value-number">{{item.value}}</text>
            <text class="value-unit">{{item.unit}}</text>
          </view>
          <view class="data-status">
            <text class="status-indicator {{item.status}}"></text>
            <text class="status-text">{{item.statusText}}</text>
          </view>
          <view class="data-range">
            <text class="range-text">正常: {{item.normalRange}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 24小时趋势 -->
    <view class="card">
      <view class="card-title">
        <text>24小时趋势</text>
        <picker range="{{indicatorOptions}}" value="{{selectedIndicator}}" bindchange="onIndicatorChange">
          <view class="picker-btn">{{indicatorOptions[selectedIndicator]}}</view>
        </picker>
      </view>
      
      <view class="trend-chart">
        <view class="chart-container">
          <!-- 简单图表展示 -->
          <view class="simple-chart">
            <view class="chart-y-axis">
              <text class="y-label" wx:for="{{chartData.yLabels}}" wx:key="*this">{{item}}</text>
            </view>
            
            <view class="chart-content">
              <view class="chart-grid">
                <view class="grid-line" wx:for="{{6}}" wx:key="*this"></view>
              </view>
              <view class="chart-line">
                <view class="chart-point" wx:for="{{chartData.points}}" wx:key="index"
                      style="left: {{item.x}}%; bottom: {{item.y}}%">
                  <view class="point-dot"></view>
                  <view class="point-value" wx:if="{{item.showValue}}">{{item.value}}</view>
                </view>
              </view>
            </view>
            
            <view class="chart-x-axis">
              <text class="x-label" wx:for="{{chartData.xLabels}}" wx:key="*this">{{item}}</text>
            </view>
          </view>
        </view>
        
        <view class="chart-stats">
          <view class="stat-item">
            <text class="stat-label">最大值</text>
            <text class="stat-value">{{chartStats.max}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">最小值</text>
            <text class="stat-value">{{chartStats.min}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">平均值</text>
            <text class="stat-value">{{chartStats.avg}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 预警设置 -->
    <view class="card">
      <view class="card-title">预警设置</view>
      <view class="alarm-settings">
        <view class="alarm-item" wx:for="{{alarmSettings}}" wx:key="type">
          <view class="alarm-header">
            <text class="alarm-name">{{item.name}}</text>
            <switch checked="{{item.enabled}}" bindchange="toggleAlarm" data-type="{{item.type}}"/>
          </view>
          
          <view class="alarm-config" wx:if="{{item.enabled}}">
            <view class="threshold-group">
              <text class="threshold-label">上限值</text>
              <input type="number" value="{{item.maxValue}}" bindinput="onMaxValueInput" 
                     data-type="{{item.type}}" class="threshold-input"/>
              <text class="threshold-unit">{{item.unit}}</text>
            </view>
            
            <view class="threshold-group">
              <text class="threshold-label">下限值</text>
              <input type="number" value="{{item.minValue}}" bindinput="onMinValueInput" 
                     data-type="{{item.type}}" class="threshold-input"/>
              <text class="threshold-unit">{{item.unit}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="alarm-actions">
        <button class="btn btn-primary" bindtap="saveAlarmSettings">保存设置</button>
        <button class="btn btn-outline" bindtap="resetAlarmSettings">重置默认值</button>
      </view>
    </view>

    <!-- 数据记录 -->
    <view class="card">
      <view class="card-title">
        <text>数据记录</text>
        <view class="record-filter">
          <picker range="{{timeRangeOptions}}" value="{{selectedTimeRange}}" bindchange="onTimeRangeChange">
            <view class="picker-btn">{{timeRangeOptions[selectedTimeRange]}}</view>
          </picker>
        </view>
      </view>
      
      <view class="data-table">
        <view class="table-header">
          <view class="header-cell time">时间</view>
          <view class="header-cell" wx:for="{{tableHeaders}}" wx:key="*this">{{item}}</view>
        </view>
        
        <scroll-view class="table-body" scroll-y>
          <view class="table-row" wx:for="{{dataRecords}}" wx:key="time">
            <view class="table-cell time">{{item.time}}</view>
            <view class="table-cell" wx:for="{{item.values}}" wx:key="index">
              {{item}}
            </view>
          </view>
        </scroll-view>
      </view>
      
      <view class="table-actions">
        <button class="btn btn-outline" bindtap="exportData">
          📄 导出数据
        </button>
        <button class="btn btn-outline" bindtap="viewMoreHistory">
          📈 查看更多
        </button>
      </view>
    </view>

    <!-- 维护信息 -->
    <view class="card">
      <view class="card-title">维护信息</view>
      <view class="maintenance-info">
        <view class="maintenance-item">
          <text class="maintenance-label">上次校准</text>
          <text class="maintenance-value">{{maintenanceInfo.lastCalibration}}</text>
        </view>
        <view class="maintenance-item">
          <text class="maintenance-label">下次校准</text>
          <text class="maintenance-value">{{maintenanceInfo.nextCalibration}}</text>
        </view>
        <view class="maintenance-item">
          <text class="maintenance-label">电池更换</text>
          <text class="maintenance-value">{{maintenanceInfo.batteryReplacement}}</text>
        </view>
        <view class="maintenance-item">
          <text class="maintenance-label">保修期至</text>
          <text class="maintenance-value">{{maintenanceInfo.warrantyExpiry}}</text>
        </view>
      </view>
      
      <view class="maintenance-actions">
        <button class="btn btn-outline" bindtap="calibrateSensor">
          🔧 立即校准
        </button>
        <button class="btn btn-outline" bindtap="scheduleMaintenance">
          📅 预约维护
        </button>
      </view>
    </view>

  </view>
</view>