package com.agriculture.iot.mapper;

import com.agriculture.iot.entity.FertilizerRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 施肥记录数据访问层
 *
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Mapper
public interface FertilizerRecordMapper extends BaseMapper<FertilizerRecord> {

    /**
     * 分页查询施肥记录（包含关联信息）
     */
    IPage<FertilizerRecord> selectFertilizerRecordsPage(
            Page<FertilizerRecord> page,
            @Param("plotId") Long plotId,
            @Param("deviceId") Long deviceId,
            @Param("status") String status,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 获取施肥状态信息
     */
    FertilizerRecord.StatusInfo selectFertilizerStatus(@Param("recordId") Long recordId);

    /**
     * 获取施肥统计数据
     */
    FertilizerRecord.Statistics selectFertilizerStatistics(
            @Param("plotId") Long plotId,
            @Param("deviceId") Long deviceId,
            @Param("days") Integer days);

    /**
     * 获取施肥效果分析数据
     */
    List<FertilizerRecord.EffectivenessData> selectFertilizerEffectiveness(
            @Param("plotId") Long plotId,
            @Param("deviceId") Long deviceId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 获取施肥报告数据
     */
    FertilizerRecord.ReportData selectFertilizerReport(
            @Param("reportType") String reportType,
            @Param("deviceId") Long deviceId,
            @Param("plotId") Long plotId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 获取肥料配方列表
     */
    List<FertilizerRecord.Formula> selectFertilizerFormulas(
            @Param("cropType") String cropType,
            @Param("growthStage") String growthStage);

    /**
     * 获取推荐配方
     */
    List<FertilizerRecord.Formula> selectRecommendedFormulas(
            @Param("cropType") String cropType,
            @Param("growthStage") String growthStage,
            @Param("soilType") String soilType,
            @Param("plotId") Long plotId);

    /**
     * 创建肥料配方
     */
    int insertFertilizerFormula(FertilizerRecord.Formula formula);

    /**
     * 获取营养元素分析
     */
    FertilizerRecord.NutrientAnalysis selectNutrientAnalysis(@Param("plotId") Long plotId, @Param("days") Integer days);

    /**
     * 获取施肥建议
     */
    List<FertilizerRecord.Suggestion> selectFertilizerSuggestions(@Param("deviceId") Long deviceId, @Param("plotId") Long plotId);

    /**
     * 获取提醒配置
     */
    List<FertilizerRecord.ReminderConfig> selectFertilizerReminders(@Param("deviceId") Long deviceId, @Param("plotId") Long plotId);

    /**
     * 设置施肥提醒
     */
    int insertFertilizerReminder(FertilizerRecord.ReminderConfig config);

    /**
     * 获取成本分析
     */
    FertilizerRecord.CostAnalysis selectFertilizerCostAnalysis(
            @Param("plotId") Long plotId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 获取施肥计划列表
     */
    IPage<FertilizerRecord.Plan> selectFertilizerPlansPage(
            Page<FertilizerRecord.Plan> page,
            @Param("plotId") Long plotId,
            @Param("status") String status);

    /**
     * 创建施肥计划
     */
    int insertFertilizerPlan(FertilizerRecord.Plan plan);

    /**
     * 更新施肥计划
     */
    int updateFertilizerPlan(FertilizerRecord.Plan plan);

    /**
     * 获取施肥模板列表
     */
    IPage<FertilizerRecord.Template> selectFertilizerTemplatesPage(
            Page<FertilizerRecord.Template> page,
            @Param("cropType") String cropType);

    /**
     * 创建施肥模板
     */
    int insertFertilizerTemplate(FertilizerRecord.Template template);

    /**
     * 更新施肥模板
     */
    int updateFertilizerTemplate(FertilizerRecord.Template template);

    /**
     * 获取施肥提醒列表
     */
    IPage<FertilizerRecord.Reminder> selectFertilizerRemindersPage(
            Page<FertilizerRecord.Reminder> page,
            @Param("status") String status);

    /**
     * 创建施肥提醒
     */
    int insertFertilizerReminderRecord(FertilizerRecord.Reminder reminder);

    /**
     * 更新施肥提醒
     */
    int updateFertilizerReminder(FertilizerRecord.Reminder reminder);

    /**
     * 删除指定时间之前的记录
     */
    int deleteBefore(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * 批量施肥操作
     */
    int batchInsertFertilizerRecords(@Param("records") List<FertilizerRecord> records);

    /**
     * 获取设备当前施肥状态
     */
    FertilizerRecord selectCurrentFertilizerRecord(@Param("deviceId") Long deviceId);

    /**
     * 更新施肥记录状态
     */
    int updateFertilizerRecordStatus(@Param("recordId") Long recordId, @Param("status") String status);
}