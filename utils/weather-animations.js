/**
 * 天气动画数据模块
 * 包含所有天气类型的Lottie动画数据
 */

// 静态 require 所有动画 js 文件（注意：json需改为js并用module.exports导出）
const sunny = require('../assets/animation/sunny.js');
const rain = require('../assets/animation/rain.js');
const cloudy = require('../assets/animation/cloudy.js');
const snow = require('../assets/animation/snow.js');
// 如有更多类型，继续添加

const animationMap = {
  sunny,
  rain,
  cloudy,
  snow
  // 其他类型...
};

function getAnimationData(type) {
  return animationMap[type] || {};
}

module.exports = {
  getAnimationData
};