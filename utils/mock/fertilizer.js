/**
 * 施肥配方相关Mock API
 * 提供施肥配方管理、施肥操作记录等功能
 */

const dataModule = require('./data');

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 验证Token
function verifyToken(token) {
  // 开发环境始终返回 mock 用户信息，跳过 token 校验
  return {
    userId: 'mock_user',
    username: 'testuser',
    exp: Math.floor(Date.now() / 1000) + 3600
  };
}

// 施肥配方数据
const fertilizerFormulas = [
  {
    id: 'formula_001',
    name: '番茄开花期配方',
    type: 'tomato',
    stage: 'flowering',
    description: '适用于番茄开花期的专用营养配方',
    isDefault: true,
    isActive: true,
    creator: 'system',
    nutrients: {
      nitrogen: { percentage: 18, unit: '%' },     // 氮含量
      phosphorus: { percentage: 12, unit: '%' },   // 磷含量  
      potassium: { percentage: 20, unit: '%' },    // 钾含量
      calcium: { percentage: 8, unit: '%' },       // 钙含量
      magnesium: { percentage: 3, unit: '%' },     // 镁含量
      sulfur: { percentage: 2, unit: '%' },        // 硫含量
      iron: { percentage: 0.15, unit: '%' },       // 铁含量
      zinc: { percentage: 0.05, unit: '%' },       // 锌含量
      boron: { percentage: 0.02, unit: '%' },      // 硼含量
      manganese: { percentage: 0.1, unit: '%' }    // 锰含量
    },
    concentration: {
      ec: { min: 1.8, max: 2.2, optimal: 2.0, unit: 'mS/cm' },
      ph: { min: 5.8, max: 6.5, optimal: 6.2, unit: '' },
      ratio: 500, // 稀释比例 1:500
      dosage: 2.5 // 每平米用量 (ml/m²)
    },
    application: {
      frequency: 'daily',           // 施用频率
      duration: 20,                // 每次施用时长(分钟)
      flowRate: 40,                // 流速 L/min
      bestTime: ['06:00-08:00', '18:00-20:00'], // 最佳施用时间
      waterRequirement: 150,       // 配水量 L/m²
      temperature: { min: 18, max: 30 } // 适用温度范围
    },
    suitableCrops: ['番茄', '辣椒', '茄子'],
    growthStages: ['开花期', '结果期'],
    seasons: ['春季', '夏季', '秋季'],
    tags: ['高钾', '促花', '增产'],
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-15T10:30:00.000Z'
  },
  {
    id: 'formula_002',
    name: '生菜生长期配方',
    type: 'lettuce',
    stage: 'growth',
    description: '适用于生菜快速生长期的均衡营养配方',
    isDefault: true,
    isActive: true,
    creator: 'system',
    nutrients: {
      nitrogen: { percentage: 20, unit: '%' },
      phosphorus: { percentage: 8, unit: '%' },
      potassium: { percentage: 15, unit: '%' },
      calcium: { percentage: 6, unit: '%' },
      magnesium: { percentage: 4, unit: '%' },
      sulfur: { percentage: 3, unit: '%' },
      iron: { percentage: 0.12, unit: '%' },
      zinc: { percentage: 0.03, unit: '%' },
      boron: { percentage: 0.015, unit: '%' },
      manganese: { percentage: 0.08, unit: '%' }
    },
    concentration: {
      ec: { min: 1.2, max: 1.6, optimal: 1.4, unit: 'mS/cm' },
      ph: { min: 6.0, max: 6.8, optimal: 6.4, unit: '' },
      ratio: 800,
      dosage: 1.8
    },
    application: {
      frequency: 'twice_daily',
      duration: 15,
      flowRate: 35,
      bestTime: ['07:00-09:00', '17:00-19:00'],
      waterRequirement: 120,
      temperature: { min: 15, max: 28 }
    },
    suitableCrops: ['生菜', '菠菜', '小白菜'],
    growthStages: ['生长期', '叶片发育期'],
    seasons: ['春季', '秋季', '冬季'],
    tags: ['高氮', '促叶', '速生'],
    createdAt: '2024-01-05T00:00:00.000Z',
    updatedAt: '2024-01-20T15:45:00.000Z'
  },
  {
    id: 'formula_003',
    name: '萝卜根茎发育配方',
    type: 'radish',
    stage: 'root_development',
    description: '专为萝卜根茎发育期设计的高钾配方',
    isDefault: false,
    isActive: true,
    creator: 'user_001',
    nutrients: {
      nitrogen: { percentage: 12, unit: '%' },
      phosphorus: { percentage: 15, unit: '%' },
      potassium: { percentage: 25, unit: '%' },
      calcium: { percentage: 10, unit: '%' },
      magnesium: { percentage: 5, unit: '%' },
      sulfur: { percentage: 4, unit: '%' },
      iron: { percentage: 0.18, unit: '%' },
      zinc: { percentage: 0.08, unit: '%' },
      boron: { percentage: 0.03, unit: '%' },
      manganese: { percentage: 0.12, unit: '%' }
    },
    concentration: {
      ec: { min: 1.6, max: 2.0, optimal: 1.8, unit: 'mS/cm' },
      ph: { min: 6.2, max: 7.0, optimal: 6.6, unit: '' },
      ratio: 600,
      dosage: 2.2
    },
    application: {
      frequency: 'every_other_day',
      duration: 25,
      flowRate: 30,
      bestTime: ['06:30-08:30', '16:30-18:30'],
      waterRequirement: 180,
      temperature: { min: 12, max: 25 }
    },
    suitableCrops: ['萝卜', '胡萝卜', '白萝卜'],
    growthStages: ['根茎发育期', '膨大期'],
    seasons: ['秋季', '冬季'],
    tags: ['高钾', '促根', '膨大'],
    createdAt: '2024-01-08T00:00:00.000Z',
    updatedAt: '2024-01-25T09:15:00.000Z'
  },
  {
    id: 'formula_004',
    name: '通用基础配方',
    type: 'universal',
    stage: 'all',
    description: '适用于大多数蔬菜的通用营养配方',
    isDefault: true,
    isActive: true,
    creator: 'system',
    nutrients: {
      nitrogen: { percentage: 16, unit: '%' },
      phosphorus: { percentage: 10, unit: '%' },
      potassium: { percentage: 18, unit: '%' },
      calcium: { percentage: 7, unit: '%' },
      magnesium: { percentage: 3.5, unit: '%' },
      sulfur: { percentage: 2.5, unit: '%' },
      iron: { percentage: 0.13, unit: '%' },
      zinc: { percentage: 0.04, unit: '%' },
      boron: { percentage: 0.02, unit: '%' },
      manganese: { percentage: 0.09, unit: '%' }
    },
    concentration: {
      ec: { min: 1.4, max: 1.8, optimal: 1.6, unit: 'mS/cm' },
      ph: { min: 5.8, max: 6.8, optimal: 6.3, unit: '' },
      ratio: 700,
      dosage: 2.0
    },
    application: {
      frequency: 'daily',
      duration: 18,
      flowRate: 45,
      bestTime: ['07:00-09:00', '17:00-19:00'],
      waterRequirement: 140,
      temperature: { min: 15, max: 32 }
    },
    suitableCrops: ['番茄', '生菜', '黄瓜', '青椒', '茄子'],
    growthStages: ['幼苗期', '生长期', '开花期'],
    seasons: ['春季', '夏季', '秋季', '冬季'],
    tags: ['通用', '均衡', '基础'],
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-10T12:00:00.000Z'
  }
];

// 施肥操作记录
function generateFertilizationRecords() {
  const records = [];
  const formulas = fertilizerFormulas;
  
  for (let i = 0; i < 15; i++) {
    const formula = formulas[Math.floor(Math.random() * formulas.length)];
    const startTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
    const duration = 15 + Math.random() * 20; // 15-35分钟
    const endTime = new Date(startTime.getTime() + duration * 60 * 1000);
    
    records.push({
      id: `fert_record_${String(i + 1).padStart(3, '0')}`,
      formulaId: formula.id,
      formulaName: formula.name,
      deviceId: `device_${String(Math.floor(Math.random() * 3) + 1).padStart(3, '0')}`,
      plotId: `plot_${String(Math.floor(Math.random() * 3) + 1).padStart(3, '0')}`,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      duration: Math.round(duration),
      status: Math.random() > 0.1 ? 'completed' : 'failed',
      actualEC: (formula.concentration.ec.optimal + (Math.random() - 0.5) * 0.2).toFixed(2),
      actualPH: (formula.concentration.ph.optimal + (Math.random() - 0.5) * 0.3).toFixed(2),
      volumeUsed: Math.round((formula.application.flowRate * duration / 60) * 10) / 10, // L
      concentrationRatio: formula.concentration.ratio,
      waterUsed: Math.round(formula.application.waterRequirement * 0.5), // 假设0.5m²
      efficiency: Math.round((85 + Math.random() * 15) * 10) / 10, // 85-100%效率
      operationType: Math.random() > 0.3 ? 'automatic' : 'manual',
      operatorId: Math.random() > 0.3 ? 'system' : 'user_001',
      notes: Math.random() > 0.7 ? '施肥完成，作物长势良好' : '',
      weather: {
        temperature: Math.round((18 + Math.random() * 15) * 10) / 10,
        humidity: Math.round((45 + Math.random() * 40) * 10) / 10,
        condition: ['晴', '多云', '阴'][Math.floor(Math.random() * 3)]
      },
      createdAt: startTime.toISOString(),
      updatedAt: endTime.toISOString()
    });
  }
  
  return records.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
}

module.exports = {
  init() { 
    console.log('施肥配方模块初始化完成'); 
  },

  // 获取施肥配方列表
  async getFormulas(token, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { type, stage, active, page = 1, pageSize = 20 } = params;
    
    let formulas = [...fertilizerFormulas];
    
    // 过滤条件
    if (type && type !== 'all') {
      formulas = formulas.filter(formula => formula.type === type);
    }
    
    if (stage && stage !== 'all') {
      formulas = formulas.filter(formula => 
        formula.stage === stage || formula.stage === 'all'
      );
    }
    
    if (active !== undefined) {
      formulas = formulas.filter(formula => formula.isActive === active);
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedFormulas = formulas.slice(startIndex, endIndex);

    return createApiResponse({
      formulas: paginatedFormulas,
      pagination: {
        page,
        pageSize,
        total: formulas.length,
        totalPages: Math.ceil(formulas.length / pageSize),
        hasNext: endIndex < formulas.length,
        hasPrev: page > 1
      },
      summary: {
        total: formulas.length,
        active: formulas.filter(f => f.isActive).length,
        default: formulas.filter(f => f.isDefault).length,
        custom: formulas.filter(f => f.creator !== 'system').length
      }
    });
  },

  // 创建施肥配方
  async createFormula(token, data) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { name, type, stage, description, nutrients, concentration, application } = data;

    if (!name || !type || !nutrients) {
      return createErrorResponse('缺少必要字段', 400);
    }

    const newFormula = {
      id: `formula_${Date.now()}`,
      name,
      type,
      stage: stage || 'all',
      description: description || '',
      isDefault: false,
      isActive: true,
      creator: payload.userId,
      nutrients,
      concentration,
      application,
      suitableCrops: data.suitableCrops || [],
      growthStages: data.growthStages || [],
      seasons: data.seasons || [],
      tags: data.tags || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 在实际应用中，这里应该保存到数据库
    fertilizerFormulas.push(newFormula);

    return createApiResponse(newFormula, '配方创建成功');
  },

  // 更新施肥配方
  async updateFormula(token, formulaId, data) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const formulaIndex = fertilizerFormulas.findIndex(f => f.id === formulaId);
    if (formulaIndex === -1) {
      return createErrorResponse('配方不存在', 404);
    }

    const formula = fertilizerFormulas[formulaIndex];
    
    // 检查权限
    if (formula.creator !== payload.userId && formula.creator === 'system') {
      return createErrorResponse('无权限修改系统默认配方', 403);
    }

    // 更新字段
    const updatedFormula = {
      ...formula,
      ...data,
      id: formulaId, // 保持ID不变
      creator: formula.creator, // 保持创建者不变
      createdAt: formula.createdAt, // 保持创建时间不变
      updatedAt: new Date().toISOString()
    };

    fertilizerFormulas[formulaIndex] = updatedFormula;

    return createApiResponse(updatedFormula, '配方更新成功');
  },

  // 删除施肥配方
  async deleteFormula(token, formulaId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const formulaIndex = fertilizerFormulas.findIndex(f => f.id === formulaId);
    if (formulaIndex === -1) {
      return createErrorResponse('配方不存在', 404);
    }

    const formula = fertilizerFormulas[formulaIndex];
    
    // 检查权限
    if (formula.creator !== payload.userId && formula.creator === 'system') {
      return createErrorResponse('无权限删除系统默认配方', 403);
    }

    // 检查是否正在使用
    const isInUse = Math.random() > 0.8; // 20%概率正在使用
    if (isInUse) {
      return createErrorResponse('配方正在使用中，无法删除', 400);
    }

    fertilizerFormulas.splice(formulaIndex, 1);

    return createApiResponse(null, '配方删除成功');
  },

  // 获取施肥记录
  async getRecords(token, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { 
      formulaId, 
      deviceId, 
      plotId, 
      status, 
      startDate, 
      endDate, 
      page = 1, 
      pageSize = 20 
    } = params;
    
    let records = generateFertilizationRecords();
    
    // 过滤条件
    if (formulaId) {
      records = records.filter(record => record.formulaId === formulaId);
    }
    
    if (deviceId) {
      records = records.filter(record => record.deviceId === deviceId);
    }
    
    if (plotId) {
      records = records.filter(record => record.plotId === plotId);
    }
    
    if (status) {
      records = records.filter(record => record.status === status);
    }
    
    // 日期过滤
    if (startDate || endDate) {
      const start = startDate ? new Date(startDate) : new Date('1900-01-01');
      const end = endDate ? new Date(endDate) : new Date('2100-12-31');
      
      records = records.filter(record => {
        const recordDate = new Date(record.startTime);
        return recordDate >= start && recordDate <= end;
      });
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedRecords = records.slice(startIndex, endIndex);

    return createApiResponse({
      records: paginatedRecords,
      pagination: {
        page,
        pageSize,
        total: records.length,
        totalPages: Math.ceil(records.length / pageSize),
        hasNext: endIndex < records.length,
        hasPrev: page > 1
      },
      statistics: {
        totalRecords: records.length,
        successRate: Math.round((records.filter(r => r.status === 'completed').length / records.length) * 100 * 10) / 10,
        totalVolume: Math.round(records.reduce((sum, r) => sum + r.volumeUsed, 0) * 10) / 10,
        avgEfficiency: Math.round(records.reduce((sum, r) => sum + r.efficiency, 0) / records.length * 10) / 10,
        todayRecords: records.filter(r => {
          const today = new Date().toISOString().split('T')[0];
          const recordDate = new Date(r.startTime).toISOString().split('T')[0];
          return recordDate === today;
        }).length
      }
    });
  },

  // 开始施肥操作
  async startFertilization(token, data) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { formulaId, deviceId, plotId, duration, customParameters } = data;

    if (!formulaId || !deviceId || !plotId) {
      return createErrorResponse('缺少必要参数', 400);
    }

    const formula = fertilizerFormulas.find(f => f.id === formulaId);
    if (!formula) {
      return createErrorResponse('配方不存在', 404);
    }

    const recordId = `fert_record_${Date.now()}`;
    const startTime = new Date().toISOString();

    return createApiResponse({
      recordId,
      formulaId,
      formulaName: formula.name,
      deviceId,
      plotId,
      startTime,
      estimatedDuration: duration || formula.application.duration,
      estimatedEndTime: new Date(Date.now() + (duration || formula.application.duration) * 60 * 1000).toISOString(),
      status: 'running',
      parameters: {
        ...formula.application,
        ...customParameters
      }
    }, '施肥操作已启动');
  },

  // 停止施肥操作
  async stopFertilization(token, recordId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    return createApiResponse({
      recordId,
      endTime: new Date().toISOString(),
      status: 'stopped',
      actualDuration: Math.round(Math.random() * 30 + 10) // 10-40分钟
    }, '施肥操作已停止');
  },

  // 获取推荐配方
  async getRecommendations(token, params) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { cropType, growthStage, season, soilCondition } = params;

    // 根据条件过滤推荐配方
    let recommendations = fertilizerFormulas.filter(formula => {
      let match = true;
      
      if (cropType && !formula.suitableCrops.includes(cropType)) {
        match = false;
      }
      
      if (growthStage && !formula.growthStages.includes(growthStage) && formula.stage !== 'all') {
        match = false;
      }
      
      if (season && !formula.seasons.includes(season)) {
        match = false;
      }
      
      return match;
    });

    // 按推荐度排序
    recommendations = recommendations.map(formula => ({
      ...formula,
      recommendationScore: Math.round((80 + Math.random() * 20) * 10) / 10,
      matchReasons: [
        cropType && formula.suitableCrops.includes(cropType) ? '适合作物类型' : null,
        growthStage && formula.growthStages.includes(growthStage) ? '匹配生长阶段' : null,
        season && formula.seasons.includes(season) ? '适合当前季节' : null
      ].filter(Boolean)
    })).sort((a, b) => b.recommendationScore - a.recommendationScore);

    return createApiResponse({
      recommendations: recommendations.slice(0, 5), // 返回前5个推荐
      criteria: { cropType, growthStage, season, soilCondition }
    });
  }
};