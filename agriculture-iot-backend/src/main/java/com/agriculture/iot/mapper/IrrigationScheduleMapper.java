package com.agriculture.iot.mapper;

import com.agriculture.iot.entity.IrrigationSchedule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 灌溉计划Mapper接口
 *
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface IrrigationScheduleMapper extends BaseMapper<IrrigationSchedule> {

    /**
     * 查找活跃的灌溉计划
     */
    @Select("SELECT * FROM irrigation_schedule " +
            "WHERE status = 'ACTIVE' " +
            "AND start_time <= #{now} " +
            "AND end_time >= #{now}")
    List<IrrigationSchedule> findActiveSchedules(@Param("now") LocalDateTime now);
}