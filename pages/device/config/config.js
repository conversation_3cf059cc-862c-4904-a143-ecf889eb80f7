Page({
  data: {
    activeTab: 'status',
    
    // 设备状态概览
    deviceStatus: {
      online: true,
      statusText: '在线运行',
      temperature: 23.5,
      humidity: 65,
      waterLevel: 75,
      fertilizerLevel: 60,
      pressure: 2.3,
      flowRate: 85,
      totalRunTime: '156小时32分钟',
      lastMaintenance: '2024-01-15',
      nextMaintenance: '2024-04-15'
    },
    
    // 基础配置
    basicConfig: {
      deviceName: '水肥一体机-01',
      location: '1号大棚东侧',
      groupIndex: 0,
      maxPressure: '3.0',
      maxFlowRate: '100',
      safeMode: true,
      autoStart: false,
      workMode: 0, // 0:手动 1:自动 2:定时
      alertEnabled: true
    },
    
    groupOptions: ['温室大棚', '露天农场', '果园', '蔬菜基地'],
    
    
    // 网络配置
    networkConfig: {
      ssid: 'SmartFarm_WiFi',
      password: '',
      wifiStatus: 'connected',
      wifiStatusText: '已连接',
      ipAddress: '*************',
      subnetMask: '*************',
      gateway: '***********',
      dns: '*******',
      serverUrl: 'https://api.smartfarm.com',
      deviceId: 'SF20240001',
      reportInterval: '30',
      cloudSync: true
    },
    
    // 传感器配置
    // 参数配置
    paramConfig: {
      // 流量参数
      ratedFlowRate: '80',
      minFlowRate: '10', 
      flowPrecisionIndex: 1,
      
      // 压力参数
      workingPressure: '2.5',
      pressureLimit: '3.5',
      pressureAlarm: '1.0',
      
      // 施肥参数
      defaultEC: '1.8',
      defaultPH: '6.5',
      mixRatioIndex: 1,
      autoMix: true,
      
      // 安全保护
      timeoutProtection: '120',
      lowWaterProtection: true,
      overPressureProtection: true,
      dryRunProtection: true
    },
    
    workModeOptions: ['手动模式', '自动模式', '定时模式'],
    flowPrecisionOptions: ['粗调(10L/h)', '中精度(5L/h)', '高精度(1L/h)'],
    mixRatioOptions: ['1:100', '1:200', '1:300', '1:500', '自定义'],
    
    // 弹窗相关
    showPicker: false,
    pickerTitle: '',
    pickerValue: [],
    pickerOptions: [],
    pickerType: '',
  },

  onLoad() {
    this.loadConfig();
    this.startStatusUpdate();
  },

  onUnload() {
    if (this.statusTimer) {
      clearInterval(this.statusTimer);
    }
  },

  loadConfig() {
    // 模拟加载配置数据
    console.log('加载设备配置');
  },

  // 启动状态更新定时器
  startStatusUpdate() {
    this.statusTimer = setInterval(() => {
      this.updateDeviceStatus();
    }, 5000);
  },

  // 更新设备状态
  updateDeviceStatus() {
    // 模拟实时数据更新
    const status = this.data.deviceStatus;
    const currentTemp = parseFloat(status.temperature) || 23.5;
    const currentHumidity = parseInt(status.humidity) || 65;
    const currentPressure = parseFloat(status.pressure) || 2.3;
    const currentFlowRate = parseInt(status.flowRate) || 85;
    
    // 计算新的数值并确保范围合理
    const newTemp = Math.max(15, Math.min(35, currentTemp + (Math.random() - 0.5) * 2));
    const newHumidity = Math.max(30, Math.min(100, currentHumidity + (Math.random() - 0.5) * 5));
    const newPressure = Math.max(1.5, Math.min(4.0, currentPressure + (Math.random() - 0.5) * 0.2));
    const newFlowRate = Math.max(60, Math.min(100, currentFlowRate + (Math.random() - 0.5) * 10));
    
    this.setData({
      'deviceStatus.temperature': parseFloat(newTemp.toFixed(1)),
      'deviceStatus.humidity': Math.round(newHumidity),
      'deviceStatus.pressure': parseFloat(newPressure.toFixed(1)),
      'deviceStatus.flowRate': Math.round(newFlowRate)
    });
  },

  // 获取状态颜色
  getStatusColor(value, min, max) {
    if (value >= min && value <= max) return 'success';
    return value < min ? 'warning' : 'error';
  },

  // 标签切换
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  // 刷新设备状态
  refreshStatus() {
    wx.showLoading({
      title: '刷新中...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      this.updateDeviceStatus();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    }, 1000);
  },

  // 查看运行报告
  viewRunningReport() {
    wx.navigateTo({
      url: '/pages/device/logs/logs'
    });
  },


  // 显示选择器
  showGroupPicker() {
    this.setData({
      showPicker: true,
      pickerTitle: '请选择设备分组',
      pickerValue: [this.data.basicConfig.groupIndex],
      pickerOptions: [this.data.groupOptions],
      pickerType: 'group'
    });
  },

  showWorkModePicker() {
    this.setData({
      showPicker: true,
      pickerTitle: '请选择工作模式',
      pickerValue: [this.data.basicConfig.workMode],
      pickerOptions: [this.data.workModeOptions],
      pickerType: 'workMode'
    });
  },

  showFlowPrecisionPicker() {
    this.setData({
      showPicker: true,
      pickerTitle: '请选择流量精度',
      pickerValue: [this.data.paramConfig.flowPrecisionIndex],
      pickerOptions: [this.data.flowPrecisionOptions],
      pickerType: 'flowPrecision'
    });
  },

  showMixRatioPicker() {
    this.setData({
      showPicker: true,
      pickerTitle: '请选择混合比例',
      pickerValue: [this.data.paramConfig.mixRatioIndex],
      pickerOptions: [this.data.mixRatioOptions],
      pickerType: 'mixRatio'
    });
  },

  showCropPicker() {
    this.setData({
      showPicker: true,
      pickerTitle: '请选择种植作物',
      pickerValue: [this.data.editingZone.cropIndex],
      pickerOptions: [this.data.cropOptions],
      pickerType: 'crop'
    });
  },

  showIrrigationPicker() {
    this.setData({
      showPicker: true,
      pickerTitle: '请选择灌渉方式',
      pickerValue: [this.data.editingZone.irrigationIndex],
      pickerOptions: [this.data.irrigationOptions],
      pickerType: 'irrigation'
    });
  },

  // 选择器事件
  onPickerChange(e) {
    this.setData({
      pickerValue: e.detail.value
    });
  },

  onPickerConfirm(e) {
    const value = e.detail.value[0];
    const type = this.data.pickerType;
    
    switch(type) {
      case 'group':
        this.setData({
          'basicConfig.groupIndex': value
        });
        break;
      case 'workMode':
        this.setData({
          'basicConfig.workMode': value
        });
        break;
      case 'flowPrecision':
        this.setData({
          'paramConfig.flowPrecisionIndex': value
        });
        break;
      case 'mixRatio':
        this.setData({
          'paramConfig.mixRatioIndex': value
        });
        break;
      case 'crop':
        this.setData({
          'editingZone.cropIndex': value
        });
        break;
      case 'irrigation':
        this.setData({
          'editingZone.irrigationIndex': value
        });
        break;
    }
    
    this.setData({
      showPicker: false
    });
  },

  onPickerCancel() {
    this.setData({
      showPicker: false
    });
  },

  // 基础配置相关方法
  onDeviceNameInput(e) {
    this.setData({
      'basicConfig.deviceName': e.detail.value
    });
  },

  onLocationInput(e) {
    this.setData({
      'basicConfig.location': e.detail.value
    });
  },


  onMaxPressureInput(e) {
    const value = e.detail.value;
    if (this.validateInput(value, 0.5, 5.0, '最大压力')) {
      this.setData({
        'basicConfig.maxPressure': value
      });
    }
  },

  onMaxFlowRateInput(e) {
    const value = e.detail.value;
    if (this.validateInput(value, 10, 200, '最大流量')) {
      this.setData({
        'basicConfig.maxFlowRate': value
      });
    }
  },

  onSafeModeChange(e) {
    this.setData({
      'basicConfig.safeMode': e.detail.value
    });
  },

  onAutoStartChange(e) {
    this.setData({
      'basicConfig.autoStart': e.detail.value
    });
  },

  onWorkModeChange(e) {
    this.setData({
      'basicConfig.workMode': e.detail.value
    });
  },

  onAlertEnabledChange(e) {
    this.setData({
      'basicConfig.alertEnabled': e.detail.value
    });
  },

  // 验证输入参数
  validateInput(value, min, max, field) {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      wx.showToast({
        title: '请输入有效数字',
        icon: 'none'
      });
      return false;
    }
    if (numValue < min || numValue > max) {
      wx.showToast({
        title: `${field}范围: ${min}-${max}`,
        icon: 'none'
      });
      return false;
    }
    return true;
  },



  // 网络配置相关方法
  onSSIDInput(e) {
    this.setData({
      'networkConfig.ssid': e.detail.value
    });
  },

  onPasswordInput(e) {
    this.setData({
      'networkConfig.password': e.detail.value
    });
  },

  onIPInput(e) {
    this.setData({
      'networkConfig.ipAddress': e.detail.value
    });
  },

  onSubnetInput(e) {
    this.setData({
      'networkConfig.subnetMask': e.detail.value
    });
  },

  onGatewayInput(e) {
    this.setData({
      'networkConfig.gateway': e.detail.value
    });
  },

  onDNSInput(e) {
    this.setData({
      'networkConfig.dns': e.detail.value
    });
  },

  onServerUrlInput(e) {
    this.setData({
      'networkConfig.serverUrl': e.detail.value
    });
  },

  onDeviceIdInput(e) {
    this.setData({
      'networkConfig.deviceId': e.detail.value
    });
  },

  onReportIntervalInput(e) {
    this.setData({
      'networkConfig.reportInterval': e.detail.value
    });
  },

  onCloudSyncChange(e) {
    this.setData({
      'networkConfig.cloudSync': e.detail.value
    });
  },

  testWiFiConnection() {
    wx.showLoading({
      title: '测试连接中...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: 'WiFi连接正常',
        icon: 'success'
      });
      
      this.setData({
        'networkConfig.wifiStatus': 'connected',
        'networkConfig.wifiStatusText': '已连接'
      });
    }, 2000);
  },

  // 参数配置相关方法
  onRatedFlowRateInput(e) {
    const value = e.detail.value;
    if (this.validateInput(value, 10, 200, '额定流量')) {
      this.setData({
        'paramConfig.ratedFlowRate': value
      });
    }
  },

  onMinFlowRateInput(e) {
    const value = e.detail.value;
    if (this.validateInput(value, 5, 50, '最小流量')) {
      this.setData({
        'paramConfig.minFlowRate': value
      });
    }
  },

  onWorkingPressureInput(e) {
    const value = e.detail.value;
    if (this.validateInput(value, 1.0, 4.0, '工作压力')) {
      this.setData({
        'paramConfig.workingPressure': value
      });
    }
  },

  onPressureLimitInput(e) {
    const value = e.detail.value;
    if (this.validateInput(value, 1.5, 5.0, '压力上限')) {
      this.setData({
        'paramConfig.pressureLimit': value
      });
    }
  },

  onPressureAlarmInput(e) {
    const value = e.detail.value;
    if (this.validateInput(value, 0.5, 2.0, '压力下限')) {
      this.setData({
        'paramConfig.pressureAlarm': value
      });
    }
  },

  onDefaultECInput(e) {
    const value = e.detail.value;
    if (this.validateInput(value, 0.5, 3.0, '默认EC值')) {
      this.setData({
        'paramConfig.defaultEC': value
      });
    }
  },

  onDefaultPHInput(e) {
    const value = e.detail.value;
    if (this.validateInput(value, 5.5, 7.5, '默认pH值')) {
      this.setData({
        'paramConfig.defaultPH': value
      });
    }
  },

  onTimeoutProtectionInput(e) {
    const value = e.detail.value;
    if (this.validateInput(value, 30, 300, '超时保护时间')) {
      this.setData({
        'paramConfig.timeoutProtection': value
      });
    }
  },

  onAutoMixChange(e) {
    this.setData({
      'paramConfig.autoMix': e.detail.value
    });
  },

  onLowWaterProtectionChange(e) {
    this.setData({
      'paramConfig.lowWaterProtection': e.detail.value
    });
  },

  onOverPressureProtectionChange(e) {
    this.setData({
      'paramConfig.overPressureProtection': e.detail.value
    });
  },

  onDryRunProtectionChange(e) {
    this.setData({
      'paramConfig.dryRunProtection': e.detail.value
    });
  },


  // 保存和重置配置
  saveConfig() {
    wx.showLoading({
      title: '保存配置中...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '配置保存成功',
        icon: 'success'
      });
    }, 1500);
  },

  resetConfig() {
    wx.showModal({
      title: '恢复默认配置',
      content: '确定要恢复所有配置到默认状态吗？此操作不可撤销。',
      success: (res) => {
        if (res.confirm) {
          // 重置所有配置到默认值
          this.setData({
            activeTab: 'status',
            'basicConfig.safeMode': true,
            'basicConfig.autoStart': false,
            'networkConfig.cloudSync': true,
            'paramConfig.autoMix': true,
            'paramConfig.lowWaterProtection': true,
            'paramConfig.overPressureProtection': true,
            'paramConfig.dryRunProtection': true
          });
          
          wx.showToast({
            title: '已恢复默认配置',
            icon: 'success'
          });
        }
      }
    });
  }
})