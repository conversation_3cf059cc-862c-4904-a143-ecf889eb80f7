package com.agriculture.iot.service.impl;

import com.agriculture.iot.entity.SensorData;
import com.agriculture.iot.entity.SensorType;
import com.agriculture.iot.mapper.SensorDataMapper;
import com.agriculture.iot.mapper.SensorTypeMapper;
import com.agriculture.iot.service.SensorService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 传感器数据服务实现类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SensorServiceImpl extends ServiceImpl<SensorDataMapper, SensorData> implements SensorService {

    private final SensorDataMapper sensorDataMapper;
    private final SensorTypeMapper sensorTypeMapper;

    @Override
    public IPage<SensorData> getSensorDataList(Page<SensorData> page, Long deviceId, Long plotId, 
                                             Long sensorTypeId, LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<SensorData> wrapper = new QueryWrapper<>();
        
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        if (sensorTypeId != null) {
            wrapper.eq("sensor_type_id", sensorTypeId);
        }
        if (startTime != null) {
            wrapper.ge("timestamp", startTime);
        }
        if (endTime != null) {
            wrapper.le("timestamp", endTime);
        }
        
        wrapper.orderByDesc("timestamp");
        return this.page(page, wrapper);
    }

    @Override
    public List<SensorData> getLatestSensorData(Long deviceId, Long plotId) {
        QueryWrapper<SensorData> wrapper = new QueryWrapper<>();
        
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        
        wrapper.orderByDesc("timestamp");
        wrapper.last("LIMIT 10");
        return this.list(wrapper);
    }

    @Override
    public List<SensorData> getHistorySensorData(Long deviceId, String sensorType, 
                                               LocalDateTime startTime, LocalDateTime endTime, Integer interval) {
        QueryWrapper<SensorData> wrapper = new QueryWrapper<>();
        
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (sensorType != null && !sensorType.trim().isEmpty()) {
            wrapper.eq("sensor_type", sensorType);
        }
        if (startTime != null) {
            wrapper.ge("timestamp", startTime);
        }
        if (endTime != null) {
            wrapper.le("timestamp", endTime);
        }
        
        wrapper.orderByDesc("timestamp");
        
        // 如果指定了时间间隔，可以在这里实现数据采样逻辑
        return this.list(wrapper);
    }

    @Override
    public SensorData.Statistics getSensorDataStatistics(Long deviceId, Long plotId, 
                                                        String sensorType, Integer days) {
        // 获取指定时间范围内的数据
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        LocalDateTime endTime = LocalDateTime.now();
        
        QueryWrapper<SensorData> wrapper = new QueryWrapper<>();
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        if (sensorType != null && !sensorType.trim().isEmpty()) {
            wrapper.eq("sensor_type", sensorType);
        }
        wrapper.ge("timestamp", startTime);
        wrapper.le("timestamp", endTime);
        
        List<SensorData> dataList = this.list(wrapper);
        
        // 计算统计数据
        SensorData.Statistics statistics = new SensorData.Statistics();
        if (!dataList.isEmpty()) {
            BigDecimal sum = BigDecimal.ZERO;
            BigDecimal min = dataList.get(0).getValue();
            BigDecimal max = dataList.get(0).getValue();
            
            for (SensorData data : dataList) {
                BigDecimal value = data.getValue();
                sum = sum.add(value);
                if (value.compareTo(min) < 0) {
                    min = value;
                }
                if (value.compareTo(max) > 0) {
                    max = value;
                }
            }
            
            statistics.setCount(dataList.size());
            statistics.setSum(sum);
            statistics.setAvg(sum.divide(BigDecimal.valueOf(dataList.size()), 2, BigDecimal.ROUND_HALF_UP));
            statistics.setMin(min);
            statistics.setMax(max);
        }
        
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SensorData uploadSensorData(SensorData sensorData) {
        // 设置上传时间
        sensorData.setTimestamp(LocalDateTime.now());
        
        boolean saved = this.save(sensorData);
        return saved ? sensorData : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUploadSensorData(List<SensorData> sensorDataList) {
        // 批量设置上传时间
        LocalDateTime now = LocalDateTime.now();
        for (SensorData sensorData : sensorDataList) {
            sensorData.setTimestamp(now);
        }
        
        return this.saveBatch(sensorDataList);
    }

    @Override
    public Map<String, Object> getDeviceSensorData(Long deviceId, Integer hours) {
        Map<String, Object> result = new HashMap<>();
        
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        LocalDateTime endTime = LocalDateTime.now();
        
        QueryWrapper<SensorData> wrapper = new QueryWrapper<>();
        wrapper.eq("device_id", deviceId);
        wrapper.ge("timestamp", startTime);
        wrapper.le("timestamp", endTime);
        wrapper.orderByDesc("timestamp");
        
        List<SensorData> dataList = this.list(wrapper);
        
        // 按传感器类型分组
        Map<String, List<SensorData>> groupedData = new HashMap<>();
        for (SensorData data : dataList) {
            String sensorType = data.getSensorType();
            if (!groupedData.containsKey(sensorType)) {
                groupedData.put(sensorType, new ArrayList<>());
            }
            groupedData.get(sensorType).add(data);
        }
        
        result.put("deviceId", deviceId);
        result.put("timeRange", hours + "小时");
        result.put("totalCount", dataList.size());
        result.put("sensorData", groupedData);
        result.put("updateTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public Map<String, Object> getPlotSensorData(Long plotId, Integer hours) {
        Map<String, Object> result = new HashMap<>();
        
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        LocalDateTime endTime = LocalDateTime.now();
        
        QueryWrapper<SensorData> wrapper = new QueryWrapper<>();
        wrapper.eq("plot_id", plotId);
        wrapper.ge("timestamp", startTime);
        wrapper.le("timestamp", endTime);
        wrapper.orderByDesc("timestamp");
        
        List<SensorData> dataList = this.list(wrapper);
        
        // 按设备ID分组
        Map<Long, List<SensorData>> groupedData = new HashMap<>();
        for (SensorData data : dataList) {
            Long deviceId = data.getDeviceId();
            if (!groupedData.containsKey(deviceId)) {
                groupedData.put(deviceId, new ArrayList<>());
            }
            groupedData.get(deviceId).add(data);
        }
        
        result.put("plotId", plotId);
        result.put("timeRange", hours + "小时");
        result.put("totalCount", dataList.size());
        result.put("deviceData", groupedData);
        result.put("updateTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public List<SensorType> getSensorTypes() {
        return sensorTypeMapper.selectList(null);
    }

    @Override
    public SensorData.TrendData getSensorDataTrend(Long deviceId, String sensorType, 
                                                  LocalDateTime startTime, LocalDateTime endTime, String trendType) {
        QueryWrapper<SensorData> wrapper = new QueryWrapper<>();
        wrapper.eq("device_id", deviceId);
        wrapper.eq("sensor_type", sensorType);
        wrapper.ge("timestamp", startTime);
        wrapper.le("timestamp", endTime);
        wrapper.orderByAsc("timestamp");
        
        List<SensorData> dataList = this.list(wrapper);
        
        SensorData.TrendData trendData = new SensorData.TrendData();
        trendData.setDeviceId(deviceId);
        trendData.setSensorType(sensorType);
        trendData.setTrendType(trendType);
        trendData.setStartTime(startTime);
        trendData.setEndTime(endTime);
        
        if (!dataList.isEmpty()) {
            // 计算趋势数据
            List<SensorData.TrendPoint> trendPoints = new ArrayList<>();
            for (SensorData data : dataList) {
                SensorData.TrendPoint point = new SensorData.TrendPoint();
                point.setTimestamp(data.getTimestamp());
                point.setValue(data.getValue());
                trendPoints.add(point);
            }
            trendData.setTrendPoints(trendPoints);
            
            // 计算趋势指标
            calculateTrendIndicators(trendData, dataList);
        }
        
        return trendData;
    }

    @Override
    public SensorData.CompareData compareSensorData(List<Long> deviceIds, String sensorType, 
                                                  LocalDateTime startTime, LocalDateTime endTime) {
        SensorData.CompareData compareData = new SensorData.CompareData();
        compareData.setDeviceIds(deviceIds);
        compareData.setSensorType(sensorType);
        compareData.setStartTime(startTime);
        compareData.setEndTime(endTime);
        
        Map<Long, List<SensorData>> deviceDataMap = new HashMap<>();
        
        // 获取每个设备的数据
        for (Long deviceId : deviceIds) {
            QueryWrapper<SensorData> wrapper = new QueryWrapper<>();
            wrapper.eq("device_id", deviceId);
            wrapper.eq("sensor_type", sensorType);
            wrapper.ge("timestamp", startTime);
            wrapper.le("timestamp", endTime);
            wrapper.orderByAsc("timestamp");
            
            List<SensorData> dataList = this.list(wrapper);
            deviceDataMap.put(deviceId, dataList);
        }
        
        compareData.setDeviceData(deviceDataMap);
        
        // 计算比较指标
        calculateCompareIndicators(compareData, deviceDataMap);
        
        return compareData;
    }

    @Override
    public SensorData.ReportData getSensorDataReport(Long plotId, List<Long> deviceIds, String reportType, 
                                                   LocalDateTime startTime, LocalDateTime endTime) {
        SensorData.ReportData reportData = new SensorData.ReportData();
        reportData.setPlotId(plotId);
        reportData.setDeviceIds(deviceIds);
        reportData.setReportType(reportType);
        reportData.setStartTime(startTime);
        reportData.setEndTime(endTime);
        
        // 获取报表数据
        QueryWrapper<SensorData> wrapper = new QueryWrapper<>();
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        if (deviceIds != null && !deviceIds.isEmpty()) {
            wrapper.in("device_id", deviceIds);
        }
        wrapper.ge("timestamp", startTime);
        wrapper.le("timestamp", endTime);
        wrapper.orderByAsc("timestamp");
        
        List<SensorData> dataList = this.list(wrapper);
        reportData.setDataList(dataList);
        
        // 生成报表摘要
        generateReportSummary(reportData, dataList);
        
        return reportData;
    }

    @Override
    public String exportSensorData(Long deviceId, Long plotId, LocalDateTime startTime, 
                                 LocalDateTime endTime, String format) {
        // 获取要导出的数据
        QueryWrapper<SensorData> wrapper = new QueryWrapper<>();
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        wrapper.ge("timestamp", startTime);
        wrapper.le("timestamp", endTime);
        wrapper.orderByAsc("timestamp");
        
        List<SensorData> dataList = this.list(wrapper);
        
        // 生成导出文件
        String fileName = generateExportFileName(deviceId, plotId, startTime, endTime, format);
        
        // TODO: 实现实际的文件导出逻辑
        // 这里应该根据format生成相应格式的文件（Excel、CSV等）
        
        return "/exports/" + fileName;
    }

    @Override
    public List<SensorData.AlertInfo> getSensorAlerts(Long deviceId, Long plotId, String alertLevel) {
        List<SensorData.AlertInfo> alerts = new ArrayList<>();
        
        // 获取最新的传感器数据
        QueryWrapper<SensorData> wrapper = new QueryWrapper<>();
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        wrapper.orderByDesc("timestamp");
        wrapper.last("LIMIT 100");
        
        List<SensorData> dataList = this.list(wrapper);
        
        // 检查预警条件
        for (SensorData data : dataList) {
            SensorData.AlertInfo alert = checkAlertCondition(data, alertLevel);
            if (alert != null) {
                alerts.add(alert);
            }
        }
        
        return alerts;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setSensorThreshold(SensorData.ThresholdConfig thresholdConfig) {
        // TODO: 实现阈值配置的保存
        // 这里应该保存到阈值配置表
        return true;
    }

    @Override
    public List<SensorData.ThresholdConfig> getSensorThresholds(Long deviceId, String sensorType) {
        // TODO: 实现阈值配置的查询
        // 这里应该从阈值配置表查询
        return new ArrayList<>();
    }

    /**
     * 计算趋势指标
     */
    private void calculateTrendIndicators(SensorData.TrendData trendData, List<SensorData> dataList) {
        if (dataList.size() < 2) return;
        
        // 计算趋势方向
        BigDecimal firstValue = dataList.get(0).getValue();
        BigDecimal lastValue = dataList.get(dataList.size() - 1).getValue();
        BigDecimal change = lastValue.subtract(firstValue);
        
        if (change.compareTo(BigDecimal.ZERO) > 0) {
            trendData.setTrendDirection("上升");
        } else if (change.compareTo(BigDecimal.ZERO) < 0) {
            trendData.setTrendDirection("下降");
        } else {
            trendData.setTrendDirection("稳定");
        }
        
        trendData.setTrendValue(change);
    }

    /**
     * 计算比较指标
     */
    private void calculateCompareIndicators(SensorData.CompareData compareData, 
                                          Map<Long, List<SensorData>> deviceDataMap) {
        Map<Long, SensorData.Statistics> deviceStats = new HashMap<>();
        
        for (Map.Entry<Long, List<SensorData>> entry : deviceDataMap.entrySet()) {
            Long deviceId = entry.getKey();
            List<SensorData> dataList = entry.getValue();
            
            if (!dataList.isEmpty()) {
                SensorData.Statistics stats = calculateStatistics(dataList);
                deviceStats.put(deviceId, stats);
            }
        }
        
        compareData.setDeviceStatistics(deviceStats);
    }

    /**
     * 计算统计数据
     */
    private SensorData.Statistics calculateStatistics(List<SensorData> dataList) {
        SensorData.Statistics statistics = new SensorData.Statistics();
        
        if (dataList.isEmpty()) return statistics;
        
        BigDecimal sum = BigDecimal.ZERO;
        BigDecimal min = dataList.get(0).getValue();
        BigDecimal max = dataList.get(0).getValue();
        
        for (SensorData data : dataList) {
            BigDecimal value = data.getValue();
            sum = sum.add(value);
            if (value.compareTo(min) < 0) {
                min = value;
            }
            if (value.compareTo(max) > 0) {
                max = value;
            }
        }
        
        statistics.setCount(dataList.size());
        statistics.setSum(sum);
        statistics.setAvg(sum.divide(BigDecimal.valueOf(dataList.size()), 2, BigDecimal.ROUND_HALF_UP));
        statistics.setMin(min);
        statistics.setMax(max);
        
        return statistics;
    }

    /**
     * 生成报表摘要
     */
    private void generateReportSummary(SensorData.ReportData reportData, List<SensorData> dataList) {
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalRecords", dataList.size());
        summary.put("generateTime", LocalDateTime.now());
        
        // 按传感器类型分组统计
        Map<String, Integer> typeCounts = new HashMap<>();
        for (SensorData data : dataList) {
            String sensorType = data.getSensorType();
            typeCounts.put(sensorType, typeCounts.getOrDefault(sensorType, 0) + 1);
        }
        summary.put("typeCounts", typeCounts);
        
        reportData.setSummary(summary);
    }

    /**
     * 生成导出文件名
     */
    private String generateExportFileName(Long deviceId, Long plotId, LocalDateTime startTime, 
                                        LocalDateTime endTime, String format) {
        StringBuilder fileName = new StringBuilder("sensor_data_");
        if (deviceId != null) {
            fileName.append("device_").append(deviceId).append("_");
        }
        if (plotId != null) {
            fileName.append("plot_").append(plotId).append("_");
        }
        fileName.append(System.currentTimeMillis()).append(".").append(format.toLowerCase());
        return fileName.toString();
    }

    /**
     * 检查预警条件
     */
    private SensorData.AlertInfo checkAlertCondition(SensorData data, String alertLevel) {
        // TODO: 实现实际的预警逻辑
        // 这里应该根据阈值配置检查是否需要预警
        return null;
    }
}