package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 农场实体类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("farms")
public class Farm {

    /**
     * 农场ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 农场名称
     */
    @TableField("name")
    private String name;

    /**
     * 农场描述
     */
    @TableField("description")
    private String description;

    /**
     * 农场位置
     */
    @TableField("location")
    private String location;

    /**
     * 纬度
     */
    @TableField("latitude")
    private BigDecimal latitude;

    /**
     * 经度
     */
    @TableField("longitude")
    private BigDecimal longitude;

    /**
     * 农场面积(亩)
     */
    @TableField("area")
    private BigDecimal area;

    /**
     * 农场主ID
     */
    @TableField("owner_id")
    private Long ownerId;

    /**
     * 状态: 0-禁用, 1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 关联的地块列表 (一对多)
     */
    @TableField(exist = false)
    private List<Plot> plots;

    /**
     * 农场统计信息
     */
    @TableField(exist = false)
    private FarmStats farmStats;

    /**
     * 农场统计信息内部类
     */
    @Data
    public static class FarmStats {
        /**
         * 地块数量
         */
        private Integer plotCount;

        /**
         * 地块总面积
         */
        private BigDecimal totalPlotArea;

        /**
         * 设备总数
         */
        private Integer deviceCount;

        /**
         * 在线设备数
         */
        private Integer onlineDeviceCount;

        /**
         * 灌溉设备数
         */
        private Integer irrigationDeviceCount;

        /**
         * 施肥设备数
         */
        private Integer fertilizerDeviceCount;

        /**
         * 传感器设备数
         */
        private Integer sensorDeviceCount;

        /**
         * 设备在线率
         */
        private BigDecimal onlineRate;
    }
}