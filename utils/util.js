const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return ''
  
  const d = new Date(date)
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  const hour = d.getHours()
  const minute = d.getMinutes()
  const second = d.getSeconds()
  
  const replacements = {
    'YYYY': year,
    'MM': formatNumber(month),
    'DD': formatNumber(day),
    'HH': formatNumber(hour),
    'mm': formatNumber(minute),
    'ss': formatNumber(second)
  }
  
  let result = format
  Object.keys(replacements).forEach(key => {
    result = result.replace(key, replacements[key])
  })
  
  return result
}

const getRelativeTime = (date) => {
  if (!date) return ''
  
  const now = new Date()
  const target = new Date(date)
  const diff = now - target
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else {
    return `${Math.floor(diff / day)}天前`
  }
}

const debounce = (func, delay) => {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

const throttle = (func, delay) => {
  let lastExecTime = 0
  return function (...args) {
    const currentTime = Date.now()
    if (currentTime - lastExecTime >= delay) {
      func.apply(this, args)
      lastExecTime = currentTime
    }
  }
}

const validatePhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const showLoading = (title = '加载中...') => {
  wx.showLoading({
    title: title,
    mask: true
  })
}

const hideLoading = () => {
  wx.hideLoading()
}

const showToast = (title, icon = 'none', duration = 2000) => {
  wx.showToast({
    title: title,
    icon: icon,
    duration: duration
  })
}

const showModal = (title, content) => {
  return new Promise((resolve) => {
    wx.showModal({
      title: title,
      content: content,
      success: (res) => {
        resolve(res.confirm)
      }
    })
  })
}

const navigateTo = (url, params = {}) => {
  let queryString = ''
  if (Object.keys(params).length > 0) {
    queryString = '?' + Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&')
  }
  
  wx.navigateTo({
    url: url + queryString
  })
}

const getImageUrl = (path) => {
  if (!path) return ''
  if (path.startsWith('http')) return path
  return `https://your-cdn-domain.com${path}`
}

const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  
  const clonedObj = {}
  Object.keys(obj).forEach(key => {
    clonedObj[key] = deepClone(obj[key])
  })
  
  return clonedObj
}

const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

const isIPhoneX = () => {
  const systemInfo = wx.getSystemInfoSync()
  const model = systemInfo.model
  return model.includes('iPhone X') || 
         model.includes('iPhone 11') || 
         model.includes('iPhone 12') || 
         model.includes('iPhone 13') || 
         model.includes('iPhone 14')
}

const getStatusBarHeight = () => {
  const systemInfo = wx.getSystemInfoSync()
  return systemInfo.statusBarHeight || 20
}

const getCustomHeaderHeight = () => {
  const systemInfo = wx.getSystemInfoSync()
  const isIOS = systemInfo.system.includes('iOS')
  const statusBarHeight = systemInfo.statusBarHeight || 20
  const titleBarHeight = isIOS ? 44 : 48
  return statusBarHeight + titleBarHeight
}

const saveToLocal = (key, data) => {
  try {
    wx.setStorageSync(key, data)
    return true
  } catch (error) {
    console.error('保存数据失败:', error)
    return false
  }
}

const getFromLocal = (key, defaultValue = null) => {
  try {
    const data = wx.getStorageSync(key)
    return data || defaultValue
  } catch (error) {
    console.error('获取数据失败:', error)
    return defaultValue
  }
}

const removeFromLocal = (key) => {
  try {
    wx.removeStorageSync(key)
    return true
  } catch (error) {
    console.error('删除数据失败:', error)
    return false
  }
}

module.exports = {
  formatTime,
  formatNumber,
  formatDate,
  getRelativeTime,
  debounce,
  throttle,
  validatePhone,
  validateEmail,
  showLoading,
  hideLoading,
  showToast,
  showModal,
  navigateTo,
  getImageUrl,
  deepClone,
  generateUUID,
  isIPhoneX,
  getStatusBarHeight,
  getCustomHeaderHeight,
  saveToLocal,
  getFromLocal,
  removeFromLocal
}