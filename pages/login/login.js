// 智慧农业登录页面 - 原生版本
const api = require('../../utils/api.js');
import lottie from 'lottie-miniprogram';
import logoAnimationData from '../../assets/animation/logo.js';

Page({
  data: {
    // 登录状态
    wxLoginLoading: false,
    phoneLoginLoading: false,
    sendingCode: false,
    
    // 登录方式
    showPhoneLogin: false,
    
    // 手机号登录表单
    phone: '',
    code: '',
    
    // 验证码相关
    canSendCode: false,
    codeButtonText: '获取验证码',
    codeCountDown: 0,
    
    // 用户协议
    agreedToTerms: false,
    showAgreementDialog: false,
    agreementTitle: '',
    agreementContent: '',
    
    // 应用信息
    version: '2.0.0',

    // 动画播放状态
    isLottiePlaying: false
  },

  onLoad(options) {
    // 检查登录状态
    this.checkLoginStatus();
    
    // 获取应用版本信息
    this.getVersionInfo();
    
    // 初始化Lottie动画
    this.initLottieLogo();
  },

  // 初始化登录页Lottie动画
  initLottieLogo() {
    wx.createSelectorQuery().select('#loginLottieCanvas').node(res => {
      const canvas = res.node;
      if (canvas) {
        const context = canvas.getContext('2d');
        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = 400 * dpr;
        canvas.height = 380 * dpr;
        context.scale(dpr, dpr);
        try {
          lottie.setup(canvas);
          lottie.loadAnimation({
            loop: true,
            autoplay: true,
            animationData: logoAnimationData,
            rendererSettings: { context }
          });
          this.setData({ isLottiePlaying: true });
        } catch (error) {
          console.error('Lottie动画初始化失败:', error);
        }
      }
    }).exec();
  },

  // 检查是否已登录
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.token) {
      // 已登录，跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  // 获取版本信息
  getVersionInfo() {
    const accountInfo = wx.getAccountInfoSync();
    this.setData({
      version: accountInfo.miniProgram.version || '2.0.0'
    });
  },

  // 微信授权登录
  onWxLogin() {
    this.setData({ wxLoginLoading: true });

    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功', res);
        
        // 获取登录凭证
        wx.login({
          success: (loginRes) => {
            if (loginRes.code) {
              this.doWxLogin(loginRes.code, res.userInfo);
            } else {
              this.showToast('获取登录凭证失败');
              this.setData({ wxLoginLoading: false });
            }
          },
          fail: () => {
            this.showToast('微信登录失败');
            this.setData({ wxLoginLoading: false });
          }
        });
      },
      fail: (err) => {
        console.log('用户拒绝授权', err);
        this.setData({ wxLoginLoading: false });
        this.showToast('需要授权才能登录');
      }
    });
  },

  // 执行微信登录
  doWxLogin(code, userInfo) {
    api.login({
      code: code,
      userInfo: userInfo
    }).then(res => {
      // 登录成功
      this.saveUserInfo(res.data);
      this.showToast('登录成功');
      
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }, 1500);
    }).catch(err => {
      this.showToast(err.message || '登录失败');
    }).finally(() => {
      this.setData({ wxLoginLoading: false });
    });
  },

  // 手机号输入
  onPhoneChange(e) {
    const phone = e.detail.value;
    this.setData({ 
      phone: phone,
      canSendCode: this.validatePhone(phone)
    });
  },

  // 验证码输入
  onCodeChange(e) {
    this.setData({ code: e.detail.value });
  },

  // 验证手机号格式
  validatePhone(phone) {
    const phoneReg = /^1[3-9]\d{9}$/;
    return phoneReg.test(phone);
  },

  // 发送验证码
  sendCode() {
    if (!this.validatePhone(this.data.phone)) {
      this.showToast('请输入正确的手机号');
      return;
    }

    this.setData({ sendingCode: true });

    api.sendVerificationCode({
      phone: this.data.phone
    }).then(res => {
      this.showToast('验证码已发送');
      this.startCountDown();
    }).catch(err => {
      this.showToast(err.message || '发送失败');
    }).finally(() => {
      this.setData({ sendingCode: false });
    });
  },

  // 开始倒计时
  startCountDown() {
    let countDown = 60;
    this.setData({ 
      canSendCode: false,
      codeCountDown: countDown,
      codeButtonText: `${countDown}秒后重发`
    });

    const timer = setInterval(() => {
      countDown--;
      if (countDown > 0) {
        this.setData({ 
          codeCountDown: countDown,
          codeButtonText: `${countDown}秒后重发`
        });
      } else {
        clearInterval(timer);
        this.setData({
          canSendCode: true,
          codeButtonText: '重新发送',
          codeCountDown: 0
        });
      }
    }, 1000);
  },

  // 手机号登录
  onPhoneLogin() {
    if (!this.validatePhone(this.data.phone)) {
      this.showToast('请输入正确的手机号');
      return;
    }

    if (!this.data.code || this.data.code.length !== 6) {
      this.showToast('请输入6位验证码');
      return;
    }

    this.setData({ phoneLoginLoading: true });

    api.phoneLogin({
      phone: this.data.phone,
      code: this.data.code
    }).then(res => {
      this.saveUserInfo(res.data);
      this.showToast('登录成功');
      
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }, 1500);
    }).catch(err => {
      this.showToast(err.message || '登录失败');
    }).finally(() => {
      this.setData({ phoneLoginLoading: false });
    });
  },

  // 切换登录方式
  toggleLoginMethod() {
    this.setData({
      showPhoneLogin: !this.data.showPhoneLogin,
      phone: '',
      code: '',
      canSendCode: false,
      codeButtonText: '获取验证码'
    });
  },

  // 用户协议勾选
  onAgreementChange(e) {
    this.setData({
      agreedToTerms: e.detail.checked
    });
  },

  // 显示用户协议
  showUserAgreement() {
    this.setData({
      showAgreementDialog: true,
      agreementTitle: '用户协议',
      agreementContent: '这里是用户协议的具体内容...'
    });
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    this.setData({
      showAgreementDialog: true,
      agreementTitle: '隐私政策',
      agreementContent: '这里是隐私政策的具体内容...'
    });
  },

  // 关闭协议弹窗
  closeAgreementDialog() {
    this.setData({
      showAgreementDialog: false
    });
  },

  // 保存用户信息
  saveUserInfo(userData) {
    const { user, token, refreshToken } = userData;
    
    // 保存用户信息和令牌
    wx.setStorageSync('userInfo', user);
    wx.setStorageSync('token', token);
    wx.setStorageSync('refreshToken', refreshToken);
    
    // 设置全局用户信息
    getApp().globalData.userInfo = user;
  },

  // 显示提示
  showToast(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  // 计算属性：是否可以手机号登录
  get canPhoneLogin() {
    return this.validatePhone(this.data.phone) && 
           this.data.code.length === 6 && 
           this.data.agreedToTerms;
  }
});