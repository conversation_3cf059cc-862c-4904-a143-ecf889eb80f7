/* 作物管理页面样式 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #E8F5E8 0%, #F0F8F0 100%);
  padding-bottom: 100rpx;
}

/* 标签栏样式 */
.tab-bar {
  display: flex;
  background: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  border-radius: 0 0 20rpx 20rpx;
  margin: 0 20rpx;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  color: white;
}

.tab-item.active .tab-text {
  color: white;
  font-weight: 600;
}

.tab-text {
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.tab-item:not(.active):hover {
  background: #f5f5f5;
}

/* 内容区域通用样式 */
.archive-content,
.records-content,
.harvest-content {
  padding: 20rpx;
  margin-top: 20rpx;
}

/* 搜索和筛选栏 */
.search-filter-bar {
  background: white;
  padding: 20rpx;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 15rpx 20rpx;
  margin-bottom: 20rpx;
}

.search-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  color: #666;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
}

.filter-buttons {
  display: flex;
  gap: 15rpx;
}

.filter-btn {
  padding: 15rpx 25rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  background: white;
  transition: all 0.3s ease;
}

.filter-btn.active {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

/* 作物列表样式 */
.crop-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.crop-card {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: flex-start;
  transition: all 0.3s ease;
}

.crop-card:hover {
  transform: translateY(-5rpx);
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.12);
}

.crop-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  background: linear-gradient(135deg, #81C784, #4CAF50);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 25rpx;
  flex-shrink: 0;
}

.crop-emoji {
  font-size: 48rpx;
}

.crop-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.crop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.crop-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.crop-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: white;
}

.status-growing {
  background: #4CAF50;
}

.status-harvesting {
  background: #FF9800;
}

.status-completed {
  background: #757575;
}

.status-failed {
  background: #F44336;
}

.crop-variety,
.crop-plot,
.crop-date {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.crop-stage {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-top: 10rpx;
}

.stage-label {
  font-size: 24rpx;
  color: #666;
}

.stage-badge {
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: white;
}

.stage-seeding { background: #8BC34A; }
.stage-germination { background: #4CAF50; }
.stage-growth { background: #2E7D32; }
.stage-flowering { background: #FF9800; }
.stage-fruiting { background: #F44336; }
.stage-harvest { background: #9C27B0; }
.stage-completed { background: #757575; }

.crop-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-left: 20rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 10rpx;
  font-size: 22rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  min-width: 100rpx;
}

.stage-btn {
  background: #2196F3;
  color: white;
}

.edit-btn {
  background: #FF9800;
  color: white;
}

.delete-btn {
  background: #F44336;
  color: white;
}

.action-btn:hover {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 记录列表样式 */
.record-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.record-card {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.record-card:hover {
  transform: translateY(-3rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.record-type {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.type-icon {
  font-size: 32rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.type-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.type-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.record-date {
  font-size: 24rpx;
  color: #999;
}

.record-content {
  margin-bottom: 15rpx;
}

.content-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

.record-notes {
  margin-top: 10rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #4CAF50;
}

.notes-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.record-meta {
  display: flex;
  gap: 20rpx;
  margin-bottom: 15rpx;
  font-size: 22rpx;
  color: #888;
}

.record-actions {
  display: flex;
  justify-content: flex-end;
}

/* 收获管理样式 */
.harvest-summary {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-around;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.summary-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #4CAF50;
}

.summary-label {
  font-size: 24rpx;
  color: #666;
}

.harvest-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.harvest-card {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.harvest-card:hover {
  transform: translateY(-3rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.harvest-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.harvest-crop {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.crop-icon {
  font-size: 32rpx;
}

.harvest-date {
  font-size: 24rpx;
  color: #999;
}

.harvest-details {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  width: 80rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.detail-value.income {
  color: #4CAF50;
  font-weight: 600;
}

.quality-badge {
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: white;
}

.quality-A {
  background: #4CAF50;
}

.quality-B {
  background: #FF9800;
}

.quality-C {
  background: #F44336;
}

.harvest-notes {
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
}

.harvest-actions {
  display: flex;
  justify-content: flex-end;
}

/* 添加按钮样式 */
.add-button {
  position: fixed;
  bottom: 30rpx;
  right: 30rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
}

.add-button:hover {
  transform: scale(1.1);
  box-shadow: 0 12rpx 30rpx rgba(76, 175, 80, 0.6);
}

.add-icon {
  font-size: 36rpx;
  color: white;
  font-weight: 300;
}

.add-text {
  font-size: 18rpx;
  color: white;
  margin-top: 4rpx;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5rpx);
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-50rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 25rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background: #f8f9fa;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 36rpx;
  color: #999;
  cursor: pointer;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f0f0f0;
  color: #666;
}

.modal-body {
  padding: 25rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 20rpx 25rpx;
  border-top: 1rpx solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 15rpx;
  background: #f8f9fa;
}

/* 表单样式 */
.form-group {
  margin-bottom: 25rpx;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #333;
  background: white;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #4CAF50;
  background: #f8fff8;
}

.form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #333;
  background: white;
  min-height: 120rpx;
  resize: vertical;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #4CAF50;
  background: #f8fff8;
}

.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: white;
  transition: all 0.3s ease;
}

.picker-content:hover {
  border-color: #4CAF50;
}

.picker-text {
  font-size: 26rpx;
  color: #333;
}

.picker-text:empty::before {
  content: attr(placeholder);
  color: #999;
}

/* 按钮样式 */
.btn {
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 120rpx;
  text-align: center;
}

.btn-primary {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #45a049, #1B5E20);
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.btn-secondary:hover {
  background: #e8e8e8;
  border-color: #ccc;
}

.btn-outline {
  background: white;
  color: #4CAF50;
  border: 2rpx solid #4CAF50;
}

.btn-outline:hover {
  background: #4CAF50;
  color: white;
}

.btn-small {
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  min-width: 80rpx;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .crop-card {
    flex-direction: column;
    align-items: center;
  }
  
  .crop-image {
    margin-right: 0;
    margin-bottom: 20rpx;
  }
  
  .crop-actions {
    flex-direction: row;
    margin-left: 0;
    margin-top: 20rpx;
  }
  
  .harvest-summary {
    flex-direction: column;
    gap: 20rpx;
  }
  
  .summary-item {
    flex-direction: row;
    justify-content: space-between;
  }
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #666;
}

.loading::before {
  content: '';
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 15rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  text-align: center;
  line-height: 1.4;
  opacity: 0.8;
}