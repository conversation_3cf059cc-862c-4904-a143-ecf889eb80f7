Page({
  data: {
    actionType: 'irrigation', // irrigation | fertilizer
    hasSelection: false,
    showZoneSelector: false,
    selectedDevices: [],
    selectedZones: [],
    
    // 可用设备列表
    availableDevices: [
      {
        id: 1,
        name: '水肥一体机-01',
        location: '1号大棚A区',
        status: 'online',
        statusText: '在线',
        waterLevel: 85,
        fertilizerLevel: 72,
        zone: 'A'
      },
      {
        id: 2,
        name: '水肥一体机-02', 
        location: '1号大棚B区',
        status: 'online',
        statusText: '在线',
        waterLevel: 76,
        fertilizerLevel: 68,
        zone: 'B'
      },
      {
        id: 3,
        name: '水肥一体机-03',
        location: '2号大棚A区', 
        status: 'online',
        statusText: '在线',
        waterLevel: 91,
        fertilizerLevel: 55,
        zone: 'C'
      }
    ],
    
    // 区域列表
    zoneList: [
      {
        id: 'A',
        name: '1号大棚A区',
        deviceCount: 1,
        allOnline: true,
        selected: false
      },
      {
        id: 'B', 
        name: '1号大棚B区',
        deviceCount: 1,
        allOnline: true,
        selected: false
      },
      {
        id: 'C',
        name: '2号大棚A区',
        deviceCount: 1, 
        allOnline: true,
        selected: false
      }
    ]
  },

  onLoad: function (options) {
    // 从参数获取操作类型
    const actionType = options.action || 'irrigation';
    this.setData({
      actionType: actionType
    });
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: actionType === 'irrigation' ? '选择灌溉设备' : '选择施肥设备'
    });
    
    // 加载可用设备
    this.loadAvailableDevices();
  },

  // 加载可用设备
  loadAvailableDevices: function () {
    const actionType = this.data.actionType;
    
    wx.request({
      url: 'https://api.your-domain.com/device/available',
      data: {
        type: actionType === 'irrigation' ? 'irrigation' : 'fertilizer',
        status: 'online'
      },
      success: (res) => {
        if (res.data.code === 200) {
          this.setData({
            availableDevices: res.data.data.devices || [],
            zoneList: res.data.data.zones || []
          });
        }
      },
      fail: (err) => {
        console.error('获取可用设备失败:', err);
        wx.showToast({
          title: '获取设备列表失败',
          icon: 'none'
        });
      }
    });
  },

  // 选择单个设备
  selectDevice: function (e) {
    const device = e.currentTarget.dataset.device;
    
    // 检查设备状态
    if (device.status !== 'online') {
      wx.showToast({
        title: '设备离线，无法操作',
        icon: 'none'
      });
      return;
    }
    
    // 检查水位和肥料余量
    if (this.data.actionType === 'irrigation' && device.waterLevel < 20) {
      wx.showModal({
        title: '水位不足',
        content: `${device.name}水位仅剩${device.waterLevel}%，是否继续？`,
        success: (res) => {
          if (res.confirm) {
            this.proceedWithDevice(device);
          }
        }
      });
      return;
    }
    
    if (this.data.actionType === 'fertilizer' && device.fertilizerLevel < 20) {
      wx.showModal({
        title: '肥料不足',
        content: `${device.name}肥料仅剩${device.fertilizerLevel}%，是否继续？`,
        success: (res) => {
          if (res.confirm) {
            this.proceedWithDevice(device);
          }
        }
      });
      return;
    }
    
    this.proceedWithDevice(device);
  },

  // 继续设备操作
  proceedWithDevice: function (device) {
    this.setData({
      selectedDevices: [device],
      hasSelection: true
    });
    
    // 直接跳转到控制页面
    this.navigateToControl();
  },

  // 全部设备操作
  selectAllDevices: function () {
    const onlineDevices = this.data.availableDevices.filter(device => device.status === 'online');
    
    if (onlineDevices.length === 0) {
      wx.showToast({
        title: '没有在线设备',
        icon: 'none'
      });
      return;
    }
    
    // 检查资源充足性
    const lowWaterDevices = onlineDevices.filter(device => device.waterLevel < 20);
    const lowFertilizerDevices = onlineDevices.filter(device => device.fertilizerLevel < 20);
    
    let warningMessage = '';
    if (this.data.actionType === 'irrigation' && lowWaterDevices.length > 0) {
      warningMessage = `${lowWaterDevices.length}台设备水位不足，是否继续？`;
    } else if (this.data.actionType === 'fertilizer' && lowFertilizerDevices.length > 0) {
      warningMessage = `${lowFertilizerDevices.length}台设备肥料不足，是否继续？`;
    }
    
    if (warningMessage) {
      wx.showModal({
        title: '资源警告',
        content: warningMessage,
        success: (res) => {
          if (res.confirm) {
            this.setData({
              selectedDevices: onlineDevices,
              hasSelection: true
            });
          }
        }
      });
    } else {
      this.setData({
        selectedDevices: onlineDevices,
        hasSelection: true
      });
    }
  },

  // 按区域选择
  selectByZone: function () {
    this.setData({
      showZoneSelector: true
    });
  },

  // 切换区域选择
  toggleZone: function (e) {
    const zone = e.currentTarget.dataset.zone;
    const zoneList = this.data.zoneList.map(item => {
      if (item.id === zone.id) {
        item.selected = !item.selected;
      }
      return item;
    });
    
    this.setData({
      zoneList: zoneList
    });
  },

  // 取消区域选择
  cancelZoneSelection: function () {
    this.setData({
      showZoneSelector: false,
      zoneList: this.data.zoneList.map(zone => ({
        ...zone,
        selected: false
      }))
    });
  },

  // 确认区域选择
  confirmZoneSelection: function () {
    const selectedZones = this.data.zoneList.filter(zone => zone.selected);
    
    if (selectedZones.length === 0) {
      wx.showToast({
        title: '请选择至少一个区域',
        icon: 'none'
      });
      return;
    }
    
    // 获取选中区域的设备
    const selectedZoneIds = selectedZones.map(zone => zone.id);
    const selectedDevices = this.data.availableDevices.filter(device => 
      selectedZoneIds.includes(device.zone) && device.status === 'online'
    );
    
    this.setData({
      selectedDevices: selectedDevices,
      selectedZones: selectedZones,
      hasSelection: selectedDevices.length > 0,
      showZoneSelector: false
    });
  },

  // 跳转到控制页面
  goToControl: function () {
    if (!this.data.hasSelection) {
      wx.showToast({
        title: '请先选择设备',
        icon: 'none'
      });
      return;
    }
    
    this.navigateToControl();
  },

  // 导航到控制页面
  navigateToControl: function () {
    const actionType = this.data.actionType;
    const selectedDevices = this.data.selectedDevices;
    
    // 将选中的设备信息传递给控制页面
    const deviceIds = selectedDevices.map(device => device.id).join(',');
    
    let targetUrl;
    if (actionType === 'irrigation') {
      targetUrl = `/pages/device/irrigation/irrigation?devices=${deviceIds}&mode=quick`;
    } else {
      targetUrl = `/pages/device/fertilizer/fertilizer?devices=${deviceIds}&mode=quick`;
    }
    
    wx.navigateTo({
      url: targetUrl
    });
  },

  // 返回首页
  goBack: function () {
    wx.navigateBack();
  }
});