/* 地块绘制页面样式 */

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  padding-top: env(safe-area-inset-top);
}

/* 顶部栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: #08C160;
  color: white;
  position: relative;
  z-index: 10;
}

.back-btn {
  background: rgba(255,255,255,0.2);
  color: white;
  border: none;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
}

.area {
  font-size: 24rpx;
  background: rgba(255,255,255,0.2);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #eee;
  gap: 20rpx;
  position: relative;
  z-index: 5;
}

.search-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 35rpx;
  font-size: 28rpx;
  background: #f8f8f8;
}

.search-btn {
  height: 70rpx;
  padding: 0 30rpx;
  background: #08C160;
  color: white;
  border: none;
  border-radius: 35rpx;
  font-size: 28rpx;
}

/* 地图容器 */
.map-container {
  height: 60vh;
  position: relative;
  margin: 0;
  padding: 0;
}

.map-box {
  width: 100%;
  height: 60vh;
  position: relative;
  overflow: hidden;
}

.map {
  width: 100%;
  height: 60vh;
  max-width: 100vw;
  max-height: 60vh;
  display: block;
}

.loading {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

/* 浮动工具栏 */
.floating-toolbar {
  position: absolute;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(8, 193, 96, 0.1);
}

.tip {
  display: block;
  text-align: center;
  color: #666;
  font-size: 26rpx;
  margin-bottom: 20rpx;
}

.buttons {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.btn {
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
  color: white;
}

.btn.start {
  background: #08C160;
}

.btn.finish {
  background: #07C160;
}

.btn.clear {
  background: #fa5151;
}