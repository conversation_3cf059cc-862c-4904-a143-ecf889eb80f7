<!--设备配置页面 - 原生版本-->
<view class="config-container">
  
  <!-- 顶部Tab导航 -->
  <view class="config-tabs">
    <view class="tab-nav">
      <view class="tab-item {{activeTab === 'status' ? 'active' : ''}}" data-tab="status" bind:tap="onTabChange">
        <text>设备状态</text>
      </view>
      <view class="tab-item {{activeTab === 'basic' ? 'active' : ''}}" data-tab="basic" bind:tap="onTabChange">
        <text>基础配置</text>
      </view>
      <view class="tab-item {{activeTab === 'network' ? 'active' : ''}}" data-tab="network" bind:tap="onTabChange">
        <text>网络配置</text>
      </view>
      <view class="tab-item {{activeTab === 'params' ? 'active' : ''}}" data-tab="params" bind:tap="onTabChange">
        <text>参数配置</text>
      </view>
    </view>
  </view>
  
  <!-- 设备状态页面 -->
  <view class="tab-content" wx:if="{{activeTab === 'status'}}">
    
    <!-- 设备连接状态 -->
    <view class="config-card status-overview">
      <view class="status-header">
        <view class="device-info">
          <text class="device-name">水肥一体机-01</text>
          <view class="connection-status {{deviceStatus.online ? 'online' : 'offline'}}">
            <view class="status-dot"></view>
            <text class="status-text">{{deviceStatus.statusText}}</text>
          </view>
        </view>
        <button class="refresh-btn" bind:tap="refreshStatus">
          <text class="refresh-icon">⟳</text>
          <text>刷新</text>
        </button>
      </view>
      
      <!-- 实时状态指标 -->
      <view class="status-grid">
        <view class="status-item">
          <view class="status-icon temperature">
            <text class="icon">🌡️</text>
          </view>
          <view class="status-info">
            <text class="status-label">环境温度</text>
            <text class="status-value">{{deviceStatus.temperature}}°C</text>
          </view>
        </view>
        
        <view class="status-item">
          <view class="status-icon humidity">
            <text class="icon">💧</text>
          </view>
          <view class="status-info">
            <text class="status-label">环境湿度</text>
            <text class="status-value">{{deviceStatus.humidity}}%</text>
          </view>
        </view>
        
        <view class="status-item">
          <view class="status-icon water">
            <text class="icon">🚰</text>
          </view>
          <view class="status-info">
            <text class="status-label">水箱液位</text>
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{deviceStatus.waterLevel}}%"></view>
            </view>
            <text class="status-value">{{deviceStatus.waterLevel}}%</text>
          </view>
        </view>
        
        <view class="status-item">
          <view class="status-icon fertilizer">
            <text class="icon">🧪</text>
          </view>
          <view class="status-info">
            <text class="status-label">肥料余量</text>
            <view class="progress-bar">
              <view class="progress-fill fertilizer" style="width: {{deviceStatus.fertilizerLevel}}%"></view>
            </view>
            <text class="status-value">{{deviceStatus.fertilizerLevel}}%</text>
          </view>
        </view>
        
        <view class="status-item">
          <view class="status-icon pressure">
            <text class="icon">📊</text>
          </view>
          <view class="status-info">
            <text class="status-label">当前压力</text>
            <text class="status-value">{{deviceStatus.pressure}} bar</text>
          </view>
        </view>
        
        <view class="status-item">
          <view class="status-icon flow">
            <text class="icon">▶️</text>
          </view>
          <view class="status-info">
            <text class="status-label">当前流量</text>
            <text class="status-value">{{deviceStatus.flowRate}} L/h</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 运行统计 -->
    <view class="config-card">
      <view class="card-header">
        <text class="card-title">运行统计</text>
      </view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-label">累计运行时间</text>
          <text class="stats-value">{{deviceStatus.totalRunTime}}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">上次维护</text>
          <text class="stats-value">{{deviceStatus.lastMaintenance}}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">下次维护</text>
          <text class="stats-value">{{deviceStatus.nextMaintenance}}</text>
        </view>
      </view>
      
      <view class="config-actions">
        <button class="action-btn" bind:tap="viewRunningReport">
          <text class="btn-icon">📈</text>
          <text>运行报告</text>
        </button>
        <button class="action-btn primary" bind:tap="refreshStatus">
          <text class="btn-icon">⟳</text>
          <text>刷新状态</text>
        </button>
      </view>
    </view>
    
  </view>
  
  <!-- 基础配置页面 -->
  <view class="tab-content" wx:if="{{activeTab === 'basic'}}">
    
    <!-- 设备信息配置 -->
    <view class="config-card">
      <view class="card-header">
        <text class="card-title">设备信息</text>
      </view>
      <view class="config-form">
        <view class="form-item">
          <text class="form-label">设备名称</text>
          <input class="form-input" 
            value="{{basicConfig.deviceName}}"
            bindinput="onDeviceNameInput"
            placeholder="请输入设备名称" />
        </view>
        
        <view class="form-item">
          <text class="form-label">安装位置</text>
          <input class="form-input" 
            value="{{basicConfig.location}}"
            bindinput="onLocationInput"
            placeholder="请输入安装位置" />
        </view>
        
        <view class="form-item" bind:tap="showGroupPicker">
          <text class="form-label">设备分组</text>
          <view class="picker-field">
            <text class="picker-value">{{groupOptions[basicConfig.groupIndex]}}</text>
            <text class="picker-arrow">></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 运行参数配置 -->
    <view class="config-card">
      <view class="card-header">
        <text class="card-title">运行参数</text>
      </view>
      <view class="config-form">
        <view class="form-item" bind:tap="showWorkModePicker">
          <text class="form-label">工作模式</text>
          <view class="picker-field">
            <text class="picker-value">{{workModeOptions[basicConfig.workMode]}}</text>
            <text class="picker-arrow">></text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">最大压力</text>
          <text class="form-desc">参数范围: 0.5-5.0 bar</text>
          <view class="input-with-unit">
            <input class="form-input" 
              type="digit"
              value="{{basicConfig.maxPressure}}"
              bindinput="onMaxPressureInput"
              placeholder="3.0" />
            <text class="unit-label">bar</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">最大流量</text>
          <text class="form-desc">参数范围: 10-200 L/h</text>
          <view class="input-with-unit">
            <input class="form-input" 
              type="number"
              value="{{basicConfig.maxFlowRate}}"
              bindinput="onMaxFlowRateInput"
              placeholder="100" />
            <text class="unit-label">L/h</text>
          </view>
        </view>
        
        <view class="form-item switch-item">
          <view class="switch-info">
            <text class="form-label">安全模式</text>
            <text class="form-desc">启用后限制最大参数</text>
          </view>
          <switch checked="{{basicConfig.safeMode}}" bindchange="onSafeModeChange" />
        </view>
        
        <view class="form-item switch-item">
          <view class="switch-info">
            <text class="form-label">自动启动</text>
            <text class="form-desc">开机自动启动设备</text>
          </view>
          <switch checked="{{basicConfig.autoStart}}" bindchange="onAutoStartChange" />
        </view>
        
        <view class="form-item switch-item">
          <view class="switch-info">
            <text class="form-label">告警提醒</text>
            <text class="form-desc">开启设备异常提醒</text>
          </view>
          <switch checked="{{basicConfig.alertEnabled}}" bindchange="onAlertEnabledChange" />
        </view>
      </view>
    </view>
    
    
    <!-- 配置操作按钮 -->
    <view class="config-card">
      <view class="config-actions">
        <button class="action-btn primary" bind:tap="saveConfig">
          <text class="btn-icon">✓</text>
          <text>保存配置</text>
        </button>
        <button class="action-btn secondary" bind:tap="resetConfig">
          <text class="btn-icon">↻</text>
          <text>恢复默认</text>
        </button>
      </view>
    </view>

  </view>
  
  <!-- 网络配置页面 -->
  <view class="tab-content" wx:if="{{activeTab === 'network'}}">
    
    <!-- WiFi配置 -->
    <view class="config-card">
      <view class="card-header">
        <text class="card-title">WiFi配置</text>
      </view>
      <view class="config-form">
        <view class="form-item">
          <text class="form-label">网络名称(SSID)</text>
          <input class="form-input" 
            value="{{networkConfig.ssid}}"
            bindinput="onSSIDInput"
            placeholder="请输入WiFi名称" />
        </view>
        
        <view class="form-item">
          <text class="form-label">密码</text>
          <input class="form-input" 
            type="password"
            value="{{networkConfig.password}}"
            bindinput="onPasswordInput"
            placeholder="请输入WiFi密码" />
        </view>
        
        <view class="form-item">
          <text class="form-label">连接状态</text>
          <view class="wifi-status {{networkConfig.wifiStatus}}">
            <view class="status-dot"></view>
            <text class="status-text">{{networkConfig.wifiStatusText}}</text>
          </view>
        </view>
      </view>
      
      <view class="config-actions">
        <button class="action-btn primary" bind:tap="testWiFiConnection">
          <text class="btn-icon">📶</text>
          <text>测试连接</text>
        </button>
      </view>
    </view>
    
    <!-- 网络参数配置 -->
    <view class="config-card">
      <view class="card-header">
        <text class="card-title">网络参数</text>
      </view>
      <view class="config-form">
        <view class="form-item">
          <text class="form-label">IP地址</text>
          <input class="form-input" 
            value="{{networkConfig.ipAddress}}"
            bindinput="onIPInput"
            placeholder="*************" />
        </view>
        
        <view class="form-item">
          <text class="form-label">子网掩码</text>
          <input class="form-input" 
            value="{{networkConfig.subnetMask}}"
            bindinput="onSubnetInput"
            placeholder="*************" />
        </view>
        
        <view class="form-item">
          <text class="form-label">网关</text>
          <input class="form-input" 
            value="{{networkConfig.gateway}}"
            bindinput="onGatewayInput"
            placeholder="***********" />
        </view>
        
        <view class="form-item">
          <text class="form-label">DNS服务器</text>
          <input class="form-input" 
            value="{{networkConfig.dns}}"
            bindinput="onDNSInput"
            placeholder="*******" />
        </view>
      </view>
    </view>
    
    <!-- 云平台配置 -->
    <view class="config-card">
      <view class="card-header">
        <text class="card-title">云平台配置</text>
      </view>
      <view class="config-form">
        <view class="form-item">
          <text class="form-label">服务器地址</text>
          <input class="form-input" 
            value="{{networkConfig.serverUrl}}"
            bindinput="onServerUrlInput"
            placeholder="https://api.smartfarm.com" />
        </view>
        
        <view class="form-item">
          <text class="form-label">设备ID</text>
          <input class="form-input" 
            value="{{networkConfig.deviceId}}"
            bindinput="onDeviceIdInput"
            placeholder="设备唯一标识" />
        </view>
        
        <view class="form-item">
          <text class="form-label">上报间隔</text>
          <view class="input-with-unit">
            <input class="form-input" 
              type="number"
              value="{{networkConfig.reportInterval}}"
              bindinput="onReportIntervalInput"
              placeholder="30" />
            <text class="unit-label">秒</text>
          </view>
        </view>
        
        <view class="form-item switch-item">
          <view class="switch-info">
            <text class="form-label">启用云同步</text>
            <text class="form-desc">同步数据到云端服务器</text>
          </view>
          <switch checked="{{networkConfig.cloudSync}}" bindchange="onCloudSyncChange" />
        </view>
      </view>
    </view>
    
    <!-- 配置操作按钮 -->
    <view class="config-card">
      <view class="config-actions">
        <button class="action-btn primary" bind:tap="saveConfig">
          <text class="btn-icon">✓</text>
          <text>保存配置</text>
        </button>
        <button class="action-btn secondary" bind:tap="resetConfig">
          <text class="btn-icon">↻</text>
          <text>恢复默认</text>
        </button>
      </view>
    </view>

  </view>
  
  <!-- 参数配置页面 -->
  <view class="tab-content" wx:if="{{activeTab === 'params'}}">
    
    <!-- 流量参数配置 -->
    <view class="config-card">
      <view class="card-header">
        <text class="card-title">流量参数</text>
      </view>
      <view class="config-form">
        <view class="form-item">
          <text class="form-label">额定流量</text>
          <text class="form-desc">范围: 10-200 L/h</text>
          <view class="input-with-unit">
            <input class="form-input" 
              type="number"
              value="{{paramConfig.ratedFlowRate}}"
              bindinput="onRatedFlowRateInput"
              placeholder="80" />
            <text class="unit-label">L/h</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">最小流量</text>
          <text class="form-desc">范围: 5-50 L/h</text>
          <view class="input-with-unit">
            <input class="form-input" 
              type="number"
              value="{{paramConfig.minFlowRate}}"
              bindinput="onMinFlowRateInput"
              placeholder="10" />
            <text class="unit-label">L/h</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">流量调节精度</text>
          <view class="picker-field" bind:tap="showFlowPrecisionPicker">
            <text class="picker-value">{{flowPrecisionOptions[paramConfig.flowPrecisionIndex]}}</text>
            <text class="picker-arrow">></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 压力参数配置 -->
    <view class="config-card">
      <view class="card-header">
        <text class="card-title">压力参数</text>
      </view>
      <view class="config-form">
        <view class="form-item">
          <text class="form-label">工作压力</text>
          <text class="form-desc">范围: 1.0-4.0 bar</text>
          <view class="input-with-unit">
            <input class="form-input" 
              type="digit"
              value="{{paramConfig.workingPressure}}"
              bindinput="onWorkingPressureInput"
              placeholder="2.5" />
            <text class="unit-label">bar</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">压力上限</text>
          <text class="form-desc">超过此值将停机保护</text>
          <view class="input-with-unit">
            <input class="form-input" 
              type="digit"
              value="{{paramConfig.pressureLimit}}"
              bindinput="onPressureLimitInput"
              placeholder="3.5" />
            <text class="unit-label">bar</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">压力下限</text>
          <text class="form-desc">低于此值将报警</text>
          <view class="input-with-unit">
            <input class="form-input" 
              type="digit"
              value="{{paramConfig.pressureAlarm}}"
              bindinput="onPressureAlarmInput"
              placeholder="1.0" />
            <text class="unit-label">bar</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 施肥参数配置 -->
    <view class="config-card">
      <view class="card-header">
        <text class="card-title">施肥参数</text>
      </view>
      <view class="config-form">
        <view class="form-item">
          <text class="form-label">默认EC值</text>
          <text class="form-desc">范围: 0.5-3.0 mS/cm</text>
          <view class="input-with-unit">
            <input class="form-input" 
              type="digit"
              value="{{paramConfig.defaultEC}}"
              bindinput="onDefaultECInput"
              placeholder="1.8" />
            <text class="unit-label">mS/cm</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">默认pH值</text>
          <text class="form-desc">范围: 5.5-7.5</text>
          <view class="input-with-unit">
            <input class="form-input" 
              type="digit"
              value="{{paramConfig.defaultPH}}"
              bindinput="onDefaultPHInput"
              placeholder="6.5" />
            <text class="unit-label">pH</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">混合比例</text>
          <view class="picker-field" bind:tap="showMixRatioPicker">
            <text class="picker-value">{{mixRatioOptions[paramConfig.mixRatioIndex]}}</text>
            <text class="picker-arrow">></text>
          </view>
        </view>
        
        <view class="form-item switch-item">
          <view class="switch-info">
            <text class="form-label">自动混合</text>
            <text class="form-desc">根据设定比例自动混合肥料</text>
          </view>
          <switch checked="{{paramConfig.autoMix}}" bindchange="onAutoMixChange" />
        </view>
      </view>
    </view>
    
    <!-- 安全保护参数 -->
    <view class="config-card">
      <view class="card-header">
        <text class="card-title">安全保护</text>
      </view>
      <view class="config-form">
        <view class="form-item">
          <text class="form-label">超时保护</text>
          <text class="form-desc">超过此时间自动停机</text>
          <view class="input-with-unit">
            <input class="form-input" 
              type="number"
              value="{{paramConfig.timeoutProtection}}"
              bindinput="onTimeoutProtectionInput"
              placeholder="120" />
            <text class="unit-label">分钟</text>
          </view>
        </view>
        
        <view class="form-item switch-item">
          <view class="switch-info">
            <text class="form-label">缺水保护</text>
            <text class="form-desc">水箱液位过低时停机</text>
          </view>
          <switch checked="{{paramConfig.lowWaterProtection}}" bindchange="onLowWaterProtectionChange" />
        </view>
        
        <view class="form-item switch-item">
          <view class="switch-info">
            <text class="form-label">过压保护</text>
            <text class="form-desc">压力过高时自动停机</text>
          </view>
          <switch checked="{{paramConfig.overPressureProtection}}" bindchange="onOverPressureProtectionChange" />
        </view>
        
        <view class="form-item switch-item">
          <view class="switch-info">
            <text class="form-label">干转保护</text>
            <text class="form-desc">水泵干转时停机保护</text>
          </view>
          <switch checked="{{paramConfig.dryRunProtection}}" bindchange="onDryRunProtectionChange" />
        </view>
      </view>
    </view>
    
    <!-- 配置操作按钮 -->
    <view class="config-card">
      <view class="config-actions">
        <button class="action-btn primary" bind:tap="saveConfig">
          <text class="btn-icon">✓</text>
          <text>保存配置</text>
        </button>
        <button class="action-btn secondary" bind:tap="resetConfig">
          <text class="btn-icon">↻</text>
          <text>恢复默认</text>
        </button>
      </view>
    </view>

  </view>
  

</view>

<!-- 选择器弹窗 -->
<view class="picker-modal" wx:if="{{showPicker}}">
  <view class="picker-overlay" bind:tap="onPickerCancel"></view>
  <view class="picker-content">
    <view class="picker-header">
      <text class="picker-cancel" bind:tap="onPickerCancel">取消</text>
      <text class="picker-title">{{pickerTitle}}</text>
      <text class="picker-confirm" bind:tap="onPickerConfirm">确定</text>
    </view>
    <picker-view class="picker-view" value="{{pickerValue}}" bindchange="onPickerChange">
      <picker-view-column>
        <view class="picker-item" wx:for="{{pickerOptions[0]}}" wx:key="*this">{{item}}</view>
      </picker-view-column>
    </picker-view>
  </view>
</view>

