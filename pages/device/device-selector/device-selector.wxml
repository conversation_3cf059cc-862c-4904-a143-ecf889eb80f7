<!--设备选择页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 操作提示 -->
    <view class="action-header">
      <view class="action-icon {{actionType === 'irrigation' ? 'primary-bg' : 'secondary-bg'}}">
        <text class="icon-text">{{actionType === 'irrigation' ? '💧' : '🌱'}}</text>
      </view>
      <view class="action-info">
        <text class="action-title">{{actionType === 'irrigation' ? '立即灌溉' : '立即施肥'}}</text>
        <text class="action-desc">请选择要执行操作的设备</text>
      </view>
    </view>

    <!-- 可用设备列表 -->
    <view class="card">
      <view class="card-title">
        <text>可用设备</text>
        <text class="available-count">{{availableDevices.length}}台在线</text>
      </view>
      <view class="device-list">
        <view class="device-item" 
              wx:for="{{availableDevices}}" 
              wx:key="id"
              bindtap="selectDevice"
              data-device="{{item}}">
          <view class="device-icon primary-bg">
            <text class="icon-text">💧</text>
          </view>
          <view class="device-info">
            <text class="device-name">{{item.name}}</text>
            <text class="device-location">📍 {{item.location}}</text>
            <view class="device-status">
              <text class="status-indicator {{item.status}}"></text>
              <text class="status-text">{{item.statusText}}</text>
              <text class="capacity-text">水位: {{item.waterLevel}}% | 肥料: {{item.fertilizerLevel}}%</text>
            </view>
          </view>
          <view class="device-action">
            <text class="action-btn">选择</text>
          </view>
        </view>
        
        <!-- 无可用设备 -->
        <view class="no-device" wx:if="{{availableDevices.length === 0}}">
          <text class="no-device-icon">🚫</text>
          <text class="no-device-text">暂无可用的设备</text>
          <text class="no-device-desc">请检查设备连接状态</text>
        </view>
      </view>
    </view>

    <!-- 批量操作选项 -->
    <view class="card" wx:if="{{availableDevices.length > 1}}">
      <view class="card-title">批量操作</view>
      <view class="batch-options">
        <view class="batch-option" bindtap="selectAllDevices">
          <view class="batch-icon warning-bg">
            <text class="icon-text">🎯</text>
          </view>
          <view class="batch-info">
            <text class="batch-name">全部设备</text>
            <text class="batch-desc">同时操作所有在线设备</text>
          </view>
        </view>
        
        <view class="batch-option" bindtap="selectByZone">
          <view class="batch-icon secondary-bg">
            <text class="icon-text">🗺️</text>
          </view>
          <view class="batch-info">
            <text class="batch-name">按区域选择</text>
            <text class="batch-desc">选择特定区域的设备</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 区域选择（当选择按区域时显示） -->
    <view class="card" wx:if="{{showZoneSelector}}">
      <view class="card-title">选择区域</view>
      <view class="zone-grid">
        <view class="zone-item {{item.selected ? 'selected' : ''}}" 
              wx:for="{{zoneList}}" 
              wx:key="id"
              bindtap="toggleZone"
              data-zone="{{item}}">
          <text class="zone-name">{{item.name}}</text>
          <text class="zone-devices">{{item.deviceCount}}台设备</text>
          <view class="zone-status {{item.allOnline ? 'online' : 'partial'}}">
            <text>{{item.allOnline ? '全部在线' : '部分在线'}}</text>
          </view>
        </view>
      </view>
      <view class="zone-actions">
        <button class="btn btn-outline" bindtap="cancelZoneSelection">取消</button>
        <button class="btn btn-primary" bindtap="confirmZoneSelection">确认选择</button>
      </view>
    </view>

    <!-- 操作确认 -->
    <view class="action-footer">
      <button class="btn btn-outline" bindtap="goBack">取消</button>
      <button class="btn btn-primary" bindtap="goToControl" disabled="{{!hasSelection}}">
        继续操作
      </button>
    </view>

  </view>
</view>