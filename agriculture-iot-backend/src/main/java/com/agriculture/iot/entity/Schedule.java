package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * 调度计划实体类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("schedules")
public class Schedule {

    /**
     * 计划ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 计划名称
     */
    @TableField("name")
    private String name;

    /**
     * 计划描述
     */
    @TableField("description")
    private String description;

    /**
     * 计划类型: irrigation-灌溉, fertilizer-施肥, monitoring-监测
     */
    @TableField("type")
    private String type;

    /**
     * 地块ID
     */
    @TableField("plot_id")
    private Long plotId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private Long deviceId;

    /**
     * 开始日期
     */
    @TableField("start_date")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @TableField("end_date")
    private LocalDate endDate;

    /**
     * 执行时间
     */
    @TableField("execution_time")
    private LocalTime executionTime;

    /**
     * 重复模式: none-不重复, daily-每日, weekly-每周, monthly-每月, custom-自定义
     */
    @TableField("repeat_pattern")
    private String repeatPattern;

    /**
     * 重复间隔
     */
    @TableField("repeat_interval")
    private Integer repeatInterval;

    /**
     * 重复规则（JSON格式）
     */
    @TableField("repeat_rules")
    private String repeatRules;

    /**
     * 执行状态: active-活跃, paused-暂停, completed-完成, cancelled-取消
     */
    @TableField("status")
    private String status;

    /**
     * 优先级: low-低, medium-中, high-高, urgent-紧急
     */
    @TableField("priority")
    private String priority;

    /**
     * 执行参数（JSON格式）
     */
    @TableField("parameters")
    private String parameters;

    /**
     * 触发条件（JSON格式）
     */
    @TableField("trigger_conditions")
    private String triggerConditions;

    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 下次执行时间
     */
    @TableField("next_execution")
    private LocalDateTime nextExecution;

    /**
     * 最后执行时间
     */
    @TableField("last_execution")
    private LocalDateTime lastExecution;

    /**
     * 执行次数
     */
    @TableField("execution_count")
    private Integer executionCount;

    /**
     * 成功次数
     */
    @TableField("success_count")
    private Integer successCount;

    /**
     * 创建者ID
     */
    @TableField("created_by")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // 关联对象
    /**
     * 关联地块信息
     */
    @TableField(exist = false)
    private Plot plot;

    /**
     * 关联设备信息
     */
    @TableField(exist = false)
    private Device device;

    // DTO内部类
    /**
     * 创建计划请求DTO
     */
    @Data
    public static class CreateRequest {
        private String name;
        private String description;
        private String type;
        private Long plotId;
        private Long deviceId;
        private LocalDate startDate;
        private LocalDate endDate;
        private LocalTime executionTime;
        private String repeatPattern;
        private Integer repeatInterval;
        private Map<String, Object> repeatRules;
        private String priority;
        private Map<String, Object> parameters;
        private Map<String, Object> triggerConditions;
        private Boolean enabled;
    }

    /**
     * 更新计划请求DTO
     */
    @Data
    public static class UpdateRequest {
        private String name;
        private String description;
        private LocalDate startDate;
        private LocalDate endDate;
        private LocalTime executionTime;
        private String repeatPattern;
        private Integer repeatInterval;
        private Map<String, Object> repeatRules;
        private String priority;
        private Map<String, Object> parameters;
        private Map<String, Object> triggerConditions;
        private Boolean enabled;
    }

    /**
     * 计划详情DTO
     */
    @Data
    public static class Detail {
        private Long id;
        private String name;
        private String description;
        private String type;
        private String typeText;
        private Long plotId;
        private String plotName;
        private Long deviceId;
        private String deviceName;
        private LocalDate startDate;
        private LocalDate endDate;
        private LocalTime executionTime;
        private String repeatPattern;
        private String repeatPatternText;
        private Integer repeatInterval;
        private Map<String, Object> repeatRules;
        private String status;
        private String statusText;
        private String priority;
        private String priorityText;
        private Map<String, Object> parameters;
        private Map<String, Object> triggerConditions;
        private Boolean enabled;
        private LocalDateTime nextExecution;
        private LocalDateTime lastExecution;
        private Integer executionCount;
        private Integer successCount;
        private BigDecimal successRate;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    /**
     * 计划执行记录DTO
     */
    @Data
    public static class ExecutionRecord {
        private Long id;
        private Long scheduleId;
        private String scheduleName;
        private LocalDateTime executionTime;
        private String status;
        private String statusText;
        private Integer duration;
        private String result;
        private String errorMessage;
        private Map<String, Object> executionData;
        private LocalDateTime createdAt;
    }

    /**
     * 计划统计DTO
     */
    @Data
    public static class Statistics {
        private LocalDate date;
        private Integer totalSchedules;
        private Integer activeSchedules;
        private Integer completedSchedules;
        private Integer failedSchedules;
        private Integer executionCount;
        private Integer successCount;
        private BigDecimal successRate;
        private Integer avgDuration;
    }

    /**
     * 计划模板DTO
     */
    @Data
    public static class Template {
        private Long id;
        private String name;
        private String description;
        private String type;
        private String category;
        private Map<String, Object> defaultParameters;
        private String repeatPattern;
        private Integer repeatInterval;
        private String usage;
        private String precautions;
        private LocalDateTime createdAt;
    }

    /**
     * 计划冲突检测DTO
     */
    @Data
    public static class ConflictInfo {
        private Boolean hasConflict;
        private List<ConflictDetail> conflicts;
        private String suggestion;
    }

    /**
     * 冲突详情DTO
     */
    @Data
    public static class ConflictDetail {
        private Long scheduleId;
        private String scheduleName;
        private LocalDateTime conflictTime;
        private String conflictType;
        private String reason;
        private String severity;
    }

    /**
     * 日历视图DTO
     */
    @Data
    public static class CalendarView {
        private LocalDate date;
        private List<CalendarItem> items;
        private Integer totalCount;
        private Boolean hasOverdue;
    }

    /**
     * 日历项目DTO
     */
    @Data
    public static class CalendarItem {
        private Long id;
        private String name;
        private String type;
        private String typeText;
        private LocalTime time;
        private String status;
        private String statusText;
        private String priority;
        private String priorityText;
        private Boolean canExecute;
    }

    /**
     * 执行结果DTO
     */
    @Data
    public static class ExecutionResult {
        private Long scheduleId;
        private String scheduleName;
        private Boolean success;
        private String message;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private Integer duration;
        private Map<String, Object> resultData;
        private String errorCode;
        private String errorMessage;
        private LocalDateTime nextExecution;
    }
}