package com.agriculture.iot.task;

import com.agriculture.iot.entity.IrrigationRecord;
import com.agriculture.iot.entity.FertilizerRecord;
import com.agriculture.iot.entity.Schedule;
import com.agriculture.iot.service.IrrigationService;
import com.agriculture.iot.service.FertilizerService;
import com.agriculture.iot.service.ScheduleService;
import com.agriculture.iot.mapper.ScheduleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定时任务服务类
 * 负责水肥一体机的定时灌溉和施肥功能
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduledTaskService {

    private final ScheduleService scheduleService;
    private final IrrigationService irrigationService;
    private final FertilizerService fertilizerService;
    private final ScheduleMapper scheduleMapper;

    /**
     * 每分钟执行一次，检查需要执行的计划
     */
    @Scheduled(cron = "0 * * * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void executeScheduledTasks() {
        log.info("开始执行定时任务检查");
        
        try {
            // 获取当前时间前后1分钟需要执行的计划
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTime = now.minusMinutes(1);
            LocalDateTime endTime = now.plusMinutes(1);
            
            // 获取需要执行的计划
            List<Schedule.CalendarItem> schedules = scheduleMapper.selectUpcomingSchedules(1, null, null);
            
            for (Schedule.CalendarItem schedule : schedules) {
                if (schedule.getCanExecute()) {
                    executeSchedule(schedule);
                }
            }
            
            log.info("定时任务检查完成，处理了 {} 个计划", schedules.size());
            
        } catch (Exception e) {
            log.error("执行定时任务失败", e);
        }
    }

    /**
     * 每小时执行一次，检查逾期的计划
     */
    @Scheduled(cron = "0 0 * * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void checkOverdueSchedules() {
        log.info("开始检查逾期计划");
        
        try {
            // 获取逾期计划
            List<Schedule.CalendarItem> overdueSchedules = scheduleMapper.selectOverdueSchedules(null, null);
            
            for (Schedule.CalendarItem schedule : overdueSchedules) {
                // 标记为逾期
                updateScheduleStatus(schedule.getId(), "overdue");
                
                // 发送逾期通知
                sendOverdueNotification(schedule);
            }
            
            log.info("逾期计划检查完成，发现 {} 个逾期计划", overdueSchedules.size());
            
        } catch (Exception e) {
            log.error("检查逾期计划失败", e);
        }
    }

    /**
     * 每天凌晨1点执行一次，清理过期的执行记录
     */
    @Scheduled(cron = "0 0 1 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void cleanupExpiredRecords() {
        log.info("开始清理过期记录");
        
        try {
            // 清理30天前的执行记录
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30);
            
            // 清理灌溉记录
            // TODO: 实现清理逻辑
            
            // 清理施肥记录
            // TODO: 实现清理逻辑
            
            log.info("过期记录清理完成");
            
        } catch (Exception e) {
            log.error("清理过期记录失败", e);
        }
    }

    /**
     * 每天早上8点执行一次，生成当日计划报告
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void generateDailyReport() {
        log.info("开始生成每日计划报告");
        
        try {
            // 获取今日计划
            List<Schedule.CalendarItem> todaySchedules = scheduleMapper.selectTodaySchedules(null, null);
            
            // 生成报告
            StringBuilder report = new StringBuilder();
            report.append("今日计划报告 - ").append(LocalDateTime.now().toLocalDate()).append("\n");
            report.append("总计划数: ").append(todaySchedules.size()).append("\n");
            
            for (Schedule.CalendarItem schedule : todaySchedules) {
                report.append("- ").append(schedule.getName())
                      .append(" (").append(schedule.getType()).append(")")
                      .append(" - ").append(schedule.getTime())
                      .append("\n");
            }
            
            // TODO: 发送报告邮件或推送通知
            log.info("每日计划报告生成完成: \n{}", report.toString());
            
        } catch (Exception e) {
            log.error("生成每日计划报告失败", e);
        }
    }

    /**
     * 每5分钟执行一次，检查设备状态和传感器数据
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void checkDeviceStatus() {
        log.info("开始检查设备状态");
        
        try {
            // TODO: 检查设备在线状态
            // TODO: 检查传感器数据异常
            // TODO: 检查设备电量和信号强度
            
            log.info("设备状态检查完成");
            
        } catch (Exception e) {
            log.error("检查设备状态失败", e);
        }
    }

    /**
     * 执行具体的计划
     */
    private void executeSchedule(Schedule.CalendarItem schedule) {
        try {
            log.info("开始执行计划: {} (ID: {})", schedule.getName(), schedule.getId());
            
            switch (schedule.getType()) {
                case "irrigation":
                    executeIrrigationSchedule(schedule);
                    break;
                case "fertilizer":
                    executeFertilizerSchedule(schedule);
                    break;
                case "monitoring":
                    executeMonitoringSchedule(schedule);
                    break;
                default:
                    log.warn("未知的计划类型: {}", schedule.getType());
            }
            
            // 更新计划的执行状态
            updateScheduleExecutionStatus(schedule.getId(), "completed");
            
        } catch (Exception e) {
            log.error("执行计划失败: {}", schedule.getName(), e);
            // 更新计划的执行状态为失败
            updateScheduleExecutionStatus(schedule.getId(), "failed");
        }
    }

    /**
     * 执行灌溉计划
     */
    private void executeIrrigationSchedule(Schedule.CalendarItem schedule) {
        try {
            // 创建灌溉请求
            IrrigationRecord.StartRequest startRequest = new IrrigationRecord.StartRequest();
            startRequest.setDeviceId(schedule.getDeviceId());
            startRequest.setPlotId(schedule.getPlotId());
            startRequest.setDuration(30); // 默认30分钟
            startRequest.setWaterAmount(50.0); // 默认50升
            startRequest.setMode("auto");
            startRequest.setTriggerSource("schedule");
            startRequest.setNotes("定时计划执行：" + schedule.getName());
            
            // 执行灌溉
            irrigationService.startIrrigation(startRequest);
            
            log.info("灌溉计划执行成功: {}", schedule.getName());
            
        } catch (Exception e) {
            log.error("执行灌溉计划失败: {}", schedule.getName(), e);
            throw e;
        }
    }

    /**
     * 执行施肥计划
     */
    private void executeFertilizerSchedule(Schedule.CalendarItem schedule) {
        try {
            // 创建施肥请求
            FertilizerRecord.ApplyRequest applyRequest = new FertilizerRecord.ApplyRequest();
            applyRequest.setDeviceId(schedule.getDeviceId());
            applyRequest.setPlotId(schedule.getPlotId());
            applyRequest.setFertilizerType("compound"); // 默认复合肥
            applyRequest.setAmount(10.0); // 默认10升
            applyRequest.setUnit("L");
            applyRequest.setConcentration(0.5); // 默认浓度0.5%
            applyRequest.setMethod("irrigation"); // 通过灌溉施肥
            applyRequest.setTriggerSource("schedule");
            applyRequest.setNotes("定时计划执行：" + schedule.getName());
            
            // 执行施肥
            fertilizerService.applyFertilizer(applyRequest);
            
            log.info("施肥计划执行成功: {}", schedule.getName());
            
        } catch (Exception e) {
            log.error("执行施肥计划失败: {}", schedule.getName(), e);
            throw e;
        }
    }

    /**
     * 执行监测计划
     */
    private void executeMonitoringSchedule(Schedule.CalendarItem schedule) {
        try {
            // TODO: 实现监测计划的具体逻辑
            // 这里可以包括：
            // - 采集传感器数据
            // - 检查设备状态
            // - 更新监测记录
            
            log.info("监测计划执行成功: {}", schedule.getName());
            
        } catch (Exception e) {
            log.error("执行监测计划失败: {}", schedule.getName(), e);
            throw e;
        }
    }

    /**
     * 更新计划状态
     */
    private void updateScheduleStatus(Long scheduleId, String status) {
        try {
            // TODO: 调用scheduleService更新状态
            log.info("更新计划状态: scheduleId={}, status={}", scheduleId, status);
        } catch (Exception e) {
            log.error("更新计划状态失败", e);
        }
    }

    /**
     * 更新计划执行状态
     */
    private void updateScheduleExecutionStatus(Long scheduleId, String status) {
        try {
            // 更新执行次数和下次执行时间
            scheduleMapper.updateExecutionCount(scheduleId);
            if ("completed".equals(status)) {
                scheduleMapper.updateSuccessCount(scheduleId);
            }
            
            log.info("更新计划执行状态: scheduleId={}, status={}", scheduleId, status);
        } catch (Exception e) {
            log.error("更新计划执行状态失败", e);
        }
    }

    /**
     * 发送逾期通知
     */
    private void sendOverdueNotification(Schedule.CalendarItem schedule) {
        try {
            // TODO: 实现逾期通知发送逻辑
            // 这里可以包括：
            // - 发送邮件通知
            // - 发送短信通知
            // - 推送应用通知
            
            log.info("发送逾期通知: {}", schedule.getName());
            
        } catch (Exception e) {
            log.error("发送逾期通知失败", e);
        }
    }
}