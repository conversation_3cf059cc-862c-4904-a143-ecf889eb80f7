/**
 * 定时调度相关Mock API
 * 提供复杂的定时任务管理、日历视图、模板等功能
 */

const dataModule = require('./data');

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 验证Token
function verifyToken(token) {
  // 开发环境直接返回模拟payload，跳过校验
  return { userId: 'mock_user', exp: Math.floor(Date.now() / 1000) + 3600 };
}

// 生成下次执行时间
function getNextRunTime(schedule) {
  const now = new Date();
  const timeSlots = schedule.timeSlots || [];
  
  if (timeSlots.length === 0) return null;
  
  // 获取今天的执行时间
  const today = now.getDay();
  const todaySelected = schedule.customWeekdays.find(w => w.day === today)?.selected;
  
  if (todaySelected) {
    // 检查今天还有没有未执行的时间段
    for (let slot of timeSlots) {
      if (!slot.enabled) continue;
      
      const [hour, minute] = slot.startTime.split(':').map(Number);
      const slotTime = new Date(now);
      slotTime.setHours(hour, minute, 0, 0);
      
      if (slotTime > now) {
        return slotTime.toISOString();
      }
    }
  }
  
  // 找下一个有效日期
  let nextDay = new Date(now);
  nextDay.setDate(nextDay.getDate() + 1);
  
  for (let i = 0; i < 7; i++) {
    const dayOfWeek = nextDay.getDay();
    const daySelected = schedule.customWeekdays.find(w => w.day === dayOfWeek)?.selected;
    
    if (daySelected && timeSlots.length > 0) {
      const firstSlot = timeSlots.find(slot => slot.enabled);
      if (firstSlot) {
        const [hour, minute] = firstSlot.startTime.split(':').map(Number);
        nextDay.setHours(hour, minute, 0, 0);
        return nextDay.toISOString();
      }
    }
    
    nextDay.setDate(nextDay.getDate() + 1);
  }
  
  return null;
}

// 调度数据
const schedules = [
  {
    id: 1,
    name: '早晨灌溉',
    type: 'irrigation',
    enabled: true,
    farmId: 'farm_001',
    plotIds: ['plot_001', 'plot_002'],
    deviceIds: ['device_001', 'device_002'],
    customWeekdays: [
      { day: 1, label: '一', selected: true },
      { day: 2, label: '二', selected: true },
      { day: 3, label: '三', selected: true },
      { day: 4, label: '四', selected: true },
      { day: 5, label: '五', selected: true },
      { day: 6, label: '六', selected: true },
      { day: 0, label: '日', selected: true }
    ],
    weekdaysText: '每天',
    timeSlots: [
      {
        id: 1,
        startTime: '07:00',
        endTime: '07:30',
        duration: 30,
        flowRate: 60,
        zones: ['1区', '2区'],
        zoneIds: ['zone_001', 'zone_002'],
        enabled: true,
        fertilizer: {
          enabled: false,
          formulaId: '',
          concentration: 0
        },
        parameters: {
          pressure: 2.5,
          temperature: { min: 18, max: 30 }
        }
      }
    ],
    priority: 'high',
    retryOnFailure: true,
    maxRetries: 3,
    notifyOnCompletion: true,
    description: '每日早晨自动灌溉计划',
    createdBy: 'user_001',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-15T10:30:00.000Z'
  },
  {
    id: 2,
    name: '傍晚灌溉',
    type: 'irrigation',
    enabled: true,
    farmId: 'farm_001',
    plotIds: ['plot_001', 'plot_002', 'plot_003'],
    deviceIds: ['device_001', 'device_002', 'device_003'],
    customWeekdays: [
      { day: 1, label: '一', selected: true },
      { day: 2, label: '二', selected: true },
      { day: 3, label: '三', selected: true },
      { day: 4, label: '四', selected: true },
      { day: 5, label: '五', selected: true },
      { day: 6, label: '六', selected: true },
      { day: 0, label: '日', selected: true }
    ],
    weekdaysText: '每天',
    timeSlots: [
      {
        id: 2,
        startTime: '18:00',
        endTime: '18:20',
        duration: 20,
        flowRate: 50,
        zones: ['1区', '3区'],
        zoneIds: ['zone_001', 'zone_003'],
        enabled: true,
        fertilizer: {
          enabled: true,
          formulaId: 'formula_001',
          concentration: 15
        },
        parameters: {
          pressure: 2.2,
          temperature: { min: 16, max: 28 }
        }
      },
      {
        id: 3,
        startTime: '19:00',
        endTime: '19:15',
        duration: 15,
        flowRate: 40,
        zones: ['2区'],
        zoneIds: ['zone_002'],
        enabled: true,
        fertilizer: {
          enabled: false,
          formulaId: '',
          concentration: 0
        },
        parameters: {
          pressure: 2.0,
          temperature: { min: 16, max: 28 }
        }
      }
    ],
    priority: 'medium',
    retryOnFailure: true,
    maxRetries: 2,
    notifyOnCompletion: true,
    description: '傍晚多时段灌溉计划，包含施肥',
    createdBy: 'user_001',
    createdAt: '2024-01-05T00:00:00.000Z',
    updatedAt: '2024-01-20T15:45:00.000Z'
  },
  {
    id: 3,
    name: '周末深度灌溉',
    type: 'irrigation',
    enabled: false,
    farmId: 'farm_001',
    plotIds: ['plot_001', 'plot_002', 'plot_003'],
    deviceIds: ['device_001', 'device_002', 'device_003'],
    customWeekdays: [
      { day: 1, label: '一', selected: false },
      { day: 2, label: '二', selected: false },
      { day: 3, label: '三', selected: false },
      { day: 4, label: '四', selected: false },
      { day: 5, label: '五', selected: false },
      { day: 6, label: '六', selected: true },
      { day: 0, label: '日', selected: true }
    ],
    weekdaysText: '周末',
    timeSlots: [
      {
        id: 4,
        startTime: '08:00',
        endTime: '08:40',
        duration: 40,
        flowRate: 80,
        zones: ['1区', '2区', '3区'],
        zoneIds: ['zone_001', 'zone_002', 'zone_003'],
        enabled: true,
        fertilizer: {
          enabled: true,
          formulaId: 'formula_004',
          concentration: 20
        },
        parameters: {
          pressure: 3.0,
          temperature: { min: 15, max: 32 }
        }
      }
    ],
    priority: 'low',
    retryOnFailure: true,
    maxRetries: 1,
    notifyOnCompletion: false,
    description: '周末深度灌溉和施肥计划',
    createdBy: 'user_001',
    createdAt: '2024-01-10T00:00:00.000Z',
    updatedAt: '2024-01-25T09:15:00.000Z'
  },
  {
    id: 4,
    name: '施肥专用计划',
    type: 'fertilization',
    enabled: true,
    farmId: 'farm_001',
    plotIds: ['plot_001'],
    deviceIds: ['device_001'],
    customWeekdays: [
      { day: 1, label: '一', selected: true },
      { day: 2, label: '二', selected: false },
      { day: 3, label: '三', selected: true },
      { day: 4, label: '四', selected: false },
      { day: 5, label: '五', selected: true },
      { day: 6, label: '六', selected: false },
      { day: 0, label: '日', selected: false }
    ],
    weekdaysText: '周一、周三、周五',
    timeSlots: [
      {
        id: 5,
        startTime: '10:00',
        endTime: '10:25',
        duration: 25,
        flowRate: 45,
        zones: ['1区'],
        zoneIds: ['zone_001'],
        enabled: true,
        fertilizer: {
          enabled: true,
          formulaId: 'formula_001',
          concentration: 18
        },
        parameters: {
          pressure: 2.3,
          temperature: { min: 20, max: 35 }
        }
      }
    ],
    priority: 'high',
    retryOnFailure: true,
    maxRetries: 3,
    notifyOnCompletion: true,
    description: '番茄开花期专用施肥计划',
    createdBy: 'user_001',
    createdAt: '2024-01-12T00:00:00.000Z',
    updatedAt: '2024-01-28T11:20:00.000Z'
  }
];

// 为每个调度计算下次执行时间
schedules.forEach(schedule => {
  schedule.nextRunTime = getNextRunTime(schedule);
});

// 调度模板
const scheduleTemplates = [
  {
    id: 'morning',
    name: '晨间灌溉',
    icon: '🌅',
    description: '早晨7:00-7:30，适合大部分作物',
    category: 'irrigation',
    timeSlots: [{
      startTime: '07:00',
      endTime: '07:30',
      duration: 30,
      flowRate: 60,
      fertilizer: { enabled: false }
    }],
    defaultWeekdays: [1, 2, 3, 4, 5, 6, 0], // 每天
    parameters: {
      priority: 'high',
      retryOnFailure: true,
      maxRetries: 3
    }
  },
  {
    id: 'evening',
    name: '傍晚灌溉',
    icon: '🌇',
    description: '傍晚18:00-18:30，避开高温时段',
    category: 'irrigation',
    timeSlots: [{
      startTime: '18:00',
      endTime: '18:30',
      duration: 30,
      flowRate: 50,
      fertilizer: { enabled: false }
    }],
    defaultWeekdays: [1, 2, 3, 4, 5, 6, 0],
    parameters: {
      priority: 'medium',
      retryOnFailure: true,
      maxRetries: 2
    }
  },
  {
    id: 'multi',
    name: '多时段灌溉',
    icon: '⏰',
    description: '早晚各一次，确保充足水分',
    category: 'irrigation',
    timeSlots: [
      {
        startTime: '07:00',
        endTime: '07:20',
        duration: 20,
        flowRate: 60,
        fertilizer: { enabled: false }
      },
      {
        startTime: '18:00',
        endTime: '18:20',
        duration: 20,
        flowRate: 50,
        fertilizer: { enabled: false }
      }
    ],
    defaultWeekdays: [1, 2, 3, 4, 5, 6, 0],
    parameters: {
      priority: 'high',
      retryOnFailure: true,
      maxRetries: 3
    }
  },
  {
    id: 'frequent',
    name: '高频灌溉',
    icon: '💧',
    description: '每4小时一次，适合高温季节',
    category: 'irrigation',
    timeSlots: [
      { startTime: '06:00', endTime: '06:10', duration: 10, flowRate: 40, fertilizer: { enabled: false } },
      { startTime: '10:00', endTime: '10:10', duration: 10, flowRate: 40, fertilizer: { enabled: false } },
      { startTime: '14:00', endTime: '14:10', duration: 10, flowRate: 40, fertilizer: { enabled: false } },
      { startTime: '18:00', endTime: '18:10', duration: 10, flowRate: 40, fertilizer: { enabled: false } }
    ],
    defaultWeekdays: [1, 2, 3, 4, 5, 6, 0],
    parameters: {
      priority: 'medium',
      retryOnFailure: true,
      maxRetries: 2
    }
  },
  {
    id: 'fertilization',
    name: '施肥计划',
    icon: '🌱',
    description: '每周三次施肥，促进生长',
    category: 'fertilization',
    timeSlots: [{
      startTime: '10:00',
      endTime: '10:25',
      duration: 25,
      flowRate: 45,
      fertilizer: { enabled: true, concentration: 15 }
    }],
    defaultWeekdays: [1, 3, 5], // 周一、周三、周五
    parameters: {
      priority: 'high',
      retryOnFailure: true,
      maxRetries: 3
    }
  },
  {
    id: 'weekend',
    name: '周末深度护理',
    icon: '🌿',
    description: '周末长时间灌溉+施肥',
    category: 'irrigation',
    timeSlots: [{
      startTime: '08:00',
      endTime: '08:45',
      duration: 45,
      flowRate: 70,
      fertilizer: { enabled: true, concentration: 20 }
    }],
    defaultWeekdays: [0, 6], // 周末
    parameters: {
      priority: 'low',
      retryOnFailure: true,
      maxRetries: 1
    }
  }
];

// 生成执行历史记录
function generateExecutionHistory() {
  const history = [];
  const now = new Date();
  
  for (let i = 0; i < 30; i++) {
    const schedule = schedules[Math.floor(Math.random() * schedules.length)];
    const timeSlot = schedule.timeSlots[Math.floor(Math.random() * schedule.timeSlots.length)];
    const executionDate = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000);
    
    const [hour, minute] = timeSlot.startTime.split(':').map(Number);
    executionDate.setHours(hour, minute, 0, 0);
    
    const duration = timeSlot.duration + (Math.random() - 0.5) * 5; // ±2.5分钟误差
    const endTime = new Date(executionDate.getTime() + duration * 60 * 1000);
    
    history.push({
      id: `execution_${String(i + 1).padStart(3, '0')}`,
      scheduleId: schedule.id,
      scheduleName: schedule.name,
      slotId: timeSlot.id,
      plannedStartTime: executionDate.toISOString(),
      actualStartTime: new Date(executionDate.getTime() + (Math.random() - 0.5) * 300000).toISOString(), // ±5分钟误差
      plannedEndTime: new Date(executionDate.getTime() + timeSlot.duration * 60 * 1000).toISOString(),
      actualEndTime: endTime.toISOString(),
      plannedDuration: timeSlot.duration,
      actualDuration: Math.round(duration),
      status: Math.random() > 0.1 ? 'completed' : Math.random() > 0.5 ? 'failed' : 'partial',
      statusText: Math.random() > 0.1 ? '完成' : Math.random() > 0.5 ? '失败' : '部分完成',
      deviceId: schedule.deviceIds[0],
      plotId: schedule.plotIds[0],
      zones: timeSlot.zones,
      parameters: {
        flowRate: timeSlot.flowRate + (Math.random() - 0.5) * 10,
        actualPressure: (timeSlot.parameters?.pressure || 2.5) + (Math.random() - 0.5) * 0.5,
        waterUsed: Math.round(timeSlot.flowRate * duration / 60 * 10) / 10,
        efficiency: Math.round((85 + Math.random() * 15) * 10) / 10
      },
      fertilizer: timeSlot.fertilizer.enabled ? {
        formulaId: timeSlot.fertilizer.formulaId,
        concentration: timeSlot.fertilizer.concentration,
        volumeUsed: Math.round(timeSlot.flowRate * duration / 60 * 0.02 * 10) / 10 // 假设2%浓度
      } : null,
      weather: {
        temperature: Math.round((18 + Math.random() * 15) * 10) / 10,
        humidity: Math.round((45 + Math.random() * 40) * 10) / 10,
        condition: ['晴', '多云', '阴', '小雨'][Math.floor(Math.random() * 4)]
      },
      errorMessage: Math.random() > 0.9 ? '设备压力异常' : null,
      retryCount: Math.random() > 0.8 ? Math.floor(Math.random() * 3) : 0,
      createdAt: executionDate.toISOString(),
      updatedAt: endTime.toISOString()
    });
  }
  
  return history.sort((a, b) => new Date(b.actualStartTime) - new Date(a.actualStartTime));
}

module.exports = {
  init() { 
    console.log('定时任务模块初始化完成'); 
  },

  // 获取调度列表
  async list(token, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { type, enabled, farmId, plotId, page = 1, pageSize = 20 } = params;
    
    let filteredSchedules = [...schedules];
    
    // 过滤条件
    if (type && type !== 'all') {
      filteredSchedules = filteredSchedules.filter(schedule => schedule.type === type);
    }
    
    if (enabled !== undefined) {
      filteredSchedules = filteredSchedules.filter(schedule => schedule.enabled === enabled);
    }
    
    if (farmId) {
      filteredSchedules = filteredSchedules.filter(schedule => schedule.farmId === farmId);
    }
    
    if (plotId) {
      filteredSchedules = filteredSchedules.filter(schedule => 
        schedule.plotIds.includes(plotId)
      );
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedSchedules = filteredSchedules.slice(startIndex, endIndex);

    // 增强数据
    const enhancedSchedules = paginatedSchedules.map(schedule => ({
      ...schedule,
      timeDisplay: schedule.timeSlots.map(slot => 
        `${slot.startTime}-${slot.endTime}`
      ).join(', '),
      executionSummary: {
        totalSlots: schedule.timeSlots.length,
        enabledSlots: schedule.timeSlots.filter(slot => slot.enabled).length,
        estimatedDailyDuration: schedule.timeSlots.reduce((sum, slot) => 
          sum + (slot.enabled ? slot.duration : 0), 0
        )
      },
      nextRunTime: getNextRunTime(schedule)
    }));

    return createApiResponse({
      schedules: enhancedSchedules,
      pagination: {
        page,
        pageSize,
        total: filteredSchedules.length,
        totalPages: Math.ceil(filteredSchedules.length / pageSize),
        hasNext: endIndex < filteredSchedules.length,
        hasPrev: page > 1
      },
      summary: {
        total: schedules.length,
        enabled: schedules.filter(s => s.enabled).length,
        disabled: schedules.filter(s => !s.enabled).length,
        irrigation: schedules.filter(s => s.type === 'irrigation').length,
        fertilization: schedules.filter(s => s.type === 'fertilization').length
      }
    });
  },

  // 创建调度
  async create(token, data) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { name, type, plotIds, deviceIds, customWeekdays, timeSlots } = data;

    if (!name || !type || !plotIds || !deviceIds || !timeSlots) {
      return createErrorResponse('缺少必要字段', 400);
    }

    const newSchedule = {
      id: Math.max(...schedules.map(s => s.id)) + 1,
      name,
      type,
      enabled: true,
      farmId: 'farm_001', // 默认农场
      plotIds,
      deviceIds,
      customWeekdays: customWeekdays || [
        { day: 1, label: '一', selected: true },
        { day: 2, label: '二', selected: true },
        { day: 3, label: '三', selected: true },
        { day: 4, label: '四', selected: true },
        { day: 5, label: '五', selected: true },
        { day: 6, label: '六', selected: true },
        { day: 0, label: '日', selected: true }
      ],
      weekdaysText: data.weekdaysText || '每天',
      timeSlots: timeSlots.map((slot, index) => ({
        id: Date.now() + index,
        ...slot,
        enabled: slot.enabled !== false
      })),
      priority: data.priority || 'medium',
      retryOnFailure: data.retryOnFailure !== false,
      maxRetries: data.maxRetries || 2,
      notifyOnCompletion: data.notifyOnCompletion !== false,
      description: data.description || '',
      createdBy: payload.userId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    newSchedule.nextRunTime = getNextRunTime(newSchedule);
    schedules.push(newSchedule);

    return createApiResponse(newSchedule, '调度创建成功');
  },

  // 更新调度
  async update(token, scheduleId, data) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const scheduleIndex = schedules.findIndex(s => s.id == scheduleId);
    if (scheduleIndex === -1) {
      return createErrorResponse('调度不存在', 404);
    }

    const schedule = schedules[scheduleIndex];
    
    // 更新字段
    const updatedSchedule = {
      ...schedule,
      ...data,
      id: schedule.id, // 保持ID不变
      createdBy: schedule.createdBy, // 保持创建者不变
      createdAt: schedule.createdAt, // 保持创建时间不变
      updatedAt: new Date().toISOString()
    };

    updatedSchedule.nextRunTime = getNextRunTime(updatedSchedule);
    schedules[scheduleIndex] = updatedSchedule;

    return createApiResponse(updatedSchedule, '调度更新成功');
  },

  // 删除调度
  async delete(token, scheduleId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const scheduleIndex = schedules.findIndex(s => s.id == scheduleId);
    if (scheduleIndex === -1) {
      return createErrorResponse('调度不存在', 404);
    }

    schedules.splice(scheduleIndex, 1);

    return createApiResponse(null, '调度删除成功');
  },

  // 复制调度
  async duplicate(token, scheduleId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const originalSchedule = schedules.find(s => s.id == scheduleId);
    if (!originalSchedule) {
      return createErrorResponse('调度不存在', 404);
    }

    const duplicatedSchedule = {
      ...originalSchedule,
      id: Math.max(...schedules.map(s => s.id)) + 1,
      name: originalSchedule.name + ' (副本)',
      enabled: false, // 副本默认禁用
      createdBy: payload.userId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    duplicatedSchedule.nextRunTime = getNextRunTime(duplicatedSchedule);
    schedules.push(duplicatedSchedule);

    return createApiResponse(duplicatedSchedule, '调度复制成功');
  },

  // 切换调度开关
  async toggle(token, scheduleId, enabled) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const scheduleIndex = schedules.findIndex(s => s.id == scheduleId);
    if (scheduleIndex === -1) {
      return createErrorResponse('调度不存在', 404);
    }

    schedules[scheduleIndex].enabled = enabled;
    schedules[scheduleIndex].updatedAt = new Date().toISOString();
    schedules[scheduleIndex].nextRunTime = enabled ? getNextRunTime(schedules[scheduleIndex]) : null;

    return createApiResponse({
      scheduleId,
      enabled,
      nextRunTime: schedules[scheduleIndex].nextRunTime
    }, enabled ? '调度已启用' : '调度已禁用');
  },

  // 获取日历数据
  async getCalendarData(token, year, month) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const calendarData = {};
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    for (let date = new Date(firstDay); date <= lastDay; date.setDate(date.getDate() + 1)) {
      const dateStr = date.toISOString().split('T')[0];
      const dayOfWeek = date.getDay();
      
      const daySchedules = [];
      
      schedules.forEach(schedule => {
        if (!schedule.enabled) return;
        
        const weekdayConfig = schedule.customWeekdays.find(w => w.day === dayOfWeek);
        if (weekdayConfig && weekdayConfig.selected) {
          schedule.timeSlots.forEach(slot => {
            if (slot.enabled) {
              daySchedules.push({
                id: `${schedule.id}-${slot.id}`,
                scheduleId: schedule.id,
                scheduleName: schedule.name,
                slotId: slot.id,
                time: slot.startTime,
                duration: slot.duration,
                type: schedule.type,
                zones: slot.zones,
                fertilizer: slot.fertilizer?.enabled || false
              });
            }
          });
        }
      });
      
      if (daySchedules.length > 0) {
        calendarData[dateStr] = daySchedules.sort((a, b) => a.time.localeCompare(b.time));
      }
    }

    return createApiResponse({
      year,
      month,
      calendarData,
      summary: {
        totalDays: Object.keys(calendarData).length,
        totalSchedules: Object.values(calendarData).reduce((sum, day) => sum + day.length, 0)
      }
    });
  },

  // 获取指定日期的调度
  async getDaySchedules(token, date) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const targetDate = new Date(date);
    const dayOfWeek = targetDate.getDay();
    const daySchedules = [];
    
    schedules.forEach(schedule => {
      if (!schedule.enabled) return;
      
      const weekdayConfig = schedule.customWeekdays.find(w => w.day === dayOfWeek);
      if (weekdayConfig && weekdayConfig.selected) {
        schedule.timeSlots.forEach(slot => {
          if (slot.enabled) {
            daySchedules.push({
              id: `${schedule.id}-${slot.id}`,
              scheduleId: schedule.id,
              scheduleName: schedule.name,
              slotId: slot.id,
              startTime: slot.startTime,
              endTime: slot.endTime,
              duration: slot.duration,
              flowRate: slot.flowRate,
              zones: slot.zones,
              zoneIds: slot.zoneIds,
              type: schedule.type,
              priority: schedule.priority,
              fertilizer: slot.fertilizer,
              parameters: slot.parameters,
              enabled: slot.enabled,
              plotIds: schedule.plotIds,
              deviceIds: schedule.deviceIds
            });
          }
        });
      }
    });

    return createApiResponse({
      date,
      dayOfWeek,
      schedules: daySchedules.sort((a, b) => a.startTime.localeCompare(b.startTime)),
      summary: {
        totalSchedules: daySchedules.length,
        totalDuration: daySchedules.reduce((sum, s) => sum + s.duration, 0),
        irrigationCount: daySchedules.filter(s => s.type === 'irrigation').length,
        fertilizationCount: daySchedules.filter(s => s.fertilizer?.enabled).length
      }
    });
  },

  // 获取调度模板
  async getTemplates(token) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    return createApiResponse({
      templates: scheduleTemplates,
      categories: {
        irrigation: scheduleTemplates.filter(t => t.category === 'irrigation'),
        fertilization: scheduleTemplates.filter(t => t.category === 'fertilization')
      }
    });
  },

  // 获取执行历史
  async getExecutionHistory(token, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { scheduleId, status, startDate, endDate, page = 1, pageSize = 20 } = params;
    
    let history = generateExecutionHistory();
    
    // 过滤条件
    if (scheduleId) {
      history = history.filter(h => h.scheduleId == scheduleId);
    }
    
    if (status && status !== 'all') {
      history = history.filter(h => h.status === status);
    }
    
    // 日期过滤
    if (startDate || endDate) {
      const start = startDate ? new Date(startDate) : new Date('1900-01-01');
      const end = endDate ? new Date(endDate) : new Date('2100-12-31');
      
      history = history.filter(h => {
        const hDate = new Date(h.actualStartTime);
        return hDate >= start && hDate <= end;
      });
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedHistory = history.slice(startIndex, endIndex);

    return createApiResponse({
      history: paginatedHistory,
      pagination: {
        page,
        pageSize,
        total: history.length,
        totalPages: Math.ceil(history.length / pageSize),
        hasNext: endIndex < history.length,
        hasPrev: page > 1
      },
      statistics: {
        totalExecutions: history.length,
        successRate: Math.round((history.filter(h => h.status === 'completed').length / history.length) * 100 * 10) / 10,
        avgEfficiency: Math.round(history.reduce((sum, h) => sum + h.parameters.efficiency, 0) / history.length * 10) / 10,
        totalWaterUsed: Math.round(history.reduce((sum, h) => sum + h.parameters.waterUsed, 0) * 10) / 10,
        todayExecutions: history.filter(h => {
          const today = new Date().toISOString().split('T')[0];
          const hDate = new Date(h.actualStartTime).toISOString().split('T')[0];
          return hDate === today;
        }).length
      }
    });
  }
};