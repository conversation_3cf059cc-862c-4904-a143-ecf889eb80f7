<!--水肥一体机控制中心-->
<view class="page-container">
  <view class="container">
    
    <!-- 设备状态概览 -->
    <view class="card">
      <view class="card-title">设备状态总览</view>
      <view class="device-status-grid">
        <view class="status-item">
          <view class="status-icon online">
            <text class="icon-text">💧</text>
          </view>
          <view class="status-info">
            <text class="status-name">灌溉系统</text>
            <text class="status-value {{irrigationStatus.class}}">{{irrigationStatus.text}}</text>
          </view>
        </view>
        
        <view class="status-item">
          <view class="status-icon {{fertilizerStatus.class}}">
            <text class="icon-text">🌱</text>
          </view>
          <view class="status-info">
            <text class="status-name">施肥系统</text>
            <text class="status-value {{fertilizerStatus.class}}">{{fertilizerStatus.text}}</text>
          </view>
        </view>
        
        <view class="status-item">
          <view class="status-icon">
            <text class="icon-text">📊</text>
          </view>
          <view class="status-info">
            <text class="status-name">水位状态</text>
            <text class="status-value">{{waterLevel}}%</text>
          </view>
        </view>
        
        <view class="status-item">
          <view class="status-icon">
            <text class="icon-text">🔋</text>
          </view>
          <view class="status-info">
            <text class="status-name">肥料余量</text>
            <text class="status-value">{{fertilizerLevel}}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快速控制面板 -->
    <view class="card">
      <view class="card-title">快速控制</view>
      <view class="quick-control-panel">
        <view class="control-row">
          <button class="control-btn primary {{irrigationStatus.class === 'status-online' ? 'active' : ''}}" 
                  bindtap="toggleIrrigation">
            <text class="btn-icon">💧</text>
            <text class="btn-text">{{irrigationStatus.action}}</text>
          </button>
          <button class="control-btn secondary {{fertilizerStatus.class === 'status-online' ? 'active' : ''}}" 
                  bindtap="toggleFertilizer">
            <text class="btn-icon">🌱</text>
            <text class="btn-text">{{fertilizerStatus.action}}</text>
          </button>
        </view>
        <view class="control-row">
          <button class="control-btn warning" bindtap="emergencyStop">
            <text class="btn-icon">⛔</text>
            <text class="btn-text">紧急停止</text>
          </button>
          <button class="control-btn" bindtap="systemFlush">
            <text class="btn-icon">🚿</text>
            <text class="btn-text">系统冲洗</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 当前运行参数 -->
    <view class="card" wx:if="{{isRunning}}">
      <view class="card-title">当前运行参数</view>
      <view class="running-params">
        <view class="param-item">
          <text class="param-label">流量</text>
          <text class="param-value">{{currentFlow}} L/min</text>
        </view>
        <view class="param-item">
          <text class="param-label">压力</text>
          <text class="param-value">{{currentPressure}} Bar</text>
        </view>
        <view class="param-item">
          <text class="param-label">运行时长</text>
          <text class="param-value">{{runningTime}}</text>
        </view>
        <view class="param-item">
          <text class="param-label">EC值</text>
          <text class="param-value">{{currentEC}} mS/cm</text>
        </view>
      </view>
      <view class="progress-info">
        <text class="progress-label">执行进度</text>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progress}}%"></view>
        </view>
        <text class="progress-text">{{progress}}%</text>
      </view>
    </view>

    <!-- 功能入口 -->
    <view class="card">
      <view class="card-title">功能管理</view>
      <view class="function-grid">
        <view class="function-item" bindtap="goToIrrigation">
          <view class="function-icon primary-bg">
            <text class="icon-text">💧</text>
          </view>
          <text class="function-name">灌溉控制</text>
          <text class="function-desc">手动控制、流量调节</text>
        </view>
        
        <view class="function-item" bindtap="goToFertilizer">
          <view class="function-icon secondary-bg">
            <text class="icon-text">🌱</text>
          </view>
          <text class="function-name">施肥管理</text>
          <text class="function-desc">配方设置、浓度控制</text>
        </view>
        
        <view class="function-item" bindtap="goToSchedule">
          <view class="function-icon">
            <text class="icon-text">⏰</text>
          </view>
          <text class="function-name">定时计划</text>
          <text class="function-desc">自动灌溉、定时施肥</text>
        </view>
        
        <view class="function-item" bindtap="goToFormula">
          <view class="function-icon warning-bg">
            <text class="icon-text">🧪</text>
          </view>
          <text class="function-name">配方管理</text>
          <text class="function-desc">NPK配比、作物配方</text>
        </view>
        
        <view class="function-item" bindtap="goToLogs">
          <view class="function-icon">
            <text class="icon-text">📋</text>
          </view>
          <text class="function-name">运行记录</text>
          <text class="function-desc">操作日志、统计报告</text>
        </view>
        
        <view class="function-item" bindtap="goToConfig">
          <view class="function-icon">
            <text class="icon-text">⚙️</text>
          </view>
          <text class="function-name">设备配置</text>
          <text class="function-desc">参数设置、系统维护</text>
        </view>
      </view>
    </view>

    <!-- 今日统计 -->
    <view class="card">
      <view class="card-title">今日统计</view>
      <view class="today-stats">
        <view class="stat-item">
          <text class="stat-value">{{todayStats.irrigationTime}}</text>
          <text class="stat-label">灌溉时长(分钟)</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{todayStats.waterUsage}}</text>
          <text class="stat-label">用水量(L)</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{todayStats.fertilizerUsage}}</text>
          <text class="stat-label">施肥量(L)</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{todayStats.operationCount}}</text>
          <text class="stat-label">操作次数</text>
        </view>
      </view>
    </view>

  </view>
</view>

<!-- 加载提示 -->
<view class="loading-overlay" wx:if="{{loading}}">
  <view class="loading-content">
    <text>操作执行中...</text>
  </view>
</view>