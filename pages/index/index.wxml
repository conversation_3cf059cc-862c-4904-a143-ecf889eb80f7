<!--智慧农业首页-->
<view class="index-container">
  
  <!-- 农场头部卡片（包含天气信息） -->
  <view class="farm-weather-card">
    <view class="farm-info">
      <view class="farm-main-info">
        <text class="farm-name">{{farmData.name }}</text>
        <text class="farm-area">{{farmData.totalArea }}亩</text>
      </view>
    </view>
    
    <view class="weather-info" bindtap="refreshWeather" bindlongpress="testWeatherAPI">
      <view class="weather-animation-container">
        <canvas
          id="weatherCanvas"
          type="2d"
          class="weather-canvas"
          style="width: 120rpx; height: 80rpx;">
        </canvas>
      </view>
      <view class="weather-details">
        <view class="weather-main">
          <text class="weather-temp">{{weatherData.temperature }}°C</text>
          <text class="weather-desc">{{weatherData.weather }}</text>
        </view>
        <view class="weather-location">
          <text class="location-icon">📍</text>
          <text class="location-text">{{weatherData.location }}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 设备状态概览 -->
  <view class="device-status-card">
    <view class="card-header">
      <text class="card-title">设备状态</text>
    </view>
    
    <view class="device-grid">
      <view class="device-item">
        <view class="device-icon water-icon"></view>
        <view class="device-info">
          <text class="device-type-name">水肥设备</text>
          <text class="device-count">2/2 在线</text>
        </view>
      </view>
      
      <view class="device-item">
        <view class="device-icon sensor-icon"></view>
        <view class="device-info">
          <text class="device-type-name">监测设备</text>
          <text class="device-count">1/2 在线</text>
        </view>
      </view>
      
      <view class="device-item">
        <view class="device-icon warning-icon"></view>
        <view class="device-info">
          <text class="device-type-name">异常设备</text>
          <text class="device-count">1 台</text>
        </view>
      </view>
      
      <view class="device-item" bind:tap="viewAllDevices">
        <view class="device-icon manage-icon"></view>
        <view class="device-info">
          <text class="device-type-name">设备管理</text>
          <text class="device-count">查看全部</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 今日数据概览 -->
  <view class="today-summary-card">
    <view class="card-header">
      <text class="card-title">今日概览</text>
    </view>
    <view class="summary-grid">
      <view class="summary-item">
        <text class="summary-label">灌溉时长</text>
        <text class="summary-value primary">45分钟</text>
      </view>
      
      <view class="summary-item">
        <text class="summary-label">施肥量</text>
        <text class="summary-value secondary">12.5L</text>
      </view>
      
      <view class="summary-item" bind:tap="viewAlarm">
        <text class="summary-label">预警次数</text>
        <text class="summary-value warning">2次</text>
      </view>
      
      <view class="summary-item">
        <text class="summary-label">平均湿度</text>
        <text class="summary-value normal">63%</text>
      </view>
    </view>
  </view>

  <!-- 实时环境数据 -->
  <view class="env-data-card">
    <view class="card-header">
      <text class="card-title">实时环境</text>
    </view>
    <view class="env-data-grid">
      <view class="env-item">
        <text class="env-label">土壤湿度</text>
        <text class="env-value">65%</text>
      </view>
      
      <view class="env-item">
        <text class="env-label">土壤温度</text>
        <text class="env-value">23°C</text>
      </view>
      
      <view class="env-item">
        <text class="env-label">PH值</text>
        <text class="env-value">6.8</text>
      </view>
      
      <view class="env-item">
        <text class="env-label">EC值</text>
        <text class="env-value">1.2 mS/cm</text>
      </view>
    </view>
  </view>

  <!-- 今日任务 -->
  <view class="task-card">
    <view class="card-header">
      <text class="card-title">今日任务</text>
    </view>
    <view class="task-list">
      <view class="task-item">
        <text class="task-name">早晨灌溉</text>
        <text class="task-time">06:00</text>
        <text class="task-status completed">已完成</text>
      </view>
      
      <view class="task-item">
        <text class="task-name">追肥计划</text>
        <text class="task-time">14:00</text>
        <text class="task-status pending">待执行</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions-card">
    <view class="card-header">
      <text class="card-title">快捷操作</text>
    </view>
    <view class="actions-grid">
      <view class="action-item" bind:tap="quickIrrigation">
        <view class="action-icon-wrapper">
          <text class="action-icon-emoji">💧</text>
        </view>
        <text class="action-text">立即灌溉</text>
      </view>
      
      <view class="action-item" bind:tap="quickFertilizer">
        <view class="action-icon-wrapper">
          <text class="action-icon-emoji">🌱</text>
        </view>
        <text class="action-text">立即施肥</text>
      </view>
      
      <view class="action-item" bind:tap="viewHistory">
        <view class="action-icon-wrapper">
          <text class="action-icon-emoji">📊</text>
        </view>
        <text class="action-text">历史记录</text>
      </view>
      
      <view class="action-item" bind:tap="viewAlarm">
        <view class="action-icon-wrapper">
          <text class="action-icon-emoji">⚠️</text>
        </view>
        <text class="action-text">预警中心</text>
      </view>
    </view>
  </view>

  <!-- 消息通知中心 -->
  <view class="notification-card">
    <view class="card-header">
      <text class="card-title">消息中心</text>
      <view class="notification-badge">2</view>
    </view>
    
    <view class="notification-list">
      <view class="notification-item warning" bind:tap="viewNotification" data-id="1">
        <view class="notification-icon-wrapper">
          <text class="notification-icon-emoji">⚠️</text>
        </view>
        <view class="notification-content">
          <text class="notification-title">土壤湿度预警</text>
          <text class="notification-desc">1号地块土壤湿度过低，建议立即灌溉</text>
          <text class="notification-time">2小时前</text>
        </view>
        <view class="notification-dot"></view>
      </view>
      
      <view class="notification-item info" bind:tap="viewNotification" data-id="2">
        <view class="notification-icon-wrapper">
          <text class="notification-icon-emoji">🔔</text>
        </view>
        <view class="notification-content">
          <text class="notification-title">系统更新通知</text>
          <text class="notification-desc">系统版本已更新至v1.2.0，新增智能预警功能</text>
          <text class="notification-time">1天前</text>
        </view>
      </view>
      
      <view class="notification-item success" bind:tap="viewNotification" data-id="3">
        <view class="notification-icon-wrapper">
          <text class="notification-icon-emoji">✅</text>
        </view>
        <view class="notification-content">
          <text class="notification-title">任务完成提醒</text>
          <text class="notification-desc">早晨灌溉任务已完成，用水量45L</text>
          <text class="notification-time">3小时前</text>
        </view>
        <view class="notification-dot"></view>
      </view>
      
      <view class="view-all-btn" bind:tap="viewAllNotifications">
        <text class="view-all-text">查看全部消息</text>
      </view>
    </view>
  </view>


</view>