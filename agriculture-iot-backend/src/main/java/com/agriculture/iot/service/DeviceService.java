package com.agriculture.iot.service;

import com.agriculture.iot.entity.Device;
import com.agriculture.iot.entity.DeviceType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 设备服务接口
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface DeviceService extends IService<Device> {

    /**
     * 分页获取设备列表
     * 
     * @param page 分页对象
     * @param name 设备名称
     * @param deviceTypeId 设备类型ID
     * @param plotId 地块ID
     * @param onlineStatus 在线状态
     * @param status 设备状态
     * @return 设备列表
     */
    IPage<Device> getDeviceList(Page<Device> page, String name, Long deviceTypeId, Long plotId, 
                               Integer onlineStatus, Integer status);

    /**
     * 根据ID获取设备详情
     * 
     * @param id 设备ID
     * @return 设备详情
     */
    Device getDeviceById(Long id);

    /**
     * 创建设备
     * 
     * @param device 设备信息
     * @return 创建的设备
     */
    Device createDevice(Device device);

    /**
     * 更新设备
     * 
     * @param device 设备信息
     * @return 更新的设备
     */
    Device updateDevice(Device device);

    /**
     * 删除设备
     * 
     * @param id 设备ID
     * @return 删除结果
     */
    boolean deleteDevice(Long id);

    /**
     * 设备控制
     * 
     * @param deviceId 设备ID
     * @param controlRequest 控制请求
     * @return 控制结果
     */
    Map<String, Object> controlDevice(Long deviceId, Device.ControlRequest controlRequest);

    /**
     * 获取设备实时状态
     * 
     * @param deviceId 设备ID
     * @return 状态信息
     */
    Device.StatusInfo getDeviceStatus(Long deviceId);

    /**
     * 获取设备类型列表
     * 
     * @return 设备类型列表
     */
    List<DeviceType> getDeviceTypes();

    /**
     * 绑定设备到地块
     * 
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @return 绑定结果
     */
    boolean bindDeviceToPlot(Long deviceId, Long plotId);

    /**
     * 解绑设备
     * 
     * @param deviceId 设备ID
     * @return 解绑结果
     */
    boolean unbindDevice(Long deviceId);

    /**
     * 获取设备操作记录
     * 
     * @param deviceId 设备ID
     * @param page 分页对象
     * @param operationType 操作类型
     * @return 操作记录
     */
    IPage<Object> getDeviceLogs(Long deviceId, Page<Object> page, String operationType);

    /**
     * 获取设备统计信息
     * 
     * @param deviceId 设备ID
     * @return 统计信息
     */
    Device.Statistics getDeviceStatistics(Long deviceId);

    /**
     * 批量更新设备状态
     * 
     * @param deviceIds 设备ID列表
     * @param status 状态
     * @return 更新结果
     */
    boolean batchUpdateDeviceStatus(List<Long> deviceIds, Integer status);

    /**
     * 设备健康检查
     * 
     * @param deviceId 设备ID
     * @return 健康信息
     */
    Device.HealthInfo deviceHealthCheck(Long deviceId);

    /**
     * 获取设备配置信息
     * 
     * @param deviceId 设备ID
     * @return 配置信息
     */
    Device.ConfigInfo getDeviceConfig(Long deviceId);

    /**
     * 更新设备配置
     * 
     * @param deviceId 设备ID
     * @param configInfo 配置信息
     * @return 更新结果
     */
    boolean updateDeviceConfig(Long deviceId, Device.ConfigInfo configInfo);
}