<!--地块配置页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">
        {{mode === 'add' ? '新增地块' : mode === 'edit' ? '编辑地块' : '地块详情'}}
      </text>
    </view>

    <!-- 基本信息 -->
    <view class="config-card">
      <view class="card-title">基本信息</view>
      
      <view class="form-item">
        <text class="form-label">地块名称 <text class="required">*</text></text>
        <input class="form-input" 
               value="{{plotData.name}}"
               bindinput="onPlotNameInput"
               placeholder="请输入地块名称"
               disabled="{{mode === 'view'}}" />
      </view>
      
      <view class="form-item">
        <text class="form-label">地块类型 <text class="required">*</text></text>
        <picker range="{{plotTypes}}" 
                range-key="label"
                value="{{plotData.plotTypeIndex || 0}}"
                bindchange="onPlotTypeChange"
                disabled="{{mode === 'view'}}">
          <view class="picker-field">
            <view class="picker-content">
              <text class="picker-icon">{{plotTypes[plotData.plotTypeIndex || 0].icon}}</text>
              <text class="picker-value">{{plotTypes[plotData.plotTypeIndex || 0].label}}</text>
            </view>
            <text class="picker-arrow" wx:if="{{mode !== 'view'}}">></text>
          </view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="form-label">面积 <text class="required">*</text></text>
        <view class="input-with-unit">
          <input class="form-input" 
                 type="digit"
                 value="{{plotData.area}}"
                 bindinput="onAreaInput"
                 placeholder="0"
                 disabled="{{mode === 'view'}}" />
          <text class="unit-label">亩</text>
        </view>
      </view>
      
      <view class="form-item">
        <text class="form-label">位置 <text class="required">*</text></text>
        <input class="form-input" 
               value="{{plotData.location}}"
               bindinput="onLocationInput"
               placeholder="请输入地块位置，如：1号大棚东侧"
               disabled="{{mode === 'view'}}" />
      </view>
    </view>

    <!-- 地块绘制 -->
    <view wx:if="{{mode !== 'view'}}" class="config-card">
      <view class="card-title">地块绘制</view>
      
      <view class="drawing-section">
        <button class="draw-btn" bindtap="openDrawingPage">
          <text class="btn-icon">📐</text>
          <text class="btn-text">进入地块绘制</text>
        </button>
        
        <view wx:if="{{plotData.polygonPath.length > 0}}" class="polygon-info">
          <view class="polygon-item">
            <text class="polygon-label">绘制状态：</text>
            <text class="polygon-value completed">✅ 已完成绘制</text>
          </view>
          <view class="polygon-item">
            <text class="polygon-label">绘制点数：</text>
            <text class="polygon-value">{{plotData.polygonPath.length}} 个边界点</text>
          </view>
          <view wx:if="{{plotData.area}}" class="polygon-item">
            <text class="polygon-label">计算面积：</text>
            <text class="polygon-value">{{plotData.area}} 亩</text>
          </view>
        </view>
        
        <view wx:if="{{plotData.polygonPath.length === 0}}" class="polygon-info">
          <view class="polygon-item">
            <text class="polygon-label">绘制状态：</text>
            <text class="polygon-value pending">⏳ 待绘制</text>
          </view>
        </view>
      </view>
    </view>


    <!-- 地块地图显示（查看模式） -->
    <view wx:if="{{mode === 'view' && plotData.polygonPath.length > 0}}" class="config-card">
      <view class="card-title">地块位置</view>
      
      <view class="view-drawing-section">
        <view class="drawing-summary">
          <view class="summary-item">
            <text class="summary-label">绘制面积：</text>
            <text class="summary-value">{{plotData.area}} 亩</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">边界点数：</text>
            <text class="summary-value">{{plotData.polygonPath.length}} 个点</text>
          </view>
        </view>
        
        <button class="view-map-btn" bindtap="viewOnMap">
          <text class="btn-icon">🗺️</text>
          <text class="btn-text">查看地块位置</text>
        </button>
      </view>
    </view>


    <!-- 灌溉配置 -->
    <view class="config-card">
      <view class="card-title">灌溉配置</view>
      
      <view class="form-item">
        <text class="form-label">灌溉方式</text>
        <picker range="{{irrigationTypes}}" 
                range-key="label"
                value="{{plotData.irrigationIndex || 0}}"
                bindchange="onIrrigationChange"
                disabled="{{mode === 'view'}}">
          <view class="picker-field">
            <text class="picker-value">{{plotData.irrigationType || '请选择灌溉方式'}}</text>
            <text class="picker-arrow" wx:if="{{mode !== 'view'}}">></text>
          </view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="form-label">地形坡度</text>
        <picker range="{{slopeOptions}}" 
                range-key="label"
                value="{{plotData.slopeIndex || 0}}"
                bindchange="onSlopeChange"
                disabled="{{mode === 'view'}}">
          <view class="picker-field">
            <text class="picker-value">{{plotData.slope || '请选择地形坡度'}}</text>
            <text class="picker-arrow" wx:if="{{mode !== 'view'}}">></text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 种植作物配置 -->
    <view class="config-card">
      <view class="card-title">种植作物</view>
      
      <view class="form-item">
        <text class="form-label">当前作物</text>
        <picker range="{{cropOptions}}" 
                value="{{plotData.currentCrop.cropIndex || 0}}"
                bindchange="onCropChange"
                disabled="{{mode === 'view'}}">
          <view class="picker-field">
            <text class="picker-value">{{plotData.currentCrop.name || '请选择作物'}}</text>
            <text class="picker-arrow" wx:if="{{mode !== 'view'}}">></text>
          </view>
        </picker>
      </view>
      
      <view wx:if="{{plotData.currentCrop.name && plotData.currentCrop.name !== '未种植'}}" class="crop-details">
        <view class="form-item">
          <text class="form-label">种植日期</text>
          <picker mode="date" 
                  value="{{plotData.currentCrop.plantDate}}"
                  bindchange="onPlantDateChange"
                  disabled="{{mode === 'view'}}">
            <view class="picker-field">
              <text class="picker-value">{{plotData.currentCrop.plantDate || '请选择种植日期'}}</text>
              <text class="picker-arrow" wx:if="{{mode !== 'view'}}">></text>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">预计收获日期</text>
          <picker mode="date" 
                  value="{{plotData.currentCrop.harvestDate}}"
                  bindchange="onHarvestDateChange"
                  disabled="{{mode === 'view'}}">
            <view class="picker-field">
              <text class="picker-value">{{plotData.currentCrop.harvestDate || '请选择收获日期'}}</text>
              <text class="picker-arrow" wx:if="{{mode !== 'view'}}">></text>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">生长阶段</text>
          <picker range="{{growthStages}}" 
                  range-key="label"
                  value="{{plotData.currentCrop.stageIndex || 0}}"
                  bindchange="onGrowthStageChange"
                  disabled="{{mode === 'view'}}">
            <view class="picker-field">
              <text class="picker-value">{{plotData.currentCrop.stage || '请选择生长阶段'}}</text>
              <text class="picker-arrow" wx:if="{{mode !== 'view'}}">></text>
            </view>
          </picker>
        </view>
        
        <view wx:if="{{plotData.currentCrop.plantDate && plotData.currentCrop.harvestDate}}" class="growth-progress">
          <text class="progress-label">生长进度</text>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{plotData.currentCrop.progress || 0}}%"></view>
          </view>
          <text class="progress-text">{{plotData.currentCrop.progress || 0}}% 完成</text>
        </view>
      </view>
      
      <view wx:if="{{plotData.currentCrop.name === '未种植' || !plotData.currentCrop.name}}" class="empty-crop">
        <text class="empty-icon">🌱</text>
        <text class="empty-text">该地块暂未种植作物</text>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="config-card">
      <view class="card-title">备注信息</view>
      
      <view class="form-item">
        <text class="form-label">地块描述</text>
        <textarea class="form-textarea" 
                  value="{{plotData.description}}"
                  bindinput="onDescriptionInput"
                  placeholder="请输入地块相关描述信息..."
                  maxlength="200"
                  disabled="{{mode === 'view'}}" />
      </view>
    </view>

    <!-- 关联设备 (查看模式时显示) -->
    <view wx:if="{{mode === 'view' && relatedDevices.length > 0}}" class="config-card">
      <view class="card-title">关联设备</view>
      
      <view class="device-list">
        <view class="device-item" wx:for="{{relatedDevices}}" wx:key="id">
          <view class="device-info">
            <text class="device-name">{{item.name}}</text>
            <text class="device-type">{{item.type}}</text>
          </view>
          <view class="device-status {{item.status}}">
            <text class="status-dot"></text>
            <text class="status-text">{{item.status === 'online' ? '在线' : '离线'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button wx:if="{{mode === 'add' || mode === 'edit'}}" 
              class="btn btn-secondary" 
              bindtap="cancelEdit">
        取消
      </button>
      <button wx:if="{{mode === 'add' || mode === 'edit'}}" 
              class="btn btn-primary" 
              bindtap="savePlot">
        {{mode === 'add' ? '创建地块' : '保存修改'}}
      </button>
      <button wx:if="{{mode === 'view'}}" 
              class="btn btn-primary" 
              bindtap="editPlot">
        编辑地块
      </button>
    </view>

  </view>
</view>