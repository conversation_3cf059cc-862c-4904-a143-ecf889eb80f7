<!--高级灌溉调度页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 日历视图头部 -->
    <view class="card">
      <view class="calendar-header">
        <view class="month-selector">
          <button class="nav-btn" bindtap="previousMonth">
            ‹
          </button>
          <text class="month-text">{{currentMonth}}</text>
          <button class="nav-btn" bindtap="nextMonth">
            ›
          </button>
        </view>
        <view class="view-toggle">
          <button class="toggle-btn {{viewMode === 'calendar' ? 'active' : ''}}" 
                  bindtap="switchViewMode" data-mode="calendar">
            日历
          </button>
          <button class="toggle-btn {{viewMode === 'list' ? 'active' : ''}}" 
                  bindtap="switchViewMode" data-mode="list">
            列表
          </button>
        </view>
      </view>
    </view>

    <!-- 日历视图 -->
    <view class="card" wx:if="{{viewMode === 'calendar'}}">
      <view class="calendar-grid">
        <!-- 星期标题 -->
        <view class="week-header">
          <view class="week-day" wx:for="{{weekDays}}" wx:key="*this">
            {{item}}
          </view>
        </view>
        
        <!-- 日历日期 -->
        <view class="calendar-body">
          <view class="date-cell {{item.class}}" 
                wx:for="{{calendarDays}}" wx:key="date"
                bindtap="selectDate" data-date="{{item.date}}">
            <text class="date-number">{{item.day}}</text>
            <view class="schedule-indicators" wx:if="{{item.schedules.length > 0}}">
              <view class="indicator {{schedule.type}}" 
                    wx:for="{{item.schedules}}" wx:key="id" wx:for-item="schedule">
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 选中日期的计划详情 -->
    <view class="card" wx:if="{{selectedDate && viewMode === 'calendar'}}">
      <view class="date-detail-header">
        <text class="selected-date">{{selectedDateText}} 的灌溉计划</text>
        <button class="btn btn-small btn-primary" bindtap="addDaySchedule">
          ➕ 添加
        </button>
      </view>
      
      <view class="day-schedules">
        <view class="schedule-time-slot" wx:for="{{daySchedules}}" wx:key="id">
          <view class="time-slot-header">
            <text class="slot-time">{{item.startTime}} - {{item.endTime}}</text>
            <view class="slot-actions">
              <switch checked="{{item.enabled}}" bindchange="toggleTimeSlot" data-id="{{item.id}}"/>
              <button class="btn btn-mini btn-outline" bindtap="editTimeSlot" data-id="{{item.id}}">
                编辑
              </button>
              <button class="btn btn-mini btn-danger" bindtap="deleteTimeSlot" data-id="{{item.id}}">
                删除
              </button>
            </view>
          </view>
          <view class="slot-details">
            <view class="detail-item">
              <text class="detail-label">时长:</text>
              <text class="detail-value">{{item.duration}}分钟</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">流量:</text>
              <text class="detail-value">{{item.flowRate}}L/min</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">区域:</text>
              <text class="detail-value">{{item.zones}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">重复:</text>
              <text class="detail-value">{{item.repeatText}}</text>
            </view>
          </view>
        </view>
        
        <view class="no-schedules" wx:if="{{daySchedules.length === 0}}">
          <text class="text-muted">当天暂无灌溉计划</text>
        </view>
      </view>
    </view>

    <!-- 列表视图 -->
    <view class="card" wx:if="{{viewMode === 'list'}}">
      <view class="card-title">
        <text>所有灌溉计划</text>
        <button class="btn btn-small btn-primary" bindtap="showAddScheduleModal">
          ➕ 新增计划
        </button>
      </view>
      
      <view class="schedule-list">
        <view class="schedule-item" wx:for="{{allSchedules}}" wx:key="id">
          <view class="schedule-header">
            <view class="schedule-info">
              <text class="schedule-name">{{item.name}}</text>
              <text class="schedule-time">{{item.timeDisplay}}</text>
            </view>
            <switch checked="{{item.enabled}}" bindchange="toggleSchedule" data-id="{{item.id}}"/>
          </view>
          <view class="schedule-details">
            <view class="detail-row">
              <text class="detail-label">执行日期:</text>
              <text class="detail-value">{{item.weekdaysText}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">时段:</text>
              <text class="detail-value">{{item.timeSlots.length}}个时段</text>
            </view>
          </view>
          <view class="schedule-actions">
            <button class="btn btn-small btn-outline" bindtap="editSchedule" data-id="{{item.id}}">
              编辑
            </button>
            <button class="btn btn-small btn-info" bindtap="duplicateSchedule" data-id="{{item.id}}">
              复制
            </button>
            <button class="btn btn-small btn-danger" bindtap="deleteSchedule" data-id="{{item.id}}">
              删除
            </button>
          </view>
        </view>
        
        <view class="no-schedule" wx:if="{{allSchedules.length === 0}}">
          <text class="text-muted">暂无灌溉计划</text>
        </view>
      </view>
    </view>


    <!-- 快速模板 -->
    <view class="card">
      <view class="card-title">快速模板</view>
      <view class="template-grid">
        <view class="template-item" wx:for="{{scheduleTemplates}}" wx:key="id"
              bindtap="applyTemplate" data-template="{{item.id}}">
          <view class="template-icon">{{item.icon}}</view>
          <text class="template-name">{{item.name}}</text>
          <text class="template-desc">{{item.description}}</text>
        </view>
      </view>
    </view>

  </view>
</view>

