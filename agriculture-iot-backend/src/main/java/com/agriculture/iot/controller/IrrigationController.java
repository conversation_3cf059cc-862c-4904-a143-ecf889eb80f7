package com.agriculture.iot.controller;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.IrrigationRecord;
import com.agriculture.iot.entity.IrrigationSchedule;
import com.agriculture.iot.service.IrrigationService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 灌溉控制器
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Tag(name = "灌溉控制管理", description = "灌溉设备的控制、计划管理和记录查询")
@RestController
@RequestMapping("/api/irrigation")
@RequiredArgsConstructor
public class IrrigationController {

    private final IrrigationService irrigationService;

    @Operation(summary = "开始灌溉", description = "启动指定设备的灌溉操作")
    @PostMapping("/start")
    public Result<Map<String, Object>> startIrrigation(@Valid @RequestBody IrrigationRecord.StartRequest startRequest) {
        Map<String, Object> result = irrigationService.startIrrigation(startRequest);
        if (result == null) {
            return Result.error("灌溉启动失败");
        }
        return Result.success("灌溉启动成功", result);
    }

    @Operation(summary = "停止灌溉", description = "停止指定设备的灌溉操作")
    @PostMapping("/stop")
    public Result<Map<String, Object>> stopIrrigation(@Valid @RequestBody IrrigationRecord.StopRequest stopRequest) {
        Map<String, Object> result = irrigationService.stopIrrigation(stopRequest);
        if (result == null) {
            return Result.error("灌溉停止失败");
        }
        return Result.success("灌溉停止成功", result);
    }

    @Operation(summary = "获取灌溉记录列表", description = "分页获取灌溉记录")
    @GetMapping("/records")
    public Result<IPage<IrrigationRecord>> getIrrigationRecords(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "开始时间") @RequestParam(required = false) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) LocalDateTime endTime) {
        
        Page<IrrigationRecord> page = new Page<>(current, size);
        IPage<IrrigationRecord> result = irrigationService.getIrrigationRecords(page, deviceId, plotId, startTime, endTime);
        return Result.success(result);
    }

    @Operation(summary = "获取灌溉状态", description = "获取设备的实时灌溉状态")
    @GetMapping("/status")
    public Result<IrrigationRecord.StatusInfo> getIrrigationStatus(
            @Parameter(description = "设备ID") @RequestParam Long deviceId) {
        
        IrrigationRecord.StatusInfo statusInfo = irrigationService.getIrrigationStatus(deviceId);
        if (statusInfo == null) {
            return Result.notFound("设备不存在或无灌溉状态");
        }
        return Result.success(statusInfo);
    }

    @Operation(summary = "获取灌溉计划列表", description = "分页获取灌溉计划")
    @GetMapping("/schedules")
    public Result<IPage<IrrigationSchedule>> getIrrigationSchedules(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        Page<IrrigationSchedule> page = new Page<>(current, size);
        IPage<IrrigationSchedule> result = irrigationService.getIrrigationSchedules(page, deviceId, plotId, status);
        return Result.success(result);
    }

    @Operation(summary = "创建灌溉计划", description = "创建新的灌溉计划")
    @PostMapping("/schedules")
    public Result<IrrigationSchedule> createIrrigationSchedule(@Valid @RequestBody IrrigationSchedule schedule) {
        IrrigationSchedule createdSchedule = irrigationService.createIrrigationSchedule(schedule);
        if (createdSchedule == null) {
            return Result.error("灌溉计划创建失败");
        }
        return Result.success("灌溉计划创建成功", createdSchedule);
    }

    @Operation(summary = "更新灌溉计划", description = "更新现有的灌溉计划")
    @PutMapping("/schedules/{id}")
    public Result<IrrigationSchedule> updateIrrigationSchedule(
            @Parameter(description = "计划ID") @PathVariable Long id,
            @Valid @RequestBody IrrigationSchedule schedule) {
        
        schedule.setId(id);
        IrrigationSchedule updatedSchedule = irrigationService.updateIrrigationSchedule(schedule);
        if (updatedSchedule == null) {
            return Result.notFound("灌溉计划不存在");
        }
        return Result.success("灌溉计划更新成功", updatedSchedule);
    }

    @Operation(summary = "删除灌溉计划", description = "删除指定的灌溉计划")
    @DeleteMapping("/schedules/{id}")
    public Result<Void> deleteIrrigationSchedule(
            @Parameter(description = "计划ID") @PathVariable Long id) {
        
        boolean deleted = irrigationService.deleteIrrigationSchedule(id);
        if (!deleted) {
            return Result.notFound("灌溉计划不存在");
        }
        return Result.success("灌溉计划删除成功", null);
    }

    @Operation(summary = "立即执行灌溉计划", description = "立即执行指定的灌溉计划")
    @PostMapping("/schedules/{id}/execute")
    public Result<Map<String, Object>> executeIrrigationSchedule(
            @Parameter(description = "计划ID") @PathVariable Long id) {
        
        Map<String, Object> result = irrigationService.executeIrrigationSchedule(id);
        if (result == null) {
            return Result.error("灌溉计划执行失败");
        }
        return Result.success("灌溉计划执行成功", result);
    }

    @Operation(summary = "启用/禁用灌溉计划", description = "切换灌溉计划的启用状态")
    @PatchMapping("/schedules/{id}/status")
    public Result<Void> toggleScheduleStatus(
            @Parameter(description = "计划ID") @PathVariable Long id,
            @Parameter(description = "状态") @RequestParam Integer status) {
        
        boolean updated = irrigationService.updateScheduleStatus(id, status);
        if (!updated) {
            return Result.notFound("灌溉计划不存在");
        }
        return Result.success("灌溉计划状态更新成功", null);
    }

    @Operation(summary = "获取灌溉统计信息", description = "获取灌溉操作的统计数据")
    @GetMapping("/statistics")
    public Result<IrrigationRecord.Statistics> getIrrigationStatistics(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        
        IrrigationRecord.Statistics statistics = irrigationService.getIrrigationStatistics(deviceId, plotId, days);
        return Result.success(statistics);
    }

    @Operation(summary = "获取灌溉效率分析", description = "分析灌溉效率和用水量")
    @GetMapping("/efficiency")
    public Result<IrrigationRecord.EfficiencyData> getIrrigationEfficiency(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        
        IrrigationRecord.EfficiencyData efficiencyData = irrigationService.getIrrigationEfficiency(deviceId, plotId, startTime, endTime);
        return Result.success(efficiencyData);
    }

    @Operation(summary = "获取灌溉报表", description = "生成灌溉操作报表")
    @GetMapping("/reports")
    public Result<IrrigationRecord.ReportData> getIrrigationReport(
            @Parameter(description = "报表类型") @RequestParam(defaultValue = "daily") String reportType,
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        
        IrrigationRecord.ReportData reportData = irrigationService.getIrrigationReport(reportType, deviceId, plotId, startTime, endTime);
        return Result.success(reportData);
    }

    @Operation(summary = "获取灌溉预设模板", description = "获取可用的灌溉预设模板")
    @GetMapping("/templates")
    public Result<List<IrrigationSchedule.Template>> getIrrigationTemplates() {
        List<IrrigationSchedule.Template> templates = irrigationService.getIrrigationTemplates();
        return Result.success(templates);
    }

    @Operation(summary = "应用灌溉模板", description = "将预设模板应用到设备")
    @PostMapping("/templates/{templateId}/apply")
    public Result<IrrigationSchedule> applyIrrigationTemplate(
            @Parameter(description = "模板ID") @PathVariable Long templateId,
            @Parameter(description = "设备ID") @RequestParam Long deviceId,
            @Valid @RequestBody IrrigationSchedule.TemplateParams templateParams) {
        
        IrrigationSchedule schedule = irrigationService.applyIrrigationTemplate(templateId, deviceId, templateParams);
        if (schedule == null) {
            return Result.error("模板应用失败");
        }
        return Result.success("模板应用成功", schedule);
    }

    @Operation(summary = "批量控制灌溉", description = "批量启动或停止多个设备的灌溉")
    @PostMapping("/batch-control")
    public Result<Map<String, Object>> batchControlIrrigation(@Valid @RequestBody IrrigationRecord.BatchControlRequest batchRequest) {
        Map<String, Object> result = irrigationService.batchControlIrrigation(batchRequest);
        return Result.success("批量控制完成", result);
    }

    @Operation(summary = "获取灌溉历史趋势", description = "获取灌溉用水量和频次的历史趋势")
    @GetMapping("/trends")
    public Result<IrrigationRecord.TrendData> getIrrigationTrends(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "趋势类型") @RequestParam(defaultValue = "monthly") String trendType,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        
        IrrigationRecord.TrendData trendData = irrigationService.getIrrigationTrends(deviceId, plotId, trendType, startTime, endTime);
        return Result.success(trendData);
    }

    @Operation(summary = "设置灌溉阈值", description = "设置自动灌溉的土壤湿度阈值")
    @PostMapping("/thresholds")
    public Result<Void> setIrrigationThreshold(@Valid @RequestBody IrrigationRecord.ThresholdConfig thresholdConfig) {
        boolean success = irrigationService.setIrrigationThreshold(thresholdConfig);
        if (!success) {
            return Result.error("阈值设置失败");
        }
        return Result.success("阈值设置成功", null);
    }

    @Operation(summary = "获取灌溉阈值配置", description = "获取设备的灌溉阈值配置")
    @GetMapping("/thresholds")
    public Result<List<IrrigationRecord.ThresholdConfig>> getIrrigationThresholds(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId) {
        
        List<IrrigationRecord.ThresholdConfig> thresholds = irrigationService.getIrrigationThresholds(deviceId, plotId);
        return Result.success(thresholds);
    }
}