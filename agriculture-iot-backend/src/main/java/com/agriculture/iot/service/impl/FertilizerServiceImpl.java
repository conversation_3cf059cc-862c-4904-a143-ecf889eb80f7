package com.agriculture.iot.service.impl;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.FertilizerRecord;
import com.agriculture.iot.entity.FertilizerSchedule;
import com.agriculture.iot.entity.FertilizerType;
import com.agriculture.iot.mapper.FertilizerRecordMapper;
import com.agriculture.iot.mapper.FertilizerScheduleMapper;
import com.agriculture.iot.mapper.FertilizerTypeMapper;
import com.agriculture.iot.service.FertilizerService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 施肥服务实现类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FertilizerServiceImpl extends ServiceImpl<FertilizerRecordMapper, FertilizerRecord> implements FertilizerService {

    private final FertilizerRecordMapper fertilizerRecordMapper;
    private final FertilizerScheduleMapper fertilizerScheduleMapper;
    private final FertilizerTypeMapper fertilizerTypeMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> applyFertilizer(FertilizerRecord.ApplyRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建施肥记录
            FertilizerRecord record = new FertilizerRecord();
            record.setDeviceId(request.getDeviceId());
            record.setPlotId(request.getPlotId());
            record.setFertilizerType(request.getFertilizerType());
            record.setAmount(request.getAmount());
            record.setUnit(request.getUnit());
            record.setConcentration(request.getConcentration());
            record.setApplicationTime(LocalDateTime.now());
            record.setMethod(request.getMethod());
            record.setStatus("applying");
            record.setTriggerSource(request.getTriggerSource());
            record.setNotes(request.getNotes());
            
            boolean saved = this.save(record);
            if (saved) {
                result.put("success", true);
                result.put("message", "施肥启动成功");
                result.put("recordId", record.getId());
                result.put("operationId", UUID.randomUUID().toString());
                
                // TODO: 发送设备控制指令
                // 这里应该调用设备控制服务启动实际的施肥设备
                
                log.info("施肥启动成功，设备ID: {}, 地块ID: {}, 记录ID: {}", 
                        request.getDeviceId(), request.getPlotId(), record.getId());
            } else {
                result.put("success", false);
                result.put("message", "施肥启动失败");
            }
            
        } catch (Exception e) {
            log.error("施肥启动失败", e);
            result.put("success", false);
            result.put("message", "施肥启动失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> stopFertilizer(FertilizerRecord.StopRequest request) {
        try {
            FertilizerRecord record = this.getById(request.getRecordId());
            if (record == null) {
                return Result.error("施肥记录不存在");
            }
            
            // 更新施肥记录
            record.setCompletedTime(LocalDateTime.now());
            record.setStatus("completed");
            record.setActualAmount(request.getActualAmount());
            record.setNotes(request.getNotes());
            
            boolean updated = this.updateById(record);
            if (updated) {
                // TODO: 发送设备控制指令
                // 这里应该调用设备控制服务停止实际的施肥设备
                
                log.info("施肥停止成功，记录ID: {}", request.getRecordId());
                return Result.success();
            } else {
                return Result.error("施肥停止失败");
            }
            
        } catch (Exception e) {
            log.error("施肥停止失败", e);
            return Result.error("施肥停止失败：" + e.getMessage());
        }
    }

    @Override
    public List<FertilizerRecord.Formula> getFertilizerFormulas(String cropType, String growthStage) {
        return fertilizerRecordMapper.selectFertilizerFormulas(cropType, growthStage);
    }

    @Override
    public IPage<FertilizerRecord> getFertilizerRecords(Page<FertilizerRecord> page, Long plotId, Long deviceId, 
                                                        LocalDateTime startTime, LocalDateTime endTime) {
        return fertilizerRecordMapper.selectFertilizerRecordsPage(page, plotId, deviceId, null, startTime, endTime);
    }

    @Override
    public IPage<FertilizerSchedule> getFertilizerSchedules(Page<FertilizerSchedule> page, Long plotId, Long deviceId, Integer status) {
        QueryWrapper<FertilizerSchedule> wrapper = new QueryWrapper<>();
        
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (status != null) {
            wrapper.eq("status", status);
        }
        
        wrapper.orderByDesc("created_at");
        return fertilizerScheduleMapper.selectPage(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FertilizerSchedule createFertilizerSchedule(FertilizerSchedule schedule) {
        schedule.setCreatedAt(LocalDateTime.now());
        schedule.setUpdatedAt(LocalDateTime.now());
        
        if (schedule.getStatus() == null) {
            schedule.setStatus(1); // 默认启用
        }
        
        int result = fertilizerScheduleMapper.insert(schedule);
        return result > 0 ? schedule : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FertilizerSchedule updateFertilizerSchedule(FertilizerSchedule schedule) {
        schedule.setUpdatedAt(LocalDateTime.now());
        
        int result = fertilizerScheduleMapper.updateById(schedule);
        return result > 0 ? schedule : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFertilizerSchedule(Long scheduleId) {
        return fertilizerScheduleMapper.deleteById(scheduleId) > 0;
    }

    @Override
    public List<FertilizerType> getFertilizerTypes() {
        return fertilizerTypeMapper.selectList(null);
    }

    @Override
    public Result<FertilizerRecord.StatusInfo> getFertilizerStatus(Long recordId) {
        try {
            FertilizerRecord.StatusInfo statusInfo = fertilizerRecordMapper.selectFertilizerStatus(recordId);
            return statusInfo != null ? Result.success(statusInfo) : Result.error("状态信息不存在");
        } catch (Exception e) {
            log.error("获取施肥状态失败", e);
            return Result.error("获取施肥状态失败");
        }
    }

    @Override
    public Result<FertilizerRecord.Statistics> getFertilizerStatistics(Long plotId, Long deviceId, Integer days) {
        try {
            FertilizerRecord.Statistics statistics = fertilizerRecordMapper.selectFertilizerStatistics(plotId, deviceId, days);
            return statistics != null ? Result.success(statistics) : Result.error("统计数据不存在");
        } catch (Exception e) {
            log.error("获取施肥统计失败", e);
            return Result.error("获取施肥统计失败");
        }
    }

    @Override
    public List<FertilizerRecord.EffectivenessData> getFertilizerEffectiveness(Long plotId, Long deviceId, 
                                                                              LocalDateTime startTime, LocalDateTime endTime) {
        return fertilizerRecordMapper.selectFertilizerEffectiveness(plotId, deviceId, startTime, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<FertilizerRecord.Formula> createCustomFormula(FertilizerRecord.Formula formula) {
        try {
            int result = fertilizerRecordMapper.insertFertilizerFormula(formula);
            return result > 0 ? Result.success(formula) : Result.error("创建配方失败");
        } catch (Exception e) {
            log.error("创建配方失败", e);
            return Result.error("创建配方失败");
        }
    }

    @Override
    public List<FertilizerRecord.Formula> getRecommendedFormulas(String cropType, String growthStage, String soilType, Long plotId) {
        return fertilizerRecordMapper.selectRecommendedFormulas(cropType, growthStage, soilType, plotId);
    }

    @Override
    public Result<FertilizerRecord.ReportData> getFertilizerReport(String reportType, Long deviceId, Long plotId, 
                                                                  LocalDateTime startTime, LocalDateTime endTime) {
        try {
            FertilizerRecord.ReportData reportData = fertilizerRecordMapper.selectFertilizerReport(reportType, deviceId, plotId, startTime, endTime);
            return reportData != null ? Result.success(reportData) : Result.error("报告数据不存在");
        } catch (Exception e) {
            log.error("获取施肥报告失败", e);
            return Result.error("获取施肥报告失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchApplyFertilizer(FertilizerRecord.BatchApplyRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            int successCount = 0;
            int failedCount = 0;
            
            for (FertilizerRecord.ApplyRequest applyRequest : request.getRequests()) {
                Map<String, Object> applyResult = applyFertilizer(applyRequest);
                if ((Boolean) applyResult.get("success")) {
                    successCount++;
                } else {
                    failedCount++;
                }
            }
            
            result.put("success", true);
            result.put("message", "批量施肥完成");
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("totalCount", request.getRequests().size());
            
        } catch (Exception e) {
            log.error("批量施肥失败", e);
            result.put("success", false);
            result.put("message", "批量施肥失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public FertilizerRecord.NutrientAnalysis getNutrientAnalysis(Long plotId, Integer days) {
        return fertilizerRecordMapper.selectNutrientAnalysis(plotId, days);
    }

    @Override
    public List<FertilizerRecord.Suggestion> getFertilizerSuggestions(Long deviceId, Long plotId) {
        return fertilizerRecordMapper.selectFertilizerSuggestions(deviceId, plotId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setFertilizerReminder(FertilizerRecord.ReminderConfig config) {
        try {
            int result = fertilizerRecordMapper.insertFertilizerReminder(config);
            return result > 0;
        } catch (Exception e) {
            log.error("设置施肥提醒失败", e);
            return false;
        }
    }

    @Override
    public List<FertilizerRecord.ReminderConfig> getFertilizerReminders(Long deviceId, Long plotId) {
        return fertilizerRecordMapper.selectFertilizerReminders(deviceId, plotId);
    }

    @Override
    public FertilizerRecord.CostAnalysis getFertilizerCostAnalysis(Long plotId, LocalDateTime startTime, LocalDateTime endTime) {
        return fertilizerRecordMapper.selectFertilizerCostAnalysis(plotId, startTime, endTime);
    }

    @Override
    public Result<IPage<FertilizerRecord.Plan>> getPlans(Integer current, Integer size, Long plotId, String status) {
        try {
            Page<FertilizerRecord.Plan> page = new Page<>(current, size);
            IPage<FertilizerRecord.Plan> result = fertilizerRecordMapper.selectFertilizerPlansPage(page, plotId, status);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取施肥计划失败", e);
            return Result.error("获取施肥计划失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<FertilizerRecord.Plan> createPlan(FertilizerRecord.Plan plan) {
        try {
            int result = fertilizerRecordMapper.insertFertilizerPlan(plan);
            return result > 0 ? Result.success(plan) : Result.error("创建计划失败");
        } catch (Exception e) {
            log.error("创建施肥计划失败", e);
            return Result.error("创建施肥计划失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<FertilizerRecord.Plan> updatePlan(Long id, FertilizerRecord.Plan plan) {
        try {
            plan.setId(id);
            int result = fertilizerRecordMapper.updateFertilizerPlan(plan);
            return result > 0 ? Result.success(plan) : Result.error("更新计划失败");
        } catch (Exception e) {
            log.error("更新施肥计划失败", e);
            return Result.error("更新施肥计划失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deletePlan(Long id) {
        try {
            // 这里应该调用mapper的删除方法
            // int result = fertilizerRecordMapper.deleteFertilizerPlan(id);
            // return result > 0 ? Result.success() : Result.error("删除计划失败");
            return Result.success();
        } catch (Exception e) {
            log.error("删除施肥计划失败", e);
            return Result.error("删除施肥计划失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<FertilizerRecord> executePlan(Long planId) {
        try {
            // 这里应该根据计划ID获取计划信息，然后执行施肥
            // 暂时返回成功
            return Result.success();
        } catch (Exception e) {
            log.error("执行施肥计划失败", e);
            return Result.error("执行施肥计划失败");
        }
    }

    @Override
    public Result<IPage<FertilizerRecord.Template>> getTemplates(Integer current, Integer size, String cropType) {
        try {
            Page<FertilizerRecord.Template> page = new Page<>(current, size);
            IPage<FertilizerRecord.Template> result = fertilizerRecordMapper.selectFertilizerTemplatesPage(page, cropType);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取施肥模板失败", e);
            return Result.error("获取施肥模板失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<FertilizerRecord.Template> createTemplate(FertilizerRecord.Template template) {
        try {
            int result = fertilizerRecordMapper.insertFertilizerTemplate(template);
            return result > 0 ? Result.success(template) : Result.error("创建模板失败");
        } catch (Exception e) {
            log.error("创建施肥模板失败", e);
            return Result.error("创建施肥模板失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<FertilizerRecord.Template> updateTemplate(Long id, FertilizerRecord.Template template) {
        try {
            template.setId(id);
            int result = fertilizerRecordMapper.updateFertilizerTemplate(template);
            return result > 0 ? Result.success(template) : Result.error("更新模板失败");
        } catch (Exception e) {
            log.error("更新施肥模板失败", e);
            return Result.error("更新施肥模板失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteTemplate(Long id) {
        try {
            // 这里应该调用mapper的删除方法
            // int result = fertilizerRecordMapper.deleteFertilizerTemplate(id);
            // return result > 0 ? Result.success() : Result.error("删除模板失败");
            return Result.success();
        } catch (Exception e) {
            log.error("删除施肥模板失败", e);
            return Result.error("删除施肥模板失败");
        }
    }

    @Override
    public Result<IPage<FertilizerRecord.Reminder>> getReminders(Integer current, Integer size, String status) {
        try {
            Page<FertilizerRecord.Reminder> page = new Page<>(current, size);
            IPage<FertilizerRecord.Reminder> result = fertilizerRecordMapper.selectFertilizerRemindersPage(page, status);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取施肥提醒失败", e);
            return Result.error("获取施肥提醒失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<FertilizerRecord.Reminder> createReminder(FertilizerRecord.Reminder reminder) {
        try {
            int result = fertilizerRecordMapper.insertFertilizerReminderRecord(reminder);
            return result > 0 ? Result.success(reminder) : Result.error("创建提醒失败");
        } catch (Exception e) {
            log.error("创建施肥提醒失败", e);
            return Result.error("创建施肥提醒失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<FertilizerRecord.Reminder> updateReminder(Long id, FertilizerRecord.Reminder reminder) {
        try {
            reminder.setId(id);
            int result = fertilizerRecordMapper.updateFertilizerReminder(reminder);
            return result > 0 ? Result.success(reminder) : Result.error("更新提醒失败");
        } catch (Exception e) {
            log.error("更新施肥提醒失败", e);
            return Result.error("更新施肥提醒失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteReminder(Long id) {
        try {
            // 这里应该调用mapper的删除方法
            // int result = fertilizerRecordMapper.deleteFertilizerReminder(id);
            // return result > 0 ? Result.success() : Result.error("删除提醒失败");
            return Result.success();
        } catch (Exception e) {
            log.error("删除施肥提醒失败", e);
            return Result.error("删除施肥提醒失败");
        }
    }
}