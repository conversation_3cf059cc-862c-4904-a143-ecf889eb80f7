package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 施肥记录实体类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("fertilizer_records")
public class FertilizerRecord {

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private Long deviceId;

    /**
     * 地块ID
     */
    @TableField("plot_id")
    private Long plotId;

    /**
     * 肥料类型ID
     */
    @TableField("fertilizer_type_id")
    private Long fertilizerTypeId;

    /**
     * 施肥开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 施肥结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 施肥持续时间(分钟)
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 肥料用量(千克)
     */
    @TableField("fertilizer_amount")
    private BigDecimal fertilizerAmount;

    /**
     * 浓度(%)
     */
    @TableField("concentration")
    private BigDecimal concentration;

    /**
     * 触发方式: manual-手动, auto-自动, scheduled-定时
     */
    @TableField("trigger_type")
    private String triggerType;

    /**
     * 触发条件
     */
    @TableField("trigger_condition")
    private String triggerCondition;

    /**
     * 执行状态: pending-待执行, running-执行中, completed-已完成, failed-失败, cancelled-已取消
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("notes")
    private String notes;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // 关联对象
    /**
     * 关联设备信息
     */
    @TableField(exist = false)
    private Device device;

    /**
     * 关联地块信息
     */
    @TableField(exist = false)
    private Plot plot;

    /**
     * 关联肥料类型信息
     */
    @TableField(exist = false)
    private FertilizerType fertilizerType;

    // DTO内部类
    /**
     * 施肥请求DTO
     */
    @Data
    public static class ApplyRequest {
        private Long deviceId;
        private Long plotId;
        private Long fertilizerTypeId;
        private BigDecimal fertilizerAmount;
        private BigDecimal concentration;
        private Integer duration;
        private String triggerType;
        private String triggerCondition;
        private String notes;
    }

    /**
     * 停止施肥请求DTO
     */
    @Data
    public static class StopRequest {
        private Long recordId;
        private String reason;
        private String notes;
    }

    /**
     * 肥料配方DTO
     */
    @Data
    public static class Formula {
        private Long id;
        private String name;
        private String description;
        private String cropType;
        private String growthStage;
        private BigDecimal nitrogenRatio;
        private BigDecimal phosphorusRatio;
        private BigDecimal potassiumRatio;
        private BigDecimal concentration;
        private String usage;
        private String precautions;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    /**
     * 施肥状态信息DTO
     */
    @Data
    public static class StatusInfo {
        private Long recordId;
        private String status;
        private String statusText;
        private BigDecimal currentAmount;
        private BigDecimal targetAmount;
        private Integer remainingTime;
        private BigDecimal progress;
        private String currentPhase;
        private LocalDateTime lastUpdate;
    }

    /**
     * 施肥统计数据DTO
     */
    @Data
    public static class Statistics {
        private LocalDate date;
        private Integer totalRecords;
        private BigDecimal totalAmount;
        private Integer totalDuration;
        private Integer successCount;
        private Integer failedCount;
        private BigDecimal successRate;
        private BigDecimal avgAmount;
        private Integer avgDuration;
        private BigDecimal costAmount;
    }

    /**
     * 施肥效果数据DTO
     */
    @Data
    public static class EffectivenessData {
        private Long recordId;
        private Long plotId;
        private String cropType;
        private LocalDate fertilizerDate;
        private BigDecimal beforeNitrogen;
        private BigDecimal afterNitrogen;
        private BigDecimal beforePhosphorus;
        private BigDecimal afterPhosphorus;
        private BigDecimal beforePotassium;
        private BigDecimal afterPotassium;
        private BigDecimal yieldIncrease;
        private BigDecimal qualityScore;
        private String effectiveness;
        private String comments;
    }

    /**
     * 施肥报告数据DTO
     */
    @Data
    public static class ReportData {
        private String reportType;
        private LocalDate startDate;
        private LocalDate endDate;
        private Integer totalRecords;
        private BigDecimal totalAmount;
        private BigDecimal totalCost;
        private List<Statistics> dailyStats;
        private List<EffectivenessData> effectivenessData;
        private Map<String, Object> summary;
        private String recommendations;
    }

    /**
     * 施肥计划DTO
     */
    @Data
    public static class Plan {
        private Long id;
        private String name;
        private Long plotId;
        private String plotName;
        private String cropType;
        private String growthStage;
        private LocalDateTime plannedTime;
        private Long fertilizerTypeId;
        private String fertilizerName;
        private BigDecimal plannedAmount;
        private BigDecimal concentration;
        private Integer duration;
        private String frequency;
        private String status;
        private String notes;
        private LocalDateTime createdAt;
    }

    /**
     * 施肥模板DTO
     */
    @Data
    public static class Template {
        private Long id;
        private String name;
        private String description;
        private String cropType;
        private String applicableStage;
        private List<TemplateItem> items;
        private String instructions;
        private String precautions;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    /**
     * 施肥模板项DTO
     */
    @Data
    public static class TemplateItem {
        private Long fertilizerTypeId;
        private String fertilizerName;
        private BigDecimal amount;
        private BigDecimal concentration;
        private Integer duration;
        private Integer sequence;
        private String notes;
    }

    /**
     * 施肥提醒DTO
     */
    @Data
    public static class Reminder {
        private Long id;
        private Long plotId;
        private String plotName;
        private String cropType;
        private String reminderType;
        private LocalDateTime reminderTime;
        private String title;
        private String message;
        private String priority;
        private String status;
        private LocalDateTime createdAt;
    }

    /**
     * 批量施肥请求DTO
     */
    @Data
    public static class BatchApplyRequest {
        private List<ApplyRequest> requests;
        private String notes;
        private Boolean synchronous;
    }

    /**
     * 营养元素分析DTO
     */
    @Data
    public static class NutrientAnalysis {
        private Long plotId;
        private String plotName;
        private LocalDate analysisDate;
        private BigDecimal currentNitrogen;
        private BigDecimal currentPhosphorus;
        private BigDecimal currentPotassium;
        private BigDecimal optimalNitrogen;
        private BigDecimal optimalPhosphorus;
        private BigDecimal optimalPotassium;
        private BigDecimal nitrogenDeficit;
        private BigDecimal phosphorusDeficit;
        private BigDecimal potassiumDeficit;
        private String recommendation;
        private String severity;
    }

    /**
     * 施肥建议DTO
     */
    @Data
    public static class Suggestion {
        private Long id;
        private String title;
        private String description;
        private String urgency;
        private String suggestionType;
        private Long fertilizerTypeId;
        private String fertilizerName;
        private BigDecimal recommendedAmount;
        private BigDecimal concentration;
        private String reason;
        private String benefits;
        private LocalDateTime validUntil;
    }

    /**
     * 提醒配置DTO
     */
    @Data
    public static class ReminderConfig {
        private Long id;
        private Long plotId;
        private String cropType;
        private String frequency;
        private Integer intervalDays;
        private LocalDateTime nextReminder;
        private String reminderMessage;
        private Boolean enabled;
        private String contacts;
        private String reminderMethod;
    }

    /**
     * 成本分析DTO
     */
    @Data
    public static class CostAnalysis {
        private LocalDate startDate;
        private LocalDate endDate;
        private BigDecimal totalCost;
        private BigDecimal fertilizerCost;
        private BigDecimal laborCost;
        private BigDecimal equipmentCost;
        private BigDecimal avgCostPerHectare;
        private BigDecimal yield;
        private BigDecimal revenue;
        private BigDecimal profit;
        private BigDecimal roi;
        private String recommendations;
    }
}