package com.agriculture.iot.task;

import com.agriculture.iot.entity.*;
import com.agriculture.iot.mapper.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 水肥一体机定时任务
 * 负责自动灌溉、施肥、报警检测等
 *
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IrrigationFertilizerTask {

    private final IrrigationScheduleMapper irrigationScheduleMapper;
    private final FertilizerScheduleMapper fertilizerScheduleMapper;
    private final SensorDataMapper sensorDataMapper;
    private final IrrigationLogMapper irrigationLogMapper;
    private final FertilizerLogMapper fertilizerLogMapper;
    private final AlarmRecordMapper alarmRecordMapper;
    private final DeviceMapper deviceMapper;

    /**
     * 每分钟检查一次灌溉计划
     */
    @Scheduled(cron = "0 * * * * ?")
    public void checkIrrigationSchedule() {
        log.info("开始检查灌溉计划...");
        
        LocalDateTime now = LocalDateTime.now();
        List<IrrigationSchedule> schedules = irrigationScheduleMapper.findActiveSchedules(now);
        
        for (IrrigationSchedule schedule : schedules) {
            try {
                executeIrrigation(schedule);
            } catch (Exception e) {
                log.error("执行灌溉计划失败: {}", schedule.getId(), e);
                createAlarm(schedule.getDeviceId(), "灌溉执行失败", e.getMessage());
            }
        }
    }

    /**
     * 每分钟检查一次施肥计划
     */
    @Scheduled(cron = "0 * * * * ?")
    public void checkFertilizerSchedule() {
        log.info("开始检查施肥计划...");
        
        LocalDateTime now = LocalDateTime.now();
        List<FertilizerSchedule> schedules = fertilizerScheduleMapper.findActiveSchedules(now);
        
        for (FertilizerSchedule schedule : schedules) {
            try {
                executeFertilizer(schedule);
            } catch (Exception e) {
                log.error("执行施肥计划失败: {}", schedule.getId(), e);
                createAlarm(schedule.getDeviceId(), "施肥执行失败", e.getMessage());
            }
        }
    }

    /**
     * 每5分钟检查一次传感器数据报警
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void checkSensorAlarms() {
        log.info("开始检查传感器报警...");
        
        List<Device> devices = deviceMapper.findActiveDevices();
        
        for (Device device : devices) {
            try {
                checkDeviceAlarms(device);
            } catch (Exception e) {
                log.error("检查设备报警失败: {}", device.getId(), e);
            }
        }
    }

    /**
     * 执行灌溉
     */
    private void executeIrrigation(IrrigationSchedule schedule) {
        // 检查土壤湿度
        SensorData soilMoisture = sensorDataMapper.findLatestByType(
            schedule.getDeviceId(), 1L); // 1 = 土壤湿度
        
        if (soilMoisture != null && soilMoisture.getValue() < schedule.getMinMoisture()) {
            // 开始灌溉
            IrrigationRecord record = new IrrigationRecord();
            record.setDeviceId(schedule.getDeviceId());
            record.setPlotId(schedule.getPlotId());
            record.setStartTime(LocalDateTime.now());
            record.setDuration(schedule.getDuration());
            record.setWaterAmount(schedule.getWaterAmount());
            record.setStatus(1); // 进行中
            
            irrigationLogMapper.insert(record);
            
            log.info("开始灌溉: 设备={}, 水量={}L, 时长={}分钟", 
                schedule.getDeviceId(), schedule.getWaterAmount(), schedule.getDuration());
            
            // 模拟灌溉完成
            record.setEndTime(LocalDateTime.now().plusMinutes(schedule.getDuration()));
            record.setStatus(2); // 已完成
            irrigationLogMapper.updateById(record);
        }
    }

    /**
     * 执行施肥
     */
    private void executeFertilizer(FertilizerSchedule schedule) {
        // 检查土壤养分
        SensorData soilNutrient = sensorDataMapper.findLatestByType(
            schedule.getDeviceId(), 2L); // 2 = 土壤养分
        
        if (soilNutrient != null && soilNutrient.getValue() < schedule.getMinNutrient()) {
            // 开始施肥
            FertilizerRecord record = new FertilizerRecord();
            record.setDeviceId(schedule.getDeviceId());
            record.setPlotId(schedule.getPlotId());
            record.setStartTime(LocalDateTime.now());
            record.setDuration(schedule.getDuration());
            record.setFertilizerAmount(schedule.getFertilizerAmount());
            record.setFertilizerTypeId(schedule.getFertilizerTypeId());
            record.setStatus(1); // 进行中
            
            fertilizerLogMapper.insert(record);
            
            log.info("开始施肥: 设备={}, 肥料={}, 用量={}g, 时长={}分钟", 
                schedule.getDeviceId(), schedule.getFertilizerTypeId(), 
                schedule.getFertilizerAmount(), schedule.getDuration());
            
            // 模拟施肥完成
            record.setEndTime(LocalDateTime.now().plusMinutes(schedule.getDuration()));
            record.setStatus(2); // 已完成
            fertilizerLogMapper.updateById(record);
        }
    }

    /**
     * 检查设备报警
     */
    private void checkDeviceAlarms(Device device) {
        // 检查温度报警
        SensorData temperature = sensorDataMapper.findLatestByType(device.getId(), 3L);
        if (temperature != null && temperature.getValue() > 35) {
            createAlarm(device.getId(), "温度过高", 
                String.format("当前温度: %.1f°C", temperature.getValue()));
        }
        
        // 检查湿度报警
        SensorData humidity = sensorDataMapper.findLatestByType(device.getId(), 4L);
        if (humidity != null && humidity.getValue() < 20) {
            createAlarm(device.getId(), "湿度过低", 
                String.format("当前湿度: %.1f%%", humidity.getValue()));
        }
        
        // 检查土壤湿度报警
        SensorData soilMoisture = sensorDataMapper.findLatestByType(device.getId(), 1L);
        if (soilMoisture != null && soilMoisture.getValue() < 10) {
            createAlarm(device.getId(), "土壤湿度过低", 
                String.format("当前土壤湿度: %.1f%%", soilMoisture.getValue()));
        }
    }

    /**
     * 创建报警记录
     */
    private void createAlarm(Long deviceId, String alarmType, String message) {
        Alarm alarm = new Alarm();
        alarm.setDeviceId(deviceId);
        alarm.setAlarmType(alarmType);
        alarm.setMessage(message);
        alarm.setAlarmTime(LocalDateTime.now());
        alarm.setStatus(0); // 未处理
        
        alarmRecordMapper.insert(alarm);
        
        log.warn("创建报警: 设备={}, 类型={}, 消息={}", deviceId, alarmType, message);
    }

    /**
     * 每日凌晨2点清理过期数据
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupOldData() {
        log.info("开始清理过期数据...");
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(90);
        
        // 清理90天前的传感器数据
        int sensorCount = sensorDataMapper.deleteBefore(cutoffDate);
        
        // 清理90天前的灌溉记录
        int irrigationCount = irrigationLogMapper.deleteBefore(cutoffDate);
        
        // 清理90天前的施肥记录
        int fertilizerCount = fertilizerLogMapper.deleteBefore(cutoffDate);
        
        // 清理90天前的报警记录
        int alarmCount = alarmRecordMapper.deleteBefore(cutoffDate);
        
        log.info("数据清理完成: 传感器数据={}, 灌溉记录={}, 施肥记录={}, 报警记录={}", 
            sensorCount, irrigationCount, fertilizerCount, alarmCount);
    }
}