<!--灌溉控制页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 设备状态面板 -->
    <view class="card">
      <view class="device-status-panel">
        <view class="status-header">
          <text class="device-name">水肥一体机-01</text>
          <view class="status-indicator {{deviceStatus.class}}">
            <text class="status-dot"></text>
            <text class="status-text">{{deviceStatus.text}}</text>
          </view>
        </view>
        <view class="status-info">
          <view class="info-row">
            <view class="info-item">
              <text class="info-label">水箱液位</text>
              <text class="info-value {{deviceInfo.waterLevel < 20 ? 'warning' : ''}}">{{deviceInfo.waterLevel}}%</text>
              <view class="info-bar">
                <view class="info-fill" style="width: {{deviceInfo.waterLevel}}%"></view>
              </view>
            </view>
            <view class="info-item">
              <text class="info-label">肥料余量</text>
              <text class="info-value {{deviceInfo.fertilizerLevel < 20 ? 'warning' : ''}}">{{deviceInfo.fertilizerLevel}}%</text>
              <view class="info-bar">
                <view class="info-fill fertilizer" style="width: {{deviceInfo.fertilizerLevel}}%"></view>
              </view>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="info-label">系统压力</text>
              <text class="info-value">{{deviceInfo.pressure}} bar</text>
            </view>
            <view class="info-item">
              <text class="info-label">运行时长</text>
              <text class="info-value">{{deviceInfo.runTime}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 实时控制 -->
    <view class="card">
      <view class="card-title">实时控制</view>
      <view class="control-panel">
        
        <!-- 主控制按钮 -->
        <view class="main-control">
          <view class="control-header">
            <text class="control-title">灌溉系统</text>
            <view class="device-status-mini">
              <text class="status-indicator {{deviceStatus.class}}"></text>
              <text class="status-text">{{deviceStatus.text}}</text>
            </view>
          </view>
          
          <!-- 大号启停按钮 -->
          <view class="main-buttons">
            <button class="main-btn start-btn {{irrigationRunning ? 'hidden' : ''}}" 
                    bindtap="startIrrigation" 
                    disabled="{{!deviceStatus.online}}">
              <view class="btn-icon start-icon"></view>
              <text class="btn-text">立即启动</text>
            </button>
            
            <button class="main-btn stop-btn {{!irrigationRunning ? 'hidden' : ''}}" 
                    bindtap="stopIrrigation">
              <view class="btn-icon stop-icon"></view>
              <text class="btn-text">停止灌溉</text>
            </button>
          </view>
          
          <view class="control-status" wx:if="{{irrigationRunning}}">
            <view class="running-info">
              <text class="running-text">💧 系统运行中...</text>
              <text class="remaining-time">剩余时间: {{remainingTime}}</text>
            </view>
            <view class="realtime-data">
              <view class="realtime-item">
                <text class="realtime-label">当前流量</text>
                <text class="realtime-value">{{currentFlow || '--'}} L/min</text>
              </view>
              <view class="realtime-item">
                <text class="realtime-label">实时压力</text>
                <text class="realtime-value">{{currentPressure || '--'}} bar</text>
              </view>
            </view>
          </view>
          
        </view>

        <!-- 控制参数 -->
        <view class="control-params" wx:if="{{irrigationRunning}}">
          <view class="param-group">
            <view class="param-item flow-control">
              <view class="param-header">
                <text class="param-label">流量控制</text>
                <view class="flow-display">
                  <text class="flow-value">{{flowRate}}</text>
                  <text class="flow-unit">L/min</text>
                </view>
              </view>
              <view class="custom-slider">
                <view class="slider-track">
                  <view class="slider-fill" style="width: {{(flowRate - 10) / 90 * 100}}%"></view>
                  <view class="slider-thumb" 
                        style="left: {{(flowRate - 10) / 90 * 100}}%" 
                        bindtouchstart="onSliderTouchStart"
                        bindtouchmove="onSliderTouchMove"
                        bindtouchend="onSliderTouchEnd">
                    <view class="thumb-inner"></view>
                  </view>
                </view>
                <view class="slider-labels">
                  <text class="slider-min">10</text>
                  <text class="slider-max">100</text>
                </view>
              </view>
            </view>
            
            <view class="param-item">
              <text class="param-label">灌溉时长</text>
              <view class="param-control">
                <input type="number" value="{{duration}}" bindinput="onDurationInput" 
                       placeholder="输入分钟" class="duration-input"/>
                <text class="param-unit">分钟</text>
              </view>
            </view>

            <view class="param-item">
              <text class="param-label">地块选择</text>
              <view class="plot-selector">
                <view class="plot-item {{item.selected ? 'selected' : ''}}" 
                      wx:for="{{plotList}}" wx:key="id"
                      bindtap="togglePlot" data-id="{{item.id}}">
                  <text class="plot-name">{{item.name}}</text>
                  <text class="plot-area">{{item.area}}亩</text>
                  <text class="device-count" wx:if="{{item.deviceCount > 0}}">{{item.deviceCount}}台设备</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 快捷设置 -->
          <view class="quick-settings">
            <text class="section-title">快捷设置</text>
            <view class="preset-buttons">
              <button class="preset-btn" bindtap="applyPreset" data-preset="light">
                轻度灌溉 (10分钟)
              </button>
              <button class="preset-btn" bindtap="applyPreset" data-preset="medium">
                中度灌溉 (20分钟)
              </button>
              <button class="preset-btn" bindtap="applyPreset" data-preset="heavy">
                重度灌溉 (40分钟)
              </button>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="control-actions">
            <button class="btn btn-outline" bindtap="pauseIrrigation">
              ⏸️ 暂停
            </button>
            <button class="btn btn-danger" bindtap="stopIrrigation">
              ⏹️ 停止
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 定时设置 -->
    <view class="card">
      <view class="card-title">
        <text>定时灌溉</text>
        <button class="btn btn-small btn-primary" bindtap="openAdvancedSchedule">
          📅 高级调度
        </button>
      </view>
      
      <!-- 今日计划概览 -->
      <view class="today-schedule">
        <view class="schedule-header">
          <text class="today-title">今日计划 ({{todayScheduleCount}}个)</text>
          <text class="next-schedule">下次灌溉: {{nextIrrigationTime}}</text>
        </view>
        
        <view class="schedule-timeline">
          <view class="timeline-item {{item.status}}" wx:for="{{todaySchedules}}" wx:key="id">
            <view class="timeline-time">{{item.time}}</view>
            <view class="timeline-content">
              <text class="timeline-name">{{item.name}}</text>
              <text class="timeline-details">{{item.details}}</text>
            </view>
            <view class="timeline-status">
              <text class="status-text">{{item.statusText}}</text>
            </view>
          </view>
          
          <view class="no-schedule" wx:if="{{todaySchedules.length === 0}}">
            <text class="text-muted">今日暂无定时计划</text>
          </view>
        </view>
      </view>
      
      
      <!-- 快速操作 -->
      <view class="quick-schedule">
        <text class="section-title">快速计划</text>
        <view class="quick-actions">
          <button class="quick-btn" bindtap="quickSchedule" data-type="morning">
            🌅 早晨7点
          </button>
          <button class="quick-btn" bindtap="quickSchedule" data-type="evening">
            🌇 傍晚6点
          </button>
          <button class="quick-btn" bindtap="quickSchedule" data-type="twice">
            ⏱️ 早晚各一次
          </button>
        </view>
      </view>
    </view>

    <!-- 智能控制 -->
    <view class="card">
      <view class="card-title">智能控制</view>
      <view class="smart-control">
        
        <!-- 土壤湿度触发 -->
        <view class="smart-item">
          <view class="smart-header">
            <text class="smart-title">土壤湿度自动灌溉</text>
            <switch checked="{{smartControl.humidity.enabled}}" bindchange="toggleSmartHumidity"/>
          </view>
          <view class="smart-params" wx:if="{{smartControl.humidity.enabled}}">
            <view class="param-row">
              <text class="param-label">触发湿度</text>
              <slider value="{{smartControl.humidity.threshold}}" min="20" max="80" 
                      bindchanging="onHumidityThresholdChange" show-value/>
              <text class="param-unit">%</text>
            </view>
            <view class="param-row">
              <text class="param-label">灌溉时长</text>
              <input type="number" value="{{smartControl.humidity.duration}}" 
                     bindinput="onSmartDurationInput" class="smart-input"/>
              <text class="param-unit">分钟</text>
            </view>
          </view>
        </view>


        <!-- 节水模式 -->
        <view class="smart-item">
          <view class="smart-header">
            <text class="smart-title">节水模式</text>
            <switch checked="{{smartControl.waterSaving.enabled}}" bindchange="toggleWaterSaving"/>
          </view>
          <view class="smart-params" wx:if="{{smartControl.waterSaving.enabled}}">
            <text class="water-saving-desc">
              启用节水模式后，系统将自动优化灌溉策略，减少30%的用水量
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 运行记录 -->
    <view class="card">
      <view class="card-title">
        <text>运行记录</text>
        <text class="record-count">今日: {{todayRecords.length}}次</text>
      </view>
      <view class="record-list">
        <view class="record-item" wx:for="{{todayRecords}}" wx:key="id">
          <view class="record-time">{{item.time}}</view>
          <view class="record-content">
            <text class="record-action">{{item.action}}</text>
            <text class="record-details">{{item.details}}</text>
          </view>
          <view class="record-status {{item.status}}">
            {{item.statusText}}
          </view>
        </view>
        
        <view class="view-more" bindtap="viewAllRecords">
          <text>查看完整记录 ></text>
        </view>
      </view>
    </view>

  </view>
</view>