package com.agriculture.iot.mapper;

import com.agriculture.iot.entity.Alarm;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 报警数据访问层
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Mapper
public interface AlarmMapper extends BaseMapper<Alarm> {

    /**
     * 分页查询报警列表（包含关联信息）
     */
    IPage<Alarm.Detail> selectAlarmDetailsPage(
            Page<Alarm.Detail> page,
            @Param("level") String level,
            @Param("status") String status,
            @Param("type") String type,
            @Param("plotId") Long plotId,
            @Param("deviceId") Long deviceId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 根据ID获取报警详情
     */
    Alarm.Detail selectAlarmDetailById(@Param("id") Long id);

    /**
     * 获取最新报警列表
     */
    List<Alarm.Detail> selectLatestAlarms(@Param("limit") Integer limit);

    /**
     * 获取活跃报警列表
     */
    List<Alarm.Detail> selectActiveAlarms(@Param("plotId") Long plotId, @Param("level") String level);

    /**
     * 获取逾期未处理报警
     */
    List<Alarm.Detail> selectOverdueAlarms(@Param("hours") Integer hours, @Param("plotId") Long plotId);

    /**
     * 获取高优先级报警
     */
    List<Alarm.Detail> selectHighPriorityAlarms(@Param("plotId") Long plotId);

    /**
     * 批量更新报警状态
     */
    int batchUpdateAlarmStatus(@Param("alarmIds") List<Long> alarmIds, @Param("status") String status);

    /**
     * 确认报警
     */
    int acknowledgeAlarm(@Param("id") Long id, @Param("notes") String notes, @Param("acknowledgedAt") LocalDateTime acknowledgedAt);

    /**
     * 解决报警
     */
    int resolveAlarm(@Param("id") Long id, @Param("notes") String notes, @Param("resolvedAt") LocalDateTime resolvedAt);

    /**
     * 获取报警统计数据
     */
    Alarm.Statistics selectAlarmStatistics(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("plotId") Long plotId,
            @Param("type") String type);

    /**
     * 获取报警趋势数据
     */
    List<Alarm.TrendData> selectAlarmTrends(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("period") String period,
            @Param("plotId") Long plotId,
            @Param("type") String type);

    /**
     * 获取报警仪表板数据
     */
    Alarm.Dashboard selectAlarmDashboard(@Param("plotId") Long plotId);

    /**
     * 根据类型统计报警数量
     */
    Map<String, Integer> selectAlarmCountByType(@Param("plotId") Long plotId);

    /**
     * 根据级别统计报警数量
     */
    Map<String, Integer> selectAlarmCountByLevel(@Param("plotId") Long plotId);

    /**
     * 根据设备统计报警数量
     */
    Map<String, Integer> selectAlarmCountByDevice(@Param("plotId") Long plotId);

    /**
     * 获取报警规则列表
     */
    IPage<Alarm.Rule> selectAlarmRulesPage(
            Page<Alarm.Rule> page,
            @Param("type") String type,
            @Param("enabled") Boolean enabled,
            @Param("plotId") Long plotId,
            @Param("deviceId") Long deviceId);

    /**
     * 根据ID获取报警规则
     */
    Alarm.Rule selectAlarmRuleById(@Param("id") Long id);

    /**
     * 创建报警规则
     */
    int insertAlarmRule(Alarm.Rule rule);

    /**
     * 更新报警规则
     */
    int updateAlarmRule(Alarm.Rule rule);

    /**
     * 启用/禁用报警规则
     */
    int toggleAlarmRule(@Param("id") Long id, @Param("enabled") Boolean enabled);

    /**
     * 获取设备相关的报警规则
     */
    List<Alarm.Rule> selectRulesByDevice(@Param("deviceId") Long deviceId);

    /**
     * 获取参数相关的报警规则
     */
    List<Alarm.Rule> selectRulesByParameter(@Param("deviceId") Long deviceId, @Param("parameter") String parameter);

    /**
     * 获取报警配置
     */
    List<Alarm.Config> selectAlarmConfigs(@Param("plotId") Long plotId, @Param("deviceId") Long deviceId);

    /**
     * 更新报警配置
     */
    int updateAlarmConfig(Alarm.Config config);

    /**
     * 获取报警通知记录
     */
    IPage<Alarm.Notification> selectAlarmNotificationsPage(
            Page<Alarm.Notification> page,
            @Param("alarmId") Long alarmId,
            @Param("method") String method,
            @Param("status") String status);

    /**
     * 创建报警通知记录
     */
    int insertAlarmNotification(Alarm.Notification notification);

    /**
     * 更新通知状态
     */
    int updateNotificationStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 获取报警响应记录
     */
    IPage<Alarm.Response> selectAlarmResponsesPage(
            Page<Alarm.Response> page,
            @Param("alarmId") Long alarmId,
            @Param("responseType") String responseType);

    /**
     * 创建报警响应记录
     */
    int insertAlarmResponse(Alarm.Response response);

    /**
     * 清理历史报警数据
     */
    int cleanupAlarmHistory(@Param("retentionDays") Integer retentionDays);

    /**
     * 获取级联报警规则
     */
    List<Alarm.Rule> selectCascadeRules(@Param("triggeredRuleId") Long triggeredRuleId);

    /**
     * 今日报警数量
     */
    Integer selectTodayAlarmCount(@Param("plotId") Long plotId);

    /**
     * 活跃报警数量
     */
    Integer selectActiveAlarmCount(@Param("plotId") Long plotId);

    /**
     * 未处理报警数量
     */
    Integer selectUnprocessedAlarmCount(@Param("plotId") Long plotId);
}