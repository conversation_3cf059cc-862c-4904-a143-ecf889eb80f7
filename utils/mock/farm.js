/**
 * 农场相关Mock API
 */

const dataModule = require('./data');

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 验证Token
function verifyToken(token) {
  // 开发环境始终返回 mock 用户信息，跳过 token 校验
  return {
    userId: 'mock_user',
    username: 'testuser',
    exp: Math.floor(Date.now() / 1000) + 3600
  };
}

module.exports = {
  init() {
    console.log('农场模块初始化完成');
  },

  // 获取农场列表
  async list(token) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const farms = dataModule.getFarms();
    
    return createApiResponse({
      farms: farms.map(farm => ({
        id: farm.id,
        name: farm.name,
        location: farm.location,
        totalArea: farm.totalArea,
        description: farm.description,
        status: farm.status,
        plotCount: farm.plotCount,
        deviceCount: farm.deviceCount,
        sensorCount: farm.sensorCount,
        cropTypes: farm.cropTypes,
        establishedDate: farm.establishedDate,
        certification: farm.certification,
        createdAt: farm.createdAt,
        updatedAt: farm.updatedAt
      }))
    });
  },

  // 获取农场详情
  async getDetail(token, farmId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const farm = dataModule.getFarmById(farmId);
    if (!farm) {
      return createErrorResponse('农场不存在', 404);
    }

    // 获取农场的地块信息
    const plots = dataModule.getPlots(farmId);
    const devices = dataModule.getDevices().filter(device => device.farmId === farmId);
    const sensors = dataModule.getSensors().filter(sensor => sensor.farmId === farmId);

    // 计算农场统计数据
    const stats = {
      totalPlots: plots.length,
      activePlots: plots.filter(plot => plot.status === 'active').length,
      totalDevices: devices.length,
      onlineDevices: devices.filter(device => device.status.online).length,
      totalSensors: sensors.length,
      onlineSensors: sensors.filter(sensor => sensor.status.online).length,
      totalArea: farm.totalArea,
      plantedArea: plots.reduce((sum, plot) => sum + (plot.currentCrop ? plot.currentCrop.plantingArea : 0), 0),
      cropTypes: [...new Set(plots.map(plot => plot.currentCrop?.type).filter(Boolean))],
      avgGrowthProgress: plots.reduce((sum, plot) => sum + (plot.currentCrop?.progress || 0), 0) / plots.length
    };

    return createApiResponse({
      farm: {
        ...farm,
        plots: plots.map(plot => ({
          id: plot.id,
          name: plot.name,
          area: plot.area,
          status: plot.status,
          soilType: plot.soilType,
          irrigationType: plot.irrigationType,
          currentCrop: plot.currentCrop,
          deviceCount: plot.devices.length,
          createdAt: plot.createdAt,
          updatedAt: plot.updatedAt
        }))
      },
      stats
    });
  },

  // 创建农场
  async create(token, data) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { name, location, coordinates, totalArea, description } = data;

    if (!name || !location || !totalArea) {
      return createErrorResponse('缺少必要字段', 400);
    }

    const newFarm = {
      id: `farm_${Date.now()}`,
      ownerId: payload.userId,
      name,
      location,
      coordinates: coordinates || { latitude: 0, longitude: 0 },
      totalArea,
      description: description || '',
      status: 'active',
      plotCount: 0,
      deviceCount: 0,
      sensorCount: 0,
      cropTypes: [],
      establishedDate: new Date().toISOString().split('T')[0],
      certification: null,
      contactPerson: null,
      contactPhone: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 在实际应用中，这里应该保存到数据库
    // 这里只是模拟返回成功
    return createApiResponse(newFarm, '农场创建成功');
  },

  // 更新农场
  async update(token, farmId, data) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const farm = dataModule.getFarmById(farmId);
    if (!farm) {
      return createErrorResponse('农场不存在', 404);
    }

    // 检查权限
    if (farm.ownerId !== payload.userId) {
      return createErrorResponse('没有权限修改此农场', 403);
    }

    // 更新允许的字段
    const allowedFields = ['name', 'location', 'coordinates', 'totalArea', 'description', 'certification', 'contactPerson', 'contactPhone'];
    const updatedFields = {};
    
    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        updatedFields[field] = data[field];
        farm[field] = data[field];
      }
    });

    farm.updatedAt = new Date().toISOString();

    return createApiResponse({
      farm,
      updatedFields
    }, '农场信息更新成功');
  },

  // 删除农场
  async delete(token, farmId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const farm = dataModule.getFarmById(farmId);
    if (!farm) {
      return createErrorResponse('农场不存在', 404);
    }

    // 检查权限
    if (farm.ownerId !== payload.userId) {
      return createErrorResponse('没有权限删除此农场', 403);
    }

    // 检查是否有关联的地块和设备
    const plots = dataModule.getPlots(farmId);
    const devices = dataModule.getDevices().filter(device => device.farmId === farmId);
    
    if (plots.length > 0 || devices.length > 0) {
      return createErrorResponse('农场下存在地块或设备，无法删除', 400);
    }

    // 在实际应用中，这里应该从数据库删除
    return createApiResponse(null, '农场删除成功');
  },

  // 获取地块列表
  async getPlots(token, farmId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const plots = dataModule.getPlots(farmId);
    
    const plotsWithDeviceInfo = plots.map(plot => {
      const devices = dataModule.getDevices().filter(device => device.plotId === plot.id);
      const sensors = dataModule.getSensors().filter(sensor => sensor.plotId === plot.id);
      
      return {
        ...plot,
        deviceCount: devices.length,
        sensorCount: sensors.length,
        onlineDevices: devices.filter(device => device.status.online).length,
        onlineSensors: sensors.filter(sensor => sensor.status.online).length,
        devices: devices.map(device => ({
          id: device.id,
          name: device.name,
          type: device.type,
          status: device.status
        })),
        sensors: sensors.map(sensor => ({
          id: sensor.id,
          name: sensor.name,
          type: sensor.type,
          status: sensor.status,
          currentReading: sensor.currentReading
        }))
      };
    });

    return createApiResponse({
      plots: plotsWithDeviceInfo,
      summary: {
        total: plots.length,
        active: plots.filter(plot => plot.status === 'active').length,
        maintenance: plots.filter(plot => plot.status === 'maintenance').length,
        totalArea: plots.reduce((sum, plot) => sum + plot.area, 0),
        plantedArea: plots.reduce((sum, plot) => sum + (plot.currentCrop ? plot.currentCrop.plantingArea : 0), 0)
      }
    });
  },

  // 创建地块
  async createPlot(token, data) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { farmId, name, area, coordinates, soilType, irrigationType, currentCrop } = data;

    if (!farmId || !name || !area) {
      return createErrorResponse('缺少必要字段', 400);
    }

    const farm = dataModule.getFarmById(farmId);
    if (!farm) {
      return createErrorResponse('农场不存在', 404);
    }

    const newPlot = {
      id: `plot_${Date.now()}`,
      farmId,
      name,
      area,
      coordinates: coordinates || { latitude: 0, longitude: 0 },
      soilType: soilType || '未知',
      drainageStatus: '良好',
      irrigationType: irrigationType || '滴灌',
      slopeLevel: '平地',
      status: 'active',
      currentCrop: currentCrop || null,
      devices: [],
      soilAnalysis: {
        ph: 7.0,
        organicMatter: 3.0,
        nitrogen: 1.5,
        phosphorus: 0.8,
        potassium: 2.0,
        lastTestDate: new Date().toISOString().split('T')[0]
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return createApiResponse(newPlot, '地块创建成功');
  },

  // 获取地块详情
  async getPlotDetail(token, plotId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const plot = dataModule.getPlotById(plotId);
    if (!plot) {
      return createErrorResponse('地块不存在', 404);
    }

    const devices = dataModule.getDevices().filter(device => device.plotId === plotId);
    const sensors = dataModule.getSensors().filter(sensor => sensor.plotId === plotId);

    return createApiResponse({
      plot: {
        ...plot,
        devices: devices.map(device => ({
          id: device.id,
          name: device.name,
          type: device.type,
          model: device.model,
          status: device.status,
          installLocation: device.installLocation,
          currentTask: device.currentTask
        })),
        sensors: sensors.map(sensor => ({
          id: sensor.id,
          name: sensor.name,
          type: sensor.type,
          model: sensor.model,
          status: sensor.status,
          installLocation: sensor.installLocation,
          currentReading: sensor.currentReading
        }))
      }
    });
  },

  // 更新地块
  async updatePlot(token, plotId, data) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const plot = dataModule.getPlotById(plotId);
    if (!plot) {
      return createErrorResponse('地块不存在', 404);
    }

    // 更新允许的字段
    const allowedFields = ['name', 'area', 'coordinates', 'soilType', 'irrigationType', 'currentCrop', 'soilAnalysis'];
    const updatedFields = {};
    
    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        updatedFields[field] = data[field];
        plot[field] = data[field];
      }
    });

    plot.updatedAt = new Date().toISOString();

    return createApiResponse({
      plot,
      updatedFields
    }, '地块信息更新成功');
  },

  // 删除地块
  async deletePlot(token, plotId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const plot = dataModule.getPlotById(plotId);
    if (!plot) {
      return createErrorResponse('地块不存在', 404);
    }

    // 检查是否有关联的设备
    const devices = dataModule.getDevices().filter(device => device.plotId === plotId);
    if (devices.length > 0) {
      return createErrorResponse('地块下存在设备，无法删除', 400);
    }

    return createApiResponse(null, '地块删除成功');
  }
};