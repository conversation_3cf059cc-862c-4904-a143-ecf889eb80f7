/* 监测页面样式 */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 图表组件样式 */

.main-chart-wrapper {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #f0f0f0;
  position: static;
}

.chart-info-section {
  margin: 15rpx 0 25rpx 0;
  clear: both;
}

.chart-stats-row {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.stat-card {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  border: 1rpx solid #e9ecef;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #08C160;
  display: block;
}


/* 简化图表样式 */
.chart-wrapper {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.chart-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.chart-mock {
  position: relative;
  height: 300rpx;
  background: linear-gradient(to top, #E8F8EC 0%, transparent 100%);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.chart-points {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.chart-point {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: #08C160;
  border: 3rpx solid white;
  border-radius: 6rpx;
  transform: translate(-50%, 50%);
  box-shadow: 0 2rpx 8rpx rgba(8, 193, 96, 0.3);
}

.chart-info {
  display: flex;
  justify-content: space-around;
  font-size: 24rpx;
  color: #666;
}

.chart-x-axis {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
  padding: 0 20rpx;
}

.x-label {
  font-size: 20rpx;
  color: #666;
}


.container {
  padding: 20rpx;
}

.container::after {
  content: "";
  display: table;
  clear: both;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.chart-card {
  margin-bottom: 80rpx;
  position: relative;
  z-index: 1;
  overflow: hidden;
  padding-bottom: 20rpx;
}

.alarm-card {
  margin-top: 40rpx;
  clear: both;
  position: relative;
  z-index: 2;
  overflow: visible;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #08C160;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 视图模式切换 */
.view-mode-tabs {
  display: flex;
  background-color: #f0f2f5;
  border-radius: 12rpx;
  padding: 8rpx;
}

.mode-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.mode-tab.active {
  background-color: #08C160;
  color: white;
}

/* 仪表盘模式 */
.dashboard-mode {
  margin-top: 30rpx;
}

.gauge-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.gauge-item {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  text-align: center;
}

.gauge-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.gauge-circle {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.gauge-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 12rpx solid #e0e0e0;
  border-radius: 50%;
}

.gauge-fill {
  position: absolute;
  top: 6rpx;
  left: 6rpx;
  right: 6rpx;
  bottom: 6rpx;
  border-radius: 50%;
  border: 12rpx solid transparent;
  border-top-color: #08C160;
  transform-origin: center;
  transition: transform 0.8s ease;
}

.gauge-fill.warning {
  border-top-color: #FF9800;
}

.gauge-fill.danger {
  border-top-color: #F44336;
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.gauge-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #08C160;
  display: block;
  line-height: 1;
}

.gauge-unit {
  font-size: 20rpx;
  color: #666;
  margin-top: 4rpx;
}

.gauge-info {
  text-align: center;
}

.gauge-icon {
  font-size: 40rpx;
  display: block;
  margin-bottom: 12rpx;
}

.gauge-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.gauge-range {
  font-size: 20rpx;
  color: #999;
}

/* 地图模式 */
.map-mode {
  margin-top: 30rpx;
}

.sensor-map {
  background-color: #f8f9fa;
  border-radius: 16rpx;
  overflow: hidden;
}

.map-area {
  position: relative;
  height: 500rpx;
  background: linear-gradient(135deg, #E8F8EC 0%, #C8F2D6 100%);
}

.farm-image {
  width: 100%;
  height: 100%;
  opacity: 0.3;
}

.sensor-pin {
  position: absolute;
  transform: translate(-50%, -100%);
  animation: pulse 2s infinite;
}

.pin-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  margin: 0 auto 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

.sensor-pin.online .pin-dot {
  background-color: #08C160;
}

.sensor-pin.warning .pin-dot {
  background-color: #FF9800;
}

.sensor-pin.offline .pin-dot {
  background-color: #F44336;
}

.pin-label {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  white-space: nowrap;
  margin-bottom: 4rpx;
}

.pin-value {
  background-color: #08C160;
  color: white;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  font-weight: 600;
}

@keyframes pulse {
  0% { transform: translate(-50%, -100%) scale(1); }
  50% { transform: translate(-50%, -100%) scale(1.1); }
  100% { transform: translate(-50%, -100%) scale(1); }
}

.map-legend {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  background-color: white;
  border-top: 1rpx solid #e0e0e0;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.legend-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
}

.legend-dot.online {
  background-color: #08C160;
}

.legend-dot.warning {
  background-color: #FF9800;
}

.legend-dot.offline {
  background-color: #F44336;
}

/* 深度监测模式 */
.depth-mode {
  margin-top: 30rpx;
}

.depth-selector {
  display: flex;
  background-color: #f0f2f5;
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
}

.depth-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.depth-tab.active {
  background-color: #08C160;
  color: white;
}

.depth-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.depth-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border-left: 6rpx solid #08C160;
}

.depth-card .depth-value.warning {
  color: #FF9800;
}

.depth-card .depth-value.danger {
  color: #F44336;
}

.depth-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.depth-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.depth-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.depth-value {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.value-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #08C160;
  margin-right: 8rpx;
}

.value-unit {
  font-size: 24rpx;
  color: #666;
}

.depth-trend {
  margin-bottom: 16rpx;
}

.trend-text {
  font-size: 22rpx;
  color: #999;
}

.mini-chart {
  height: 60rpx;
  position: relative;
  background-color: rgba(8, 193, 96, 0.1);
  border-radius: 8rpx;
  overflow: hidden;
}

.mini-line {
  position: relative;
  height: 100%;
}

.mini-point {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background-color: #08C160;
  border-radius: 3rpx;
  transform: translate(-50%, 50%);
}

/* 传感器状态 */
.sensor-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20rpx;
}

.sensor-item {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
}

.sensor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.sensor-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}

.sensor-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 4rpx;
}

.sensor-status.online .status-dot {
  background-color: #08C160;
}

.sensor-status.offline .status-dot {
  background-color: #F44336;
}

.sensor-location {
  font-size: 20rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.sensor-update {
  font-size: 18rpx;
  color: #999;
}

/* 趋势图表 */
.chart-controls {
  margin-left: auto;
}

.picker-view {
  background-color: #f0f2f5;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.chart-container {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.chart-tabs {
  display: flex;
  background-color: #f0f2f5;
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 20rpx;
}

.chart-tab {
  flex: 1;
  text-align: center;
  padding: 12rpx 8rpx;
  font-size: 22rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.chart-tab.active {
  background-color: #08C160;
  color: white;
}

.chart-container .chart-content {
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  margin-bottom: 0;
  position: relative;
  z-index: auto;
  overflow: hidden;
}

.chart-placeholder {
  text-align: center;
}

.chart-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.chart-mock {
  position: relative;
  height: 300rpx;
  background: linear-gradient(to top, #E8F8EC 0%, transparent 100%);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.chart-line {
  position: absolute;
  top: 50%;
  left: 10%;
  right: 10%;
  height: 4rpx;
  background: linear-gradient(to right, #08C160, #08C160);
  border-radius: 2rpx;
  transform: translateY(-50%);
}

.chart-points {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.chart-point {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: #08C160;
  border: 3rpx solid white;
  border-radius: 6rpx;
  transform: translate(-50%, 50%);
  box-shadow: 0 2rpx 8rpx rgba(8, 193, 96, 0.3);
}

.chart-info {
  display: flex;
  justify-content: space-around;
  font-size: 24rpx;
  color: #666;
}

/* 多级预警系统 */
.alarm-summary {
  display: flex;
  gap: 12rpx;
}

.alarm-count {
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-weight: 500;
}

.alarm-count.light {
  background-color: #f3f4f6;
  color: #6b7280;
}

.alarm-count.moderate {
  background-color: #fff3e0;
  color: #FF9800;
}

.alarm-count.severe {
  background-color: #ffebee;
  color: #F44336;
}

.alarm-count.continuous {
  background-color: #f3e5f5;
  color: #9c27b0;
}

/* 预警过滤 */
.alarm-filter {
  margin: 20rpx 0;
}

.filter-tabs {
  display: flex;
  background-color: #f0f2f5;
  border-radius: 12rpx;
  padding: 6rpx;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 12rpx;
  font-size: 24rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background-color: #08C160;
  color: white;
}

.alarm-list {
  margin-top: 20rpx;
}

.alarm-item {
  display: flex;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.alarm-item.light {
  border-left-color: #6b7280;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

.alarm-item.moderate {
  border-left-color: #FF9800;
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
}

.alarm-item.severe {
  border-left-color: #F44336;
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
}

.alarm-item.continuous {
  border-left-color: #9c27b0;
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
}

.alarm-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
  min-width: 80rpx;
}

.alarm-level-badge {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
  font-weight: 600;
}

.alarm-level-badge.light {
  background-color: #6b7280;
  color: white;
}

.alarm-level-badge.moderate {
  background-color: #FF9800;
  color: white;
}

.alarm-level-badge.severe {
  background-color: #F44336;
  color: white;
}

.alarm-level-badge.continuous {
  background-color: #9c27b0;
  color: white;
}

.level-text {
  font-size: 24rpx;
}

.priority-tag {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  font-weight: 500;
}

.priority-tag.low {
  background-color: #f3f4f6;
  color: #6b7280;
}

.priority-tag.medium {
  background-color: #fff3e0;
  color: #FF9800;
}

.priority-tag.high {
  background-color: #ffebee;
  color: #F44336;
}

.priority-tag.urgent {
  background-color: #f3e5f5;
  color: #9c27b0;
}

.alarm-content {
  flex: 1;
  margin-right: 20rpx;
}

.alarm-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.alarm-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}

.alarm-time {
  font-size: 20rpx;
  color: #999;
  white-space: nowrap;
}

.alarm-details {
  margin-bottom: 16rpx;
}

.alarm-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.alarm-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.alarm-sensor,
.alarm-duration {
  font-size: 22rpx;
  color: #888;
}

.alarm-data {
  display: flex;
  justify-content: space-between;
}

.current-value,
.threshold-range {
  font-size: 22rpx;
  color: #666;
}

.current-value {
  font-weight: 600;
}

.alarm-suggestion {
  background-color: rgba(8, 193, 96, 0.1);
  padding: 16rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #08C160;
}

.suggestion-label {
  font-size: 22rpx;
  color: #08C160;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.suggestion-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
}

.alarm-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  min-width: 120rpx;
}

.no-alarm {
  text-align: center;
  padding: 60rpx 0;
}

.no-alarm-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.text-muted {
  color: #999;
  font-size: 28rpx;
}

.alarm-settings-entry {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e0e0e0;
}

/* 数据操作 */
.data-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

/* 按钮样式 */
.btn {
  padding: 20rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(45deg, #08C160, #08C160);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #08C160;
  color: #08C160;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-mini {
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  border-radius: 8rpx;
}

.btn-text {
  background-color: transparent;
  border: none;
  color: #666;
}