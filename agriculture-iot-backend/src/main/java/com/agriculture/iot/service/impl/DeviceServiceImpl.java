package com.agriculture.iot.service.impl;

import com.agriculture.iot.entity.Device;
import com.agriculture.iot.entity.DeviceType;
import com.agriculture.iot.mapper.DeviceMapper;
import com.agriculture.iot.mapper.DeviceTypeMapper;
import com.agriculture.iot.service.DeviceService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 设备服务实现类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {

    private final DeviceMapper deviceMapper;
    private final DeviceTypeMapper deviceTypeMapper;
    private final ObjectMapper objectMapper;

    @Override
    public IPage<Device> getDeviceList(Page<Device> page, String name, Long deviceTypeId, Long plotId, 
                                     Integer onlineStatus, Integer status) {
        QueryWrapper<Device> wrapper = new QueryWrapper<>();
        
        if (name != null && !name.trim().isEmpty()) {
            wrapper.like("name", name);
        }
        if (deviceTypeId != null) {
            wrapper.eq("device_type_id", deviceTypeId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        if (onlineStatus != null) {
            wrapper.eq("online_status", onlineStatus);
        }
        if (status != null) {
            wrapper.eq("status", status);
        }
        
        wrapper.orderByDesc("created_at");
        return this.page(page, wrapper);
    }

    @Override
    public Device getDeviceById(Long id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Device createDevice(Device device) {
        // 生成设备编码
        if (device.getDeviceCode() == null || device.getDeviceCode().isEmpty()) {
            device.setDeviceCode(generateDeviceCode());
        }
        
        // 设置默认状态
        if (device.getStatus() == null) {
            device.setStatus(1); // 默认启用
        }
        if (device.getOnlineStatus() == null) {
            device.setOnlineStatus(0); // 默认离线
        }
        if (device.getWorkStatus() == null) {
            device.setWorkStatus(0); // 默认停止
        }
        
        boolean saved = this.save(device);
        return saved ? device : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Device updateDevice(Device device) {
        if (this.getById(device.getId()) == null) {
            return null;
        }
        
        boolean updated = this.updateById(device);
        return updated ? device : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDevice(Long id) {
        Device device = this.getById(id);
        if (device == null) {
            return false;
        }
        
        // TODO: 检查是否有关联数据，如传感器数据、操作记录等
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> controlDevice(Long deviceId, Device.ControlRequest controlRequest) {
        Device device = this.getById(deviceId);
        if (device == null) {
            return null;
        }
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 记录控制操作
            String operationId = UUID.randomUUID().toString();
            result.put("operationId", operationId);
            result.put("deviceId", deviceId);
            result.put("action", controlRequest.getAction());
            result.put("timestamp", LocalDateTime.now());
            
            // 根据不同的控制动作执行相应操作
            switch (controlRequest.getAction()) {
                case "start_irrigation":
                    result.put("status", "success");
                    result.put("message", "灌溉启动成功");
                    // 更新设备工作状态
                    device.setWorkStatus(1);
                    this.updateById(device);
                    break;
                    
                case "stop_irrigation":
                    result.put("status", "success");
                    result.put("message", "灌溉停止成功");
                    // 更新设备工作状态
                    device.setWorkStatus(0);
                    this.updateById(device);
                    break;
                    
                case "start_fertilizer":
                    result.put("status", "success");
                    result.put("message", "施肥启动成功");
                    device.setWorkStatus(1);
                    this.updateById(device);
                    break;
                    
                case "stop_fertilizer":
                    result.put("status", "success");
                    result.put("message", "施肥停止成功");
                    device.setWorkStatus(0);
                    this.updateById(device);
                    break;
                    
                case "set_parameters":
                    result.put("status", "success");
                    result.put("message", "参数设置成功");
                    // 更新设备配置参数
                    device.setConfigParams(controlRequest.getParameters());
                    this.updateById(device);
                    break;
                    
                default:
                    result.put("status", "error");
                    result.put("message", "不支持的控制动作");
            }
            
            // TODO: 发送控制指令到实际设备
            // 这里应该集成实际的设备控制协议
            
        } catch (Exception e) {
            log.error("设备控制失败", e);
            result.put("status", "error");
            result.put("message", "控制失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public Device.StatusInfo getDeviceStatus(Long deviceId) {
        Device device = this.getById(deviceId);
        if (device == null) {
            return null;
        }
        
        Device.StatusInfo statusInfo = new Device.StatusInfo();
        statusInfo.setDeviceId(deviceId);
        statusInfo.setOnlineStatus(device.getOnlineStatus());
        statusInfo.setWorkStatus(device.getWorkStatus());
        statusInfo.setBatteryLevel(device.getBatteryLevel());
        statusInfo.setSignalStrength(device.getSignalStrength());
        statusInfo.setLastHeartbeat(device.getLastHeartbeat());
        statusInfo.setCurrentParams(device.getConfigParams());
        
        // TODO: 获取实时状态数据
        // 这里应该从设备或缓存中获取最新的状态信息
        
        return statusInfo;
    }

    @Override
    public List<DeviceType> getDeviceTypes() {
        return deviceTypeMapper.selectList(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindDeviceToPlot(Long deviceId, Long plotId) {
        Device device = this.getById(deviceId);
        if (device == null) {
            return false;
        }
        
        device.setPlotId(plotId);
        return this.updateById(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindDevice(Long deviceId) {
        Device device = this.getById(deviceId);
        if (device == null) {
            return false;
        }
        
        device.setPlotId(null);
        return this.updateById(device);
    }

    @Override
    public IPage<Object> getDeviceLogs(Long deviceId, Page<Object> page, String operationType) {
        // TODO: 实现设备操作日志查询
        // 这里需要查询设备操作记录表
        return deviceMapper.getDeviceLogs(deviceId, page, operationType);
    }

    @Override
    public Device.Statistics getDeviceStatistics(Long deviceId) {
        Device device = this.getById(deviceId);
        if (device == null) {
            return null;
        }
        
        Device.Statistics statistics = new Device.Statistics();
        statistics.setDeviceId(deviceId);
        
        // TODO: 统计设备运行数据
        statistics.setTotalRunningHours(deviceMapper.getTotalRunningHours(deviceId));
        statistics.setMonthlyRunningHours(deviceMapper.getMonthlyRunningHours(deviceId));
        statistics.setTotalOperations(deviceMapper.getTotalOperations(deviceId));
        statistics.setMonthlyOperations(deviceMapper.getMonthlyOperations(deviceId));
        statistics.setFaultCount(deviceMapper.getFaultCount(deviceId));
        statistics.setMaintenanceCount(deviceMapper.getMaintenanceCount(deviceId));
        
        // 计算在线率
        // TODO: 实现在线率计算逻辑
        
        statistics.setLastMaintenanceTime(device.getLastMaintenanceDate().atStartOfDay());
        statistics.setNextMaintenanceTime(device.getNextMaintenanceDate().atStartOfDay());
        
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateDeviceStatus(List<Long> deviceIds, Integer status) {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return false;
        }
        
        return deviceMapper.batchUpdateStatus(deviceIds, status) > 0;
    }

    @Override
    public Device.HealthInfo deviceHealthCheck(Long deviceId) {
        Device device = this.getById(deviceId);
        if (device == null) {
            return null;
        }
        
        Device.HealthInfo healthInfo = new Device.HealthInfo();
        healthInfo.setDeviceId(deviceId);
        healthInfo.setCheckTime(LocalDateTime.now());
        
        // TODO: 实现设备健康检查逻辑
        // 这里应该包含电池电量、信号强度、最后心跳时间等检查
        
        int healthScore = calculateHealthScore(device);
        healthInfo.setHealthScore(healthScore);
        
        if (healthScore >= 80) {
            healthInfo.setHealthStatus("健康");
        } else if (healthScore >= 60) {
            healthInfo.setHealthStatus("良好");
        } else if (healthScore >= 40) {
            healthInfo.setHealthStatus("一般");
        } else {
            healthInfo.setHealthStatus("不佳");
        }
        
        return healthInfo;
    }

    @Override
    public Device.ConfigInfo getDeviceConfig(Long deviceId) {
        Device device = this.getById(deviceId);
        if (device == null) {
            return null;
        }
        
        Device.ConfigInfo configInfo = new Device.ConfigInfo();
        configInfo.setDeviceId(deviceId);
        
        // TODO: 从设备配置参数中解析配置信息
        // 这里应该解析device.getConfigParams()字符串
        
        return configInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDeviceConfig(Long deviceId, Device.ConfigInfo configInfo) {
        Device device = this.getById(deviceId);
        if (device == null) {
            return false;
        }
        
        try {
            // 将配置信息转换为JSON字符串
            String configJson = objectMapper.writeValueAsString(configInfo);
            device.setConfigParams(configJson);
            
            return this.updateById(device);
        } catch (JsonProcessingException e) {
            log.error("配置信息序列化失败", e);
            return false;
        }
    }

    /**
     * 生成设备编码
     */
    private String generateDeviceCode() {
        return "DEV" + System.currentTimeMillis();
    }

    /**
     * 计算设备健康评分
     */
    private int calculateHealthScore(Device device) {
        int score = 100;
        
        // 电池电量评分
        if (device.getBatteryLevel() != null) {
            if (device.getBatteryLevel() < 20) {
                score -= 30;
            } else if (device.getBatteryLevel() < 50) {
                score -= 15;
            }
        }
        
        // 信号强度评分
        if (device.getSignalStrength() != null) {
            if (device.getSignalStrength() < 30) {
                score -= 20;
            } else if (device.getSignalStrength() < 60) {
                score -= 10;
            }
        }
        
        // 在线状态评分
        if (device.getOnlineStatus() != null && device.getOnlineStatus() == 0) {
            score -= 40;
        }
        
        // 工作状态评分
        if (device.getWorkStatus() != null && device.getWorkStatus() == 2) {
            score -= 50; // 故障状态
        }
        
        return Math.max(0, score);
    }
}