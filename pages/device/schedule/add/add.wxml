<!--新增灌溉计划页面-->
<view class="page-container">
  
  <!-- 基本信息 -->
  <view class="card">
    <view class="section-title">基本信息</view>
    
    <view class="form-item">
      <text class="form-label">计划名称</text>
      <input class="form-input" 
             value="{{form.name}}" 
             bindinput="onFormInput" 
             data-field="name" 
             placeholder="请输入计划名称"
             type="text"
             maxlength="20"/>
    </view>
    
    <!-- 选择执行日期 -->
    <view class="form-item">
      <text class="form-label">选择执行日期</text>
      <view class="weekday-selector">
        <view class="weekday-item {{item.selected ? 'selected' : ''}}" 
              wx:for="{{form.customWeekdays}}" wx:key="day"
              bindtap="toggleWeekday" data-day="{{item.day}}">
          <text class="weekday-text">{{item.label}}</text>
        </view>
      </view>
      <text class="form-desc">选择灌溉计划执行的星期几，可多选</text>
    </view>
  </view>
  
  <!-- 时间设置 -->
  <view class="card">
    <view class="section-title">
      <text>时间设置</text>
      <button class="btn btn-primary btn-small" bindtap="addTimeSlot">
        ➕ 添加时段
      </button>
    </view>
    
    <view class="time-slots">
      <view class="time-slot-edit" wx:for="{{form.timeSlots}}" wx:key="tempId">
        <view class="slot-header">
          <text class="slot-title">时段 {{index + 1}}</text>
          <button class="btn btn-danger btn-mini" bindtap="removeTimeSlot" data-index="{{index}}">
            删除
          </button>
        </view>
        
        <view class="slot-form">
          <view class="time-inputs">
            <view class="time-input-group">
              <text class="input-label">开始时间</text>
              <picker mode="time" value="{{item.startTime}}" 
                      bindchange="onTimeSlotChange" data-index="{{index}}" data-field="startTime">
                <view class="picker-view">{{item.startTime}}</view>
              </picker>
            </view>
            <view class="time-input-group">
              <text class="input-label">结束时间</text>
              <picker mode="time" value="{{item.endTime}}" 
                      bindchange="onTimeSlotChange" data-index="{{index}}" data-field="endTime">
                <view class="picker-view">{{item.endTime}}</view>
              </picker>
            </view>
          </view>
          
          <view class="param-inputs">
            <view class="param-input-group">
              <text class="input-label">时长(分钟)</text>
              <input type="number" value="{{item.duration}}" 
                     bindinput="onTimeSlotInput" data-index="{{index}}" data-field="duration"
                     class="param-input" placeholder="30"/>
            </view>
            <view class="param-input-group">
              <text class="input-label">流量(L/min)</text>
              <input type="number" value="{{item.flowRate}}" 
                     bindinput="onTimeSlotInput" data-index="{{index}}" data-field="flowRate"
                     class="param-input" placeholder="60"/>
            </view>
          </view>
          
          <view class="zone-selector">
            <text class="input-label">灌溉区域</text>
            <view class="zone-grid">
              <view class="zone-option {{zone.selected ? 'selected' : ''}}" 
                    wx:for="{{item.zoneOptions}}" wx:key="id" wx:for-item="zone"
                    bindtap="toggleSlotZone" data-slot="{{index}}" data-zone="{{zone.id}}">
                <text class="zone-name">{{zone.name}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="btn btn-outline btn-large" bindtap="cancelSchedule">
      取消
    </button>
    <button class="btn btn-primary btn-large" bindtap="saveSchedule">
      {{editMode ? '更新计划' : '保存计划'}}
    </button>
  </view>
  
</view>