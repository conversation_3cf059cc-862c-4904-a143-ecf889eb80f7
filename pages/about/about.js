Page({
  data: {
    appInfo: {
      name: '智慧农业物联网小程序',
      version: 'v1.0.0',
      buildNumber: '20240708',
      description: '集成水肥一体机控制和土壤墒情监测的综合农业管理平台',
      developer: '智慧农业科技有限公司',
      contact: '400-123-4567',
      email: '<EMAIL>',
      website: 'https://www.smartfarm.com'
    },
    features: [
      {
        icon: '💧',
        title: '水肥一体控制',
        desc: '远程控制灌溉和施肥设备'
      },
      {
        icon: '📊',
        title: '土壤监测',
        desc: '实时监测土壤环境数据'
      },
      {
        icon: '⚡',
        title: '智能预警',
        desc: '异常情况及时通知提醒'
      },
      {
        icon: '📱',
        title: '移动管理',
        desc: '随时随地管理农场设备'
      }
    ],
    teamMembers: [
      {
        name: '技术团队',
        role: '产品研发',
        desc: '专业的农业物联网技术团队'
      },
      {
        name: '农业专家',
        role: '农艺指导',
        desc: '资深农业专家提供技术支持'
      },
      {
        name: '客服团队',
        role: '用户服务',
        desc: '7x24小时用户服务支持'
      }
    ],
    changelog: [
      {
        version: 'v1.0.0',
        date: '2024-07-08',
        changes: [
          '初始版本发布',
          '基础设备控制功能',
          '土壤监测数据展示',
          '用户管理系统'
        ]
      }
    ]
  },

  onLoad() {
    this.loadAppInfo();
  },

  loadAppInfo() {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    console.log('System Info:', systemInfo);
  },

  copyContact() {
    wx.setClipboardData({
      data: this.data.appInfo.contact,
      success: () => {
        wx.showToast({
          title: '电话已复制',
          icon: 'success'
        });
      }
    });
  },

  copyEmail() {
    wx.setClipboardData({
      data: this.data.appInfo.email,
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        });
      }
    });
  },

  viewPrivacy() {
    wx.showModal({
      title: '隐私政策',
      content: '我们非常重视您的隐私保护，请访问我们的官网查看完整的隐私政策内容。',
      showCancel: true,
      cancelText: '取消',
      confirmText: '访问官网',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: this.data.appInfo.website,
            success: () => {
              wx.showToast({
                title: '链接已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  viewTerms() {
    wx.showModal({
      title: '用户协议',
      content: '请仔细阅读用户协议内容。使用本小程序即表示您同意遵守相关协议条款。',
      showCancel: true,
      cancelText: '取消',
      confirmText: '我同意',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '感谢您的支持',
            icon: 'success'
          });
        }
      }
    });
  },

  checkUpdate() {
    wx.showLoading({
      title: '检查更新中...'
    });

    // 模拟检查更新
    setTimeout(() => {
      wx.hideLoading();
      wx.showModal({
        title: '版本检查',
        content: '当前已是最新版本 v1.0.0',
        showCancel: false,
        confirmText: '确定'
      });
    }, 2000);
  },

  contactSupport() {
    wx.showActionSheet({
      itemList: ['拨打客服电话', '复制客服邮箱', '意见反馈'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            wx.makePhoneCall({
              phoneNumber: this.data.appInfo.contact
            });
            break;
          case 1:
            this.copyEmail();
            break;
          case 2:
            wx.navigateTo({
              url: '/pages/feedback/feedback'
            });
            break;
        }
      }
    });
  },

  shareApp() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        wx.showToast({
          title: '分享成功',
          icon: 'success'
        });
      }
    });
  }
})