<!--设备管理页面-->
<view class="device-container">
  
  <!-- 设备状态总览 -->
  <view class="device-overview-card">
    <view class="card-header">
      <text class="card-title">设备状态总览</text>
    </view>
    
    <view class="device-list">
      <view class="device-item" wx:for="{{deviceList}}" wx:key="id">
        <view class="left-line {{item.status === 'online' ? 'green-line' : 'red-line'}}"></view>
        <view class="device-info">
          <text class="device-name">{{item.name}}</text>
          <text class="device-type">{{item.type}}</text>
          <text class="device-location">{{item.location}}</text>
        </view>
        <view class="device-status">
          <text class="status-text {{item.status}}">{{item.statusText}}</text>
          <view class="control-btn {{item.status === 'online' ? (item.canControl ? 'green-btn' : 'outline-btn') : 'disabled-btn'}}" bind:tap="controlDevice" data-id="{{item.id}}">{{item.actionText}}</view>
          <view class="detail-btn" bind:tap="navigateToDetail" data-id="{{item.id}}">详情</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 设备分类 -->
  <view class="device-category-card">
    <view class="card-header">
      <text class="card-title">设备分类</text>
    </view>
    
    <view class="category-grid">
      <view class="category-item" bind:tap="filterByCategory" data-category="irrigation">
        <view class="category-icon water-icon"></view>
        <view class="category-info">
          <text class="category-name">灌溉设备</text>
          <text class="category-count">{{irrigationCount}}台</text>
        </view>
      </view>
      
      <view class="category-item" bind:tap="filterByCategory" data-category="sensor">
        <view class="category-icon sensor-icon"></view>
        <view class="category-info">
          <text class="category-name">传感设备</text>
          <text class="category-count">{{sensorCount}}台</text>
        </view>
      </view>
      
      <view class="category-item" bind:tap="filterByCategory" data-category="control">
        <view class="category-icon control-icon"></view>
        <view class="category-info">
          <text class="category-name">控制设备</text>
          <text class="category-count">{{controlCount}}台</text>
        </view>
      </view>
      
      <view class="category-item" bind:tap="filterByCategory" data-category="all">
        <view class="category-icon all-icon"></view>
        <view class="category-info">
          <text class="category-name">全部设备</text>
          <text class="category-count">{{totalCount}}台</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作历史 -->
  <view class="operation-history-card">
    <view class="card-header">
      <text class="card-title">最近操作</text>
      <text class="view-all-link" bind:tap="viewAllOperations">查看全部</text>
    </view>
    
    <view class="operation-list">
      <view class="operation-item" wx:for="{{recentOperations}}" wx:key="id">
        <view class="operation-icon {{item.icon}}-icon"></view>
        <view class="operation-info">
          <text class="operation-name">{{item.name}}</text>
          <text class="operation-time">{{item.time}}</text>
        </view>
        <text class="operation-status {{item.status}}">{{item.statusText}}</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions-card">
    <view class="card-header">
      <text class="card-title">快捷操作</text>
    </view>
    
    <view class="quick-action-item" bind:tap="quickAddDevice">
      <view class="action-icon add-icon"></view>
      <text class="action-text">添加设备</text>
    </view>
    
    <view class="quick-action-item" bind:tap="refreshDevices">
      <view class="action-icon refresh-icon"></view>
      <text class="action-text">刷新设备状态</text>
    </view>
  </view>


</view>