<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agriculture.iot.mapper.DeviceMapper">

    <!-- 分页查询设备列表 -->
    <select id="selectDevicePage" resultType="com.agriculture.iot.entity.Device">
        SELECT *
        FROM devices
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="plotId != null">
                AND plot_id = #{plotId}
            </if>
            <if test="typeId != null">
                AND type_id = #{typeId}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 查询设备详情 -->
    <select id="selectDeviceDetail" resultType="com.agriculture.iot.entity.Device">
        SELECT d.*, t.type_name
        FROM devices d
        LEFT JOIN device_types t ON d.type_id = t.id
        WHERE d.id = #{deviceId}
    </select>

    <!-- 获取设备操作日志 -->
    <select id="getDeviceLogs" resultType="java.lang.Object">
        SELECT 
            ol.id,
            ol.operation_type,
            ol.operation_content,
            ol.operation_result,
            ol.operator_id,
            ol.created_at,
            ol.parameters
        FROM operation_logs ol
        WHERE ol.device_id = #{deviceId}
        <if test="operationType != null and operationType != ''">
            AND ol.operation_type = #{operationType}
        </if>
        ORDER BY ol.created_at DESC
    </select>

    <!-- 获取设备总运行时长 -->
    <select id="getTotalRunningHours" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(TIMESTAMPDIFF(HOUR, start_time, end_time)), 0)
        FROM device_operation_records
        WHERE device_id = #{deviceId}
        AND operation_type IN ('irrigation', 'fertilizer')
        AND end_time IS NOT NULL
    </select>

    <!-- 获取设备本月运行时长 -->
    <select id="getMonthlyRunningHours" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(TIMESTAMPDIFF(HOUR, start_time, end_time)), 0)
        FROM device_operation_records
        WHERE device_id = #{deviceId}
        AND operation_type IN ('irrigation', 'fertilizer')
        AND end_time IS NOT NULL
        AND created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')
        AND created_at &lt; DATE_ADD(DATE_FORMAT(NOW(), '%Y-%m-01'), INTERVAL 1 MONTH)
    </select>

    <!-- 获取设备总操作次数 -->
    <select id="getTotalOperations" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM device_operation_records
        WHERE device_id = #{deviceId}
    </select>

    <!-- 获取设备本月操作次数 -->
    <select id="getMonthlyOperations" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM device_operation_records
        WHERE device_id = #{deviceId}
        AND created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')
        AND created_at &lt; DATE_ADD(DATE_FORMAT(NOW(), '%Y-%m-01'), INTERVAL 1 MONTH)
    </select>

    <!-- 获取设备故障次数 -->
    <select id="getFaultCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM device_fault_records
        WHERE device_id = #{deviceId}
    </select>

    <!-- 获取设备维护次数 -->
    <select id="getMaintenanceCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM device_maintenance_records
        WHERE device_id = #{deviceId}
    </select>

    <!-- 批量更新设备状态 -->
    <update id="batchUpdateStatus">
        UPDATE devices
        SET status = #{status}, updated_at = NOW()
        WHERE id IN
        <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </update>

</mapper>