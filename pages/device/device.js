const app = getApp();
const api = require('../../utils/api.js');

Page({
  data: {
    deviceList: [],
    // 设备分类统计
    irrigationCount: 0,
    sensorCount: 0,
    controlCount: 0,
    totalCount: 0,
    deviceDetails: [],
    recentOperations: [],
    loading: false,
    refreshing: false
  },

  onLoad() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.loadDeviceData();
  },

  onShow() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.refreshDeviceStatus();
  },

  onPullDownRefresh() {
    this.refreshDeviceStatus();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  async loadDeviceData() {
    this.setData({ loading: true });
    
    try {
      wx.showLoading({ title: '加载中...' });
      
      // 加载设备列表
      const deviceResponse = await api.getDeviceList();
      console.log('设备列表API响应:', deviceResponse);
      
      if (deviceResponse && deviceResponse.code === 200 && deviceResponse.data) {
        const devices = deviceResponse.data.devices || [];
        
        // 处理设备数据格式
        const deviceList = devices.map(device => ({
          id: device.id,
          name: device.name,
          type: device.type === 'irrigation' ? '智能灌溉设备' : '环境监测设备',
          location: device.installLocation || device.plotId,
          status: device.status.online ? 'online' : 'offline',
          statusText: device.status.online ? '在线' : '离线',
          canControl: device.type === 'irrigation' && device.status.online,
          actionText: device.status.online ? (device.type === 'irrigation' ? '控制' : '查看') : '离线'
        }));
        
        // 计算统计数据
        const irrigationCount = devices.filter(d => d.type === 'irrigation').length;
        const sensorCount = devices.filter(d => d.type === 'sensor').length;
        const controlCount = devices.filter(d => d.type === 'control').length;
        
        this.setData({
          deviceList: deviceList,
          irrigationCount: irrigationCount,
          sensorCount: sensorCount,
          controlCount: controlCount,
          totalCount: devices.length
        });
      }
      
      // 加载最近操作记录
      await this.loadRecentOperations();
      
    } catch (error) {
      console.error('加载设备数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
      this.setData({ loading: false });
    }
  },

  async loadRecentOperations() {
    try {
      const response = await api.getOperationLogs({ 
        limit: 10,
        type: 'all'
      });
      
      if (response.code === 200) {
        const logs = response.data.logs || [];
        const operations = logs.slice(0, 5).map(log => ({
          id: log.id,
          name: log.title,
          time: this.formatTime(log.time),
          type: log.type,
          icon: log.icon,
          status: log.status,
          statusText: log.statusText
        }));
        
        this.setData({
          recentOperations: operations
        });
      }
    } catch (error) {
      console.error('加载操作记录失败:', error);
    }
  },

  formatTime(timeString) {
    const time = new Date(timeString);
    const now = new Date();
    const diff = now - time;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else {
      return `${days}天前`;
    }
  },

  async refreshDeviceStatus() {
    this.setData({ refreshing: true });
    
    try {
      await this.loadDeviceData();
    } catch (error) {
      console.error('刷新设备状态失败:', error);
    } finally {
      this.setData({ refreshing: false });
    }
  },

  controlDevice(e) {
    const deviceId = e.currentTarget.dataset.id;
    console.log('点击了控制按钮，设备ID:', deviceId);
    
    const device = this.data.deviceList.find(item => item.id == deviceId);
    console.log('找到的设备:', device);
    
    if (device) {
      // 根据设备类型跳转到不同的控制页面
      if (device.type === '智能灌溉设备') {
        console.log('跳转到设备详情页面进行控制');
        wx.navigateTo({
          url: `/pages/device/detail/detail?id=${deviceId}`
        });
      } else if (device.type === '环境监测设备') {
        console.log('跳转到监测页面');
        wx.switchTab({
          url: '/pages/monitor/monitor'
        });
      } else {
        console.log('显示控制确认对话框');
        wx.showModal({
          title: '设备控制',
          content: `确定要控制 ${device.name} 吗？`,
          success: (res) => {
            if (res.confirm) {
              wx.showToast({
                title: '控制指令已发送',
                icon: 'success'
              });
            }
          }
        });
      }
    } else {
      console.error('未找到设备信息');
      wx.showToast({
        title: '设备信息不存在',
        icon: 'none'
      });
    }
  },

  // 新增导航方法
  navigateToDetail(e) {
    const deviceId = e.currentTarget.dataset.id;
    console.log('点击了详情按钮，设备ID:', deviceId);
    
    const device = this.data.deviceList.find(item => item.id == deviceId);
    console.log('找到的设备:', device);
    
    if (device) {
      // 根据设备类型跳转到不同的详情页面
      if (device.type === '智能灌溉设备') {
        // 水肥一体机跳转到设备详情页面
        console.log('跳转到设备详情页面');
        wx.navigateTo({
          url: `/pages/device/detail/detail?id=${deviceId}`,
          success: (res) => {
            console.log('导航成功');
          },
          fail: (err) => {
            console.error('导航失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else if (device.type === '环境监测设备') {
        // 传感器跳转到传感器详情页面
        console.log('跳转到传感器详情页面');
        let sensorId = deviceId;
        if (deviceId == 2) {
          sensorId = 1; // 土壤传感器-01
        }
        wx.navigateTo({
          url: `/pages/monitor/sensor/detail/detail?id=${sensorId}`,
          success: (res) => {
            console.log('导航成功');
          },
          fail: (err) => {
            console.error('导航失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else {
        // 其他设备跳转到通用设备详情页面
        console.log('跳转到通用设备详情页面');
        wx.navigateTo({
          url: `/pages/device/detail/detail?id=${deviceId}`,
          success: (res) => {
            console.log('导航成功');
          },
          fail: (err) => {
            console.error('导航失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    } else {
      console.error('未找到设备信息');
      wx.showToast({
        title: '设备信息不存在',
        icon: 'none'
      });
    }
  },

  navigateToConfig(e) {
    const deviceId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/device/config/config?id=${deviceId}`
    });
  },

  navigateToLogs(e) {
    const deviceId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/device/logs/logs?id=${deviceId}`
    });
  },

  // 设备分类过滤
  filterByCategory(e) {
    const category = e.currentTarget.dataset.category;
    let filteredDevices = [];
    
    switch (category) {
      case 'irrigation':
        filteredDevices = this.data.deviceList.filter(device => 
          device.type === '智能灌溉设备'
        );
        wx.showToast({
          title: `显示${filteredDevices.length}台灌溉设备`,
          icon: 'none'
        });
        break;
      case 'sensor':
        filteredDevices = this.data.deviceList.filter(device => 
          device.type === '环境监测设备'
        );
        wx.showToast({
          title: `显示${filteredDevices.length}台传感设备`,
          icon: 'none'
        });
        break;
      case 'control':
        filteredDevices = this.data.deviceList.filter(device => 
          device.type === '控制设备'
        );
        wx.showToast({
          title: `显示${filteredDevices.length}台控制设备`,
          icon: 'none'
        });
        break;
      case 'all':
      default:
        filteredDevices = this.data.deviceList;
        wx.showToast({
          title: `显示全部${filteredDevices.length}台设备`,
          icon: 'none'
        });
        break;
    }
    
    // 这里可以更新显示的设备列表
    console.log('过滤后的设备:', filteredDevices);
  },

  openIrrigationDetail() {
    wx.navigateTo({
      url: '/pages/device/irrigation/irrigation'
    });
  },

  openFertilizerDetail() {
    wx.navigateTo({
      url: '/pages/device/fertilizer/fertilizer'
    });
  }
})