# 智慧农业物联网小程序

## 项目简介

智慧农业物联网小程序是一个集成水肥一体机控制和土壤墒情监测的综合农业管理平台，帮助农户实现精准农业和智能化管理。

## 功能特点

- 🌱 **水肥一体机控制**：远程控制灌溉和施肥系统
- 📡 **土壤监测**：实时监测土壤湿度、温度、pH值等参数
- 📊 **数据分析**：历史数据图表展示和趋势分析
- ⚠️ **预警系统**：土壤指标异常时自动报警
- 🏡 **地块管理**：多地块农场统一管理
- 👤 **用户管理**：个人信息和系统设置

## 技术栈

- **前端**：微信小程序原生开发
- **UI框架**：原生组件 + 自定义组件
- **图表库**：ECharts
- **状态管理**：页面级状态管理
- **网络请求**：封装的API模块

## 项目结构

```
nongye/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 站点地图配置
├── project.config.json   # 项目配置文件
├── pages/                # 页面文件夹
│   ├── index/            # 首页
│   ├── device/           # 设备管理
│   ├── monitor/          # 监测数据
│   ├── farm/             # 地块管理
│   ├── profile/          # 个人中心
│   └── login/            # 登录页面
├── components/           # 自定义组件
│   └── chart/            # 图表组件
├── utils/                # 工具函数
│   ├── api.js            # API接口封装
│   └── util.js           # 工具函数
└── images/               # 图片资源
```

## 开发环境设置

1. 安装微信开发者工具
2. 导入项目到微信开发者工具
3. 配置小程序AppID（project.config.json）
4. 修改API接口地址（utils/api.js）

## 配置说明

### API接口配置

编辑 `utils/api.js` 文件，修改 `API_BASE_URL` 为你的后端API地址：

```javascript
const API_BASE_URL = 'https://your-api-domain.com';
```

### 小程序配置

编辑 `project.config.json` 文件，设置你的小程序AppID：

```json
{
  "appid": "your-app-id"
}
```

## 页面说明

### 首页（index）
- 显示农场概览信息
- 设备状态监控
- 环境数据展示
- 快捷操作按钮
- 今日任务列表

### 设备管理（device）
- 水肥一体机控制
- 灌溉系统管理
- 施肥计划设置
- 设备状态监控

### 监测数据（monitor）
- 实时传感器数据
- 历史数据分析
- 预警信息管理
- 数据导出功能

### 地块管理（farm）
- 地块信息维护
- 作物管理
- 设备绑定关系

### 个人中心（profile）
- 用户信息管理
- 系统设置
- 帮助中心

## 开发指南

### 添加新页面

1. 在 `pages` 目录下创建新的页面文件夹
2. 在 `app.json` 中注册页面路径
3. 实现页面的 `.wxml`、`.wxss`、`.js`、`.json` 文件

### 使用图表组件

```xml
<chart 
  title="土壤湿度趋势" 
  type="line" 
  data="{{chartData}}" 
  width="{{350}}" 
  height="{{300}}"
  bind:export="onExportChart">
</chart>
```

### API调用示例

```javascript
const api = require('../../utils/api.js');

// 获取设备状态
api.getDeviceStatus().then(res => {
  console.log(res.data);
}).catch(err => {
  console.error(err);
});
```

## 部署说明

1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 登录微信公众平台提交审核
4. 审核通过后发布上线

## 注意事项

- 确保API接口支持HTTPS
- 配置合法域名到小程序后台
- 测试各种网络环境下的功能
- 遵循微信小程序开发规范

## 更新日志

### v1.0.0
- 基础框架搭建
- 首页功能实现
- 设备控制基础功能
- 监测数据展示
- 用户管理系统

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。