<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriculture.iot.mapper.AlarmMapper">

    <!-- Alarm.Detail结果映射 -->
    <resultMap id="AlarmDetailResultMap" type="com.agriculture.iot.entity.Alarm$Detail">
        <id column="id" property="id"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_type_text" property="alarmTypeText"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_level_text" property="alarmLevelText"/>
        <result column="title" property="title"/>
        <result column="description" property="description"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_name" property="deviceName"/>
        <result column="plot_id" property="plotId"/>
        <result column="plot_name" property="plotName"/>
        <result column="parameter" property="parameter"/>
        <result column="current_value" property="currentValue"/>
        <result column="threshold_value" property="thresholdValue"/>
        <result column="unit" property="unit"/>
        <result column="status" property="status"/>
        <result column="status_text" property="statusText"/>
        <result column="suggestion" property="suggestion"/>
        <result column="acknowledged_at" property="acknowledgedAt"/>
        <result column="acknowledged_by" property="acknowledgedBy"/>
        <result column="resolved_at" property="resolvedAt"/>
        <result column="resolved_by" property="resolvedBy"/>
        <result column="resolution_notes" property="resolutionNotes"/>
        <result column="duration" property="duration"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- Alarm.Rule结果映射 -->
    <resultMap id="AlarmRuleResultMap" type="com.agriculture.iot.entity.Alarm$Rule">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="type" property="type"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_name" property="deviceName"/>
        <result column="plot_id" property="plotId"/>
        <result column="plot_name" property="plotName"/>
        <result column="parameter" property="parameter"/>
        <result column="condition" property="condition"/>
        <result column="threshold_value" property="thresholdValue"/>
        <result column="min_value" property="minValue"/>
        <result column="max_value" property="maxValue"/>
        <result column="level" property="level"/>
        <result column="level_text" property="levelText"/>
        <result column="enabled" property="enabled"/>
        <result column="notification_methods" property="notificationMethods"/>
        <result column="notification_users" property="notificationUsers"/>
        <result column="auto_action" property="autoAction"/>
        <result column="action_type" property="actionType"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 分页查询报警列表 -->
    <select id="selectAlarmDetailsPage" resultMap="AlarmDetailResultMap">
        SELECT 
            a.id,
            a.alarm_type,
            CASE a.alarm_type 
                WHEN 'sensor' THEN '传感器异常'
                WHEN 'device' THEN '设备故障'
                WHEN 'threshold' THEN '阈值超限'
                WHEN 'offline' THEN '设备离线'
                ELSE a.alarm_type
            END as alarm_type_text,
            a.alarm_level,
            CASE a.alarm_level
                WHEN 'low' THEN '低'
                WHEN 'medium' THEN '中'
                WHEN 'high' THEN '高'
                WHEN 'critical' THEN '严重'
                ELSE a.alarm_level
            END as alarm_level_text,
            a.title,
            a.description,
            a.device_id,
            d.name as device_name,
            a.plot_id,
            p.name as plot_name,
            a.status,
            CASE a.status
                WHEN 'active' THEN '活跃'
                WHEN 'acknowledged' THEN '已确认'
                WHEN 'resolved' THEN '已解决'
                ELSE a.status
            END as status_text,
            a.acknowledged_at,
            a.resolved_at,
            a.resolution_notes,
            TIMESTAMPDIFF(MINUTE, a.created_at, COALESCE(a.resolved_at, NOW())) as duration,
            a.created_at,
            a.updated_at
        FROM alarms a
        LEFT JOIN devices d ON a.device_id = d.id
        LEFT JOIN plots p ON a.plot_id = p.id
        <where>
            <if test="level != null and level != ''">
                AND a.alarm_level = #{level}
            </if>
            <if test="status != null and status != ''">
                AND a.status = #{status}
            </if>
            <if test="type != null and type != ''">
                AND a.alarm_type = #{type}
            </if>
            <if test="plotId != null">
                AND a.plot_id = #{plotId}
            </if>
            <if test="deviceId != null">
                AND a.device_id = #{deviceId}
            </if>
            <if test="startTime != null">
                AND a.created_at >= #{startTime}
            </if>
            <if test="endTime != null">
                AND a.created_at &lt;= #{endTime}
            </if>
        </where>
        ORDER BY a.created_at DESC
    </select>

    <!-- 根据ID获取报警详情 -->
    <select id="selectAlarmDetailById" resultMap="AlarmDetailResultMap">
        SELECT 
            a.id,
            a.alarm_type,
            a.alarm_level,
            a.title,
            a.description,
            a.device_id,
            d.name as device_name,
            a.plot_id,
            p.name as plot_name,
            a.status,
            a.acknowledged_at,
            a.resolved_at,
            a.resolution_notes,
            a.created_at,
            a.updated_at
        FROM alarms a
        LEFT JOIN devices d ON a.device_id = d.id
        LEFT JOIN plots p ON a.plot_id = p.id
        WHERE a.id = #{id}
    </select>

    <!-- 获取最新报警列表 -->
    <select id="selectLatestAlarms" resultMap="AlarmDetailResultMap">
        SELECT 
            a.id,
            a.alarm_type,
            a.alarm_level,
            a.title,
            a.description,
            a.device_id,
            d.name as device_name,
            a.plot_id,
            p.name as plot_name,
            a.status,
            a.created_at
        FROM alarms a
        LEFT JOIN devices d ON a.device_id = d.id
        LEFT JOIN plots p ON a.plot_id = p.id
        ORDER BY a.created_at DESC
        LIMIT #{limit}
    </select>

    <!-- 获取活跃报警列表 -->
    <select id="selectActiveAlarms" resultMap="AlarmDetailResultMap">
        SELECT 
            a.id,
            a.alarm_type,
            a.alarm_level,
            a.title,
            a.description,
            a.device_id,
            d.name as device_name,
            a.plot_id,
            p.name as plot_name,
            a.status,
            a.created_at
        FROM alarms a
        LEFT JOIN devices d ON a.device_id = d.id
        LEFT JOIN plots p ON a.plot_id = p.id
        WHERE a.status = 'active'
        <if test="plotId != null">
            AND a.plot_id = #{plotId}
        </if>
        <if test="level != null and level != ''">
            AND a.alarm_level = #{level}
        </if>
        ORDER BY a.alarm_level DESC, a.created_at DESC
    </select>

    <!-- 批量更新报警状态 -->
    <update id="batchUpdateAlarmStatus">
        UPDATE alarms 
        SET status = #{status}, updated_at = NOW()
        WHERE id IN
        <foreach collection="alarmIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 确认报警 -->
    <update id="acknowledgeAlarm">
        UPDATE alarms 
        SET status = 'acknowledged', 
            acknowledged_at = #{acknowledgedAt},
            resolution_notes = #{notes},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 解决报警 -->
    <update id="resolveAlarm">
        UPDATE alarms 
        SET status = 'resolved', 
            resolved_at = #{resolvedAt},
            resolution_notes = #{notes},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 分页查询报警规则 -->
    <select id="selectAlarmRulesPage" resultMap="AlarmRuleResultMap">
        SELECT 
            ar.id,
            ar.name,
            ar.description,
            ar.type,
            ar.device_id,
            d.name as device_name,
            ar.plot_id,
            p.name as plot_name,
            ar.parameter,
            ar.condition,
            ar.threshold_value,
            ar.min_value,
            ar.max_value,
            ar.level,
            CASE ar.level
                WHEN 'low' THEN '低'
                WHEN 'medium' THEN '中'
                WHEN 'high' THEN '高'
                WHEN 'critical' THEN '严重'
                ELSE ar.level
            END as level_text,
            ar.enabled,
            ar.notification_methods,
            ar.notification_users,
            ar.auto_action,
            ar.action_type,
            ar.created_at,
            ar.updated_at
        FROM alarm_rules ar
        LEFT JOIN devices d ON ar.device_id = d.id
        LEFT JOIN plots p ON ar.plot_id = p.id
        <where>
            <if test="type != null and type != ''">
                AND ar.type = #{type}
            </if>
            <if test="enabled != null">
                AND ar.enabled = #{enabled}
            </if>
            <if test="plotId != null">
                AND ar.plot_id = #{plotId}
            </if>
            <if test="deviceId != null">
                AND ar.device_id = #{deviceId}
            </if>
        </where>
        ORDER BY ar.created_at DESC
    </select>

    <!-- 启用/禁用报警规则 -->
    <update id="toggleAlarmRule">
        UPDATE alarm_rules 
        SET enabled = #{enabled}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 获取设备相关的报警规则 -->
    <select id="selectRulesByDevice" resultMap="AlarmRuleResultMap">
        SELECT * FROM alarm_rules 
        WHERE device_id = #{deviceId} AND enabled = 1
        ORDER BY level DESC
    </select>

    <!-- 获取今日报警数量 -->
    <select id="selectTodayAlarmCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM alarms 
        WHERE DATE(created_at) = CURDATE()
        <if test="plotId != null">
            AND plot_id = #{plotId}
        </if>
    </select>

    <!-- 获取活跃报警数量 -->
    <select id="selectActiveAlarmCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM alarms 
        WHERE status = 'active'
        <if test="plotId != null">
            AND plot_id = #{plotId}
        </if>
    </select>

    <!-- 获取未处理报警数量 -->
    <select id="selectUnprocessedAlarmCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM alarms 
        WHERE status IN ('active', 'acknowledged')
        <if test="plotId != null">
            AND plot_id = #{plotId}
        </if>
    </select>

    <!-- 清理历史报警数据 -->
    <delete id="cleanupAlarmHistory">
        DELETE FROM alarms 
        WHERE created_at &lt; DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
        AND status = 'resolved'
    </delete>

</mapper>