/**
 * 基础数据模块
 * 包含所有Mock数据的定义和基础操作
 */

// 辅助函数：生成下次运行时间
function getNextRunTime(time, days = [1, 2, 3, 4, 5, 6, 7]) {
  const now = new Date();
  const [hour, minute] = time.split(':').map(Number);
  
  let nextRun = new Date(now);
  nextRun.setHours(hour, minute, 0, 0);
  
  // 如果今天的时间已过，找下一个符合条件的日期
  if (nextRun <= now || !days.includes(now.getDay() || 7)) {
    do {
      nextRun.setDate(nextRun.getDate() + 1);
    } while (!days.includes(nextRun.getDay() || 7));
  }
  
  return nextRun.toISOString();
}

// 辅助函数：生成传感器历史数据
function generateSensorHistoryData(sensorId, hours = 24) {
  const data = [];
  const now = new Date();
  
  for (let i = hours; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
    
    // 根据传感器类型生成不同的数据
    const baseData = {
      timestamp: timestamp.toISOString(),
      sensorId
    };
    
    // 模拟不同传感器的数据变化
    if (sensorId === 'sensor_001') {
      data.push({
        ...baseData,
        moisture: Math.max(0, Math.min(100, 65 + Math.sin(i * 0.1) * 10 + Math.random() * 5)),
        temperature: Math.max(-20, Math.min(80, 24 + Math.sin(i * 0.2) * 3 + Math.random() * 2)),
        ph: Math.max(0, Math.min(14, 6.8 + Math.sin(i * 0.05) * 0.3 + Math.random() * 0.1)),
        ec: Math.max(0, Math.min(5000, 1200 + Math.sin(i * 0.15) * 200 + Math.random() * 50)),
        nitrogen: Math.max(0, Math.min(1000, 80 + Math.sin(i * 0.08) * 15 + Math.random() * 10)),
        phosphorus: Math.max(0, Math.min(500, 40 + Math.sin(i * 0.12) * 8 + Math.random() * 5)),
        potassium: Math.max(0, Math.min(2000, 170 + Math.sin(i * 0.18) * 25 + Math.random() * 15))
      });
    } else if (sensorId === 'sensor_002') {
      data.push({
        ...baseData,
        moisture: Math.max(0, Math.min(100, 70 + Math.sin(i * 0.12) * 8 + Math.random() * 4)),
        temperature: Math.max(-20, Math.min(80, 25 + Math.sin(i * 0.18) * 2.5 + Math.random() * 1.5)),
        ph: Math.max(0, Math.min(14, 6.9 + Math.sin(i * 0.06) * 0.2 + Math.random() * 0.08)),
        ec: Math.max(0, Math.min(5000, 1180 + Math.sin(i * 0.14) * 180 + Math.random() * 40))
      });
    } else if (sensorId === 'sensor_003') {
      data.push({
        ...baseData,
        moisture: Math.max(0, Math.min(100, 55 + Math.sin(i * 0.14) * 12 + Math.random() * 6)),
        temperature: Math.max(-10, Math.min(60, 22 + Math.sin(i * 0.16) * 4 + Math.random() * 2.5))
      });
    } else if (sensorId === 'sensor_004') {
      data.push({
        ...baseData,
        moisture: Math.max(0, Math.min(100, 35 + Math.sin(i * 0.10) * 15 + Math.random() * 8)),
        temperature: Math.max(-10, Math.min(60, 18 + Math.sin(i * 0.20) * 5 + Math.random() * 3))
      });
    }
  }
  
  return data;
}

// 辅助函数：生成天气数据
function generateWeatherData() {
  const weathers = ['晴', '多云', '阴', '小雨', '中雨'];
  const icons = ['☀️', '⛅', '☁️', '🌦️', '🌧️'];
  const weatherIndex = Math.floor(Math.random() * weathers.length);
  
  return {
    temperature: Math.round((18 + Math.random() * 15) * 10) / 10, // 18-33度
    weather: weathers[weatherIndex],
    icon: icons[weatherIndex],
    humidity: Math.round((45 + Math.random() * 40) * 10) / 10, // 45-85%
    windSpeed: Math.round((2 + Math.random() * 12) * 10) / 10, // 2-14 m/s
    pressure: Math.round((1000 + Math.random() * 50) * 10) / 10, // 1000-1050 hPa
    uvIndex: Math.floor(Math.random() * 11), // 0-10
    visibility: Math.round((10 + Math.random() * 20) * 10) / 10, // 10-30 km
    forecast: [
      { date: '今天', weather: weathers[weatherIndex], temp: '18°/33°' },
      { date: '明天', weather: weathers[Math.floor(Math.random() * weathers.length)], temp: '16°/31°' },
      { date: '后天', weather: weathers[Math.floor(Math.random() * weathers.length)], temp: '20°/35°' }
    ]
  };
}

// Mock数据存储
const mockData = {
  // 农场数据
  farms: [
    {
      id: 'farm_001',
      ownerId: 'user_001',
      name: '张三的有机农场',
      location: '平阴县',
      coordinates: {
       latitude: 36.2899,
        longitude: 116.4551
      },
      totalArea: 120.5,
      description: '专注有机蔬菜种植的现代化农场',
      status: 'active',
      plotCount: 3,
      deviceCount: 6,
      sensorCount: 4,
      cropTypes: ['番茄', '生菜', '萝卜'],
      establishedDate: '2024-01-01',
      certification: '有机认证',
      contactPerson: '张三',
      contactPhone: '13800138000',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    }
  ],

  // 地块数据
  plots: [
    {
      id: 'plot_001',
      farmId: 'farm_001',
      name: '1号大棚',
      area: 50.0,
      coordinates: {
        latitude: 29.5647,
        longitude: 106.5507
      },
      soilType: '壤土',
      drainageStatus: '良好',
      irrigationType: '滴灌',
      slopeLevel: '平地',
      status: 'active',
      currentCrop: {
        type: '番茄',
        variety: '樱桃番茄',
        plantDate: '2024-01-10',
        growthStage: '开花期',
        progress: 75,
        expectedHarvestDate: '2024-04-10',
        plantingArea: 45.0,
        plantDensity: 4000
      },
      devices: ['device_001', 'sensor_001', 'sensor_002'],
      soilAnalysis: {
        ph: 6.8,
        organicMatter: 3.2,
        nitrogen: 1.8,
        phosphorus: 0.8,
        potassium: 2.1,
        lastTestDate: '2024-01-01'
      },
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    },
    {
      id: 'plot_002',
      farmId: 'farm_001',
      name: '2号大棚',
      area: 45.0,
      coordinates: {
        latitude: 29.5650,
        longitude: 106.5510
      },
      soilType: '沙壤土',
      drainageStatus: '良好',
      irrigationType: '喷灌',
      slopeLevel: '缓坡',
      status: 'active',
      currentCrop: {
        type: '生菜',
        variety: '奶油生菜',
        plantDate: '2024-01-20',
        growthStage: '生长期',
        progress: 60,
        expectedHarvestDate: '2024-03-20',
        plantingArea: 42.0,
        plantDensity: 8000
      },
      devices: ['device_002', 'sensor_003'],
      soilAnalysis: {
        ph: 6.5,
        organicMatter: 2.8,
        nitrogen: 1.5,
        phosphorus: 0.6,
        potassium: 1.8,
        lastTestDate: '2024-01-05'
      },
      createdAt: '2024-01-05T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    },
    {
      id: 'plot_003',
      farmId: 'farm_001',
      name: '露天菜地',
      area: 25.5,
      coordinates: {
        latitude: 29.5645,
        longitude: 106.5505
      },
      soilType: '黏土',
      drainageStatus: '一般',
      irrigationType: '漫灌',
      slopeLevel: '平地',
      status: 'maintenance',
      currentCrop: {
        type: '萝卜',
        variety: '白萝卜',
        plantDate: '2024-01-08',
        growthStage: '根部发育期',
        progress: 40,
        expectedHarvestDate: '2024-04-08',
        plantingArea: 22.0,
        plantDensity: 5000
      },
      devices: ['device_003', 'sensor_004'],
      soilAnalysis: {
        ph: 7.2,
        organicMatter: 3.8,
        nitrogen: 2.1,
        phosphorus: 1.0,
        potassium: 2.5,
        lastTestDate: '2024-01-03'
      },
      createdAt: '2024-01-03T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    }
  ],

  // 设备数据
  devices: [
    {
      id: 'device_001',
      serialNumber: 'WF001-2024-001',
      type: 'irrigation',
      subType: 'water_fertilizer_integrated',
      name: '1号水肥一体机',
      model: 'WF-PRO-2000',
      manufacturer: '智慧农业科技有限公司',
      version: 'v2.1.0',
      farmId: 'farm_001',
      plotId: 'plot_001',
      installLocation: '1号大棚东侧',
      installDate: '2024-01-01',
      warrantyExpiry: '2027-01-01',
      status: {
        online: true,
        working: false,
        lastSeen: new Date().toISOString(),
        batteryLevel: 85,
        signalStrength: -45,
        temperature: 28.5,
        humidity: 65
      },
      capabilities: {
        maxZones: 4,
        valveTypes: ['solenoid', 'motorized'],
        flowRateRange: [10, 200],
        pressureRange: [1.0, 5.0],
        fertilizerTanks: 3,
        supportedProtocols: ['mqtt', 'http', 'modbus'],
        supportedCrops: ['番茄', '黄瓜', '辣椒', '茄子'],
        automation: true,
        remoteControl: true,
        dataLogging: true
      },
      config: {
        valves: [
          { id: 'VALVE_1', name: 'A区域阀门', status: 'closed', flowRate: 0, maxFlowRate: 80 },
          { id: 'VALVE_2', name: 'B区域阀门', status: 'closed', flowRate: 0, maxFlowRate: 80 },
          { id: 'VALVE_3', name: '备用阀门3', status: 'closed', flowRate: 0, maxFlowRate: 60 },
          { id: 'VALVE_4', name: '备用阀门4', status: 'closed', flowRate: 0, maxFlowRate: 60 }
        ],
        fertilizer: {
          tank1: { type: 'N', name: '氮肥', concentration: 20, level: 75, capacity: 100 },
          tank2: { type: 'P', name: '磷肥', concentration: 10, level: 60, capacity: 100 },
          tank3: { type: 'K', name: '钾肥', concentration: 15, level: 80, capacity: 100 }
        },
        pump: {
          maxPressure: 5.0,
          currentPressure: 0,
          status: 'stopped',
          totalRuntime: 245.5
        }
      },
      currentTask: null,
      totalRuntime: 1200,
      totalWaterUsage: 15000,
      totalFertilizerUsage: 450,
      maintenanceRecord: {
        lastMaintenance: '2024-01-01T00:00:00.000Z',
        nextMaintenance: '2024-04-01T00:00:00.000Z',
        maintenanceInterval: 90
      },
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'device_002',
      serialNumber: 'WF002-2024-002',
      type: 'irrigation',
      subType: 'water_fertilizer_integrated',
      name: '2号水肥一体机',
      model: 'WF-STANDARD-1500',
      manufacturer: '智慧农业科技有限公司',
      version: 'v2.0.3',
      farmId: 'farm_001',
      plotId: 'plot_002',
      installLocation: '2号大棚中央',
      installDate: '2024-01-02',
      warrantyExpiry: '2027-01-02',
      status: {
        online: true,
        working: true,
        lastSeen: new Date().toISOString(),
        batteryLevel: 92,
        signalStrength: -38,
        temperature: 26.8,
        humidity: 58
      },
      capabilities: {
        maxZones: 2,
        valveTypes: ['solenoid'],
        flowRateRange: [20, 150],
        pressureRange: [1.5, 4.0],
        fertilizerTanks: 2,
        supportedProtocols: ['mqtt', 'http'],
        supportedCrops: ['生菜', '菠菜', '小白菜'],
        automation: true,
        remoteControl: true,
        dataLogging: true
      },
      config: {
        valves: [
          { id: 'VALVE_1', name: '主阀门', status: 'open', flowRate: 80, maxFlowRate: 120 },
          { id: 'VALVE_2', name: '副阀门', status: 'closed', flowRate: 0, maxFlowRate: 100 }
        ],
        fertilizer: {
          tank1: { type: 'NPK', name: '复合肥', concentration: 25, level: 45, capacity: 80 },
          tank2: { type: 'Micro', name: '微量元素', concentration: 5, level: 70, capacity: 50 }
        },
        pump: {
          maxPressure: 4.0,
          currentPressure: 2.8,
          status: 'running',
          totalRuntime: 186.2
        }
      },
      currentTask: {
        taskId: 'task_001',
        type: 'irrigation',
        startTime: new Date(Date.now() - 1800000).toISOString(),
        estimatedEndTime: new Date(Date.now() + 900000).toISOString(),
        parameters: {
          duration: 2700,
          flowRate: 80,
          zones: ['VALVE_1'],
          fertilizer: {
            enabled: false
          }
        }
      },
      totalRuntime: 890,
      totalWaterUsage: 8500,
      totalFertilizerUsage: 280,
      maintenanceRecord: {
        lastMaintenance: '2024-01-02T00:00:00.000Z',
        nextMaintenance: '2024-04-02T00:00:00.000Z',
        maintenanceInterval: 90
      },
      createdAt: '2024-01-02T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'device_003',
      serialNumber: 'WF003-2024-003',
      type: 'irrigation',
      subType: 'simple_irrigation',
      name: '3号灌溉设备',
      model: 'WF-BASIC-800',
      manufacturer: '智慧农业科技有限公司',
      version: 'v1.5.2',
      farmId: 'farm_001',
      plotId: 'plot_003',
      installLocation: '露天菜地西侧',
      installDate: '2024-01-03',
      warrantyExpiry: '2027-01-03',
      status: {
        online: false,
        working: false,
        lastSeen: new Date(Date.now() - 1800000).toISOString(),
        batteryLevel: 15,
        signalStrength: -72,
        temperature: 22.1,
        humidity: 45
      },
      capabilities: {
        maxZones: 1,
        valveTypes: ['solenoid'],
        flowRateRange: [30, 120],
        pressureRange: [2.0, 6.0],
        fertilizerTanks: 1,
        supportedProtocols: ['http'],
        supportedCrops: ['萝卜', '胡萝卜', '白菜'],
        automation: false,
        remoteControl: true,
        dataLogging: false
      },
      config: {
        valves: [
          { id: 'VALVE_1', name: '主阀门', status: 'closed', flowRate: 0, maxFlowRate: 100 }
        ],
        fertilizer: {
          tank1: { type: 'Compound', name: '复合肥', concentration: 15, level: 25, capacity: 60 }
        },
        pump: {
          maxPressure: 6.0,
          currentPressure: 0,
          status: 'stopped',
          totalRuntime: 156.8
        }
      },
      currentTask: null,
      totalRuntime: 350,
      totalWaterUsage: 4200,
      totalFertilizerUsage: 120,
      maintenanceRecord: {
        lastMaintenance: '2024-01-03T00:00:00.000Z',
        nextMaintenance: '2024-04-03T00:00:00.000Z',
        maintenanceInterval: 90
      },
      createdAt: '2024-01-03T00:00:00.000Z',
      updatedAt: new Date(Date.now() - 1800000).toISOString()
    }
  ],

  // 传感器数据
  sensors: [
    {
      id: 'sensor_001',
      serialNumber: 'SM001-2024-001',
      type: 'soil',
      subType: 'multi_parameter',
      name: '1号土壤传感器',
      model: 'SM-PRO-2000',
      manufacturer: '农业传感器有限公司',
      version: 'v3.1.2',
      farmId: 'farm_001',
      plotId: 'plot_001',
      installLocation: '1号大棚A区域',
      installDepth: 30,
      installDate: '2024-01-01',
      warrantyExpiry: '2027-01-01',
      status: {
        online: true,
        batteryLevel: 78,
        signalStrength: -42,
        lastSeen: new Date().toISOString(),
        calibrationDate: '2024-01-01T00:00:00.000Z',
        temperature: 35.2
      },
      capabilities: {
        parameters: ['moisture', 'temperature', 'ph', 'ec', 'npk'],
        measurementRange: {
          moisture: [0, 100],
          temperature: [-20, 80],
          ph: [0, 14],
          ec: [0, 5000],
          nitrogen: [0, 1000],
          phosphorus: [0, 500],
          potassium: [0, 2000]
        },
        accuracy: {
          moisture: 2,
          temperature: 0.5,
          ph: 0.1,
          ec: 50,
          nitrogen: 10,
          phosphorus: 5,
          potassium: 20
        },
        dataInterval: 300,
        batteryLife: 365
      },
      currentReading: {
        timestamp: new Date().toISOString(),
        moisture: 68.5,
        temperature: 24.2,
        ph: 6.8,
        ec: 1250,
        nitrogen: 85,
        phosphorus: 45,
        potassium: 180
      },
      thresholds: {
        moisture: { min: 40, max: 80, optimal: [60, 70] },
        temperature: { min: 15, max: 35, optimal: [20, 30] },
        ph: { min: 6.0, max: 7.5, optimal: [6.5, 7.0] },
        ec: { min: 800, max: 2000, optimal: [1000, 1500] },
        nitrogen: { min: 50, max: 200, optimal: [80, 120] },
        phosphorus: { min: 20, max: 100, optimal: [40, 60] },
        potassium: { min: 100, max: 300, optimal: [150, 250] }
      },
      alerts: {
        enabled: true,
        checkInterval: 600,
        notificationTypes: ['wechat', 'sms', 'push']
      },
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'sensor_002',
      serialNumber: 'SM002-2024-002',
      type: 'soil',
      subType: 'standard',
      name: '2号土壤传感器',
      model: 'SM-STANDARD-1500',
      manufacturer: '农业传感器有限公司',
      version: 'v2.8.1',
      farmId: 'farm_001',
      plotId: 'plot_001',
      installLocation: '1号大棚B区域',
      installDepth: 25,
      installDate: '2024-01-01',
      warrantyExpiry: '2027-01-01',
      status: {
        online: true,
        batteryLevel: 82,
        signalStrength: -45,
        lastSeen: new Date().toISOString(),
        calibrationDate: '2024-01-01T00:00:00.000Z',
        temperature: 33.8
      },
      capabilities: {
        parameters: ['moisture', 'temperature', 'ph', 'ec'],
        measurementRange: {
          moisture: [0, 100],
          temperature: [-20, 80],
          ph: [0, 14],
          ec: [0, 5000]
        },
        accuracy: {
          moisture: 2,
          temperature: 0.5,
          ph: 0.1,
          ec: 50
        },
        dataInterval: 300,
        batteryLife: 365
      },
      currentReading: {
        timestamp: new Date().toISOString(),
        moisture: 72.3,
        temperature: 25.1,
        ph: 6.9,
        ec: 1180
      },
      thresholds: {
        moisture: { min: 40, max: 80, optimal: [60, 70] },
        temperature: { min: 15, max: 35, optimal: [20, 30] },
        ph: { min: 6.0, max: 7.5, optimal: [6.5, 7.0] },
        ec: { min: 800, max: 2000, optimal: [1000, 1500] }
      },
      alerts: {
        enabled: true,
        checkInterval: 600,
        notificationTypes: ['wechat', 'push']
      },
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'sensor_003',
      serialNumber: 'SM003-2024-003',
      type: 'soil',
      subType: 'basic',
      name: '3号土壤传感器',
      model: 'SM-BASIC-1000',
      manufacturer: '农业传感器有限公司',
      version: 'v1.9.5',
      farmId: 'farm_001',
      plotId: 'plot_002',
      installLocation: '2号大棚中央',
      installDepth: 20,
      installDate: '2024-01-05',
      warrantyExpiry: '2027-01-05',
      status: {
        online: true,
        batteryLevel: 65,
        signalStrength: -50,
        lastSeen: new Date().toISOString(),
        calibrationDate: '2024-01-05T00:00:00.000Z',
        temperature: 31.5
      },
      capabilities: {
        parameters: ['moisture', 'temperature'],
        measurementRange: {
          moisture: [0, 100],
          temperature: [-10, 60]
        },
        accuracy: {
          moisture: 3,
          temperature: 1.0
        },
        dataInterval: 600,
        batteryLife: 180
      },
      currentReading: {
        timestamp: new Date().toISOString(),
        moisture: 58.7,
        temperature: 22.8
      },
      thresholds: {
        moisture: { min: 30, max: 75, optimal: [50, 65] },
        temperature: { min: 10, max: 40, optimal: [18, 32] }
      },
      alerts: {
        enabled: true,
        checkInterval: 1200,
        notificationTypes: ['wechat']
      },
      createdAt: '2024-01-05T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'sensor_004',
      serialNumber: 'SM004-2024-004',
      type: 'soil',
      subType: 'basic',
      name: '4号土壤传感器',
      model: 'SM-BASIC-1000',
      manufacturer: '农业传感器有限公司',
      version: 'v1.9.5',
      farmId: 'farm_001',
      plotId: 'plot_003',
      installLocation: '露天菜地中央',
      installDepth: 35,
      installDate: '2024-01-03',
      warrantyExpiry: '2027-01-03',
      status: {
        online: false,
        batteryLevel: 8,
        signalStrength: -80,
        lastSeen: new Date(Date.now() - 3600000).toISOString(),
        calibrationDate: '2024-01-03T00:00:00.000Z',
        temperature: 28.2
      },
      capabilities: {
        parameters: ['moisture', 'temperature'],
        measurementRange: {
          moisture: [0, 100],
          temperature: [-10, 60]
        },
        accuracy: {
          moisture: 3,
          temperature: 1.0
        },
        dataInterval: 600,
        batteryLife: 180
      },
      currentReading: {
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        moisture: 35.2,
        temperature: 18.5
      },
      thresholds: {
        moisture: { min: 25, max: 70, optimal: [40, 60] },
        temperature: { min: 5, max: 45, optimal: [15, 35] }
      },
      alerts: {
        enabled: true,
        checkInterval: 1200,
        notificationTypes: ['wechat']
      },
      createdAt: '2024-01-03T00:00:00.000Z',
      updatedAt: new Date(Date.now() - 3600000).toISOString()
    }
  ],

  // 历史数据缓存
  sensorHistoryData: {
    'sensor_001': generateSensorHistoryData('sensor_001'),
    'sensor_002': generateSensorHistoryData('sensor_002'),
    'sensor_003': generateSensorHistoryData('sensor_003'),
    'sensor_004': generateSensorHistoryData('sensor_004')
  },

  // 天气数据
  weatherData: generateWeatherData(),

  // 今日统计数据
  todayStats: {
    date: new Date().toISOString().split('T')[0],
    irrigation: {
      totalDuration: 5400,
      totalVolume: 12500,
      totalTasks: 3,
      completedTasks: 2,
      failedTasks: 0,
      pendingTasks: 1,
      efficiency: 96.5,
      cost: 31.25
    },
    fertilization: {
      totalDuration: 3600,
      totalVolume: 180,
      totalTasks: 2,
      completedTasks: 1,
      failedTasks: 1,
      pendingTasks: 0,
      efficiency: 85.2,
      cost: 57.6
    },
    alarms: {
      total: 4,
      active: 2,
      resolved: 2,
      critical: 1,
      warning: 2,
      info: 1
    },
    devices: {
      total: 3,
      online: 2,
      offline: 1,
      working: 1,
      idle: 1,
      maintenance: 1
    },
    sensors: {
      total: 4,
      online: 3,
      offline: 1,
      normal: 2,
      warning: 1,
      critical: 1
    }
  },

  // 任务数据
  tasks: [
    {
      id: 'task_001',
      title: '1号大棚灌溉',
      description: '对1号大棚进行早间灌溉',
      date: new Date().toISOString().split('T')[0],
      time: '08:00',
      type: 'irrigation',
      status: 'pending',
      priority: 'medium',
      plotId: 'plot_001',
      deviceId: 'device_001',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'task_002',
      title: '2号大棚施肥',
      description: '对2号大棚进行肥料补充',
      date: new Date().toISOString().split('T')[0],
      time: '10:00',
      type: 'fertilization',
      status: 'completed',
      priority: 'high',
      plotId: 'plot_002',
      deviceId: 'device_002',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'task_003',
      title: '土壤湿度检测',
      description: '检查所有地块土壤湿度',
      date: new Date().toISOString().split('T')[0],
      time: '14:00',
      type: 'monitoring',
      status: 'pending',
      priority: 'low',
      plotId: 'plot_003',
      deviceId: 'sensor_001',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],

  // 地块和区域数据 - 支持灌溉控制
  zones: [
    {
      id: 'zone_001',
      plotId: 'plot_001',
      name: '1区',
      area: 25.0,
      valveId: 'VALVE_1',
      soilType: '壤土',
      currentCrop: '番茄',
      installDate: '2024-01-01',
      status: 'active'
    },
    {
      id: 'zone_002',
      plotId: 'plot_001',
      name: '2区',
      area: 25.0,
      valveId: 'VALVE_2',
      soilType: '壤土',
      currentCrop: '番茄',
      installDate: '2024-01-01',
      status: 'active'
    },
    {
      id: 'zone_003',
      plotId: 'plot_002',
      name: '3区',
      area: 45.0,
      valveId: 'VALVE_1',
      soilType: '沙壤土',
      currentCrop: '生菜',
      installDate: '2024-01-05',
      status: 'active'
    }
  ],

  // 作物管理数据
  crops: [
    {
      id: 'crop_001',
      plotId: 'plot_001',
      type: '番茄',
      variety: '樱桃番茄',
      plantDate: '2024-01-10',
      expectedHarvestDate: '2024-04-10',
      growthStage: '开花期',
      progress: 75,
      plantingArea: 45.0,
      plantDensity: 4000,
      status: 'growing',
      healthStatus: 'healthy',
      yield: {
        expected: 2500, // kg
        actual: 0
      },
      records: [
        {
          id: 'record_001',
          date: '2024-01-10',
          type: 'planting',
          description: '种植樱桃番茄',
          operator: 'user_001'
        },
        {
          id: 'record_002',
          date: '2024-01-20',
          type: 'watering',
          description: '首次灌溉',
          operator: 'system'
        }
      ],
      createdAt: '2024-01-10T00:00:00.000Z',
      updatedAt: '2024-01-25T10:30:00.000Z'
    }
  ]
};

// 数据访问接口
module.exports = {
  // 获取所有数据
  getAllData() {
    return mockData;
  },

  // 农场数据
  getFarms() {
    return mockData.farms;
  },

  getFarmById(farmId) {
    return mockData.farms.find(farm => farm.id === farmId);
  },

  // 地块数据
  getPlots(farmId = null) {
    if (farmId) {
      return mockData.plots.filter(plot => plot.farmId === farmId);
    }
    return mockData.plots;
  },

  getPlotById(plotId) {
    return mockData.plots.find(plot => plot.id === plotId);
  },

  // 设备数据
  getDevices(plotId = null) {
    if (plotId) {
      return mockData.devices.filter(device => device.plotId === plotId);
    }
    return mockData.devices;
  },

  getDeviceById(deviceId) {
    return mockData.devices.find(device => device.id === deviceId);
  },

  // 传感器数据
  getSensors(plotId = null) {
    if (plotId) {
      return mockData.sensors.filter(sensor => sensor.plotId === plotId);
    }
    return mockData.sensors;
  },

  getSensorById(sensorId) {
    return mockData.sensors.find(sensor => sensor.id === sensorId);
  },

  // 历史数据
  getSensorHistoryData(sensorId, hours = 24) {
    if (mockData.sensorHistoryData[sensorId]) {
      return mockData.sensorHistoryData[sensorId];
    }
    return generateSensorHistoryData(sensorId, hours);
  },

  // 天气数据
  getWeatherData() {
    return mockData.weatherData;
  },

  // 今日统计
  getTodayStats() {
    return mockData.todayStats;
  },

  // 任务相关方法
  getTasks(params = {}) {
    const tasks = mockData.tasks || [];
    
    if (params.today) {
      const today = new Date().toISOString().split('T')[0];
      return {
        tasks: tasks.filter(task => task.date === today),
        total: tasks.filter(task => task.date === today).length
      };
    }
    
    return {
      tasks: tasks,
      total: tasks.length
    };
  },

  // 获取区域数据
  getZones(plotId = null) {
    if (plotId) {
      return mockData.zones.filter(zone => zone.plotId === plotId);
    }
    return mockData.zones;
  },

  getZoneById(zoneId) {
    return mockData.zones.find(zone => zone.id === zoneId);
  },

  // 获取作物数据
  getCrops(plotId = null) {
    if (plotId) {
      return mockData.crops.filter(crop => crop.plotId === plotId);
    }
    return mockData.crops;
  },

  getCropById(cropId) {
    return mockData.crops.find(crop => crop.id === cropId);
  },

  createTask(taskData) {
    const newTask = {
      id: Date.now().toString(),
      title: taskData.title,
      description: taskData.description || '',
      date: taskData.date || new Date().toISOString().split('T')[0],
      time: taskData.time || '08:00',
      type: taskData.type || 'irrigation',
      status: taskData.status || 'pending',
      priority: taskData.priority || 'medium',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    if (!mockData.tasks) {
      mockData.tasks = [];
    }
    
    mockData.tasks.push(newTask);
    return newTask;
  },

  // 辅助函数
  generateSensorHistoryData,
  generateWeatherData,
  getNextRunTime
};