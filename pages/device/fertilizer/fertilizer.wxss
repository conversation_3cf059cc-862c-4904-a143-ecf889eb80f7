/* 施肥管理页面样式 */

/* 标签导航 */
.tab-nav {
  display: flex;
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  padding: 25rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666666;
  background-color: #F8F9FA;
  border-right: 1rpx solid #E0E0E0;
  transition: all 0.3s ease;
}

.tab-item:last-child {
  border-right: none;
}

.tab-item.active {
  background-color: #08C160;
  color: white;
  font-weight: 600;
}

/* 设备状态面板 */
.device-status-panel {
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.device-name {
  font-size: 32rpx;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: white;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
}

.status-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-label {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 10rpx;
}

.info-value {
  font-size: 28rpx;
  font-weight: 600;
}

/* 实时控制面板 */
.real-time-control {
  background-color: #F8F9FA;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.control-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.main-switch {
  transform: scale(1.2);
}

.control-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx;
  background-color: rgba(8, 193, 96, 0.1);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.running-text {
  font-size: 28rpx;
  color: #08C160;
  font-weight: 600;
}

.remaining-time {
  font-size: 24rpx;
  color: #666666;
}

/* 控制参数 */
.control-params {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.param-item {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.param-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.param-control {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.param-unit {
  font-size: 26rpx;
  color: #666666;
  min-width: 60rpx;
}

.duration-input {
  flex: 1;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 28rpx;
}

.ec-ph-inputs {
  display: flex;
  gap: 15rpx;
}

.ec-ph-inputs .param-control {
  flex: 1;
}

/* 配方选择 */
.formula-picker {
  padding: 15rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  background-color: #ffffff;
  color: #333333;
  flex: 1;
}

/* 快捷设置 */
.quick-presets {
  background-color: #F0F4F7;
  border-radius: 12rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

.preset-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.preset-btn {
  padding: 20rpx;
  background-color: white;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

.preset-btn:active {
  background-color: #08C160;
  color: white;
}

/* 操作按钮 */
.control-actions {
  display: flex;
  gap: 15rpx;
  justify-content: center;
  margin-top: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 15rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 600;
}

.btn-pause {
  background-color: #FF9800;
  color: white;
}

.btn-flush {
  background-color: #2196F3;
  color: white;
}

/* 配方管理 */
.formula-management {
  background-color: #F8F9FA;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.formula-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.add-btn {
  padding: 10rpx 20rpx;
  background-color: #08C160;
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.formula-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.formula-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: white;
  border-radius: 12rpx;
  border: 2rpx solid #E0E0E0;
  transition: all 0.3s ease;
}

.formula-item.active {
  border-color: #08C160;
  background-color: rgba(8, 193, 96, 0.05);
}

.formula-info {
  flex: 1;
}

.formula-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.formula-details {
  display: flex;
  gap: 15rpx;
  margin-bottom: 8rpx;
}

.formula-detail {
  font-size: 22rpx;
  color: #666666;
}

.formula-crop {
  font-size: 24rpx;
  color: #08C160;
  font-weight: 500;
}

.formula-values {
  display: flex;
  gap: 20rpx;
  margin-top: 10rpx;
}

.value-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5rpx;
}

.value-label {
  font-size: 20rpx;
  color: #999999;
}

.value-number {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

.formula-badge {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  margin-right: 10rpx;
}

.badge-preset {
  background-color: #E8F8EC;
  color: #08C160;
}

.badge-custom {
  background-color: #FFF3E0;
  color: #FF9800;
}

.formula-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.action-edit {
  background-color: #E3F2FD;
  color: #2196F3;
}

.action-copy {
  background-color: #FFF3E0;
  color: #FF9800;
}

.action-delete {
  background-color: #FFEBEE;
  color: #F44336;
}

/* 运行状态 */
.running-status {
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  color: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.status-label {
  font-size: 26rpx;
  opacity: 0.9;
}

.status-value {
  font-size: 28rpx;
  font-weight: 600;
}

/* 历史记录 */
.operation-history {
  background-color: #F8F9FA;
  border-radius: 16rpx;
  padding: 30rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: white;
  border-radius: 12rpx;
}

.history-time {
  font-size: 24rpx;
  color: #666666;
  min-width: 80rpx;
}

.history-content {
  flex: 1;
  margin-left: 20rpx;
}

.history-action {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.history-details {
  font-size: 24rpx;
  color: #666666;
}

.history-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.history-status.success {
  background-color: #E8F8EC;
  color: #08C160;
}

.history-status.failed {
  background-color: #FFEBEE;
  color: #F44336;
}

/* 停止状态 */
.stopped-status {
  text-align: center;
  padding: 40rpx;
  color: #999999;
}

.stopped-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.stopped-text {
  font-size: 26rpx;
}

/* 无数据状态 */
.no-data {
  text-align: center;
  padding: 60rpx;
  color: #999999;
}

.no-data-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.no-data-text {
  font-size: 28rpx;
}

/* 定时任务管理 */
.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.schedule-item {
  background-color: white;
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #08C160;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.schedule-info {
  flex: 1;
}

.schedule-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.schedule-time {
  font-size: 24rpx;
  color: #666666;
}

.schedule-details {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-bottom: 20rpx;
  padding: 15rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.detail-row {
  display: flex;
  gap: 15rpx;
}

.detail-label {
  font-size: 24rpx;
  color: #666666;
  min-width: 80rpx;
}

.detail-value {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

.schedule-actions {
  display: flex;
  gap: 15rpx;
  justify-content: flex-end;
}

.no-schedule {
  text-align: center;
  padding: 80rpx;
  color: #999999;
  font-size: 28rpx;
}

/* 定时任务编辑弹窗 */
.schedule-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background-color: white;
  border-radius: 16rpx;
  width: 90%;
  max-height: 80%;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #E0E0E0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-body {
  padding: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  padding: 15rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.time-picker,
.formula-picker {
  width: 100%;
  padding: 15rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  background-color: white;
}

.picker-view {
  font-size: 28rpx;
  color: #333333;
}

.slider-group,
.input-group {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.unit-text {
  font-size: 26rpx;
  color: #666666;
  min-width: 60rpx;
}

.checkbox-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-right: 20rpx;
  margin-bottom: 15rpx;
}

.checkbox-text {
  font-size: 26rpx;
  color: #333333;
}

.modal-footer {
  display: flex;
  gap: 15rpx;
  justify-content: flex-end;
  padding: 30rpx;
  border-top: 1rpx solid #E0E0E0;
}

/* ======= 自定义浓度滑块样式 ======= */

.concentration-control {
  background-color: #F8F9FA;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.concentration-display {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  background-color: #FF9800;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.concentration-value {
  font-size: 32rpx;
  font-weight: 600;
}

.concentration-unit {
  font-size: 20rpx;
  opacity: 0.9;
}

/* 自定义滑块 */
.concentration-control .custom-slider {
  margin: 30rpx 0 0;
}

.concentration-control .slider-track {
  position: relative;
  height: 8rpx;
  background-color: #E0E0E0;
  border-radius: 4rpx;
  margin: 0 20rpx;
}

.concentration-control .slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #FF9800 0%, #F57C00 100%);
  border-radius: 4rpx;
  transition: width 0.1s ease;
}

.concentration-control .slider-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 40rpx;
  height: 40rpx;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.concentration-control .thumb-inner {
  width: 20rpx;
  height: 20rpx;
  background-color: #FF9800;
  border-radius: 50%;
}

.concentration-control .slider-labels {
  display: flex;
  justify-content: space-between;
  margin: 20rpx 20rpx 0;
}

.concentration-control .slider-min,
.concentration-control .slider-max {
  font-size: 22rpx;
  color: #666666;
}