/* 首页 - 农业设备控制页面样式 */

.page-container {


}

/* 顶部背景图片区域 */
.header-bg-section {
  height: 550rpx;
  position: relative;
  overflow: hidden;
}

/* 背景图片 */
.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 自定义导航栏 */
.custom-navbar {
  position: absolute;
  top: 90rpx;
  left: 0;
  right: 0;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

.navbar-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #FFFFFF;
}

/* 农场信息区域 */
.farm-info-section {
  position: absolute;
  bottom: 180rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx;
  z-index: 2;
}
.farm-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.farm-name {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.name-text {
  font-size: 40rpx;
  font-weight: 700;
  color: #222;
  margin-right: 12rpx;
}
.switch-icon {
  font-size: 32rpx;
  color: #13BD9D;
  margin-left: 4rpx;
}
.farm-location {
  display: flex;
  align-items: center;
}
.location-icon {
  font-size: 24rpx;
  margin-right: 6rpx;
}
.location-text, .area-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

/* 天气信息区域 */
.weather-section {
  display: flex;
  align-items: center;
}
.weather-visual {
  display: flex;
  align-items: center;
}
.weather-visual canvas {
  display: block;
}
.weather-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
  white-space: nowrap;
  margin-left: 8rpx;
}
.weather-animation {
  display: flex;
  align-items: center;
  margin-right: 0;
  padding: 0;
}
.temperature {
  font-size: 48rpx;
  font-weight: 600;
  color: #222;
  line-height: 1;
}
.weather-desc {
  text-align: left;
  font-size: 22rpx;
  color: #666;
  margin-top: 2rpx;
}

/* lottie canvas 尺寸适配 */
.weather-animation canvas {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  width: 240rpx !important;
  height: 160rpx !important;
}

/* 设备控制区域 */
.device-section {

  border-radius: 40rpx 40rpx 0 0;
  padding: 16rpx 0 0 0;
  position: relative;
  z-index: 3;
  min-height: calc(100vh - 350rpx);
  margin-top: -200rpx;
}

/* 设备类型切换 */
.device-tabs {
  display: flex;
  padding: 0 40rpx 30rpx 40rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.tab-item {
  margin-right: 60rpx;
  position: relative;
  padding-bottom: 20rpx;
}

.tab-item text {
  font-size: 32rpx;
  color: #999999;
  font-weight: 400;
}

.tab-item.active text {
  color: #333333;
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: -1rpx;
  left: 0;
  right: 0;
  height: 6rpx;
  background-color: #13BD9D;
  border-radius: 3rpx;
}

/* 设备列表 - 卡片式两列布局 */
.device-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 12rpx 24rpx 0 24rpx;
}
.device-item {
  width: 48%;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.06);
  margin-bottom: 32rpx;
  padding: 32rpx 0 24rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.device-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border-radius: 0;
}
.device-image {
  width: 100rpx;
  height: 100rpx;
}
.device-control {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 56rpx;
  height: 56rpx;
}
.power-button {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: #F3F3F3;
  box-shadow: 0 2rpx 8rpx 0 rgba(19,189,157,0.10);
  transition: all 0.3s;
}
.power-button.on {
  background: #13BD9D;
  box-shadow: 0 2rpx 8rpx 0 rgba(19,189,157,0.18);
}
.power-icon {
  font-size: 28rpx;
  color: #13BD9D;
}
.power-button.on .power-icon {
  color: #fff;
}
.device-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 5rpx;
}
.device-name {
  font-size: 30rpx;
  color: #222;
  font-weight: 600;
  margin-bottom: 6rpx;
}
.device-status {
  font-size: 24rpx;
  color: #FF8C00;
}

/* 球阀控制区卡片化 */
.valve-control {
  width: 100%;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.06);
  margin-top: 0rpx;
  padding: 32rpx 24rpx 24rpx 24rpx;
  border: none;
  /* 让其在flex布局下占满两列 */
  display: block;
}
.valve-info {
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
}
.valve-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 16rpx;
}
.valve-image {
  width: 64rpx;
  height: 64rpx;
}
.valve-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.valve-name {
  font-size: 28rpx;
  color: #222;
  font-weight: 600;
  margin-bottom: 2rpx;
}
.valve-specs {
  font-size: 20rpx;
  color: #999;
}
.valve-controls {
  background: none;
  border-radius: 0;
  padding: 0;
}
.channel-buttons {
  display: flex;
  margin-bottom: 18rpx;
}
.channel-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 18rpx;
  border-radius: 16rpx;
  margin-right: 12rpx;
  background: #F3F3F3;
  border: none;
}
.channel-btn.active {
  background: #13BD9D;
}
.channel-icon {
  font-size: 20rpx;
  margin-right: 4rpx;
  color: #13BD9D;
}
.channel-btn.active .channel-icon {
  color: #fff;
}
.channel-text {
  font-size: 20rpx;
  color: #13BD9D;
}
.channel-btn.active .channel-text {
  color: #fff;
}
.valve-slider {
  margin-top: 8rpx;
}
.slider-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
}
.slider-label {
  font-size: 20rpx;
  color: #999;
}
.slider-value {
  font-size: 24rpx;
  color: #FF8C00;
  font-weight: 600;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .header-section {
    height: 280rpx;
    padding: 50rpx 30rpx 30rpx 30rpx;
  }
  
  .name-text {
    font-size: 42rpx;
  }
  
  .temperature {
    font-size: 64rpx;
  }
  
  .device-list {
    padding: 30rpx;
  }
  
  .device-tabs {
    padding: 0 30rpx 20rpx 30rpx;
  }
}