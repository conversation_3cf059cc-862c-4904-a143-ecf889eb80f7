package com.agriculture.iot.service.impl;

import com.agriculture.iot.entity.Farm;
import com.agriculture.iot.mapper.FarmMapper;
import com.agriculture.iot.service.FarmService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 农场服务实现类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
public class FarmServiceImpl extends ServiceImpl<FarmMapper, Farm> implements FarmService {

    private final FarmMapper farmMapper;

    @Override
    public IPage<Farm> getFarmList(Page<Farm> page, String name, Integer status) {
        LambdaQueryWrapper<Farm> wrapper = new LambdaQueryWrapper<>();
        
        if (name != null && !name.trim().isEmpty()) {
            wrapper.like(Farm::getName, name);
        }
        
        if (status != null) {
            wrapper.eq(Farm::getStatus, status);
        }
        
        wrapper.orderByDesc(Farm::getCreatedAt);
        
        return farmMapper.selectPage(page, wrapper);
    }

    @Override
    public Farm getFarmById(Long id) {
        return farmMapper.selectById(id);
    }

    @Override
    public Farm createFarm(Farm farm) {
        farm.setCreatedAt(System.currentTimeMillis());
        farm.setUpdatedAt(System.currentTimeMillis());
        farm.setStatus(1); // 默认启用状态
        farmMapper.insert(farm);
        return farm;
    }

    @Override
    public Farm updateFarm(Farm farm) {
        farm.setUpdatedAt(System.currentTimeMillis());
        farmMapper.updateById(farm);
        return farm;
    }

    @Override
    public boolean deleteFarm(Long id) {
        return farmMapper.deleteById(id) > 0;
    }

    @Override
    public List<Farm> getAllActiveFarms() {
        LambdaQueryWrapper<Farm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Farm::getStatus, 1)
               .orderByDesc(Farm::getCreatedAt);
        return farmMapper.selectList(wrapper);
    }

    @Override
    public Farm.Statistics getFarmStatistics(Long id) {
        Farm farm = farmMapper.selectById(id);
        if (farm == null) {
            return null;
        }
        
        // 这里可以添加更复杂的统计逻辑
        // 例如：查询地块数量、设备数量、传感器数量等
        return farm.getStatistics();
    }

    @Override
    public boolean updateFarmStatus(Long id, Integer status) {
        Farm farm = new Farm();
        farm.setId(id);
        farm.setStatus(status);
        farm.setUpdatedAt(System.currentTimeMillis());
        return farmMapper.updateById(farm) > 0;
    }
}