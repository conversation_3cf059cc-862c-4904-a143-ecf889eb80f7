<!--监测历史分析页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 查询条件 -->
    <view class="card">
      <view class="card-title">查询条件</view>
      <view class="query-form">
        <view class="form-row">
          <view class="form-item">
            <text class="form-label">时间范围</text>
            <picker mode="date" value="{{startDate}}" bindchange="onStartDateChange">
              <view class="date-picker">{{startDate || '选择开始日期'}}</view>
            </picker>
            <text class="date-separator">至</text>
            <picker mode="date" value="{{endDate}}" bindchange="onEndDateChange">
              <view class="date-picker">{{endDate || '选择结束日期'}}</view>
            </picker>
          </view>
        </view>
        
        <view class="form-row">
          <view class="form-item">
            <text class="form-label">监测指标</text>
            <view class="indicator-selector">
              <view class="indicator-item {{item.selected ? 'selected' : ''}}" 
                    wx:for="{{indicators}}" wx:key="type"
                    bindtap="toggleIndicator" data-type="{{item.type}}">
                <text class="indicator-icon">{{item.icon}}</text>
                <text class="indicator-name">{{item.name}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="form-actions">
          <button class="btn btn-primary" bindtap="queryData">查询数据</button>
          <button class="btn btn-outline" bindtap="resetQuery">重置条件</button>
        </view>
      </view>
    </view>

    <!-- 数据统计 -->
    <view class="card" wx:if="{{statisticsData.length > 0}}">
      <view class="card-title">数据统计</view>
      <view class="statistics-grid">
        <view class="stat-item" wx:for="{{statisticsData}}" wx:key="type">
          <text class="stat-icon">{{item.icon}}</text>
          <view class="stat-content">
            <text class="stat-name">{{item.name}}</text>
            <text class="stat-value">{{item.value}}</text>
            <text class="stat-desc">{{item.description}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 趋势图表 -->
    <view class="card" wx:if="{{chartData.datasets.length > 0}}">
      <view class="card-title">
        <text>趋势分析</text>
        <button class="btn btn-small btn-outline" bindtap="exportChart">
          📊 导出图表
        </button>
      </view>
      
      <view class="chart-container">
        <!-- ECharts 图表组件 -->
        <view class="echarts-wrapper">
          <chart 
            title="历史数据趋势分析"
            type="line"
            data="{{echartsData}}"
            width="{{350}}"
            height="{{250}}"
            showActions="{{true}}"
            bind:export="onChartExport"
            bind:fullscreen="onChartFullscreen">
          </chart>
        </view>
        
        <!-- 多指标对比图表 -->
        <view class="comparison-chart" wx:if="{{statisticsData.length > 1}}">
          <text class="chart-subtitle">指标对比分析</text>
          <chart 
            title="各指标对比"
            type="bar"
            data="{{statisticsData}}"
            width="{{350}}"
            height="{{200}}"
            showActions="{{false}}">
          </chart>
        </view>
      </view>
      
      <view class="chart-legend">
        <view class="legend-item" wx:for="{{chartData.datasets}}" wx:key="label">
          <view class="legend-dot" style="background-color: {{item.color}}"></view>
          <text class="legend-label">{{item.label}}</text>
        </view>
      </view>
    </view>

    <!-- 数据表格 -->
    <view class="card" wx:if="{{tableData.length > 0}}">
      <view class="card-title">
        <text>详细数据</text>
        <button class="btn btn-small btn-outline" bindtap="exportTableData">
          📄 导出数据
        </button>
      </view>
      
      <view class="data-table">
        <view class="table-header">
          <view class="header-cell time">时间</view>
          <view class="header-cell" wx:for="{{tableHeaders}}" wx:key="*this">
            {{item}}
          </view>
        </view>
        
        <scroll-view class="table-body" scroll-y>
          <view class="table-row" wx:for="{{tableData}}" wx:key="time">
            <view class="table-cell time">{{item.time}}</view>
            <view class="table-cell" wx:for="{{item.values}}" wx:key="index">
              {{item}}
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view class="no-data" wx:if="{{noData}}">
      <text class="no-data-icon">📈</text>
      <text class="no-data-text">暂无数据</text>
      <text class="no-data-desc">请调整查询条件后重试</text>
    </view>

  </view>
</view>