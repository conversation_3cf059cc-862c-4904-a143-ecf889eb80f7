/* 地图选点页面样式 */

.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F5F7FA;
}

/* 顶部搜索栏 */
.search-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-bottom: 2rpx solid #E2E8F0;
  gap: 20rpx;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #F7FAFC;
  border-radius: 25rpx;
  overflow: hidden;
}

.search-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: none;
  background: transparent;
}

.search-btn {
  width: 70rpx;
  height: 70rpx;
  background-color: #2E7D32;
  color: #FFFFFF;
  border: none;
  border-radius: 0 25rpx 25rpx 0;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn[disabled] {
  background-color: #CBD5E0;
}

.location-btn {
  height: 70rpx;
  padding: 0 20rpx;
  background-color: #1976D2;
  color: #FFFFFF;
  border: none;
  border-radius: 25rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 地图容器 */
.map-container {
  flex: 1;
  position: relative;
}

.location-map {
  width: 100%;
  height: 100%;
}

.map-center-mark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 100;
}

.crosshair {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #FF4444;
  border-radius: 50%;
  background-color: rgba(255, 68, 68, 0.2);
  position: relative;
}

.crosshair::before,
.crosshair::after {
  content: '';
  position: absolute;
  background-color: #FF4444;
}

.crosshair::before {
  width: 2rpx;
  height: 20rpx;
  top: -30rpx;
  left: 50%;
  transform: translateX(-50%);
}

.crosshair::after {
  width: 20rpx;
  height: 2rpx;
  top: 50%;
  left: -30rpx;
  transform: translateY(-50%);
}

/* 搜索结果面板 */
.search-results-panel {
  position: absolute;
  top: 110rpx;
  left: 20rpx;
  right: 20rpx;
  max-height: 60%;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  z-index: 200;
}

.results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 25rpx;
  border-bottom: 2rpx solid #E2E8F0;
}

.results-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2D3748;
}

.close-btn {
  width: 50rpx;
  height: 50rpx;
  background-color: #F7FAFC;
  color: #718096;
  border: none;
  border-radius: 25rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.results-list {
  max-height: 400rpx;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx;
  border-bottom: 1rpx solid #E2E8F0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:active {
  background-color: #F7FAFC;
}

.result-info {
  flex: 1;
}

.result-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #2D3748;
  margin-bottom: 8rpx;
}

.result-address {
  display: block;
  font-size: 24rpx;
  color: #718096;
}

.result-distance {
  font-size: 24rpx;
  color: #4A5568;
  margin-left: 15rpx;
}

.no-results {
  padding: 60rpx 25rpx;
  text-align: center;
}

.no-results-text {
  font-size: 28rpx;
  color: #A0AEC0;
}

/* 底部信息面板 */
.bottom-panel {
  background-color: #FFFFFF;
  border-top: 2rpx solid #E2E8F0;
  padding: 25rpx;
  max-height: 50%;
  display: flex;
  flex-direction: column;
}

/* 选中位置信息 */
.selected-location {
  margin-bottom: 25rpx;
}

.location-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.location-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.location-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2D3748;
}

.location-details {
  padding-left: 42rpx;
}

.location-name {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #2D3748;
  margin-bottom: 8rpx;
}

.location-address {
  display: block;
  font-size: 26rpx;
  color: #4A5568;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.location-coords {
  display: block;
  font-size: 24rpx;
  color: #718096;
  font-family: monospace;
}

/* 常用地点 */
.common-places {
  margin-bottom: 25rpx;
}

.section-title {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #4A5568;
  margin-bottom: 15rpx;
}

.places-list {
  white-space: nowrap;
}

.place-item {
  display: inline-block;
  padding: 12rpx 20rpx;
  margin-right: 15rpx;
  background-color: #EDF2F7;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
}

.place-item:active {
  background-color: #E2E8F0;
  border-color: #2E7D32;
}

.place-name {
  font-size: 24rpx;
  color: #4A5568;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: auto;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #2E7D32;
  color: #FFFFFF;
}

.btn-primary:active {
  background-color: #1B5E20;
}

.btn-secondary {
  background-color: #F7FAFC;
  color: #4A5568;
  border: 2rpx solid #E2E8F0;
}

.btn-secondary:active {
  background-color: #EDF2F7;
}