<!--智慧农业设置页面 - TDesign版本-->
<view class="settings-container">
  
  <!-- 通知设置 -->
  <view class="settings-section">
    <view class="section-header">
      <t-icon name="notification" size="20" color="#2E7D32" />
      <text class="section-title">消息通知</text>
    </view>
    
    <view class="settings-group">
      <t-cell
        title="设备异常提醒"
        description="设备离线、故障时推送通知"
        arrow="{{false}}"
      >
        <t-switch 
          slot="right-icon"
          checked="{{notificationSettings.deviceAlert}}"
          bind:change="onDeviceAlertChange"
        />
      </t-cell>
      
      <t-cell
        title="数据预警通知"
        description="土壤监测数据异常时提醒"
        arrow="{{false}}"
      >
        <t-switch 
          slot="right-icon"
          checked="{{notificationSettings.dataWarning}}"
          bind:change="onDataWarningChange"
        />
      </t-cell>
      
      <t-cell
        title="任务计划提醒"
        description="灌溉、施肥计划执行提醒"
        arrow="{{false}}"
      >
        <t-switch 
          slot="right-icon"
          checked="{{notificationSettings.taskReminder}}"
          bind:change="onTaskReminderChange"
        />
      </t-cell>
      
      <t-cell
        title="系统消息"
        description="版本更新、维护通知"
        arrow="{{false}}"
      >
        <t-switch 
          slot="right-icon"
          checked="{{notificationSettings.systemMessage}}"
          bind:change="onSystemMessageChange"
        />
      </t-cell>
    </view>
  </view>

  <!-- 数据设置 -->
  <view class="settings-section">
    <view class="section-header">
      <t-icon name="chart-bar" size="20" color="#2E7D32" />
      <text class="section-title">数据显示</text>
    </view>
    
    <view class="settings-group">
      <t-cell
        title="数据刷新频率"
        description="{{refreshFrequencyText}}"
        arrow
        bind:tap="showRefreshFrequencyPicker"
      />
      
      <t-cell
        title="历史数据保留"
        description="{{dataRetentionText}}"
        arrow
        bind:tap="showDataRetentionPicker"
      />
      
      <t-cell
        title="温度单位"
        description="{{temperatureUnitText}}"
        arrow
        bind:tap="showTemperatureUnitPicker"
      />
      
      <t-cell
        title="时间格式"
        description="{{timeFormatText}}"
        arrow
        bind:tap="showTimeFormatPicker"
      />
    </view>
  </view>

  <!-- 安全设置 -->
  <view class="settings-section">
    <view class="section-header">
      <t-icon name="secured" size="20" color="#2E7D32" />
      <text class="section-title">安全设置</text>
    </view>
    
    <view class="settings-group">
      <t-cell
        title="修改密码"
        description="更改登录密码"
        arrow
        bind:tap="changePassword"
      />
      
      <t-cell
        title="设备控制权限"
        description="设置设备操作权限"
        arrow
        bind:tap="manageDevicePermissions"
      />
      
      <t-cell
        title="自动登录"
        description="{{autoLoginDays}}天免登录"
        arrow="{{false}}"
      >
        <t-switch 
          slot="right-icon"
          checked="{{securitySettings.autoLogin}}"
          bind:change="onAutoLoginChange"
        />
      </t-cell>
      
      <t-cell
        title="生物识别登录"
        description="指纹/面容识别快速登录"
        arrow="{{false}}"
        wx:if="{{supportBiometric}}"
      >
        <t-switch 
          slot="right-icon"
          checked="{{securitySettings.biometricLogin}}"
          bind:change="onBiometricLoginChange"
        />
      </t-cell>
    </view>
  </view>

  <!-- 数据管理 -->
  <view class="settings-section">
    <view class="section-header">
      <t-icon name="folder" size="20" color="#2E7D32" />
      <text class="section-title">数据管理</text>
    </view>
    
    <view class="settings-group">
      <t-cell
        title="清除缓存"
        description="{{cacheSize}}"
        arrow
        bind:tap="clearCache"
      />
      
      <t-cell
        title="数据备份"
        description="备份设备配置和历史数据"
        arrow
        bind:tap="backupData"
      />
      
      <t-cell
        title="数据恢复"
        description="从备份文件恢复数据"
        arrow
        bind:tap="restoreData"
      />
      
      <t-cell
        title="导出数据"
        description="导出Excel格式数据报告"
        arrow
        bind:tap="exportData"
      />
    </view>
  </view>

  <!-- 关于应用 -->
  <view class="settings-section">
    <view class="section-header">
      <t-icon name="info-circle" size="20" color="#2E7D32" />
      <text class="section-title">关于应用</text>
    </view>
    
    <view class="settings-group">
      <t-cell
        title="检查更新"
        description="当前版本：{{appVersion}}"
        arrow
        bind:tap="checkUpdate"
      />
      
      <t-cell
        title="用户协议"
        arrow
        bind:tap="showUserAgreement"
      />
      
      <t-cell
        title="隐私政策"
        arrow
        bind:tap="showPrivacyPolicy"
      />
      
      <t-cell
        title="帮助与反馈"
        arrow
        bind:tap="showHelpFeedback"
      />
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <t-button 
      theme="default" 
      variant="outline" 
      size="large" 
      block
      bind:tap="logout"
      class="logout-btn"
    >
      退出登录
    </t-button>
  </view>

</view>

<!-- 选择器弹窗 -->
<t-picker
  visible="{{showPicker}}"
  title="{{pickerTitle}}"
  value="{{pickerValue}}"
  options="{{pickerOptions}}"
  bind:change="onPickerChange"
  bind:confirm="onPickerConfirm"
  bind:cancel="onPickerCancel"
/>

<!-- 加载提示 -->
<t-toast id="t-toast" />

<!-- 确认对话框 -->
<t-dialog
  visible="{{showConfirmDialog}}"
  title="{{confirmTitle}}"
  content="{{confirmContent}}"
  bind:confirm="onConfirm"
  bind:cancel="onCancel"
  confirm-btn="确认"
  cancel-btn="取消"
/>

<!-- 清除缓存进度 -->
<t-dialog
  visible="{{showProgressDialog}}"
  title="正在清除缓存..."
  bind:overlay-click="preventDefault"
>
  <view class="progress-content">
    <view class="progress-text">{{progressText}}</view>
  </view>
</t-dialog>