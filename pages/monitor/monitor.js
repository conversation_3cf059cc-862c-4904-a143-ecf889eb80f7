const app = getApp();
const api = require('../../utils/api.js');

Page({
  data: {
    viewMode: 'dashboard', // dashboard, map, depth
    selectedDepth: '10cm',
    
    sensorList: [],
    realTimeData: [],

    // 地图模式数据
    sensorMapData: [],

    // 深度监测数据
    depthData: {
      '10cm': [],
      '20cm': [],
      '30cm': []
    },
    selectedChart: 'humidity',
    selectedTimeRange: 0,
    timeRangeOptions: ['24小时', '7天', '30天', '自定义'],
    chartData: {},
    // ECharts图表数据
    humidityChartData: [],
    temperatureChartData: [],
    phChartData: [],
    ecChartData: [],
    alarmList: [],
    // 预警统计
    alarmStats: {
      lightCount: 0,
      moderateCount: 0, 
      severeCount: 0,
      continuousCount: 0,
      totalCount: 0
    },
    
    // 加载状态
    loading: false,
    refreshing: false
  },

  onLoad() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.loadSensorData();
    this.loadRealTimeData();
    this.loadChartData();
    this.loadAlarmData();
  },

  onShow() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  async loadSensorData() {
    try {
      // 获取传感器列表
      const response = await api.getLatestSensorData();
      console.log('传感器数据API响应:', response);
      
      if (response && response.code === 200 && response.data) {
        const sensorData = response.data;
        const sensors = sensorData.sensors || [];
        const sensorList = sensors.map(sensor => {
          return {
            id: sensor.sensorId,
            name: sensor.sensorName,
            location: sensor.plotName,
            status: sensor.status.online ? 'online' : 'offline',
            lastUpdate: this.formatTime(sensor.lastUpdate)
          };
        });
        
        this.setData({ sensorList });
      }
    } catch (error) {
      console.error('加载传感器状态失败:', error);
    }
  },

  async loadRealTimeData() {
    try {
      const response = await api.getLatestSensorData();
      console.log('实时数据API响应:', response);
      
      if (response && response.code === 200 && response.data) {
        const sensorData = response.data;
        const sensors = sensorData.sensors || [];
        const firstSensor = sensors[0];
        
        if (firstSensor && firstSensor.currentReading) {
          const data = firstSensor.currentReading;
          const realTimeData = [
            {
              type: 'humidity',
              name: '土壤湿度',
              value: data.moisture?.toString() || '0',
              unit: '%',
              range: '50-80%',
              status: this.getStatusByValue(data.moisture, 50, 80),
              icon: '💧',
              gaugeAngle: this.calculateGaugeAngle(data.moisture || 0, 0, 100)
            },
            {
              type: 'temperature',
              name: '土壤温度',
              value: data.temperature?.toString() || '0',
              unit: '°C',
              range: '18-28°C',
              status: this.getStatusByValue(data.temperature, 18, 28),
              icon: '🌡️',
              gaugeAngle: this.calculateGaugeAngle(data.temperature || 0, 0, 50)
            },
            {
              type: 'ph',
              name: 'PH值',
              value: data.ph?.toString() || '0',
              unit: '',
              range: '6.0-7.5',
              status: this.getStatusByValue(data.ph, 6.0, 7.5),
              icon: '🧪',
              gaugeAngle: this.calculateGaugeAngle(data.ph || 0, 0, 14)
            },
            {
              type: 'ec',
              name: 'EC值',
              value: (data.ec ? (data.ec / 1000).toFixed(1) : '0'),
              unit: 'mS/cm',
              range: '0.8-2.0',
              status: this.getStatusByValue(data.ec / 1000, 0.8, 2.0),
              icon: '⚡',
              gaugeAngle: this.calculateGaugeAngle((data.ec || 0) / 1000, 0, 5)
            }
          ];
          
          this.setData({ realTimeData });
        }
      }
    } catch (error) {
      console.error('加载实时数据失败:', error);
    }
  },

  async loadChartData() {
    try {
      // 为简化，使用传感器历史数据
      const firstSensorId = 'sensor_001';
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
      
      const response = await api.getHistoryData({
        sensorId: firstSensorId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        interval: 'hour'
      });
      
      if (response.code === 200) {
        const historyData = response.data.data;
        
        if (historyData.length > 0) {
          const humidityChartData = historyData.map(item => ({
            name: new Date(item.timestamp).toLocaleTimeString().substring(0, 5),
            value: item.moisture || 0
          }));
          
          const temperatureChartData = historyData.map(item => ({
            name: new Date(item.timestamp).toLocaleTimeString().substring(0, 5),
            value: item.temperature || 0
          }));
          
          const phChartData = historyData.map(item => ({
            name: new Date(item.timestamp).toLocaleTimeString().substring(0, 5),
            value: item.ph || 0
          }));
          
          const ecChartData = historyData.map(item => ({
            name: new Date(item.timestamp).toLocaleTimeString().substring(0, 5),
            value: (item.ec || 0) / 1000
          }));
          
          this.setData({
            humidityChartData,
            temperatureChartData,
            phChartData,
            ecChartData
          });
        }
      }
    } catch (error) {
      console.error('加载图表数据失败:', error);
    }
  },

  async loadAlarmData() {
    try {
      const response = await api.getAlarms({ status: 'active' });
      
      if (response.code === 200) {
        const alarms = response.data.alarms || [];
        const alarmList = alarms.map(alarm => ({
          id: alarm.id,
          level: alarm.level,
          priority: alarm.level === 'critical' ? 'urgent' : alarm.level === 'warning' ? 'medium' : 'low',
          title: alarm.title,
          description: alarm.description,
          time: this.formatTime(alarm.time || alarm.createdAt),
          duration: this.calculateDuration(alarm.time || alarm.createdAt),
          sensor: alarm.sensorId || 'Unknown',
          value: alarm.currentValue?.toString() || '--',
          threshold: alarm.thresholdValue?.toString() || '--',
          suggestion: alarm.suggestion || '请联系技术人员'
        }));
        
        // 计算统计数据
        const alarmStats = {
          lightCount: alarmList.filter(item => item.level === 'info').length,
          moderateCount: alarmList.filter(item => item.level === 'warning').length,
          severeCount: alarmList.filter(item => item.level === 'critical').length,
          continuousCount: 0, // 需要根据实际业务逻辑计算
          totalCount: alarmList.length
        };
        
        this.setData({
          alarmList,
          alarmStats
        });
      }
    } catch (error) {
      console.error('加载预警数据失败:', error);
    }
  },

  getStatusByValue(value, min, max) {
    if (!value) return 'unknown';
    if (value < min || value > max) return 'warning';
    return 'normal';
  },

  calculateDuration(createdAt) {
    const now = new Date();
    const created = new Date(createdAt);
    const diff = now - created;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    
    if (hours > 0) {
      return `持续${hours}小时`;
    } else {
      return `持续${minutes}分钟`;
    }
  },

  formatTime(timeString) {
    const time = new Date(timeString);
    const now = new Date();
    const diff = now - time;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else {
      return `${days}天前`;
    }
  },

  async refreshData() {
    this.setData({ refreshing: true });
    
    try {
      await Promise.all([
        this.loadSensorData(),
        this.loadRealTimeData(),
        this.loadChartData(),
        this.loadAlarmData()
      ]);
    } catch (error) {
      console.error('刷新数据失败:', error);
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      });
    } finally {
      this.setData({ refreshing: false });
    }
  },

  switchChart(e) {
    const chart = e.currentTarget.dataset.chart;
    this.setData({
      selectedChart: chart
    });
    
    // 更新图表数据
    let chartData = {};
    switch (chart) {
      case 'humidity':
        chartData = {
          title: '土壤湿度趋势',
          latestValue: '65%',
          avgValue: '62%',
          points: [
            { x: 10, y: 50 },
            { x: 25, y: 60 },
            { x: 40, y: 55 },
            { x: 55, y: 70 },
            { x: 70, y: 65 },
            { x: 85, y: 68 }
          ]
        };
        break;
      case 'temperature':
        chartData = {
          title: '土壤温度趋势',
          latestValue: '23°C',
          avgValue: '22°C',
          points: [
            { x: 10, y: 45 },
            { x: 25, y: 50 },
            { x: 40, y: 52 },
            { x: 55, y: 48 },
            { x: 70, y: 55 },
            { x: 85, y: 60 }
          ]
        };
        break;
      case 'ph':
        chartData = {
          title: 'pH值趋势',
          latestValue: '6.8',
          avgValue: '6.7',
          points: [
            { x: 10, y: 55 },
            { x: 25, y: 52 },
            { x: 40, y: 58 },
            { x: 55, y: 60 },
            { x: 70, y: 56 },
            { x: 85, y: 62 }
          ]
        };
        break;
      case 'ec':
        chartData = {
          title: 'EC值趋势',
          latestValue: '1.2mS/cm',
          avgValue: '1.1mS/cm',
          points: [
            { x: 10, y: 45 },
            { x: 25, y: 48 },
            { x: 40, y: 52 },
            { x: 55, y: 50 },
            { x: 70, y: 55 },
            { x: 85, y: 58 }
          ]
        };
        break;
    }
    
    this.setData({
      chartData: chartData
    });
  },

  // 获取当前选中的图表数据
  getCurrentChartData() {
    const { selectedChart } = this.data;
    switch (selectedChart) {
      case 'humidity':
        return this.data.humidityChartData;
      case 'temperature':
        return this.data.temperatureChartData;
      case 'ph':
        return this.data.phChartData;
      case 'ec':
        return this.data.ecChartData;
      default:
        return this.data.humidityChartData;
    }
  },

  // 图表导出事件
  onChartExport(e) {
    wx.showToast({
      title: '图表导出功能',
      icon: 'none'
    });
  },

  // 图表全屏事件
  onChartFullscreen(e) {
    wx.showToast({
      title: '图表全屏功能',
      icon: 'none'
    });
  },

  onTimeRangeChange(e) {
    this.setData({
      selectedTimeRange: e.detail.value
    });
    
    // 根据时间范围重新加载数据
    this.loadChartData();
  },

  // 快速处理预警
  handleAlarmQuick(e) {
    const alarmId = e.currentTarget.dataset.id;
    const alarm = this.data.alarmList.find(item => item.id == alarmId);
    
    wx.showModal({
      title: '快速处理预警',
      content: `确定要处理"${alarm.title}"吗？\n\n处理建议: ${alarm.suggestion}`,
      success: (res) => {
        if (res.confirm) {
          // 根据预警类型执行相应操作
          this.executeQuickAction(alarm);
          
          // 从列表中移除已处理的预警
          this.removeAlarm(alarmId);
        }
      }
    });
  },

  // 查看预警详情
  viewAlarmDetail(e) {
    const alarmId = e.currentTarget.dataset.id;
    const alarm = this.data.alarmList.find(item => item.id == alarmId);
    
    wx.showModal({
      title: '预警详情',
      content: `${alarm.description}\n\n传感器: ${alarm.sensor}\n持续时间: ${alarm.duration}\n当前值: ${alarm.value}\n正常范围: ${alarm.threshold}\n\n处理建议: ${alarm.suggestion}`,
      showCancel: false
    });
  },

  // 忽略预警
  ignoreAlarm(e) {
    const alarmId = e.currentTarget.dataset.id;
    const alarm = this.data.alarmList.find(item => item.id == alarmId);
    
    wx.showModal({
      title: '忽略预警',
      content: `确定要忽略"${alarm.title}"吗？\n\n注意：忽略后该预警将不再显示，但问题仍然存在。`,
      confirmText: '确定忽略',
      confirmColor: '#FF9800',
      success: (res) => {
        if (res.confirm) {
          this.removeAlarm(alarmId);
          wx.showToast({
            title: '已忽略预警',
            icon: 'none'
          });
        }
      }
    });
  },

  // 执行快速操作
  executeQuickAction(alarm) {
    wx.showLoading({
      title: '正在处理...'
    });

    // 模拟执行相应操作
    setTimeout(() => {
      wx.hideLoading();
      
      let message = '';
      switch (alarm.level) {
        case 'light':
          message = '已设置监控提醒，将密切关注数值变化';
          break;
        case 'moderate':
          message = '已启动自动调节程序';
          break;
        case 'severe':
          message = '已发送紧急处理指令';
          break;
        case 'continuous':
          message = '已安排技术人员现场检查';
          break;
      }
      
      wx.showToast({
        title: message,
        icon: 'success',
        duration: 2000
      });
    }, 1500);
  },

  // 移除预警
  removeAlarm(alarmId) {
    const alarmList = this.data.alarmList.filter(item => item.id != alarmId);
    
    // 重新计算统计数据
    const alarmStats = {
      lightCount: alarmList.filter(item => item.level === 'light').length,
      moderateCount: alarmList.filter(item => item.level === 'moderate').length,
      severeCount: alarmList.filter(item => item.level === 'severe').length,
      continuousCount: alarmList.filter(item => item.level === 'continuous').length,
      totalCount: alarmList.length
    };
    
    this.setData({
      alarmList: alarmList,
      alarmStats: alarmStats
    });
  },

  // 查看预警历史
  viewAlarmHistory() {
    wx.showModal({
      title: '预警历史',
      content: '查看预警历史记录和处理报告。',
      showCancel: false
    });
  },

  exportData() {
    wx.showToast({
      title: '正在导出数据...',
      icon: 'loading'
    });
    
    // 模拟导出过程
    setTimeout(() => {
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      });
    }, 2000);
  },

  viewHistory() {
    wx.navigateTo({
      url: '/pages/monitor/history/history'
    });
  },

  alarmSettings() {
    wx.navigateTo({
      url: '/pages/monitor/alarm/alarm'
    });
  },

  manageSensors() {
    wx.navigateTo({
      url: '/pages/monitor/sensor/sensor'
    });
  },

  // 切换显示模式
  switchViewMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({
      viewMode: mode
    });
  },

  // 切换深度
  switchDepth(e) {
    const depth = e.currentTarget.dataset.depth;
    this.setData({
      selectedDepth: depth
    });
  },

  // 查看传感器地图详情
  viewSensorMapDetail(e) {
    const sensorId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/monitor/sensor/detail/detail?id=${sensorId}`
    });
  },

  // 计算仪表盘角度
  calculateGaugeAngle(value, min, max) {
    const percentage = Math.min(Math.max((value - min) / (max - min), 0), 1);
    return percentage * 270; // 0-270度范围
  },

  // 更新仪表盘数据
  updateGaugeData() {
    const realTimeData = this.data.realTimeData.map(item => {
      let min, max, angle;
      
      switch (item.type) {
        case 'humidity':
          min = 0; max = 100;
          angle = this.calculateGaugeAngle(parseFloat(item.value), min, max);
          break;
        case 'temperature':
          min = 0; max = 50;
          angle = this.calculateGaugeAngle(parseFloat(item.value), min, max);
          break;
        case 'ph':
          min = 0; max = 14;
          angle = this.calculateGaugeAngle(parseFloat(item.value), min, max);
          break;
        case 'ec':
          min = 0; max = 5;
          angle = this.calculateGaugeAngle(parseFloat(item.value), min, max);
          break;
        default:
          angle = 0;
      }
      
      return {
        ...item,
        gaugeAngle: angle
      };
    });
    
    this.setData({
      realTimeData: realTimeData
    });
  },

})