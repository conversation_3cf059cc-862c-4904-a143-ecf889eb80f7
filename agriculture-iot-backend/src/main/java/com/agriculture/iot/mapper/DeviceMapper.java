package com.agriculture.iot.mapper;

import com.agriculture.iot.entity.Device;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备数据访问层
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Mapper
public interface DeviceMapper extends BaseMapper<Device> {
    
    /**
     * 获取设备操作日志
     * 
     * @param deviceId 设备ID
     * @param page 分页对象
     * @param operationType 操作类型
     * @return 操作日志
     */
    IPage<Object> getDeviceLogs(@Param("deviceId") Long deviceId, 
                               @Param("page") Page<Object> page, 
                               @Param("operationType") String operationType);
    
    /**
     * 获取设备总运行时长
     * 
     * @param deviceId 设备ID
     * @return 运行时长（小时）
     */
    Integer getTotalRunningHours(@Param("deviceId") Long deviceId);
    
    /**
     * 获取设备本月运行时长
     * 
     * @param deviceId 设备ID
     * @return 运行时长（小时）
     */
    Integer getMonthlyRunningHours(@Param("deviceId") Long deviceId);
    
    /**
     * 获取设备总操作次数
     * 
     * @param deviceId 设备ID
     * @return 操作次数
     */
    Integer getTotalOperations(@Param("deviceId") Long deviceId);
    
    /**
     * 获取设备本月操作次数
     * 
     * @param deviceId 设备ID
     * @return 操作次数
     */
    Integer getMonthlyOperations(@Param("deviceId") Long deviceId);
    
    /**
     * 获取设备故障次数
     * 
     * @param deviceId 设备ID
     * @return 故障次数
     */
    Integer getFaultCount(@Param("deviceId") Long deviceId);
    
    /**
     * 获取设备维护次数
     * 
     * @param deviceId 设备ID
     * @return 维护次数
     */
    Integer getMaintenanceCount(@Param("deviceId") Long deviceId);
    
    /**
     * 批量更新设备状态
     * 
     * @param deviceIds 设备ID列表
     * @param status 状态
     * @return 更新行数
     */
    int batchUpdateStatus(@Param("deviceIds") List<Long> deviceIds, @Param("status") Integer status);
}