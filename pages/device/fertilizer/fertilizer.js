const app = getApp();
const api = require('../../../utils/api.js');

Page({
  data: {
    // 当前选择的标签页
    activeTab: 'control', // control, schedule, formula
    
    // 施肥配方数据
    formulas: [],
    selectedFormula: null,
    
    // 施肥记录数据
    records: [],
    recordsFilter: {
      status: 'all',
      startDate: '',
      endDate: ''
    },
    
    // 当前施肥操作
    currentOperation: null,
    operationStatus: 'idle', // idle, running, paused
    fertilizerRunning: false,
    remainingTime: '00:00',
    
    // 设备信息
    deviceStatus: {
      online: true,
      class: 'online',
      text: '在线'
    },
    deviceInfo: {
      fertilizerA: 85,
      fertilizerB: 72,
      ecValue: 1.8,
      phValue: 6.5
    },
    
    // 控制参数
    formulaList: [],
    selectedFormula: 0,
    concentration: 15,
    duration: 20,
    targetEC: 1.8,
    targetPH: 6.5,
    
    // 定时任务列表
    scheduleList: [],
    
    // 操作参数
    operationParams: {
      formulaId: '',
      deviceId: '',
      plotId: '',
      duration: 20,
      concentration: 15,
      customParams: {}
    },
    
    // 设备和地块选项
    deviceOptions: [],
    plotOptions: [],
    
    // 推荐配方
    recommendations: [],
    
    // 统计数据
    statistics: {
      todayRecords: 0,
      totalVolume: 0,
      avgEfficiency: 0,
      successRate: 0
    },
    
    // 加载状态
    loading: false,
    refreshing: false
  },

  onLoad(options) {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    // 处理传入的参数
    if (options.tab) {
      this.setData({ activeTab: options.tab });
    }
    
    this.initData();
  },

  onShow() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化数据
  async initData() {
    this.setData({ loading: true });
    
    try {
      await Promise.all([
        this.loadFormulas(),
        this.loadRecords(),
        this.loadDeviceOptions(),
        this.loadPlotOptions(),
        this.loadStatistics(),
        this.loadScheduleList()
      ]);
      
      // 加载推荐配方
      await this.loadRecommendations();
    } catch (error) {
      console.error('初始化数据失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 刷新数据
  async refreshData() {
    this.setData({ refreshing: true });
    
    try {
      const { activeTab } = this.data;
      
      switch (activeTab) {
        case 'formula':
          await this.loadFormulas();
          await this.loadRecommendations();
          break;
        case 'schedule':
          await this.loadRecords();
          await this.loadStatistics();
          break;
        case 'control':
          await this.loadDeviceOptions();
          await this.loadPlotOptions();
          break;
      }
    } catch (error) {
      console.error('刷新数据失败:', error);
    } finally {
      this.setData({ refreshing: false });
    }
  },

  // 加载施肥配方
  async loadFormulas() {
    try {
      const response = await api.getFertilizerFormulas({
        active: true,
        page: 1,
        pageSize: 20
      });
      
      if (response.code === 200) {
        const formulas = response.data.formulas || [];
        
        this.setData({
          formulas: formulas,
          formulaList: formulas // 同时设置formulaList供picker组件使用
        });
      }
    } catch (error) {
      console.error('加载施肥配方失败:', error);
      wx.showToast({
        title: '加载配方失败',
        icon: 'none'
      });
    }
  },

  // 加载施肥记录
  async loadRecords() {
    try {
      const { recordsFilter } = this.data;
      
      const response = await api.getFertilizerRecords({
        status: recordsFilter.status === 'all' ? undefined : recordsFilter.status,
        startDate: recordsFilter.startDate,
        endDate: recordsFilter.endDate,
        page: 1,
        pageSize: 20
      });
      
      if (response.code === 200) {
        const records = response.data.records || [];
        
        this.setData({
          records: records
        });
      }
    } catch (error) {
      console.error('加载施肥记录失败:', error);
      wx.showToast({
        title: '加载记录失败',
        icon: 'none'
      });
    }
  },

  // 加载设备选项
  async loadDeviceOptions() {
    try {
      const response = await api.getDeviceList();
      
      if (response.code === 200) {
        const devices = response.data.devices || [];
        const fertilizerDevices = devices.filter(device => 
          device.type === 'irrigation' && device.capabilities?.fertilizerTanks > 0
        );
        
        const deviceOptions = fertilizerDevices.map(device => ({
          id: device.id,
          name: device.name,
          status: device.status,
          fertilizerTanks: device.capabilities?.fertilizerTanks || 1
        }));
        
        this.setData({ deviceOptions });
      }
    } catch (error) {
      console.error('加载设备选项失败:', error);
    }
  },

  // 加载地块选项
  async loadPlotOptions() {
    try {
      const response = await api.getPlots('farm_001');
      
      if (response.code === 200) {
        const plots = response.data.plots || [];
        
        const plotOptions = plots.map(plot => ({
          id: plot.id,
          name: plot.name,
          area: plot.area,
          currentCrop: plot.currentCrop?.type || '未种植',
          status: plot.status
        }));
        
        this.setData({ plotOptions });
      }
    } catch (error) {
      console.error('加载地块选项失败:', error);
    }
  },

  // 加载统计数据
  async loadStatistics() {
    try {
      const response = await api.getFertilizerRecords({
        page: 1,
        pageSize: 100 // 获取更多数据用于统计
      });
      
      if (response.code === 200) {
        const statistics = response.data.statistics || {};
        
        this.setData({
          statistics: {
            todayRecords: statistics.todayRecords || 0,
            totalVolume: statistics.totalVolume || 0,
            avgEfficiency: statistics.avgEfficiency || 0,
            successRate: statistics.successRate || 0
          }
        });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  // 加载推荐配方
  async loadRecommendations() {
    try {
      const response = await api.getFertilizerRecommendations({
        cropType: '番茄', // 这里可以根据实际情况动态获取
        growthStage: '开花期',
        season: this.getCurrentSeason()
      });
      
      if (response.code === 200) {
        const recommendations = response.data.recommendations || [];
        
        this.setData({
          recommendations: recommendations.slice(0, 3) // 只显示前3个推荐
        });
      }
    } catch (error) {
      console.error('加载推荐配方失败:', error);
    }
  },

  // 加载定时任务列表
  async loadScheduleList() {
    try {
      // 模拟获取施肥定时任务数据
      const mockScheduleList = [
        {
          id: 'fertilizer_schedule_001',
          name: '早晨施肥',
          time: '07:30',
          days: ['周一', '周三', '周五'],
          formulaName: '番茄生长期配方',
          concentration: 20,
          duration: 15,
          nextRun: '明天 07:30',
          enabled: true
        },
        {
          id: 'fertilizer_schedule_002',
          name: '午间补肥',
          time: '12:00',
          days: ['周二', '周四', '周六'],
          formulaName: '生菜营养配方',
          concentration: 15,
          duration: 10,
          nextRun: '后天 12:00',
          enabled: true
        },
        {
          id: 'fertilizer_schedule_003',
          name: '傍晚施肥',
          time: '18:00',
          days: ['每天'],
          formulaName: '通用平衡配方',
          concentration: 18,
          duration: 20,
          nextRun: '今天 18:00',
          enabled: false
        }
      ];

      this.setData({
        scheduleList: mockScheduleList
      });
    } catch (error) {
      console.error('加载定时任务失败:', error);
      // 设置默认的空列表
      this.setData({
        scheduleList: []
      });
    }
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
    this.refreshData();
  },

  // 选择配方
  selectFormula(e) {
    const formulaId = e.currentTarget.dataset.id;
    const formula = this.data.formulas.find(f => f.id === formulaId);
    
    this.setData({
      selectedFormula: formula,
      'operationParams.formulaId': formulaId,
      'operationParams.concentration': formula?.concentration?.dosage || 15
    });
    
    wx.showToast({
      title: `已选择：${formula?.name}`,
      icon: 'success'
    });
  },

  // 查看配方详情
  viewFormulaDetail(e) {
    const formulaId = e.currentTarget.dataset.id;
    const formula = this.data.formulas.find(f => f.id === formulaId);
    
    if (formula) {
      const content = `类型：${formula.type}\n阶段：${formula.stage}\n\n营养成分：\n氮(N)：${formula.nutrients.nitrogen.percentage}%\n磷(P)：${formula.nutrients.phosphorus.percentage}%\n钾(K)：${formula.nutrients.potassium.percentage}%\n\n适用作物：${formula.suitableCrops.join('、')}\n稀释比例：1:${formula.concentration.ratio}`;
      
      wx.showModal({
        title: formula.name,
        content: content,
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },

  // 设置操作参数
  setOperationParam(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`operationParams.${field}`]: value
    });
  },

  // 施肥控制开关（匹配WXML中的bindchange="onFertilizerToggle"）
  onFertilizerToggle(e) {
    const checked = e.detail.value;
    
    this.setData({
      fertilizerRunning: checked
    });
    
    if (checked) {
      this.startFertilization();
    } else {
      this.stopFertilization();
    }
  },

  // 施肥控制开关（备用方法名）
  onFertilizationToggle(e) {
    this.onFertilizerToggle(e);
  },

  // 开始施肥
  async startFertilization() {
    const { operationParams, selectedFormula } = this.data;
    
    // 验证参数
    if (!operationParams.formulaId) {
      wx.showToast({
        title: '请选择施肥配方',
        icon: 'none'
      });
      return;
    }
    
    if (!operationParams.deviceId) {
      wx.showToast({
        title: '请选择设备',
        icon: 'none'
      });
      return;
    }
    
    if (!operationParams.plotId) {
      wx.showToast({
        title: '请选择地块',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '开始施肥',
      content: `确定开始施肥吗？\n配方：${selectedFormula?.name}\n时长：${operationParams.duration}分钟\n浓度：${operationParams.concentration}%`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '启动中...' });
            
            const response = await api.startFertilization({
              formulaId: operationParams.formulaId,
              deviceId: operationParams.deviceId,
              plotId: operationParams.plotId,
              duration: operationParams.duration * 60, // 转换为秒
              customParameters: {
                concentration: operationParams.concentration,
                ...operationParams.customParams
              }
            });
            
            if (response.code === 200) {
              this.setData({
                currentOperation: response.data,
                operationStatus: 'running'
              });
              
              wx.showToast({
                title: '施肥启动成功',
                icon: 'success'
              });
              
              // 开始定时器更新状态
              this.startOperationTimer();
            }
          } catch (error) {
            console.error('启动施肥失败:', error);
            wx.showToast({
              title: error.message || '启动失败',
              icon: 'none'
            });
          } finally {
            wx.hideLoading();
          }
        }
      }
    });
  },

  // 停止施肥
  async stopFertilization() {
    const { currentOperation } = this.data;
    
    if (!currentOperation) {
      return;
    }
    
    wx.showModal({
      title: '停止施肥',
      content: '确定要停止当前施肥操作吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '停止中...' });
            
            const response = await api.stopFertilization(currentOperation.recordId);
            
            if (response.code === 200) {
              this.setData({
                currentOperation: null,
                operationStatus: 'idle'
              });
              
              this.clearOperationTimer();
              
              wx.showToast({
                title: '施肥停止成功',
                icon: 'success'
              });
              
              // 刷新记录
              this.loadRecords();
            }
          } catch (error) {
            console.error('停止施肥失败:', error);
            wx.showToast({
              title: error.message || '停止失败',
              icon: 'none'
            });
          } finally {
            wx.hideLoading();
          }
        }
      }
    });
  },

  // 开始操作定时器
  startOperationTimer() {
    this.operationTimer = setInterval(() => {
      this.updateOperationStatus();
    }, 5000); // 每5秒更新一次
  },

  // 清除操作定时器
  clearOperationTimer() {
    if (this.operationTimer) {
      clearInterval(this.operationTimer);
      this.operationTimer = null;
    }
  },

  // 更新操作状态
  async updateOperationStatus() {
    const { currentOperation } = this.data;
    
    if (!currentOperation) {
      return;
    }
    
    // 这里可以调用API获取实时状态
    // 简化处理，模拟状态更新
    const now = new Date();
    const startTime = new Date(currentOperation.startTime);
    const elapsed = Math.floor((now - startTime) / 1000); // 已执行秒数
    const remaining = Math.max(0, currentOperation.estimatedDuration - elapsed);
    
    if (remaining <= 0) {
      // 操作完成
      this.setData({
        currentOperation: null,
        operationStatus: 'idle'
      });
      
      this.clearOperationTimer();
      
      wx.showToast({
        title: '施肥完成',
        icon: 'success'
      });
      
      this.loadRecords();
    } else {
      // 更新剩余时间
      const minutes = Math.floor(remaining / 60);
      const seconds = remaining % 60;
      const remainingText = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      
      this.setData({
        'currentOperation.remainingTime': remainingText
      });
    }
  },

  // 筛选记录
  filterRecords(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      'recordsFilter.status': filter
    });
    this.loadRecords();
  },

  // 查看记录详情
  viewRecordDetail(e) {
    const recordId = e.currentTarget.dataset.id;
    const record = this.data.records.find(r => r.id === recordId);
    
    if (record) {
      const content = `配方：${record.formulaName}\n开始时间：${this.formatTime(record.startTime)}\n结束时间：${this.formatTime(record.endTime)}\n持续时间：${record.duration}分钟\n用量：${record.volumeUsed}L\n效率：${record.efficiency}%\n状态：${record.status === 'completed' ? '完成' : '失败'}`;
      
      wx.showModal({
        title: '施肥记录详情',
        content: content,
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },

  // 应用推荐配方
  applyRecommendation(e) {
    const index = e.currentTarget.dataset.index;
    const recommendation = this.data.recommendations[index];
    
    if (recommendation) {
      this.setData({
        selectedFormula: recommendation,
        'operationParams.formulaId': recommendation.id,
        'operationParams.concentration': recommendation.concentration?.dosage || 15,
        activeTab: 'operation'
      });
      
      wx.showToast({
        title: `已应用推荐配方：${recommendation.name}`,
        icon: 'success'
      });
    }
  },

  // 辅助方法
  getCurrentSeason() {
    const month = new Date().getMonth() + 1;
    if (month >= 3 && month <= 5) return '春季';
    if (month >= 6 && month <= 8) return '夏季';
    if (month >= 9 && month <= 11) return '秋季';
    return '冬季';
  },

  formatTime(timeString) {
    const time = new Date(timeString);
    return time.toLocaleString();
  },

  // 页面销毁
  onUnload() {
    this.clearOperationTimer();
  },

  // 导航方法
  createFormula() {
    wx.navigateTo({
      url: '/pages/device/fertilizer/formula/formula?action=create'
    });
  },

  editFormula(e) {
    const formulaId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/device/fertilizer/formula/formula?action=edit&id=${formulaId}`
    });
  },

  addSchedule() {
    wx.navigateTo({
      url: '/pages/device/schedule/add/add?type=fertilization'
    });
  },

  viewAllRecords() {
    wx.navigateTo({
      url: '/pages/device/logs/logs?type=fertilization'
    });
  },

  // WXML中需要的其他方法
  onFormulaChange(e) {
    const index = e.detail.value;
    this.setData({
      selectedFormula: index
    });
  },

  onConcentrationTouchStart(e) {
    // 浓度滑块开始拖动
  },

  onConcentrationTouchMove(e) {
    // 浓度滑块拖动中
  },

  onConcentrationTouchEnd(e) {
    // 浓度滑块拖动结束
  },

  onDurationInput(e) {
    this.setData({
      duration: e.detail.value
    });
  },

  onTargetECInput(e) {
    this.setData({
      targetEC: e.detail.value
    });
  },

  onTargetPHInput(e) {
    this.setData({
      targetPH: e.detail.value
    });
  },

  applyPreset(e) {
    const preset = e.currentTarget.dataset.preset;
    console.log('应用预设配方:', preset);
    wx.showToast({
      title: '配方已应用',
      icon: 'success'
    });
  },

  pauseFertilizer() {
    wx.showToast({
      title: '施肥已暂停',
      icon: 'success'
    });
  },

  flushSystem() {
    wx.showModal({
      title: '系统冲洗',
      content: '确定要进行系统冲洗吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '开始冲洗系统',
            icon: 'success'
          });
        }
      }
    });
  },

  stopFertilizer() {
    this.setData({
      fertilizerRunning: false
    });
    this.stopFertilization();
  },

  toggleSchedule(e) {
    const id = e.currentTarget.dataset.id;
    const enabled = e.detail.value;
    console.log('切换定时任务:', id, enabled);
  },

  editSchedule(e) {
    const id = e.currentTarget.dataset.id;
    console.log('编辑定时任务:', id);
  },

  deleteSchedule(e) {
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: '删除任务',
      content: '确定要删除这个定时任务吗？',
      success: (res) => {
        if (res.confirm) {
          console.log('删除定时任务:', id);
          wx.showToast({
            title: '任务已删除',
            icon: 'success'
          });
        }
      }
    });
  },

  addFormula() {
    wx.navigateTo({
      url: '/pages/device/formula/formula?action=create',
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面不存在，功能开发中',
          icon: 'none'
        });
      }
    });
  },

  selectFormula(e) {
    const id = e.currentTarget.dataset.id;
    console.log('选择配方:', id);
    
    // 更新选中状态
    const formulas = this.data.formulas.map(formula => ({
      ...formula,
      active: formula.id === id
    }));
    
    this.setData({
      formulas: formulas,
      formulaList: formulas
    });
    
    wx.showToast({
      title: '配方已选择',
      icon: 'success'
    });
  },

  editFormula(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/device/formula/formula?action=edit&id=${id}`,
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面不存在，功能开发中',
          icon: 'none'
        });
      }
    });
  },

  duplicateFormula(e) {
    const id = e.currentTarget.dataset.id;
    console.log('复制配方:', id);
  },

  deleteFormula(e) {
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: '删除配方',
      content: '确定要删除这个配方吗？',
      success: (res) => {
        if (res.confirm) {
          console.log('删除配方:', id);
          wx.showToast({
            title: '配方已删除',
            icon: 'success'
          });
        }
      }
    });
  }
});