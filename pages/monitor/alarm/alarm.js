const api = require('../../../utils/api.js');

Page({
  data: {
    // 预警配置
    alarmSettings: {
      soilMoisture: {
        enabled: true,
        min: 30,
        max: 80,
        level: 'medium'
      },
      soilTemperature: {
        enabled: true,
        min: 15,
        max: 35,
        level: 'medium'
      },
      phValue: {
        enabled: true,
        min: 6.0,
        max: 7.5,
        level: 'high'
      },
      ecValue: {
        enabled: true,
        min: 500,
        max: 2000,
        level: 'medium'
      },
      nitrogen: {
        enabled: false,
        min: 20,
        max: 100,
        level: 'low'
      },
      phosphorus: {
        enabled: false,
        min: 10,
        max: 50,
        level: 'low'
      },
      potassium: {
        enabled: false,
        min: 15,
        max: 80,
        level: 'low'
      }
    },
    
    // 预警级别选项
    levelOptions: [
      { value: 'low', label: '低级预警', color: '#FFA726' },
      { value: 'medium', label: '中级预警', color: '#FF7043' },
      { value: 'high', label: '高级预警', color: '#E53935' }
    ],
    
    // 通知设置
    notificationSettings: {
      push: true,
      wechat: true,
      sms: false,
      email: false
    },
    
    // 预警历史
    alarmHistory: [],
    
    // 参数信息
    parameterInfo: {
      soilMoisture: {
        name: '土壤湿度',
        unit: '%',
        icon: '💧',
        description: '土壤含水量百分比'
      },
      soilTemperature: {
        name: '土壤温度',
        unit: '℃',
        icon: '🌡️',
        description: '土壤温度值'
      },
      phValue: {
        name: 'pH值',
        unit: '',
        icon: '⚗️',
        description: '土壤酸碱度'
      },
      ecValue: {
        name: 'EC值',
        unit: 'μS/cm',
        icon: '⚡',
        description: '电导率，反映盐分浓度'
      },
      nitrogen: {
        name: '氮含量',
        unit: 'mg/kg',
        icon: '🧪',
        description: '土壤氮素含量'
      },
      phosphorus: {
        name: '磷含量',
        unit: 'mg/kg',
        icon: '🧪',
        description: '土壤磷素含量'
      },
      potassium: {
        name: '钾含量',
        unit: 'mg/kg',
        icon: '🧪',
        description: '土壤钾素含量'
      }
    },
    
    currentTab: 'settings',
    editingParameter: null,
    tempSettings: {},
    displayData: {},
    parameterKeys: []
  },

  onLoad() {
    this.loadAlarmSettings();
    this.loadAlarmHistory();
  },

  loadAlarmSettings() {
    // 从本地存储加载预警设置
    const settings = wx.getStorageSync('alarmSettings');
    if (settings) {
      this.setData({
        alarmSettings: { ...this.data.alarmSettings, ...settings }
      });
    }
    this.updateDisplayData();
  },

  async loadAlarmHistory() {
    try {
      const response = await api.getAlarms({ status: 'all' });
      
      if (response.code === 200) {
        // 转换API数据格式为页面需要的格式
        const alarmHistory = response.data.alarms.map(alarm => ({
          id: alarm.id,
          type: this.mapAlarmTypeToParameter(alarm.type),
          value: alarm.currentValue,
          threshold: alarm.thresholdValue,
          level: alarm.level,
          message: alarm.title,
          timestamp: alarm.createdAt,
          status: alarm.status === 'resolved' ? 'handled' : 'pending'
        }));
        
        this.setData({
          alarmHistory: alarmHistory
        });
      }
    } catch (error) {
      console.error('加载预警历史失败:', error);
    }
  },

  mapAlarmTypeToParameter(apiType) {
    const typeMap = {
      'soil_moisture': 'soilMoisture',
      'soil_temperature': 'soilTemperature',
      'ph_abnormal': 'phValue',
      'ec_abnormal': 'ecValue',
      'nitrogen': 'nitrogen',
      'phosphorus': 'phosphorus',
      'potassium': 'potassium'
    };
    return typeMap[apiType] || 'soilMoisture';
  },

  updateDisplayData() {
    // 处理显示数据，避免在WXML中使用复杂表达式
    const { alarmSettings, levelOptions, parameterInfo } = this.data;
    const displayData = {};
    const parameterKeys = Object.keys(alarmSettings);
    
    parameterKeys.forEach(paramKey => {
      const setting = alarmSettings[paramKey];
      const levelOption = levelOptions.find(item => item.value === setting.level);
      
      displayData[paramKey] = {
        ...setting,
        levelLabel: levelOption ? levelOption.label : '未知级别',
        paramName: parameterInfo[paramKey].name,
        paramIcon: parameterInfo[paramKey].icon,
        paramDesc: parameterInfo[paramKey].description,
        paramUnit: parameterInfo[paramKey].unit
      };
    });
    
    this.setData({
      displayData: displayData,
      parameterKeys: parameterKeys
    });
  },

  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  toggleParameter(e) {
    const parameter = e.currentTarget.dataset.parameter;
    const enabled = e.detail.value;
    
    this.setData({
      [`alarmSettings.${parameter}.enabled`]: enabled
    });
    
    this.updateDisplayData();
    this.saveSettings();
  },

  editParameter(e) {
    const parameter = e.currentTarget.dataset.parameter;
    const settings = this.data.alarmSettings[parameter];
    
    this.setData({
      editingParameter: parameter,
      tempSettings: { ...settings }
    });
  },

  cancelEdit() {
    this.setData({
      editingParameter: null,
      tempSettings: {}
    });
  },

  confirmEdit() {
    const { editingParameter, tempSettings, alarmSettings } = this.data;
    
    // 验证参数
    if (tempSettings.min >= tempSettings.max) {
      wx.showToast({
        title: '最小值必须小于最大值',
        icon: 'none'
      });
      return;
    }
    
    // 更新设置
    alarmSettings[editingParameter] = { ...tempSettings };
    
    this.setData({
      alarmSettings: { ...alarmSettings },
      editingParameter: null,
      tempSettings: {}
    });
    
    this.updateDisplayData();
    this.saveSettings();
    
    wx.showToast({
      title: '设置已保存',
      icon: 'success'
    });
  },

  onMinInput(e) {
    const value = parseFloat(e.detail.value) || 0;
    this.setData({
      'tempSettings.min': value
    });
  },

  onMaxInput(e) {
    const value = parseFloat(e.detail.value) || 0;
    this.setData({
      'tempSettings.max': value
    });
  },

  selectLevel(e) {
    const level = e.currentTarget.dataset.level;
    this.setData({
      'tempSettings.level': level
    });
  },

  toggleNotification(e) {
    const type = e.currentTarget.dataset.type;
    const enabled = e.detail.value;
    
    this.setData({
      [`notificationSettings.${type}`]: enabled
    });
    
    this.saveNotificationSettings();
  },

  saveSettings() {
    wx.setStorageSync('alarmSettings', this.data.alarmSettings);
  },

  saveNotificationSettings() {
    wx.setStorageSync('notificationSettings', this.data.notificationSettings);
    
    wx.showToast({
      title: '通知设置已保存',
      icon: 'success'
    });
  },

  handleAlarm(e) {
    const alarmId = e.currentTarget.dataset.id;
    const { alarmHistory } = this.data;
    
    const alarmIndex = alarmHistory.findIndex(alarm => alarm.id === alarmId);
    if (alarmIndex > -1) {
      alarmHistory[alarmIndex].status = 'handled';
      
      this.setData({
        alarmHistory: [...alarmHistory]
      });
      
      wx.showToast({
        title: '预警已处理',
        icon: 'success'
      });
    }
  },

  viewAlarmDetail(e) {
    const alarmId = e.currentTarget.dataset.id;
    const alarm = this.data.alarmHistory.find(item => item.id === alarmId);
    
    if (alarm) {
      const paramInfo = this.data.parameterInfo[alarm.type];
      wx.showModal({
        title: '预警详情',
        content: `参数：${paramInfo.name}\n当前值：${alarm.value}${paramInfo.unit}\n阈值：${alarm.threshold}${paramInfo.unit}\n时间：${alarm.timestamp}\n状态：${alarm.status === 'handled' ? '已处理' : '待处理'}`,
        showCancel: false
      });
    }
  },

  testAlarm() {
    wx.showLoading({
      title: '发送测试预警...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      
      // 添加测试预警到历史记录
      const testAlarm = {
        id: Date.now(),
        type: 'soilMoisture',
        value: 25,
        threshold: 30,
        level: 'medium',
        message: '测试预警：土壤湿度过低',
        timestamp: new Date().toLocaleString(),
        status: 'pending'
      };
      
      const { alarmHistory } = this.data;
      alarmHistory.unshift(testAlarm);
      
      this.setData({
        alarmHistory: [...alarmHistory]
      });
      
      wx.showToast({
        title: '测试预警已发送',
        icon: 'success'
      });
    }, 2000);
  },

  resetSettings() {
    wx.showModal({
      title: '重置设置',
      content: '确定要恢复默认预警设置吗？',
      success: (res) => {
        if (res.confirm) {
          // 恢复默认设置
          this.setData({
            alarmSettings: {
              soilMoisture: { enabled: true, min: 30, max: 80, level: 'medium' },
              soilTemperature: { enabled: true, min: 15, max: 35, level: 'medium' },
              phValue: { enabled: true, min: 6.0, max: 7.5, level: 'high' },
              ecValue: { enabled: true, min: 500, max: 2000, level: 'medium' },
              nitrogen: { enabled: false, min: 20, max: 100, level: 'low' },
              phosphorus: { enabled: false, min: 10, max: 50, level: 'low' },
              potassium: { enabled: false, min: 15, max: 80, level: 'low' }
            }
          });
          
          this.updateDisplayData();
          this.saveSettings();
          
          wx.showToast({
            title: '已恢复默认设置',
            icon: 'success'
          });
        }
      }
    });
  },

  clearHistory() {
    wx.showModal({
      title: '清空历史',
      content: '确定要清空所有预警历史记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            alarmHistory: []
          });
          
          wx.showToast({
            title: '历史记录已清空',
            icon: 'success'
          });
        }
      }
    });
  }
})