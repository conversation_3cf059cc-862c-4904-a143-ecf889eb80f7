package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 地块实体类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("plots")
public class Plot {

    /**
     * 地块ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 农场ID
     */
    @TableField("farm_id")
    private Long farmId;

    /**
     * 地块名称
     */
    @TableField("name")
    private String name;

    /**
     * 地块描述
     */
    @TableField("description")
    private String description;

    /**
     * 地块面积(亩)
     */
    @TableField("area")
    private BigDecimal area;

    /**
     * 作物类型
     */
    @TableField("crop_type")
    private String cropType;

    /**
     * 种植日期
     */
    @TableField("planting_date")
    private LocalDate plantingDate;

    /**
     * 预期收获日期
     */
    @TableField("expected_harvest_date")
    private LocalDate expectedHarvestDate;

    /**
     * 土壤类型
     */
    @TableField("soil_type")
    private String soilType;

    /**
     * 地块坐标(GeoJSON格式)
     */
    @TableField("coordinates")
    private String coordinates;

    /**
     * 状态: 0-休耕, 1-种植中, 2-收获
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 关联的农场信息 (多对一)
     */
    @TableField(exist = false)
    private Farm farm;

    /**
     * 关联的设备列表 (一对多)
     */
    @TableField(exist = false)
    private List<Device> devices;

    /**
     * 地块设备统计信息
     */
    @TableField(exist = false)
    private PlotDeviceStats plotDeviceStats;

    /**
     * 地块设备统计信息内部类
     */
    @Data
    public static class PlotDeviceStats {
        /**
         * 设备总数
         */
        private Integer deviceCount;

        /**
         * 在线设备数
         */
        private Integer onlineDeviceCount;

        /**
         * 灌溉设备数
         */
        private Integer irrigationCount;

        /**
         * 施肥设备数
         */
        private Integer fertilizerCount;

        /**
         * 传感器设备数
         */
        private Integer sensorCount;

        /**
         * 设备在线率
         */
        private BigDecimal onlineRate;
    }

    /**
     * 地块统计信息内部类
     */
    @Data
    public static class Statistics {
        /**
         * 地块面积
         */
        private BigDecimal area;

        /**
         * 设备总数
         */
        private Integer deviceCount;

        /**
         * 在线设备数
         */
        private Integer onlineDeviceCount;

        /**
         * 传感器数量
         */
        private Integer sensorCount;

        /**
         * 本月灌溉次数
         */
        private Integer irrigationCount;

        /**
         * 本月施肥次数
         */
        private Integer fertilizerCount;

        /**
         * 本月报警次数
         */
        private Integer alarmCount;

        /**
         * 当前土壤湿度
         */
        private BigDecimal soilHumidity;

        /**
         * 当前土壤温度
         */
        private BigDecimal soilTemperature;

        /**
         * 当前pH值
         */
        private BigDecimal phValue;

        /**
         * 种植天数
         */
        private Integer plantingDays;

        /**
         * 预计收获天数
         */
        private Integer expectedHarvestDays;
    }

    /**
     * 作物信息内部类
     */
    @Data
    public static class CropInfo {
        /**
         * 作物类型
         */
        private String cropType;

        /**
         * 种植日期
         */
        private LocalDate plantingDate;

        /**
         * 预期收获日期
         */
        private LocalDate expectedHarvestDate;

        /**
         * 生长阶段
         */
        private String growthStage;

        /**
         * 生长进度(%)
         */
        private Integer progress;

        /**
         * 种植面积
         */
        private BigDecimal plantingArea;

        /**
         * 种植密度
         */
        private String plantingDensity;

        /**
         * 品种信息
         */
        private String variety;

        /**
         * 生长状态
         */
        private String growthStatus;

        /**
         * 备注
         */
        private String notes;
    }
}