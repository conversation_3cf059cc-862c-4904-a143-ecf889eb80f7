/* pages/monitor/alarm/alarm.wxss */

.page-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 标签栏 */
.tab-bar {
  display: flex;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 32rpx 16rpx;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #2E7D32;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #2E7D32;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 设置内容 */
.settings-content {
  padding: 20rpx;
}

.parameters-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.parameter-item {
  padding: 32rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.parameter-item:last-child {
  border-bottom: none;
}

.parameter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.parameter-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.parameter-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.parameter-details {
  flex: 1;
}

.parameter-name {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
}

.parameter-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.parameter-switch {
  margin-left: 20rpx;
}

.parameter-settings {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}

.threshold-display {
  display: flex;
  gap: 24rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.threshold-item {
  flex: 1;
  min-width: 120rpx;
  text-align: center;
}

.threshold-label {
  display: block;
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.threshold-value {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.level-badge {
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: #ffffff;
  display: inline-block;
}

.level-badge.level-low {
  background: #FFA726;
}

.level-badge.level-medium {
  background: #FF7043;
}

.level-badge.level-high {
  background: #E53935;
}

.settings-actions {
  display: flex;
  gap: 16rpx;
  padding: 20rpx 0;
}

/* 历史记录 */
.history-content {
  padding: 20rpx;
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #ffffff;
  border-radius: 16rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

.history-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.2s ease;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background: #f8f8f8;
}

.history-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.history-info {
  flex: 1;
  margin-right: 16rpx;
}

.history-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.history-detail {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 6rpx;
}

.history-time {
  font-size: 22rpx;
  color: #999999;
}

.history-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
}

.status-badge {
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-badge.handled {
  background: #e8f5e8;
  color: #2E7D32;
}

.history-actions {
  padding: 20rpx 0;
}

/* 通知设置 */
.notification-content {
  padding: 20rpx;
}

.notification-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.notification-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.notification-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 48rpx;
  text-align: center;
}

.notification-details {
  flex: 1;
}

.notification-name {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
}

.notification-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.notification-tip {
  background: #e8f5e8;
  border-radius: 16rpx;
  padding: 24rpx;
  border-left: 4rpx solid #2E7D32;
}

.tip-icon {
  font-size: 28rpx;
  margin-bottom: 12rpx;
}

.tip-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2E7D32;
  margin-bottom: 8rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #4a7c59;
  line-height: 1.4;
}

/* 编辑弹窗 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideInUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666666;
  transition: all 0.3s ease;
}

.modal-close:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #2E7D32;
}

.level-selector {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.level-option {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.level-option.selected {
  border-color: #2E7D32;
  background: rgba(46, 125, 50, 0.05);
}

.level-option:active {
  background: #f8f8f8;
}

.level-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
}

.level-label {
  font-size: 28rpx;
  color: #333333;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 按钮样式 */
.btn {
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.btn::after {
  border: none;
}

.btn-primary {
  flex: 1;
  background: #2E7D32;
  color: #ffffff;
  padding: 24rpx;
}

.btn-primary:active {
  background: #1B5E20;
  transform: scale(0.98);
}

.btn-secondary {
  flex: 1;
  background: #f5f5f5;
  color: #666666;
  padding: 24rpx;
}

.btn-secondary:active {
  background: #e0e0e0;
  transform: scale(0.98);
}

.btn-outline {
  flex: 1;
  background: transparent;
  border: 2rpx solid #2E7D32;
  color: #2E7D32;
  padding: 22rpx;
}

.btn-outline:active {
  background: rgba(46, 125, 50, 0.1);
  transform: scale(0.98);
}

.btn-small {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式适配 */
@media screen and (max-width: 400px) {
  .threshold-display {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .settings-actions,
  .history-actions {
    flex-direction: column;
  }
  
  .modal-content {
    margin: 20rpx;
  }
}