package com.agriculture.iot;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 智慧农业物联网系统启动类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@SpringBootApplication
@EnableScheduling
@EnableAsync
@EnableTransactionManagement
@MapperScan("com.agriculture.iot.mapper")
public class AgricultureIotApplication {

    public static void main(String[] args) {
        SpringApplication.run(AgricultureIotApplication.class, args);
        System.out.println("智慧农业物联网系统启动成功！");
    }
}