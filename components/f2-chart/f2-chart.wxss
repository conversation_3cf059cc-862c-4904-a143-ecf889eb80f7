/* F2 Chart Component Styles */
@import "../../libs/iconfont/iconfont.wxss";

.f2-chart-container {
  width: 100%;
  background: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.chart-actions {
  display: flex;
  gap: 16rpx;
}

.chart-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: transparent;
  border: 1rpx solid #2E7D32;
  border-radius: 8rpx;
  color: #2E7D32;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.chart-action:active {
  background: #2E7D32;
  color: #ffffff;
}

.chart-action .iconfont {
  font-size: 24rpx;
}

.chart-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 8rpx;
  overflow: hidden;
}

.chart-canvas {
  display: block;
  width: 100%;
  height: 100%;
}

.chart-loading,
.chart-empty,
.chart-disabled {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  color: #999;
}

.chart-loading .iconfont {
  font-size: 48rpx;
  animation: spin 1s linear infinite;
}

.chart-empty .iconfont,
.chart-disabled .iconfont {
  font-size: 64rpx;
}

.chart-loading text,
.chart-empty text,
.chart-disabled text {
  font-size: 24rpx;
  color: #999;
}

.chart-description {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
  font-size: 22rpx;
  color: #666;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }
  
  .chart-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .chart-action {
    flex: 1;
    justify-content: center;
  }
}

/* 动画效果 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.chart-wrapper {
  transition: all 0.3s ease;
}

.chart-wrapper:hover {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 图表主题颜色 */
.chart-theme-primary {
  --chart-primary-color: #2E7D32;
  --chart-secondary-color: #4CAF50;
}

.chart-theme-blue {
  --chart-primary-color: #1890FF;
  --chart-secondary-color: #40A9FF;
}

.chart-theme-green {
  --chart-primary-color: #52C41A;
  --chart-secondary-color: #73D13D;
}

.chart-theme-orange {
  --chart-primary-color: #FAAD14;
  --chart-secondary-color: #FFC53D;
}

.chart-theme-red {
  --chart-primary-color: #F5222D;
  --chart-secondary-color: #FF4D4F;
}

.chart-theme-purple {
  --chart-primary-color: #722ED1;
  --chart-secondary-color: #9254DE;
}