<!--作物管理页面-->
<view class="page-container">
  
  <!-- 标签栏 -->
  <view class="tab-bar">
    <view 
      class="tab-item {{currentTab === 'archive' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="archive"
    >
      <text class="tab-text">作物档案</text>
    </view>
    <view 
      class="tab-item {{currentTab === 'records' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="records"
    >
      <text class="tab-text">生长记录</text>
    </view>
    <view 
      class="tab-item {{currentTab === 'harvest' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="harvest"
    >
      <text class="tab-text">收获管理</text>
    </view>
  </view>

  <!-- 作物档案 -->
  <view wx:if="{{currentTab === 'archive'}}" class="archive-content">
    
    <!-- 搜索和筛选 -->
    <view class="search-filter-bar">
      <view class="search-box">
        <view class="search-icon">🔍</view>
        <input 
          class="search-input"
          placeholder="搜索作物名称..."
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
        />
      </view>
      
      <view class="filter-buttons">
        <view 
          class="filter-btn {{selectedPlot === 'all' ? 'active' : ''}}"
          bindtap="filterByPlot"
          data-plot="all"
        >
          全部地块
        </view>
        <view 
          class="filter-btn {{selectedStatus === 'all' ? 'active' : ''}}"
          bindtap="filterByStatus"
          data-status="all"
        >
          全部状态
        </view>
      </view>
    </view>

    <!-- 作物列表 -->
    <view class="crop-list">
      <view wx:for="{{cropArchives}}" wx:key="id" class="crop-card">
        
        <!-- 作物图片占位 -->
        <view class="crop-image">
          <text class="crop-emoji">🌱</text>
        </view>
        
        <!-- 作物信息 -->
        <view class="crop-info">
          <view class="crop-header">
            <text class="crop-name">{{item.name}}</text>
            <view class="crop-status status-{{item.status}}">
              <text wx:if="{{item.status === 'growing'}}">生长中</text>
              <text wx:elif="{{item.status === 'harvesting'}}">收获中</text>
              <text wx:elif="{{item.status === 'completed'}}">已完成</text>
              <text wx:else>种植失败</text>
            </view>
          </view>
          
          <text class="crop-variety">品种：{{item.variety}}</text>
          <text class="crop-plot">地块：{{item.plotName}}</text>
          <text class="crop-date">种植：{{item.plantDate}}</text>
          
          <view class="crop-stage">
            <text class="stage-label">当前阶段：</text>
            <view class="stage-badge stage-{{item.currentStage}}">
              <text wx:if="{{item.currentStage === 'seeding'}}">播种期</text>
              <text wx:elif="{{item.currentStage === 'germination'}}">发芽期</text>
              <text wx:elif="{{item.currentStage === 'growth'}}">生长期</text>
              <text wx:elif="{{item.currentStage === 'flowering'}}">开花期</text>
              <text wx:elif="{{item.currentStage === 'fruiting'}}">结果期</text>
              <text wx:elif="{{item.currentStage === 'harvest'}}">收获期</text>
              <text wx:else>完成</text>
            </view>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="crop-actions">
          <button 
            class="action-btn stage-btn"
            bindtap="updateStage"
            data-id="{{item.id}}"
          >
            更新阶段
          </button>
          <button 
            class="action-btn edit-btn"
            bindtap="editCrop"
            data-id="{{item.id}}"
          >
            编辑
          </button>
          <button 
            class="action-btn delete-btn"
            bindtap="deleteRecord"
            data-id="{{item.id}}"
            data-type="crop"
          >
            删除
          </button>
        </view>
      </view>
    </view>
    
    <!-- 添加按钮 -->
    <view class="add-button" bindtap="showAddCropForm">
      <text class="add-icon">+</text>
      <text class="add-text">添加作物</text>
    </view>
  </view>

  <!-- 生长记录 -->
  <view wx:if="{{currentTab === 'records'}}" class="records-content">
    
    <!-- 记录列表 -->
    <view class="record-list">
      <view wx:for="{{growthRecords}}" wx:key="id" class="record-card">
        <view class="record-header">
          <view class="record-type">
            <text class="type-icon">
              <text wx:if="{{item.type === 'watering'}}">💧</text>
              <text wx:elif="{{item.type === 'fertilizing'}}">🌱</text>
              <text wx:elif="{{item.type === 'pest_control'}}">🐛</text>
              <text wx:elif="{{item.type === 'pruning'}}">✂️</text>
              <text wx:elif="{{item.type === 'harvest'}}">🌾</text>
              <text wx:else>📝</text>
            </text>
            <view class="type-info">
              <text class="type-name">
                <text wx:if="{{item.type === 'watering'}}">浇水</text>
                <text wx:elif="{{item.type === 'fertilizing'}}">施肥</text>
                <text wx:elif="{{item.type === 'pest_control'}}">病虫害防治</text>
                <text wx:elif="{{item.type === 'pruning'}}">修剪</text>
                <text wx:elif="{{item.type === 'harvest'}}">收获</text>
                <text wx:else>其他</text>
              </text>
              <text class="crop-name">{{item.cropName}}</text>
            </view>
          </view>
          <text class="record-date">{{item.date}}</text>
        </view>
        
        <view class="record-content">
          <text class="content-text">{{item.content}}</text>
          <view wx:if="{{item.notes}}" class="record-notes">
            <text class="notes-text">备注：{{item.notes}}</text>
          </view>
        </view>
        
        <view class="record-meta">
          <text class="weather">天气：{{item.weather}}</text>
          <text class="temperature">温度：{{item.temperature}}°C</text>
          <text class="operator">操作员：{{item.operator}}</text>
        </view>
        
        <view class="record-actions">
          <button 
            class="action-btn delete-btn"
            bindtap="deleteRecord"
            data-id="{{item.id}}"
            data-type="growth"
          >
            删除
          </button>
        </view>
      </view>
    </view>
    
    <!-- 添加按钮 -->
    <view class="add-button" bindtap="showAddRecordForm">
      <text class="add-icon">+</text>
      <text class="add-text">添加记录</text>
    </view>
  </view>

  <!-- 收获管理 -->
  <view wx:if="{{currentTab === 'harvest'}}" class="harvest-content">
    
    <!-- 收获统计 -->
    <view class="harvest-summary">
      <view class="summary-item">
        <text class="summary-value">28.3</text>
        <text class="summary-label">总产量(kg)</text>
      </view>
      <view class="summary-item">
        <text class="summary-value">707.5</text>
        <text class="summary-label">总收入(元)</text>
      </view>
      <view class="summary-item">
        <text class="summary-value">25.0</text>
        <text class="summary-label">平均价格(元/kg)</text>
      </view>
    </view>
    
    <!-- 收获记录列表 -->
    <view class="harvest-list">
      <view wx:for="{{harvestRecords}}" wx:key="id" class="harvest-card">
        <view class="harvest-header">
          <view class="harvest-crop">
            <text class="crop-icon">🍓</text>
            <text class="crop-name">{{item.cropName}}</text>
          </view>
          <text class="harvest-date">{{item.date}}</text>
        </view>
        
        <view class="harvest-details">
          <view class="detail-row">
            <text class="detail-label">产量：</text>
            <text class="detail-value">{{item.quantity}}{{item.unit}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">品质：</text>
            <view class="quality-badge quality-{{item.quality}}">
              <text wx:if="{{item.quality === 'A'}}">A级（优）</text>
              <text wx:elif="{{item.quality === 'B'}}">B级（良）</text>
              <text wx:else>C级（中）</text>
            </view>
          </view>
          <view class="detail-row">
            <text class="detail-label">价格：</text>
            <text class="detail-value">{{item.price}}元/{{item.unit}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">收入：</text>
            <text class="detail-value income">{{item.income}}元</text>
          </view>
        </view>
        
        <view wx:if="{{item.notes}}" class="harvest-notes">
          <text class="notes-text">{{item.notes}}</text>
        </view>
        
        <view class="harvest-actions">
          <button 
            class="action-btn delete-btn"
            bindtap="deleteRecord"
            data-id="{{item.id}}"
            data-type="harvest"
          >
            删除
          </button>
        </view>
      </view>
    </view>
    
    <!-- 添加按钮 -->
    <view class="add-button" bindtap="showAddHarvestForm">
      <text class="add-icon">+</text>
      <text class="add-text">添加收获记录</text>
    </view>
  </view>

  <!-- 添加作物表单弹窗 -->
  <view wx:if="{{showAddForm}}" class="modal">
    <view class="modal-backdrop" bindtap="closeForm"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">{{editingItem ? '编辑作物' : '添加作物'}}</text>
        <view class="modal-close" bindtap="closeForm">×</view>
      </view>
      
      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">作物名称 *</text>
          <input 
            class="form-input"
            placeholder="请输入作物名称"
            value="{{formData.name}}"
            bindinput="onFormInput"
            data-field="name"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">品种 *</text>
          <input 
            class="form-input"
            placeholder="请输入品种"
            value="{{formData.variety}}"
            bindinput="onFormInput"
            data-field="variety"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">地块名称 *</text>
          <input 
            class="form-input"
            placeholder="请输入地块名称"
            value="{{formData.plotName}}"
            bindinput="onFormInput"
            data-field="plotName"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">种植日期</text>
          <picker 
            mode="date"
            value="{{formData.plantDate}}"
            bindchange="onFormInput"
            data-field="plantDate"
          >
            <view class="picker-content">
              <text class="picker-text">{{formData.plantDate || '选择日期'}}</text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">种植面积(亩)</text>
          <input 
            class="form-input"
            placeholder="请输入种植面积"
            type="digit"
            value="{{formData.area}}"
            bindinput="onFormInput"
            data-field="area"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">种子用量</text>
          <input 
            class="form-input"
            placeholder="请输入种子用量"
            value="{{formData.seedAmount}}"
            bindinput="onFormInput"
            data-field="seedAmount"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">描述</text>
          <textarea 
            class="form-textarea"
            placeholder="请输入作物描述..."
            value="{{formData.description}}"
            bindinput="onFormInput"
            data-field="description"
          ></textarea>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="btn btn-secondary" bindtap="closeForm">取消</button>
        <button class="btn btn-primary" bindtap="saveCrop">保存</button>
      </view>
    </view>
  </view>

  <!-- 添加生长记录表单弹窗 -->
  <view wx:if="{{showRecordForm}}" class="modal">
    <view class="modal-backdrop" bindtap="closeForm"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">添加生长记录</text>
        <view class="modal-close" bindtap="closeForm">×</view>
      </view>
      
      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">选择作物 *</text>
          <picker 
            range="{{cropArchives}}"
            range-key="name"
            bindchange="selectCrop"
          >
            <view class="picker-content">
              <text class="picker-text">{{formData.cropName || '选择作物'}}</text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">记录类型 *</text>
          <picker 
            range="{{recordTypes}}"
            range-key="label"
            bindchange="selectRecordType"
          >
            <view class="picker-content">
              <text class="picker-text">
                <text wx:if="{{formData.type === 'watering'}}">浇水</text>
                <text wx:elif="{{formData.type === 'fertilizing'}}">施肥</text>
                <text wx:elif="{{formData.type === 'pest_control'}}">病虫害防治</text>
                <text wx:elif="{{formData.type === 'pruning'}}">修剪</text>
                <text wx:elif="{{formData.type === 'harvest'}}">收获</text>
                <text wx:else>其他</text>
              </text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">日期</text>
          <picker 
            mode="date"
            value="{{formData.date}}"
            bindchange="onFormInput"
            data-field="date"
          >
            <view class="picker-content">
              <text class="picker-text">{{formData.date}}</text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">记录内容 *</text>
          <textarea 
            class="form-textarea"
            placeholder="请详细描述操作内容..."
            value="{{formData.content}}"
            bindinput="onFormInput"
            data-field="content"
          ></textarea>
        </view>
        
        <view class="form-group">
          <text class="form-label">天气</text>
          <input 
            class="form-input"
            placeholder="如：晴天、多云、雨天"
            value="{{formData.weather}}"
            bindinput="onFormInput"
            data-field="weather"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">备注</text>
          <textarea 
            class="form-textarea"
            placeholder="备注信息..."
            value="{{formData.notes}}"
            bindinput="onFormInput"
            data-field="notes"
          ></textarea>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="btn btn-secondary" bindtap="closeForm">取消</button>
        <button class="btn btn-primary" bindtap="saveRecord">保存</button>
      </view>
    </view>
  </view>

  <!-- 添加收获记录表单弹窗 -->
  <view wx:if="{{showHarvestForm}}" class="modal">
    <view class="modal-backdrop" bindtap="closeForm"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">添加收获记录</text>
        <view class="modal-close" bindtap="closeForm">×</view>
      </view>
      
      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">选择作物 *</text>
          <picker 
            range="{{cropArchives}}"
            range-key="name"
            bindchange="selectCrop"
          >
            <view class="picker-content">
              <text class="picker-text">{{formData.cropName || '选择作物'}}</text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">收获日期</text>
          <picker 
            mode="date"
            value="{{formData.date}}"
            bindchange="onFormInput"
            data-field="date"
          >
            <view class="picker-content">
              <text class="picker-text">{{formData.date}}</text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">产量 *</text>
          <input 
            class="form-input"
            placeholder="请输入产量"
            type="digit"
            value="{{formData.quantity}}"
            bindinput="onFormInput"
            data-field="quantity"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">单价(元/kg) *</text>
          <input 
            class="form-input"
            placeholder="请输入单价"
            type="digit"
            value="{{formData.price}}"
            bindinput="onFormInput"
            data-field="price"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">品质等级</text>
          <picker 
            range="{{qualityOptions}}"
            range-key="label"
            bindchange="selectQuality"
          >
            <view class="picker-content">
              <text class="picker-text">
                <text wx:if="{{formData.quality === 'A'}}">A级（优）</text>
                <text wx:elif="{{formData.quality === 'B'}}">B级（良）</text>
                <text wx:else>C级（中）</text>
              </text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">买家</text>
          <input 
            class="form-input"
            placeholder="请输入买家信息"
            value="{{formData.buyer}}"
            bindinput="onFormInput"
            data-field="buyer"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">备注</text>
          <textarea 
            class="form-textarea"
            placeholder="备注信息..."
            value="{{formData.notes}}"
            bindinput="onFormInput"
            data-field="notes"
          ></textarea>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="btn btn-secondary" bindtap="closeForm">取消</button>
        <button class="btn btn-primary" bindtap="saveHarvest">保存</button>
      </view>
    </view>
  </view>
</view>