-- 智慧农业物联网系统数据库表结构
-- MySQL 8.0+ 兼容
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci

-- 创建数据库
CREATE DATABASE IF NOT EXISTS agriculture_iot DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE agriculture_iot;

-- 1. 农场表 (farms)
CREATE TABLE farms (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '农场ID',
    name VARCHAR(100) NOT NULL COMMENT '农场名称',
    description TEXT COMMENT '农场描述',
    location VARCHAR(200) COMMENT '农场位置',
    latitude DECIMAL(10,8) COMMENT '纬度',
    longitude DECIMAL(11,8) COMMENT '经度',
    area DECIMAL(10,2) COMMENT '农场面积(亩)',
    owner_id BIGINT COMMENT '农场主ID',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_owner_id (owner_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='农场表';

-- 2. 地块表 (plots)
CREATE TABLE plots (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '地块ID',
    farm_id BIGINT NOT NULL COMMENT '农场ID',
    name VARCHAR(100) NOT NULL COMMENT '地块名称',
    description TEXT COMMENT '地块描述',
    area DECIMAL(10,2) COMMENT '地块面积(亩)',
    crop_type VARCHAR(50) COMMENT '作物类型',
    planting_date DATE COMMENT '种植日期',
    expected_harvest_date DATE COMMENT '预期收获日期',
    soil_type VARCHAR(50) COMMENT '土壤类型',
    coordinates JSON COMMENT '地块坐标(GeoJSON格式)',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-休耕, 1-种植中, 2-收获',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (farm_id) REFERENCES farms(id) ON DELETE CASCADE,
    INDEX idx_farm_id (farm_id),
    INDEX idx_crop_type (crop_type),
    INDEX idx_status (status),
    INDEX idx_planting_date (planting_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地块表';

-- 3. 设备类型表 (device_types)
CREATE TABLE device_types (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '设备类型ID',
    name VARCHAR(100) NOT NULL COMMENT '设备类型名称',
    category VARCHAR(50) NOT NULL COMMENT '设备分类: irrigation-灌溉, fertilizer-施肥, sensor-传感器',
    manufacturer VARCHAR(100) COMMENT '制造商',
    model VARCHAR(100) COMMENT '型号',
    specifications JSON COMMENT '设备规格参数',
    control_protocol VARCHAR(50) COMMENT '控制协议: mqtt, http, modbus',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备类型表';

-- 4. 设备表 (devices)
CREATE TABLE devices (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '设备ID',
    device_code VARCHAR(100) UNIQUE NOT NULL COMMENT '设备编码',
    name VARCHAR(100) NOT NULL COMMENT '设备名称',
    device_type_id BIGINT NOT NULL COMMENT '设备类型ID',
    plot_id BIGINT NOT NULL COMMENT '所属地块ID',
    location VARCHAR(200) COMMENT '设备位置描述',
    latitude DECIMAL(10,8) COMMENT '纬度',
    longitude DECIMAL(11,8) COMMENT '经度',
    install_date DATE COMMENT '安装日期',
    last_maintenance_date DATE COMMENT '最后维护日期',
    next_maintenance_date DATE COMMENT '下次维护日期',
    online_status TINYINT DEFAULT 0 COMMENT '在线状态: 0-离线, 1-在线',
    work_status TINYINT DEFAULT 0 COMMENT '工作状态: 0-停止, 1-运行, 2-故障',
    battery_level INT COMMENT '电池电量百分比',
    signal_strength INT COMMENT '信号强度',
    firmware_version VARCHAR(50) COMMENT '固件版本',
    config_params JSON COMMENT '设备配置参数',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_heartbeat TIMESTAMP COMMENT '最后心跳时间',
    FOREIGN KEY (device_type_id) REFERENCES device_types(id),
    FOREIGN KEY (plot_id) REFERENCES plots(id) ON DELETE CASCADE,
    INDEX idx_device_code (device_code),
    INDEX idx_device_type_id (device_type_id),
    INDEX idx_plot_id (plot_id),
    INDEX idx_online_status (online_status),
    INDEX idx_work_status (work_status),
    INDEX idx_last_heartbeat (last_heartbeat)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备表';

-- 5. 传感器类型表 (sensor_types)
CREATE TABLE sensor_types (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '传感器类型ID',
    name VARCHAR(100) NOT NULL COMMENT '传感器类型名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '传感器类型编码',
    unit VARCHAR(20) COMMENT '数据单位',
    data_type VARCHAR(20) DEFAULT 'DECIMAL' COMMENT '数据类型: DECIMAL, INTEGER, BOOLEAN, STRING',
    min_value DECIMAL(10,4) COMMENT '最小值',
    max_value DECIMAL(10,4) COMMENT '最大值',
    precision_digits INT DEFAULT 2 COMMENT '精度位数',
    description TEXT COMMENT '描述',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='传感器类型表';

-- 6. 传感器数据表 (sensor_data)
CREATE TABLE sensor_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '数据ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    sensor_type_id BIGINT NOT NULL COMMENT '传感器类型ID',
    value DECIMAL(10,4) NOT NULL COMMENT '传感器数值',
    unit VARCHAR(20) COMMENT '数据单位',
    quality TINYINT DEFAULT 1 COMMENT '数据质量: 0-异常, 1-正常, 2-校准中',
    collected_at TIMESTAMP NOT NULL COMMENT '采集时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (sensor_type_id) REFERENCES sensor_types(id),
    INDEX idx_device_sensor (device_id, sensor_type_id),
    INDEX idx_collected_at (collected_at),
    INDEX idx_device_collected (device_id, collected_at),
    INDEX idx_quality (quality)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='传感器数据表'
PARTITION BY RANGE (UNIX_TIMESTAMP(collected_at)) (
    PARTITION p202401 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01')),
    PARTITION p202403 VALUES LESS THAN (UNIX_TIMESTAMP('2024-04-01')),
    PARTITION p202404 VALUES LESS THAN (UNIX_TIMESTAMP('2024-05-01')),
    PARTITION p202405 VALUES LESS THAN (UNIX_TIMESTAMP('2024-06-01')),
    PARTITION p202406 VALUES LESS THAN (UNIX_TIMESTAMP('2024-07-01')),
    PARTITION p202407 VALUES LESS THAN (UNIX_TIMESTAMP('2024-08-01')),
    PARTITION p202408 VALUES LESS THAN (UNIX_TIMESTAMP('2024-09-01')),
    PARTITION p202409 VALUES LESS THAN (UNIX_TIMESTAMP('2024-10-01')),
    PARTITION p202410 VALUES LESS THAN (UNIX_TIMESTAMP('2024-11-01')),
    PARTITION p202411 VALUES LESS THAN (UNIX_TIMESTAMP('2024-12-01')),
    PARTITION p202412 VALUES LESS THAN (UNIX_TIMESTAMP('2025-01-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 7. 灌溉记录表 (irrigation_logs)
CREATE TABLE irrigation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '灌溉记录ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    plot_id BIGINT NOT NULL COMMENT '地块ID',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration INT COMMENT '持续时间(秒)',
    water_amount DECIMAL(10,2) COMMENT '用水量(升)',
    pressure DECIMAL(6,2) COMMENT '水压(bar)',
    flow_rate DECIMAL(8,2) COMMENT '流量(升/分钟)',
    trigger_type TINYINT NOT NULL COMMENT '触发类型: 1-手动, 2-定时, 3-传感器触发',
    trigger_user_id BIGINT COMMENT '触发用户ID',
    schedule_task_id BIGINT COMMENT '调度任务ID',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-异常, 1-正常, 2-中断',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (plot_id) REFERENCES plots(id) ON DELETE CASCADE,
    INDEX idx_device_id (device_id),
    INDEX idx_plot_id (plot_id),
    INDEX idx_start_time (start_time),
    INDEX idx_trigger_type (trigger_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='灌溉记录表';

-- 8. 施肥配方表 (fertilizer_formulas)
CREATE TABLE fertilizer_formulas (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配方ID',
    name VARCHAR(100) NOT NULL COMMENT '配方名称',
    crop_type VARCHAR(50) COMMENT '适用作物类型',
    growth_stage VARCHAR(50) COMMENT '生长阶段',
    nitrogen_ratio DECIMAL(5,2) COMMENT '氮含量比例',
    phosphorus_ratio DECIMAL(5,2) COMMENT '磷含量比例',
    potassium_ratio DECIMAL(5,2) COMMENT '钾含量比例',
    concentration DECIMAL(6,3) COMMENT '浓度(g/L)',
    ph_value DECIMAL(3,1) COMMENT 'pH值',
    ec_value DECIMAL(6,2) COMMENT 'EC值(mS/cm)',
    description TEXT COMMENT '配方描述',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_crop_type (crop_type),
    INDEX idx_growth_stage (growth_stage),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='施肥配方表';

-- 9. 施肥记录表 (fertilizer_logs)
CREATE TABLE fertilizer_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '施肥记录ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    plot_id BIGINT NOT NULL COMMENT '地块ID',
    formula_id BIGINT COMMENT '配方ID',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration INT COMMENT '持续时间(秒)',
    fertilizer_amount DECIMAL(10,2) COMMENT '施肥量(升)',
    concentration DECIMAL(6,3) COMMENT '实际浓度(g/L)',
    ph_value DECIMAL(3,1) COMMENT '实际pH值',
    ec_value DECIMAL(6,2) COMMENT '实际EC值(mS/cm)',
    trigger_type TINYINT NOT NULL COMMENT '触发类型: 1-手动, 2-定时, 3-传感器触发',
    trigger_user_id BIGINT COMMENT '触发用户ID',
    schedule_task_id BIGINT COMMENT '调度任务ID',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-异常, 1-正常, 2-中断',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (plot_id) REFERENCES plots(id) ON DELETE CASCADE,
    FOREIGN KEY (formula_id) REFERENCES fertilizer_formulas(id),
    INDEX idx_device_id (device_id),
    INDEX idx_plot_id (plot_id),
    INDEX idx_formula_id (formula_id),
    INDEX idx_start_time (start_time),
    INDEX idx_trigger_type (trigger_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='施肥记录表';

-- 10. 调度任务表 (schedule_tasks)
CREATE TABLE schedule_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    name VARCHAR(100) NOT NULL COMMENT '任务名称',
    task_type TINYINT NOT NULL COMMENT '任务类型: 1-灌溉, 2-施肥, 3-数据采集',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    plot_id BIGINT NOT NULL COMMENT '地块ID',
    cron_expression VARCHAR(100) NOT NULL COMMENT 'Cron表达式',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    duration INT COMMENT '执行时长(秒)',
    parameters JSON COMMENT '任务参数',
    conditions JSON COMMENT '执行条件',
    priority TINYINT DEFAULT 5 COMMENT '优先级: 1-最高, 5-普通, 10-最低',
    retry_count INT DEFAULT 3 COMMENT '重试次数',
    timeout_seconds INT DEFAULT 300 COMMENT '超时时间(秒)',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用, 2-暂停',
    last_execution_time TIMESTAMP COMMENT '最后执行时间',
    next_execution_time TIMESTAMP COMMENT '下次执行时间',
    execution_count INT DEFAULT 0 COMMENT '执行次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    failure_count INT DEFAULT 0 COMMENT '失败次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (plot_id) REFERENCES plots(id) ON DELETE CASCADE,
    INDEX idx_device_id (device_id),
    INDEX idx_plot_id (plot_id),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_next_execution_time (next_execution_time),
    INDEX idx_priority (priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='调度任务表';

-- 11. 任务执行记录表 (task_execution_logs)
CREATE TABLE task_execution_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '执行记录ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    execution_time TIMESTAMP NOT NULL COMMENT '执行时间',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration INT COMMENT '执行时长(毫秒)',
    status TINYINT NOT NULL COMMENT '执行状态: 0-失败, 1-成功, 2-超时, 3-取消',
    result_data JSON COMMENT '执行结果数据',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (task_id) REFERENCES schedule_tasks(id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id),
    INDEX idx_execution_time (execution_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务执行记录表';

-- 12. 报警规则表 (alarm_rules)
CREATE TABLE alarm_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '规则ID',
    name VARCHAR(100) NOT NULL COMMENT '规则名称',
    device_id BIGINT COMMENT '设备ID(为空表示全局规则)',
    plot_id BIGINT COMMENT '地块ID(为空表示全局规则)',
    sensor_type_id BIGINT NOT NULL COMMENT '传感器类型ID',
    condition_type TINYINT NOT NULL COMMENT '条件类型: 1-大于, 2-小于, 3-等于, 4-范围外, 5-范围内',
    threshold_min DECIMAL(10,4) COMMENT '阈值最小值',
    threshold_max DECIMAL(10,4) COMMENT '阈值最大值',
    severity TINYINT DEFAULT 2 COMMENT '严重级别: 1-严重, 2-警告, 3-信息',
    continuous_count INT DEFAULT 1 COMMENT '连续触发次数',
    silence_duration INT DEFAULT 300 COMMENT '静默时间(秒)',
    notification_methods JSON COMMENT '通知方式: ["sms", "email", "push"]',
    notification_users JSON COMMENT '通知用户ID列表',
    auto_actions JSON COMMENT '自动执行动作',
    description TEXT COMMENT '规则描述',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (plot_id) REFERENCES plots(id) ON DELETE CASCADE,
    FOREIGN KEY (sensor_type_id) REFERENCES sensor_types(id),
    INDEX idx_device_id (device_id),
    INDEX idx_plot_id (plot_id),
    INDEX idx_sensor_type_id (sensor_type_id),
    INDEX idx_status (status),
    INDEX idx_severity (severity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报警规则表';

-- 13. 报警记录表 (alarm_records)
CREATE TABLE alarm_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报警记录ID',
    rule_id BIGINT NOT NULL COMMENT '规则ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    sensor_type_id BIGINT NOT NULL COMMENT '传感器类型ID',
    trigger_value DECIMAL(10,4) NOT NULL COMMENT '触发值',
    threshold_min DECIMAL(10,4) COMMENT '阈值最小值',
    threshold_max DECIMAL(10,4) COMMENT '阈值最大值',
    severity TINYINT NOT NULL COMMENT '严重级别',
    message TEXT NOT NULL COMMENT '报警消息',
    status TINYINT DEFAULT 1 COMMENT '处理状态: 1-未处理, 2-处理中, 3-已处理, 4-已忽略',
    handle_user_id BIGINT COMMENT '处理用户ID',
    handle_time TIMESTAMP COMMENT '处理时间',
    handle_note TEXT COMMENT '处理备注',
    notification_sent TINYINT DEFAULT 0 COMMENT '是否已发送通知: 0-否, 1-是',
    auto_actions_executed TINYINT DEFAULT 0 COMMENT '是否已执行自动动作: 0-否, 1-是',
    triggered_at TIMESTAMP NOT NULL COMMENT '触发时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (rule_id) REFERENCES alarm_rules(id) ON DELETE CASCADE,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (sensor_type_id) REFERENCES sensor_types(id),
    INDEX idx_rule_id (rule_id),
    INDEX idx_device_id (device_id),
    INDEX idx_sensor_type_id (sensor_type_id),
    INDEX idx_status (status),
    INDEX idx_severity (severity),
    INDEX idx_triggered_at (triggered_at),
    INDEX idx_notification_sent (notification_sent)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报警记录表';

-- 14. 系统配置表 (system_configs)
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'STRING' COMMENT '配置类型: STRING, INTEGER, DECIMAL, BOOLEAN, JSON',
    category VARCHAR(50) COMMENT '配置分类',
    description TEXT COMMENT '配置描述',
    is_encrypted TINYINT DEFAULT 0 COMMENT '是否加密: 0-否, 1-是',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key),
    INDEX idx_category (category),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 15. 操作日志表 (operation_logs)
CREATE TABLE operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT COMMENT '操作用户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_module VARCHAR(50) NOT NULL COMMENT '操作模块',
    operation_desc TEXT COMMENT '操作描述',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_params TEXT COMMENT '请求参数',
    response_data TEXT COMMENT '响应数据',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    execution_time INT COMMENT '执行时间(毫秒)',
    status TINYINT DEFAULT 1 COMMENT '执行状态: 0-失败, 1-成功',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_module (operation_module),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 初始化传感器类型数据
INSERT INTO sensor_types (name, code, unit, data_type, min_value, max_value, precision_digits, description) VALUES
('土壤温度', 'SOIL_TEMP', '°C', 'DECIMAL', -20.00, 60.00, 2, '土壤温度传感器'),
('土壤湿度', 'SOIL_HUMIDITY', '%', 'DECIMAL', 0.00, 100.00, 2, '土壤湿度传感器'),
('空气温度', 'AIR_TEMP', '°C', 'DECIMAL', -40.00, 80.00, 2, '空气温度传感器'),
('空气湿度', 'AIR_HUMIDITY', '%', 'DECIMAL', 0.00, 100.00, 2, '空气湿度传感器'),
('光照强度', 'LIGHT_INTENSITY', 'lux', 'DECIMAL', 0.00, 100000.00, 0, '光照强度传感器'),
('土壤pH值', 'SOIL_PH', 'pH', 'DECIMAL', 0.00, 14.00, 2, '土壤pH值传感器'),
('土壤EC值', 'SOIL_EC', 'mS/cm', 'DECIMAL', 0.00, 20.00, 3, '土壤电导率传感器'),
('大气压力', 'ATMOSPHERIC_PRESSURE', 'hPa', 'DECIMAL', 800.00, 1200.00, 2, '大气压力传感器'),
('风速', 'WIND_SPEED', 'm/s', 'DECIMAL', 0.00, 50.00, 2, '风速传感器'),
('风向', 'WIND_DIRECTION', '°', 'INTEGER', 0, 360, 0, '风向传感器'),
('降雨量', 'RAINFALL', 'mm', 'DECIMAL', 0.00, 500.00, 2, '降雨量传感器'),
('紫外线指数', 'UV_INDEX', 'UV', 'DECIMAL', 0.00, 15.00, 1, '紫外线指数传感器');

-- 初始化设备类型数据
INSERT INTO device_types (name, category, manufacturer, model, specifications, control_protocol) VALUES
('滴灌控制器', 'irrigation', '智农科技', 'ZN-DG-001', '{"max_zones": 8, "max_pressure": 5, "flow_rate": "1-100L/min"}', 'mqtt'),
('喷灌控制器', 'irrigation', '智农科技', 'ZN-PG-001', '{"max_zones": 4, "max_pressure": 8, "coverage": "10-50m"}', 'mqtt'),
('施肥机', 'fertilizer', '智农科技', 'ZN-SF-001', '{"tank_capacity": 200, "mixing_ratio": "1:100-1:1000", "flow_rate": "0.5-20L/min"}', 'mqtt'),
('环境监测站', 'sensor', '智农科技', 'ZN-HJ-001', '{"sensors": ["temp", "humidity", "light", "pressure"], "power": "solar", "transmission": "4G"}', 'mqtt'),
('土壤监测器', 'sensor', '智农科技', 'ZN-TR-001', '{"sensors": ["soil_temp", "soil_humidity", "ph", "ec"], "depth": "10-30cm", "battery": "3000mAh"}', 'mqtt');

-- 初始化施肥配方数据
INSERT INTO fertilizer_formulas (name, crop_type, growth_stage, nitrogen_ratio, phosphorus_ratio, potassium_ratio, concentration, ph_value, ec_value, description) VALUES
('番茄苗期配方', '番茄', '苗期', 15.00, 10.00, 15.00, 1.200, 6.0, 1.8, '适用于番茄苗期生长的营养配方'),
('番茄开花期配方', '番茄', '开花期', 12.00, 15.00, 20.00, 1.500, 6.2, 2.0, '适用于番茄开花期的营养配方'),
('番茄结果期配方', '番茄', '结果期', 10.00, 12.00, 25.00, 1.800, 6.0, 2.2, '适用于番茄结果期的营养配方'),
('黄瓜苗期配方', '黄瓜', '苗期', 18.00, 8.00, 12.00, 1.000, 6.5, 1.6, '适用于黄瓜苗期生长的营养配方'),
('黄瓜开花期配方', '黄瓜', '开花期', 15.00, 12.00, 18.00, 1.300, 6.3, 1.9, '适用于黄瓜开花期的营养配方'),
('黄瓜结果期配方', '黄瓜', '结果期', 12.00, 10.00, 22.00, 1.600, 6.2, 2.1, '适用于黄瓜结果期的营养配方'),
('叶菜通用配方', '叶菜', '生长期', 20.00, 8.00, 15.00, 1.200, 6.8, 1.8, '适用于各种叶菜类蔬菜的通用配方'),
('果菜通用配方', '果菜', '生长期', 15.00, 12.00, 18.00, 1.400, 6.2, 2.0, '适用于各种果菜类蔬菜的通用配方');

-- 初始化系统配置数据
INSERT INTO system_configs (config_key, config_value, config_type, category, description) VALUES
('system.name', '智慧农业物联网系统', 'STRING', 'system', '系统名称'),
('system.version', '1.0.0', 'STRING', 'system', '系统版本'),
('data.retention.sensor_data', '90', 'INTEGER', 'data', '传感器数据保留天数'),
('data.retention.operation_logs', '30', 'INTEGER', 'data', '操作日志保留天数'),
('data.retention.alarm_records', '180', 'INTEGER', 'data', '报警记录保留天数'),
('notification.sms.enabled', 'true', 'BOOLEAN', 'notification', '是否启用短信通知'),
('notification.email.enabled', 'true', 'BOOLEAN', 'notification', '是否启用邮件通知'),
('notification.push.enabled', 'true', 'BOOLEAN', 'notification', '是否启用推送通知'),
('device.heartbeat.timeout', '300', 'INTEGER', 'device', '设备心跳超时时间(秒)'),
('device.offline.threshold', '600', 'INTEGER', 'device', '设备离线判定阈值(秒)'),
('irrigation.max_duration', '3600', 'INTEGER', 'irrigation', '单次灌溉最大持续时间(秒)'),
('fertilizer.max_duration', '1800', 'INTEGER', 'fertilizer', '单次施肥最大持续时间(秒)'),
('alarm.check.interval', '60', 'INTEGER', 'alarm', '报警检查间隔(秒)'),
('task.execution.timeout', '1800', 'INTEGER', 'task', '任务执行超时时间(秒)'),
('weather.api.key', '', 'STRING', 'weather', '天气API密钥'),
('weather.update.interval', '1800', 'INTEGER', 'weather', '天气数据更新间隔(秒)');

-- 创建视图：设备状态统计
CREATE VIEW v_device_status_stats AS
SELECT
    dt.category,
    dt.name as device_type_name,
    COUNT(*) as total_count,
    SUM(CASE WHEN d.online_status = 1 THEN 1 ELSE 0 END) as online_count,
    SUM(CASE WHEN d.online_status = 0 THEN 1 ELSE 0 END) as offline_count,
    SUM(CASE WHEN d.work_status = 1 THEN 1 ELSE 0 END) as working_count,
    SUM(CASE WHEN d.work_status = 2 THEN 1 ELSE 0 END) as fault_count,
    ROUND(SUM(CASE WHEN d.online_status = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as online_rate
FROM devices d
JOIN device_types dt ON d.device_type_id = dt.id
WHERE d.status = 1
GROUP BY dt.category, dt.name;

-- 创建视图：农场统计信息
CREATE VIEW v_farm_stats AS
SELECT
    f.id as farm_id,
    f.name as farm_name,
    f.area as farm_area,
    COUNT(DISTINCT p.id) as plot_count,
    SUM(p.area) as total_plot_area,
    COUNT(DISTINCT d.id) as device_count,
    SUM(CASE WHEN d.online_status = 1 THEN 1 ELSE 0 END) as online_device_count,
    COUNT(DISTINCT CASE WHEN dt.category = 'irrigation' THEN d.id END) as irrigation_device_count,
    COUNT(DISTINCT CASE WHEN dt.category = 'fertilizer' THEN d.id END) as fertilizer_device_count,
    COUNT(DISTINCT CASE WHEN dt.category = 'sensor' THEN d.id END) as sensor_device_count
FROM farms f
LEFT JOIN plots p ON f.id = p.farm_id AND p.status = 1
LEFT JOIN devices d ON p.id = d.plot_id AND d.status = 1
LEFT JOIN device_types dt ON d.device_type_id = dt.id
WHERE f.status = 1
GROUP BY f.id, f.name, f.area;

-- 创建视图：地块设备统计
CREATE VIEW v_plot_device_stats AS
SELECT
    p.id as plot_id,
    p.name as plot_name,
    p.farm_id,
    p.area as plot_area,
    p.crop_type,
    COUNT(d.id) as device_count,
    SUM(CASE WHEN d.online_status = 1 THEN 1 ELSE 0 END) as online_device_count,
    COUNT(CASE WHEN dt.category = 'irrigation' THEN d.id END) as irrigation_count,
    COUNT(CASE WHEN dt.category = 'fertilizer' THEN d.id END) as fertilizer_count,
    COUNT(CASE WHEN dt.category = 'sensor' THEN d.id END) as sensor_count
FROM plots p
LEFT JOIN devices d ON p.id = d.plot_id AND d.status = 1
LEFT JOIN device_types dt ON d.device_type_id = dt.id
WHERE p.status = 1
GROUP BY p.id, p.name, p.farm_id, p.area, p.crop_type;

-- 创建视图：最新传感器数据
CREATE VIEW v_latest_sensor_data AS
SELECT
    d.id as device_id,
    d.name as device_name,
    d.plot_id,
    st.id as sensor_type_id,
    st.name as sensor_type_name,
    st.code as sensor_type_code,
    st.unit,
    sd.value,
    sd.quality,
    sd.collected_at,
    ROW_NUMBER() OVER (PARTITION BY d.id, st.id ORDER BY sd.collected_at DESC) as rn
FROM devices d
JOIN device_types dt ON d.device_type_id = dt.id
JOIN sensor_data sd ON d.id = sd.device_id
JOIN sensor_types st ON sd.sensor_type_id = st.id
WHERE d.status = 1 AND dt.category = 'sensor'
AND sd.collected_at >= DATE_SUB(NOW(), INTERVAL 1 DAY);

-- 创建最新传感器数据的实际视图（只显示最新记录）
CREATE VIEW v_current_sensor_data AS
SELECT
    device_id,
    device_name,
    plot_id,
    sensor_type_id,
    sensor_type_name,
    sensor_type_code,
    unit,
    value,
    quality,
    collected_at
FROM v_latest_sensor_data
WHERE rn = 1;

-- 创建视图：活跃报警统计
CREATE VIEW v_active_alarm_stats AS
SELECT
    ar.severity,
    COUNT(*) as alarm_count,
    COUNT(CASE WHEN ar.status = 1 THEN 1 END) as unhandled_count,
    COUNT(CASE WHEN ar.status = 2 THEN 1 END) as handling_count,
    MIN(ar.triggered_at) as earliest_alarm,
    MAX(ar.triggered_at) as latest_alarm
FROM alarm_records ar
WHERE ar.status IN (1, 2) -- 未处理和处理中
GROUP BY ar.severity;

-- 创建存储过程：清理历史数据
DELIMITER //
CREATE PROCEDURE CleanHistoryData()
BEGIN
    DECLARE sensor_retention INT DEFAULT 90;
    DECLARE log_retention INT DEFAULT 30;
    DECLARE alarm_retention INT DEFAULT 180;
    
    -- 获取配置的数据保留天数
    SELECT CAST(config_value AS SIGNED) INTO sensor_retention
    FROM system_configs WHERE config_key = 'data.retention.sensor_data';
    
    SELECT CAST(config_value AS SIGNED) INTO log_retention
    FROM system_configs WHERE config_key = 'data.retention.operation_logs';
    
    SELECT CAST(config_value AS SIGNED) INTO alarm_retention
    FROM system_configs WHERE config_key = 'data.retention.alarm_records';
    
    -- 清理传感器数据
    DELETE FROM sensor_data
    WHERE collected_at < DATE_SUB(NOW(), INTERVAL sensor_retention DAY);
    
    -- 清理操作日志
    DELETE FROM operation_logs
    WHERE created_at < DATE_SUB(NOW(), INTERVAL log_retention DAY);
    
    -- 清理已处理的报警记录
    DELETE FROM alarm_records
    WHERE status IN (3, 4) -- 已处理、已忽略
    AND created_at < DATE_SUB(NOW(), INTERVAL alarm_retention DAY);
    
    -- 清理任务执行记录（保留最近30天）
    DELETE FROM task_execution_logs
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
END //
DELIMITER ;

-- 创建存储过程：设备状态更新
DELIMITER //
CREATE PROCEDURE UpdateDeviceStatus(
    IN p_device_id BIGINT,
    IN p_online_status TINYINT,
    IN p_work_status TINYINT,
    IN p_battery_level INT,
    IN p_signal_strength INT
)
BEGIN
    UPDATE devices
    SET
        online_status = p_online_status,
        work_status = p_work_status,
        battery_level = p_battery_level,
        signal_strength = p_signal_strength,
        last_heartbeat = NOW(),
        updated_at = NOW()
    WHERE id = p_device_id;
END //
DELIMITER ;

-- 创建存储过程：批量插入传感器数据
DELIMITER //
CREATE PROCEDURE BatchInsertSensorData(
    IN p_device_id BIGINT,
    IN p_sensor_data JSON
)
BEGIN
    DECLARE i INT DEFAULT 0;
    DECLARE data_count INT;
    DECLARE sensor_type_id BIGINT;
    DECLARE sensor_value DECIMAL(10,4);
    DECLARE sensor_unit VARCHAR(20);
    DECLARE collected_time TIMESTAMP;
    
    SET data_count = JSON_LENGTH(p_sensor_data);
    
    WHILE i < data_count DO
        SET sensor_type_id = JSON_UNQUOTE(JSON_EXTRACT(p_sensor_data, CONCAT('$[', i, '].sensor_type_id')));
        SET sensor_value = JSON_UNQUOTE(JSON_EXTRACT(p_sensor_data, CONCAT('$[', i, '].value')));
        SET sensor_unit = JSON_UNQUOTE(JSON_EXTRACT(p_sensor_data, CONCAT('$[', i, '].unit')));
        SET collected_time = JSON_UNQUOTE(JSON_EXTRACT(p_sensor_data, CONCAT('$[', i, '].collected_at')));
        
        INSERT INTO sensor_data (device_id, sensor_type_id, value, unit, collected_at)
        VALUES (p_device_id, sensor_type_id, sensor_value, sensor_unit, collected_time);
        
        SET i = i + 1;
    END WHILE;
END //
DELIMITER ;

-- 创建触发器：设备状态变更日志
DELIMITER //
CREATE TRIGGER tr_device_status_change
AFTER UPDATE ON devices
FOR EACH ROW
BEGIN
    IF OLD.online_status != NEW.online_status OR OLD.work_status != NEW.work_status THEN
        INSERT INTO operation_logs (
            operation_type,
            operation_module,
            operation_desc,
            created_at
        ) VALUES (
            'DEVICE_STATUS_CHANGE',
            'DEVICE',
            CONCAT('设备 ', NEW.name, ' 状态变更: 在线状态 ', OLD.online_status, '->', NEW.online_status,
                   ', 工作状态 ', OLD.work_status, '->', NEW.work_status),
            NOW()
        );
    END IF;
END //
DELIMITER ;

-- 创建触发器：报警记录创建时自动通知
DELIMITER //
CREATE TRIGGER tr_alarm_notification
AFTER INSERT ON alarm_records
FOR EACH ROW
BEGIN
    -- 这里可以添加自动通知逻辑，比如调用外部API或插入通知队列
    INSERT INTO operation_logs (
        operation_type,
        operation_module,
        operation_desc,
        created_at
    ) VALUES (
        'ALARM_TRIGGERED',
        'ALARM',
        CONCAT('报警触发: ', NEW.message, ', 严重级别: ', NEW.severity),
        NOW()
    );
END //
DELIMITER ;

-- 创建索引优化查询性能
CREATE INDEX idx_sensor_data_device_type_time ON sensor_data(device_id, sensor_type_id, collected_at DESC);
CREATE INDEX idx_irrigation_logs_plot_time ON irrigation_logs(plot_id, start_time DESC);
CREATE INDEX idx_fertilizer_logs_plot_time ON fertilizer_logs(plot_id, start_time DESC);
CREATE INDEX idx_alarm_records_device_time ON alarm_records(device_id, triggered_at DESC);
CREATE INDEX idx_task_execution_logs_task_time ON task_execution_logs(task_id, execution_time DESC);

-- 数据库初始化完成提示
SELECT '智慧农业物联网数据库初始化完成！' as message;