package com.agriculture.iot.controller;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.Schedule;
import com.agriculture.iot.service.ScheduleService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 调度管理控制器
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/schedules")
@RequiredArgsConstructor
public class ScheduleController {

    private final ScheduleService scheduleService;

    @GetMapping
    public Result<IPage<Schedule.Detail>> getSchedules(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long plotId,
            @RequestParam(required = false) Long deviceId) {
        
        IPage<Schedule.Detail> schedules = scheduleService.getSchedules(current, size, type, status, plotId, deviceId);
        return Result.success(schedules);
    }

    @PostMapping
    public Result<Schedule.Detail> createSchedule(@Valid @RequestBody Schedule.CreateRequest request) {
        return scheduleService.createSchedule(request);
    }

    @PutMapping("/{id}")
    public Result<Schedule.Detail> updateSchedule(
            @PathVariable Long id,
            @Valid @RequestBody Schedule.UpdateRequest request) {
        
        return scheduleService.updateSchedule(id, request);
    }

    @DeleteMapping("/{id}")
    public Result<Void> deleteSchedule(@PathVariable Long id) {
        return scheduleService.deleteSchedule(id);
    }

    @GetMapping("/{id}")
    public Result<Schedule.Detail> getScheduleDetail(@PathVariable Long id) {
        return scheduleService.getScheduleDetail(id);
    }

    @PutMapping("/{id}/toggle")
    public Result<Void> toggleSchedule(
            @PathVariable Long id,
            @RequestParam Boolean enabled) {
        
        return scheduleService.toggleSchedule(id, enabled);
    }

    @PostMapping("/{id}/execute")
    public Result<Schedule.ExecutionResult> executeSchedule(@PathVariable Long id) {
        return scheduleService.executeSchedule(id);
    }

    @PostMapping("/batch-execute")
    public Result<List<Schedule.ExecutionResult>> batchExecuteSchedules(@RequestBody List<Long> scheduleIds) {
        return scheduleService.batchExecuteSchedules(scheduleIds);
    }

    @GetMapping("/execution-records")
    public Result<IPage<Schedule.ExecutionRecord>> getExecutionRecords(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long scheduleId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime) {
        
        IPage<Schedule.ExecutionRecord> records = scheduleService.getExecutionRecords(
                current, size, scheduleId, status, startTime, endTime);
        return Result.success(records);
    }

    @GetMapping("/statistics")
    public Result<Schedule.Statistics> getScheduleStatistics(
            @RequestParam LocalDate startDate,
            @RequestParam LocalDate endDate,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) Long plotId) {
        
        return scheduleService.getScheduleStatistics(startDate, endDate, type, plotId);
    }

    @GetMapping("/today")
    public Result<List<Schedule.CalendarItem>> getTodaySchedules(
            @RequestParam(required = false) Long plotId,
            @RequestParam(required = false) String type) {
        
        return scheduleService.getTodaySchedules(plotId, type);
    }

    @GetMapping("/upcoming")
    public Result<List<Schedule.CalendarItem>> getUpcomingSchedules(
            @RequestParam(defaultValue = "24") Integer hours,
            @RequestParam(required = false) Long plotId,
            @RequestParam(required = false) String type) {
        
        return scheduleService.getUpcomingSchedules(hours, plotId, type);
    }

    @PostMapping("/{id}/pause")
    public Result<Void> pauseSchedule(@PathVariable Long id) {
        return scheduleService.pauseSchedule(id);
    }

    @PostMapping("/{id}/resume")
    public Result<Void> resumeSchedule(@PathVariable Long id) {
        return scheduleService.resumeSchedule(id);
    }

    @PostMapping("/{id}/complete")
    public Result<Void> completeSchedule(@PathVariable Long id) {
        return scheduleService.completeSchedule(id);
    }
}