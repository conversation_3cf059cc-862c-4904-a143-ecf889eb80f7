const app = getApp();
const api = require('../../utils/api.js');
const WeatherService = require('../../utils/weather-service.js');
const WeatherAnimations = require('../../utils/weather-animations.js');
//const lottie = require('../../lottie/lottie.js');
import lottie from 'lottie-miniprogram'

Page({
  data: {
    currentLocation: null,
    currentFarm: {},
    farmData: {
      name: '',
      location: '',
      totalArea: 0
    },
    weatherData: {
      temperature: 26,
      weather: '雷阵雨',
      icon: '⛈️',
      location: '平阴县',
      humidity: 68,
      windSpeed: 8,
      pressure: 1013,
      lastUpdate: new Date().toLocaleTimeString()
    },
    weatherAnimationData: null, // 天气动画数据
    isAnimationPlaying: false, // 动画播放状态
    irrigationStatus: {
      class: 'status-offline',
      text: '已停止',
      action: '启动'
    },
    sensorStatus: {
      class: 'status-online',
      text: '正常'
    },
    deviceSummary: {
      online: 3,
      total: 4,
      irrigation: {
        online: 2,
        total: 2
      },
      sensor: {
        online: 1,
        total: 2
      },
      abnormal: 1
    },
    todayData: {
      irrigationTime: 45,
      fertilizerAmount: 12.5,
      alarmCount: 2,
      avgHumidity: 63
    },
    envData: {
      humidity: 65,
      soilTemp: 23,
      ph: 6.8,
      ec: 1.2
    },
    todayTasks: [],
    notifications: [],
    unreadCount: 2,
    refreshing: false
  },

  onLoad: function (options) {
    // 检查登录状态
    if (!app.requireLogin()) {
      return; // 如果未登录，会自动跳转到登录页
    }
    
    this.getCurrentLocation();
    this.checkFarmInitialization();
    this.initData();
  },

  onReady: function() {
    // 初始化Lottie动画
    this.initLottieAnimation();
    // 初始化天气动画
    this.initWeatherAnimation();
    // 初始化动画实例存储
    this._lottieAnimation = null;
  },

  onShow: function () {
    // 检查登录状态
    if (!app.requireLogin()) {
      return; // 如果未登录，会自动跳转到登录页
    }
    
    this.loadData();
    // 每次显示页面时刷新位置（如果需要）
    this.refreshLocationIfNeeded();
  },

  onPullDownRefresh: function () {
    this.refreshData();
  },

  onReachBottom: function () {
    
  },

  // 获取当前位置
  getCurrentLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('位置获取成功:', res);
        
        this.setData({
          currentLocation: {
            latitude: res.latitude,
            longitude: res.longitude
          }
        });
        
        // 保存位置获取时间
        wx.setStorageSync('lastLocationTime', Date.now());
        
        // 获取天气数据（优先使用农场位置，其次使用当前位置）
        this.loadWeatherData();
        
        // 删除强制设置天气卡片地址为“当前位置”，让后续流程自动设置真实农场地址
        // this.setData({
        //   'weatherData.location': '当前位置'
        // });
      },
      fail: (error) => {
        console.error('获取位置失败:', error);
        
        // 位置获取失败的处理
        if (error.errMsg && error.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '需要位置权限',
            content: '为了获取准确的天气信息，请允许获取位置权限',
            confirmText: '去设置',
            cancelText: '使用默认',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              } else {
                this.useDefaultLocationAndWeather();
              }
            }
          });
        } else {
          wx.showToast({
            title: '位置获取失败，使用默认天气',
            icon: 'none',
            duration: 2000
          });
          this.useDefaultLocationAndWeather();
        }
      }
    });
  },

  // 使用默认位置和天气
  useDefaultLocationAndWeather: function() {
    // 使用山东济南平阴县坐标作为默认位置
    this.setData({
      currentLocation: {
        latitude: 36.2899,
        longitude: 116.4551
      },
      'weatherData.location': '山东省济南市平阴县'
    });
    // 加载天气数据
    this.loadWeatherData();
  },


  // 检查农场是否已初始化
  checkFarmInitialization: function () {
    // 使用API检查农场初始化状态
    api.getFarms().then(res => {
      if (res.code === 200 && res.data.farms && res.data.farms.length > 0) {
        // 农场已存在，标记为已初始化
        wx.setStorageSync('farmInitialized', true);
      } else {
        // 没有农场数据，需要初始化
        this.showFarmInitializationPrompt();
      }
    }).catch(err => {
      console.error('检查农场初始化状态失败:', err);
      // 失败时检查本地存储
      this.checkFarmInitializationFallback();
    });
  },

  // 显示农场初始化提示
  showFarmInitializationPrompt: function () {
    // 延迟显示初始化提示，避免与页面加载冲突
    setTimeout(() => {
      wx.showModal({
        title: '欢迎使用智慧农业系统',
        content: '检测到您是首次使用，需要先进行农场初始化设置。\n\n现在开始设置吗？',
        confirmText: '开始设置',
        cancelText: '稍后设置',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/farm/init/init'
            });
          } else {
            // 用户选择稍后设置，显示快速入口提示
            wx.showToast({
              title: '可在"地块"→"农场初始化"中设置',
              icon: 'none',
              duration: 3000
            });
          }
        }
      });
    }, 1000);
  },

  // 农场初始化检查的降级处理
  checkFarmInitializationFallback: function () {
    const farmInitialized = wx.getStorageSync('farmInitialized');
    const farmData = wx.getStorageSync('farmData');
    
    if (!farmInitialized || !farmData) {
      this.showFarmInitializationPrompt();
    }
  },

  initData: function () {
    // 使用API加载农场数据
    this.loadFarmData();
    // 加载天气数据
    this.loadWeatherData();
  },

  // 加载农场数据
  loadFarmData: function () {
    api.getFarms().then(res => {
      if (res.code === 200 && res.data.farms && res.data.farms.length > 0) {
        // 使用第一个农场作为当前农场
        const farm = res.data.farms[0];
        const currentFarm = {
          name: farm.name,
          area: farm.totalArea
        };
        
        const farmInfo = {
          name: farm.name,
          location: farm.location,
          totalArea: farm.totalArea,
          coordinates: farm.coordinates
        };
        
        this.setData({
          currentFarm: currentFarm,
          farmData: farmInfo
        });
        
        // 保存到全局数据
        app.globalData.currentFarm = currentFarm;
        
        // 如果农场有坐标，更新天气位置
        if (farm.coordinates) {
          this.setData({
            'weatherData.location': farm.location
          });
        }
      } else {
        // 没有农场数据，使用默认值
        this.setDefaultFarmData();
      }
    }).catch(err => {
      console.error('获取农场数据失败:', err);
      // 失败时尝试从本地存储获取，或使用默认值
      this.loadFarmDataFallback();
    });
  },

  // 设置默认农场数据
  setDefaultFarmData: function () {
    const defaultFarm = {
      name: '山东济南平阴县示例农场',
      area: 50
    };
    const defaultFarmInfo = {
      name: '平阴县示例农场',
      location: '山东省济南市平阴县',
      totalArea: 50
    };
    this.setData({
      currentFarm: defaultFarm,
      farmData: defaultFarmInfo
    });
  },

  // 农场数据加载失败的降级处理
  loadFarmDataFallback: function () {
    // 尝试从本地存储获取农场数据
    const farmData = wx.getStorageSync('farmData');
    
    if (farmData && farmData.farmInfo) {
      const currentFarm = {
        name: farmData.farmInfo.name,
        area: farmData.farmInfo.totalArea
      };
      const farmInfo = {
        name: farmData.farmInfo.name,
        location: farmData.farmInfo.location || '农场地址',
        totalArea: farmData.farmInfo.totalArea,
        coordinates: farmData.farmInfo.coordinates
      };
      
      this.setData({
        currentFarm: currentFarm,
        farmData: farmInfo
      });
    } else {
      this.setDefaultFarmData();
    }
  },

  loadWeatherData: function () {
    console.log('开始加载天气数据...');

    // 使用天气API获取当前天气
    api.getWeather().then(res => {
      const weatherData = res.data;
      console.log('天气数据:', weatherData);

      // 获取农业建议
      const advice = this.getAgricultureAdvice(weatherData);
      console.log(weatherData.location)
      // 更新天气数据
      this.setData({
        weatherData: {
          temperature: Math.round(weatherData.temperature),
          weather: weatherData.weather,
          icon: this.getWeatherIcon(weatherData.weather),
          humidity: weatherData.humidity,
          windSpeed: weatherData.windSpeed,
          pressure: weatherData.pressure,
          loading: false,
          // 优先显示API返回的地址
          location: weatherData.location || this.data.weatherData.location || '当前位置',
          advice: advice,
          lastUpdate: new Date().toLocaleTimeString()
        }
      });
      // 更新天气动画
      this.updateWeatherAnimation(weatherData.weather);
    }).catch(err => {
      console.error('获取天气数据失败:', err);
      // 降级使用原有逻辑
      this.loadWeatherDataFallback();
    });
  },
  
  // 天气数据加载降级方法
  loadWeatherDataFallback: function () {
    // 优先使用农场的位置信息
    const farmData = this.data.farmData;
    let targetLocation = null;
    
    // 如果农场有坐标信息，使用农场坐标
    if (farmData && farmData.coordinates) {
      targetLocation = {
        latitude: farmData.coordinates.latitude,
        longitude: farmData.coordinates.longitude
      };
      console.log('使用农场坐标获取天气:', targetLocation);
    }
    // 否则使用当前位置
    else if (this.data.currentLocation) {
      targetLocation = this.data.currentLocation;
      console.log('使用当前位置获取天气:', targetLocation);
    }
    
    // 如果有位置信息，调用和风天气API
    if (targetLocation) {
      this.getWeatherFromAPI(targetLocation);
    } 
    // 如果农场有地址信息但没有坐标，使用默认位置
    else if (farmData && farmData.location && farmData.location !== '农场地址') {
      console.log('农场有地址但无坐标，使用当前位置获取天气');
      this.setData({
        'weatherData.location': farmData.location
      });
      // 使用当前位置获取天气
      if (this.data.currentLocation) {
        this.getWeatherFromAPI(this.data.currentLocation);
      } else {
        this.useSimulatedWeatherData();
      }
    }
    // 最后降级到模拟数据
    else {
      console.log('无位置信息，使用模拟天气数据');
      this.useSimulatedWeatherData();
    }
  },


  // 从和风天气API获取天气数据
  getWeatherFromAPI: function(location) {
    const weatherService = new WeatherService();
    
    // 显示加载提示
    this.setData({
      'weatherData.loading': true
    });
    
    // 使用带降级的智能获取方法
    weatherService.getWeatherWithFallback(location)
      .then(data => {
        console.log('天气数据获取结果:', data);
        
        // 获取农业建议
        const advice = this.getAgricultureAdvice(data);
        
        // 无论是API数据还是模拟数据都更新UI
        this.setData({
          weatherData: {
            temperature: Math.round(data.temperature),
            weather: data.weather,
            icon: this.getWeatherIcon(data.weather),
            humidity: data.humidity,
            windLevel: data.windLevel || '微风',
            windDir: data.windDir || '无风',
            pressure: data.pressure,
            loading: false,
            // 优先显示API返回的地址
            location: data.location || this.data.weatherData.location,
            source: data.source, // 添加数据来源标识
            advice: advice // 添加农业建议
          }
        });
        
        // 更新天气动画
        this.updateWeatherAnimation(data.weather);
        
        // 显示数据来源提示
        if (data.source === 'mock') {
          wx.showToast({
            title: '使用模拟天气数据',
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch(err => {
        console.error('天气数据获取完全失败:', err);
        // 这种情况理论上不会发生，因为有降级机制
        this.useSimulatedWeatherData();
      });
  },

  // 获取农业建议
  getAgricultureAdvice: function(weatherData) {
    const weatherService = new WeatherService();
    const advices = weatherService.getAgricultureAdvice(weatherData);
    
    if (advices && advices.length > 0) {
      console.log('农业建议:', advices);
      // 返回第一条建议用于头部显示，移除emoji
      return advices[0].replace(/[🌡️💧🌵⚠️❄️✅☀️🌧️🌱]/g, '').trim();
    }
    
    return '当前天气条件良好';
  },

  // 使用模拟天气数据
  useSimulatedWeatherData: function() {
    console.log('使用模拟天气数据');
    
    const weatherService = new WeatherService();
    const mockData = weatherService.getMockWeatherData();
    
    // 获取农业建议
    const advice = this.getAgricultureAdvice(mockData);
    
    this.setData({
      weatherData: {
        temperature: mockData.temperature,
        weather: mockData.weather,
        icon: this.getWeatherIcon(mockData.weather),
        humidity: mockData.humidity,
        windLevel: `${mockData.windLevel}级`,
        windDir: mockData.windDir,
        pressure: mockData.pressure,
        loading: false,
        location: this.data.weatherData.location || '重庆江北',
        advice: advice
      }
    });
    
    // 更新天气动画
    this.updateWeatherAnimation(mockData.weather);
    
    // 显示模拟数据提示
    wx.showToast({
      title: '使用模拟天气数据',
      icon: 'none',
      duration: 2000
    });
  },

  // 获取天气图标
  getWeatherIcon: function(weather) {
    const iconMap = {
      '晴': '☀️',
      '多云': '⛅',
      '阴': '☁️',
      '小雨': '🌧️',
      '中雨': '🌧️',
      '大雨': '🌧️',
      '暴雨': '⛈️',
      '雷阵雨': '⛈️',
      '雪': '❄️',
      '雾': '🌫️',
      '霾': '😷'
    };
    
    return iconMap[weather] || '🌤️';
  },

  loadData: function () {
    this.loadDeviceStatus();
    this.loadEnvironmentData();
    this.loadTodayTasks();
    this.loadTodayData();
    this.loadNotifications();
  },

  refreshData: function () {
    this.setData({
      refreshing: true
    });
    
    this.loadData();
    
    setTimeout(() => {
      this.setData({
        refreshing: false
      });
      wx.stopPullDownRefresh();
    }, 1500);
  },


  loadDeviceStatus: function () {
    // 使用设备状态汇总API
    api.getDeviceStatus().then(res => {
      console.log('设备状态API响应:', res);
      
      if (res && res.code === 200 && res.data) {
        const deviceData = res.data;
        console.log('设备状态数据:', deviceData);
        
        // 处理设备汇总数据，确保数据结构存在
        const summary = deviceData.summary || deviceData || {};
        
        // 更新设备汇总信息
        this.setData({
          deviceSummary: {
            online: summary.online || 0,
            total: summary.total || 0,
            irrigation: {
              online: summary.online || 0,
              total: summary.total || 0
            },
            sensor: {
              online: summary.online || 0,
              total: summary.total || 0
            },
            abnormal: summary.offline || 0
          }
        });
        
        // 处理灌溉状态
        let irrigationStatus = {
          class: 'status-offline',
          text: '已停止',
          action: '启动'
        };
        
        if (summary.online > 0) {
          if (summary.working > 0) {
            irrigationStatus = {
              class: 'status-online',
              text: '运行中',
              action: '停止'
            };
          } else {
            irrigationStatus = {
              class: 'status-warning',
              text: '待机',
              action: '启动'
            };
          }
        }
        
        // 处理传感器状态
        let sensorStatus = {
          class: 'status-online',
          text: '正常'
        };
        
        if (summary.offline > 0) {
          sensorStatus = {
            class: 'status-offline',
            text: '离线'
          };
        }
        
        this.setData({
          irrigationStatus: irrigationStatus,
          sensorStatus: sensorStatus
        });
      }
    }).catch(err => {
      console.error('获取设备状态失败:', err);
      wx.showToast({
        title: '加载设备状态失败',
        icon: 'none'
      });
    });
  },

  loadEnvironmentData: function () {
    // 使用传感器最新数据API
    api.getLatestSensorData().then(res => {
      const sensorData = res.data;
      console.log('传感器数据:', sensorData);
      
      if (sensorData && sensorData.sensors && sensorData.sensors.length > 0) {
        // 处理多个传感器的数据，计算平均值
        const sensors = sensorData.sensors;
        const envData = {
          humidity: 0,
          soilTemp: 0,
          ph: 0,
          ec: 0
        };
        
        let validSensors = 0;
        let moistureCount = 0, tempCount = 0, phCount = 0, ecCount = 0;
        
        sensors.forEach(sensor => {
          if (sensor.currentReading) {
            const reading = sensor.currentReading;
            
            if (reading.moisture !== undefined) {
              envData.humidity += reading.moisture;
              moistureCount++;
            }
            if (reading.temperature !== undefined) {
              envData.soilTemp += reading.temperature;
              tempCount++;
            }
            if (reading.ph !== undefined) {
              envData.ph += reading.ph;
              phCount++;
            }
            if (reading.ec !== undefined) {
              envData.ec += reading.ec / 1000; // 转换为 mS/cm
              ecCount++;
            }
            validSensors++;
          }
        });
        
        if (validSensors > 0) {
          envData.humidity = moistureCount > 0 ? Math.round(envData.humidity / moistureCount) : 0;
          envData.soilTemp = tempCount > 0 ? Math.round((envData.soilTemp / tempCount) * 10) / 10 : 0;
          envData.ph = phCount > 0 ? Math.round((envData.ph / phCount) * 10) / 10 : 0;
          envData.ec = ecCount > 0 ? Math.round((envData.ec / ecCount) * 10) / 10 : 0;
          
          this.setData({ envData });
        }
      }
    }).catch(err => {
      console.error('获取环境数据失败:', err);
      wx.showToast({
        title: '加载环境数据失败',
        icon: 'none'
      });
    });
  },

  loadTodayTasks: function () {
    // 使用任务API获取今日任务
    api.getTodayTasks().then(res => {
      console.log('今日任务API响应:', res);
      
      if (res && res.code === 200 && res.data) {
        const tasks = res.data.tasks || res.data || [];
        console.log('今日任务:', tasks);
        
        this.setData({
          todayTasks: tasks
        });
      }
    }).catch(err => {
      console.error('获取今日任务失败:', err);
      wx.showToast({
        title: '加载今日任务失败',
        icon: 'none'
      });
    });
  },

  loadTodayData: function () {
    // 使用今日数据统计API
    api.getTodayData().then(res => {
      console.log('今日数据API响应:', res);
      
      if (res && res.code === 200 && res.data) {
        const todayData = res.data;
        console.log('今日数据:', todayData);
        
        // 处理今日数据格式，添加空值检查
        const processedData = {
          irrigationTime: (todayData.irrigation && todayData.irrigation.totalDuration) ? Math.round(todayData.irrigation.totalDuration / 60) : 0,
          fertilizerAmount: (todayData.fertilization && todayData.fertilization.totalVolume) ? Math.round(todayData.fertilization.totalVolume / 1000 * 10) / 10 : 0,
          alarmCount: (todayData.alarms && todayData.alarms.active !== undefined) ? todayData.alarms.active : 0,
          avgHumidity: (todayData.sensors && todayData.sensors.normal && todayData.sensors.total) ? Math.round(todayData.sensors.normal / todayData.sensors.total * 100) : 0
        };
        
        this.setData({
          todayData: processedData
        });
      }
    }).catch(err => {
      console.error('获取今日数据失败:', err);
      wx.showToast({
        title: '加载今日数据失败',
        icon: 'none'
      });
    });
  },

  loadNotifications: function () {
    // 使用通知API获取最新通知
    api.getNotifications({ page: 1, pageSize: 5 }).then(res => {
      console.log('通知API响应:', res);
      
      if (res && res.code === 200 && res.data) {
        const notifications = res.data.notifications || res.data || [];
        console.log('通知数据:', notifications);
        
        // 计算未读数量
        const unreadCount = notifications.filter(n => !n.isRead && !n.read).length;
        
        this.setData({
          notifications: notifications,
          unreadCount: unreadCount
        });
      }
    }).catch(err => {
      console.error('获取通知信息失败:', err);
      wx.showToast({
        title: '加载通知失败',
        icon: 'none'
      });
    });
  },

  getHumidityStatus: function (humidity) {
    if (!humidity) return { class: '' };
    if (humidity < 30) return { class: 'danger' };
    if (humidity < 50) return { class: 'warning' };
    return { class: 'normal' };
  },

  getPhStatus: function (ph) {
    if (!ph) return { class: '' };
    if (ph < 6.0 || ph > 7.5) return { class: 'warning' };
    return { class: 'normal' };
  },

  controlIrrigation: function () {
    const currentStatus = this.data.irrigationStatus;
    
    if (currentStatus.class === 'status-offline') {
      wx.showToast({
        title: '设备离线',
        icon: 'none'
      });
      return;
    }
    
    const isRunning = currentStatus.class === 'status-online' && currentStatus.text === '运行中';
    const action = isRunning ? 'stop' : 'start';
    
    wx.showModal({
      title: '确认操作',
      content: `确定要${isRunning ? '停止' : '启动'}灌溉吗？`,
      success: (res) => {
        if (res.confirm) {
          this.executeIrrigationControl(action);
        }
      }
    });
  },

  executeIrrigationControl: function (action) {
    wx.showLoading({
      title: action === 'start' ? '启动中...' : '停止中...'
    });
    
    api.controlIrrigation({
      action: action === 'start' ? 'start_irrigation' : 'stop_irrigation',
      deviceId: 'device_001', // 默认使用第一个设备
      parameters: {
        duration: 1800, // 30分钟
        flowRate: 80
      }
    }).then(res => {
      wx.hideLoading();
      wx.showToast({
        title: action === 'start' ? '启动成功' : '停止成功',
        icon: 'success'
      });
      this.loadDeviceStatus();
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: err.message || '操作失败',
        icon: 'none'
      });
    });
  },

  viewSensorData: function () {
    wx.switchTab({
      url: '/pages/monitor/monitor'
    });
  },

  quickIrrigation: function () {
    wx.navigateTo({
      url: '/pages/device/device-selector/device-selector?action=irrigation'
    });
  },

  quickFertilizer: function () {
    wx.navigateTo({
      url: '/pages/device/device-selector/device-selector?action=fertilizer'
    });
  },

  viewHistory: function () {
    wx.navigateTo({
      url: '/pages/monitor/history/history'
    });
  },

  viewAlarm: function () {
    wx.navigateTo({
      url: '/pages/monitor/alarm/alarm'
    });
  },

  viewNotification: function (e) {
    const notificationId = e.currentTarget.dataset.id;
    console.log('点击了消息，ID:', notificationId);
    
    const notification = this.data.notifications.find(n => n.id == notificationId);
    console.log('找到的消息:', notification);
    
    if (notification) {
      // 标记为已读
      if (!notification.read) {
        this.markNotificationAsRead(notificationId);
      }
      
      // 根据通知类型跳转不同页面
      console.log('消息类型:', notification.type);
      switch (notification.type) {
        case 'alarm':
          console.log('跳转到预警页面');
          wx.navigateTo({
            url: '/pages/monitor/alarm/alarm',
            success: () => {
              console.log('预警页面跳转成功');
            },
            fail: (err) => {
              console.error('预警页面跳转失败:', err);
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
          break;
        case 'task':
          console.log('跳转到设备页面');
          wx.switchTab({
            url: '/pages/device/device'
          });
          break;
        case 'system':
          console.log('显示系统消息弹窗');
          wx.showModal({
            title: notification.title,
            content: notification.description,
            showCancel: false
          });
          break;
        default:
          console.log('未知消息类型');
          break;
      }
    } else {
      console.error('未找到对应的消息');
      wx.showToast({
        title: '消息不存在',
        icon: 'none'
      });
    }
  },

  markNotificationAsRead: function (notificationId) {
    const notifications = this.data.notifications.map(n => {
      if (n.id == notificationId) {
        n.read = true;
      }
      return n;
    });
    
    const unreadCount = notifications.filter(n => !n.read).length;
    
    this.setData({
      notifications: notifications,
      unreadCount: unreadCount
    });

    // 发送已读状态到服务器
    api.markNotificationAsRead({ notificationId: notificationId })
      .then(res => {
        console.log('通知已标记为已读');
      })
      .catch(err => {
        console.error('标记通知已读失败:', err);
      });
  },

  viewAllNotifications: function () {
    wx.navigateTo({
      url: '/pages/notifications/notifications'
    });
  },

  viewAllDevices: function () {
    wx.switchTab({
      url: '/pages/device/device'
    });
  },

  // 天气卡片点击事件
  onWeatherTap(e) {
    const weatherData = e.detail.weatherData;
    console.log('点击天气卡片:', weatherData);
    
    // 显示详细天气信息
    if (weatherData) {
      const title = `${weatherData.weather} ${this.formatTemperature(weatherData.temperature)}`;
      const content = `湿度: ${weatherData.humidity || '--'}%\n风力: ${weatherData.windLevel || '--'}级\n体感温度: ${this.formatTemperature(weatherData.feelsLike || weatherData.temperature)}`;
      
      wx.showModal({
        title: title,
        content: content,
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },

  // 刷新天气数据
  refreshWeather: function() {
    console.log('手动刷新天气数据');
    
    wx.showToast({
      title: '正在更新天气...',
      icon: 'loading',
      duration: 1500
    });
    
    // 标记为加载状态
    this.setData({
      'weatherData.loading': true
    });
    
    // 重新获取位置（如果需要）
    if (!this.data.currentLocation && !this.data.farmData.coordinates) {
      this.getCurrentLocation();
    } else {
      this.loadWeatherData();
    }
  },

  // 检查是否需要刷新位置
  refreshLocationIfNeeded() {
    const lastLocationTime = wx.getStorageSync('lastLocationTime') || 0;
    const now = Date.now();
    // 如果超过10分钟没有更新位置，重新获取
    if (now - lastLocationTime > 10 * 60 * 1000) {
      this.getCurrentLocation();
    }
  },

  // 格式化温度显示
  formatTemperature(temp) {
    if (temp === null || temp === undefined) return '--°C';
    return `${Math.round(temp)}°C`;
  },

  // 测试和风天气API（仅开发环境）
  testWeatherAPI: function() {
    console.log('开始测试和风天气API...');
    
    const weatherService = new WeatherService();
    
    wx.showLoading({
      title: '正在测试API...'
    });
    
    // 显示API配置信息
    const apiInfo = weatherService.getAPIInfo();
    console.log('API配置信息:', apiInfo);
    
    // 测试API连接
    weatherService.testAPIConnection()
      .then(result => {
        wx.hideLoading();
        console.log('API测试完成:', result);
        
        let title = result.success ? '🎉 API测试成功' : '❌ API测试失败';
        let content = '';
        
        if (result.success) {
          const data = result.data;
          content = `✅ 和风天气API连接正常\n\n📍 测试位置: 重庆\n🌡️ 温度: ${data.temperature}°C\n🌤️ 天气: ${data.weather}\n💧 湿度: ${data.humidity}%\n💨 风力: ${data.windLevel || '--'}级\n🧭 风向: ${data.windDir || '--'}\n\n⏰ 更新时间: ${new Date().toLocaleTimeString()}`;
          
          // 更新UI显示真实天气数据
          this.setData({
            weatherData: {
              ...this.data.weatherData,
              temperature: Math.round(data.temperature),
              weather: data.weather,
              icon: this.getWeatherIcon(data.weather),
              humidity: data.humidity,
              windLevel: data.windLevel,
              windDir: data.windDir,
              pressure: data.pressure,
              lastUpdate: new Date().toLocaleTimeString(),
              source: 'api'
            }
          });
        } else {
          content = `❌ API连接失败\n\n🔧 错误信息: ${result.message}\n\n🔑 API Key: ${apiInfo.apiKey}\n🌐 API Host: ${apiInfo.baseUrl}\n\n💡 请检查:\n1. API Key是否正确\n2. API配额是否充足\n3. 网络连接是否正常`;
        }
        
        wx.showModal({
          title: title,
          content: content,
          showCancel: false,
          confirmText: '知道了'
        });
      })
      .catch(err => {
        wx.hideLoading();
        console.error('API测试异常:', err);
        
        wx.showModal({
          title: '🚨 测试异常',
          content: `发生意外错误:\n${err.message}\n\n请检查网络连接和API配置`,
          showCancel: false,
          confirmText: '知道了'
        });
      });
  },

  // 按照官方文档标准初始化Lottie动画
  initLottieAnimation: function() {
    console.log('开始初始化官方Lottie动画');
    wx.createSelectorQuery().select('#weatherCanvas').node(res => {
      const canvas = res.node;
      if (canvas) {
        const context = canvas.getContext('2d');
        // 适配高清显示处理
        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = 120 * dpr;
        canvas.height = 80 * dpr;
        context.scale(dpr, dpr);
        try {
          lottie.setup(canvas);
          const weatherType = this.getWeatherAnimationType(this.data.weatherData.weather);
          const animationData = WeatherAnimations.getAnimationData(weatherType);
          // 用 this._lottieAnimation 存储实例，避免 setData
          this._lottieAnimation = lottie.loadAnimation({
            loop: true,
            autoplay: true,
            animationData: animationData,
            rendererSettings: { context }
          });
          if (this._lottieAnimation) {
            this.setData({
              isAnimationPlaying: true
            });
            this._lottieAnimation.addEventListener('DOMLoaded', () => {
              console.log('Lottie动画DOM加载完成');
            });
            this._lottieAnimation.addEventListener('complete', () => {
              console.log('Lottie动画播放完成');
            });
            this._lottieAnimation.addEventListener('error', (error) => {
              console.error('Lottie动画播放错误:', error);
            });
          } else {
            console.error('Lottie动画实例创建失败');
          }
        } catch (error) {
          console.error('Lottie初始化失败:', error);
          this.showFallbackWeatherIcon();
        }
      } else {
        console.error('Canvas节点获取失败');
        this.showFallbackWeatherIcon();
      }
    }).exec();
  },

  // 显示降级天气图标
  showFallbackWeatherIcon: function() {
    console.log('使用降级天气图标显示');
    // 这里可以显示静态的天气图标或其他降级方案
  },

  // 切换动画播放状态（供外部调用）
  toggleAnimation: function() {
    // 用 this._lottieAnimation 操作动画实例
    if (this._lottieAnimation) {
      if (this.data.isAnimationPlaying) {
        this._lottieAnimation.pause();
      } else {
        this._lottieAnimation.play();
      }
      this.setData({ isAnimationPlaying: !this.data.isAnimationPlaying });
    }
  },

  // 销毁动画实例
  onUnload: function() {
    if (this._lottieAnimation) {
      this._lottieAnimation.destroy();
      this._lottieAnimation = null;
    }
  },

  // 初始化天气动画
  initWeatherAnimation: function() {
    const weatherType = this.getWeatherAnimationType(this.data.weatherData.weather);
    this.loadWeatherAnimation(weatherType);
  },

  // 根据天气类型获取动画类型
  getWeatherAnimationType: function(weather) {
    const animationMap = {
      '晴': 'sunny',
      '晴天': 'sunny',
      '多云': 'cloudy',
      '阴': 'cloudy',
      '阴天': 'cloudy',
      '小雨': 'rain',
      '中雨': 'rain',
      '大雨': 'rain',
      '暴雨': 'rain',
      '雷阵雨': 'rain',
      '雨': 'rain',
      '小雪': 'snow',
      '中雪': 'snow',
      '大雪': 'snow',
      '雪': 'snow'
    };
    
    return animationMap[weather] || 'sunny';
  },

  // 加载天气动画
  loadWeatherAnimation: function(type) {
    console.log('开始加载天气动画:', type);
    try {
      // 使用专门的动画数据模块
      const animationData = WeatherAnimations.getAnimationData(type);
      console.log('获取到的动画数据:', {
        type: type,
        hasV: !!animationData.v,
        hasLayers: !!(animationData.layers && animationData.layers.length),
        dataKeys: Object.keys(animationData),
        dataSize: JSON.stringify(animationData).length
      });
      const enhancedData = {
        ...animationData,
        type: type
      };
      this.setData({
        weatherAnimationData: enhancedData
      });
      console.log('天气动画数据设置成功');
      // 已采用canvas方式渲染动画，无需selectComponent('#weather-lottie')
    } catch (error) {
      console.error('加载天气动画失败:', error);
      // 降级到简单数据
      this.loadFallbackAnimation(type);
    }
  },

  // 降级动画加载
  loadFallbackAnimation: function(type) {
    console.log('使用降级动画:', type);
    const fallbackData = {
      type: type,
      name: `${type}天气动画`,
      description: `${type}天气动画效果`,
      duration: 3000,
      frames: 60
    };
    this.setData({
      weatherAnimationData: fallbackData
    });
    // 已采用canvas方式渲染动画，无需selectComponent('#weather-lottie')
  },

  // 更新天气动画（当天气数据变化时调用）
  updateWeatherAnimation: function(weather) {
    const newType = this.getWeatherAnimationType(weather);
    const currentType = this.data.weatherAnimationData ? this.data.weatherAnimationData.type : null;
    
    // 只有当天气类型真正变化时才更新动画
    if (newType !== currentType) {
      console.log('天气类型变化，更新动画:', currentType, '->', newType);
      this.loadWeatherAnimation(newType);
    }
  },
});