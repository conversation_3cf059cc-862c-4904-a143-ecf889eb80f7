<view class="tab-bar" style="position: fixed; bottom: 0; left: 0; right: 0; height: 100rpx; background: white; display: flex; z-index: 9999; border-top-left-radius: 50rpx; border-top-right-radius: 50rpx; box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.1);">
  <view 
    wx:for="{{list}}" 
    wx:key="index" 
    class="tab-bar-item" 
    style="flex: 1; text-align: center; display: flex; flex-direction: column; justify-content: center; align-items: center;"
    data-path="{{item.pagePath}}" 
    data-index="{{index}}" 
    bindtap="switchTab"
  >
    <image 
      src="{{selected === index ? item.selectedIconPath : item.iconPath}}" 
      style="width: 40rpx; height: 40rpx; margin-bottom: 4rpx;"
    ></image>
    <view 
      class="tab-bar-text" 
      style="font-size: 20rpx; color: {{selected === index ? selectedColor : color}};"
    >
      {{item.text}}
    </view>
  </view>
</view>