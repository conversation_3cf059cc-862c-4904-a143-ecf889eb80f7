Page({
  data: {
    sensorList: [
      {
        id: 1,
        name: '土壤传感器-01',
        type: '土壤多参数传感器',
        model: 'SS-5TM',
        location: '1号大棚 A区 中央',
        status: 'online',
        statusText: '在线',
        signalStrength: -45,
        signalLevel: 'excellent',
        battery: 85,
        lastUpdate: '2分钟前',
        icon: '🌡️',
        currentData: [
          { type: 'humidity', name: '湿度', value: 65, unit: '%', icon: '💧', status: 'normal' },
          { type: 'temperature', name: '温度', value: 23, unit: '°C', icon: '🌡️', status: 'normal' },
          { type: 'ph', name: 'PH值', value: 6.8, unit: '', icon: '🧪', status: 'normal' },
          { type: 'ec', name: 'EC值', value: 1.2, unit: 'mS/cm', icon: '⚡', status: 'normal' }
        ]
      },
      {
        id: 2,
        name: '土壤传感器-02',
        type: '土壤温湿度传感器',
        model: 'SHT-20',
        location: '1号大棚 B区 南侧',
        status: 'online',
        statusText: '在线',
        signalStrength: -62,
        signalLevel: 'good',
        battery: 72,
        lastUpdate: '5分钟前',
        icon: '🌡️',
        currentData: [
          { type: 'humidity', name: '湿度', value: 58, unit: '%', icon: '💧', status: 'warning' },
          { type: 'temperature', name: '温度', value: 25, unit: '°C', icon: '🌡️', status: 'normal' }
        ]
      },
      {
        id: 3,
        name: '土壤传感器-03',
        type: 'NPK传感器',
        model: 'NPK-S1',
        location: '2号大棚 东侧',
        status: 'warning',
        statusText: '信号弱',
        signalStrength: -78,
        signalLevel: 'weak',
        battery: 35,
        lastUpdate: '15分钟前',
        icon: '🌱',
        currentData: [
          { type: 'nitrogen', name: '氮', value: 120, unit: 'mg/kg', icon: '🌿', status: 'normal' },
          { type: 'phosphorus', name: '磷', value: 45, unit: 'mg/kg', icon: '🌾', status: 'low' },
          { type: 'potassium', name: '钾', value: 180, unit: 'mg/kg', icon: '🌼', status: 'normal' }
        ]
      },
      {
        id: 4,
        name: '土壤传感器-04',
        type: '土壤温湿度传感器',
        model: 'SHT-20',
        location: '2号大棚 西侧',
        status: 'offline',
        statusText: '离线',
        signalStrength: 0,
        signalLevel: 'none',
        battery: 0,
        lastUpdate: '2小时前',
        icon: '🌡️',
        currentData: []
      }
    ],
    
    maintenancePlan: [
      {
        id: 1,
        title: '定期校准',
        description: '对所有传感器进行校准，确保数据准确性',
        date: '2024-07-15',
        sensors: '土壤传感器-01, 土壤传感器-02',
        priority: 'normal',
        status: 'pending'
      },
      {
        id: 2,
        title: '电池更换',
        description: '更换低电量传感器的电池',
        date: '2024-07-12',
        sensors: '土壤传感器-03',
        priority: 'urgent',
        status: 'pending'
      },
      {
        id: 3,
        title: '设备检修',
        description: '检查离线传感器，排查故障原因',
        date: '2024-07-10',
        sensors: '土壤传感器-04',
        priority: 'high',
        status: 'completed'
      }
    ],
    
    statsData: {
      totalCount: 4,
      onlineCount: 2,
      warningCount: 1,
      lowBatteryCount: 1
    },
    
    showAddModal: false,
    newSensor: {
      name: '',
      location: '',
      deviceId: ''
    },
    selectedType: 0,
    sensorTypeOptions: ['土壤温湿度传感器', '土壤多参数传感器', 'NPK传感器', 'PH传感器', 'EC传感器']
  },

  onLoad() {
    this.loadSensorData();
  },

  onShow() {
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  loadSensorData() {
    // 加载传感器数据
    console.log('加载传感器数据');
  },

  refreshData() {
    // 刷新数据
    this.loadSensorData();
  },

  // 查看传感器详情
  viewSensorDetail(e) {
    const sensorId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/monitor/sensor/detail/detail?id=${sensorId}`
    });
  },

  // 校准传感器
  calibrateSensor(e) {
    const sensorId = e.currentTarget.dataset.id;
    const sensor = this.data.sensorList.find(item => item.id == sensorId);
    
    wx.showModal({
      title: '传感器校准',
      content: `确定要校准 ${sensor.name} 吗？校准过程将持续3-5分钟。`,
      success: (res) => {
        if (res.confirm) {
          this.startCalibration(sensorId);
        }
      }
    });
  },

  startCalibration(sensorId) {
    wx.showLoading({
      title: '正在校准...'
    });
    
    // 模拟校准过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '校准完成',
        icon: 'success'
      });
    }, 3000);
  },

  // 测试传感器
  testSensor(e) {
    const sensorId = e.currentTarget.dataset.id;
    const sensor = this.data.sensorList.find(item => item.id == sensorId);
    
    wx.showLoading({
      title: '正在测试...'
    });
    
    // 模拟测试过程
    setTimeout(() => {
      wx.hideLoading();
      
      if (sensor.status === 'offline') {
        wx.showModal({
          title: '测试结果',
          content: '传感器无响应，请检查电源和连接。',
          showCancel: false
        });
      } else {
        wx.showToast({
          title: '测试通过',
          icon: 'success'
        });
      }
    }, 2000);
  },

  // 配置传感器
  configureSensor(e) {
    const sensorId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/monitor/sensor/config/config?id=${sensorId}`
    });
  },

  // 添加传感器
  addSensor() {
    this.setData({
      showAddModal: true,
      newSensor: {
        name: '',
        location: '',
        deviceId: ''
      },
      selectedType: 0
    });
  },

  closeAddModal() {
    this.setData({
      showAddModal: false
    });
  },

  preventClose() {
    // 阻止冒泡
  },

  onNameInput(e) {
    this.setData({
      'newSensor.name': e.detail.value
    });
  },

  onLocationInput(e) {
    this.setData({
      'newSensor.location': e.detail.value
    });
  },

  onDeviceIdInput(e) {
    this.setData({
      'newSensor.deviceId': e.detail.value
    });
  },

  onTypeChange(e) {
    this.setData({
      selectedType: e.detail.value
    });
  },

  saveNewSensor() {
    const { name, location, deviceId } = this.data.newSensor;
    
    if (!name || !location || !deviceId) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '正在添加...'
    });
    
    // 模拟添加过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });
      
      this.closeAddModal();
      this.refreshData();
    }, 1500);
  },

  // 添加维护计划
  addMaintenancePlan() {
    wx.showModal({
      title: '添加维护计划',
      content: '请在维护管理页面添加详细的维护计划。',
      showCancel: false
    });
  },

  // 完成维护
  completeMaintenance(e) {
    const planId = e.currentTarget.dataset.id;
    const plan = this.data.maintenancePlan.find(item => item.id == planId);
    
    if (plan.status === 'completed') {
      wx.showToast({
        title: '该计划已完成',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '完成维护',
      content: `确定完成 "${plan.title}" 维护任务吗？`,
      success: (res) => {
        if (res.confirm) {
          const maintenancePlan = this.data.maintenancePlan.map(item => {
            if (item.id == planId) {
              return { ...item, status: 'completed' };
            }
            return item;
          });
          
          this.setData({
            maintenancePlan: maintenancePlan
          });
          
          wx.showToast({
            title: '维护完成',
            icon: 'success'
          });
        }
      }
    });
  }
})