/* 新增灌溉计划页面样式 */

/* 重置一些可能冲突的样式 */
input {
  -webkit-appearance: none;
  appearance: none;
}

.page-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 120rpx;
}

.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 表单样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #e8f8ec;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 16rpx;
}

.form-input {
  display: block;
  width: 100%;
  height: 88rpx;
  padding: 20rpx 24rpx;
  background-color: #ffffff;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #2c3e50;
  line-height: normal;
  box-sizing: border-box;
  outline: none;
}

.form-input:focus {
  border-color: #08C160;
  background-color: #fff;
  box-shadow: 0 0 0 4rpx rgba(8, 193, 96, 0.1);
}

.form-input::placeholder {
  color: #adb5bd;
  opacity: 1;
}

.form-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 12rpx;
  line-height: 1.5;
}

/* 星期选择器样式 */
.weekday-selector {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16rpx;
  margin-top: 20rpx;
  padding: 0 8rpx;
}

.weekday-item {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.weekday-item:active {
  transform: scale(0.96);
}

.weekday-item.selected {
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  border-color: #08C160;
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(8, 193, 96, 0.3);
}

.weekday-text {
  font-size: 24rpx;
  font-weight: 600;
}

/* 时间段编辑样式 */
.time-slot-edit {
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.slot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #dee2e6;
}

.slot-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.slot-form {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.time-inputs {
  display: flex;
  gap: 16rpx;
}

.time-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.input-label {
  font-size: 24rpx;
  color: #6c757d;
  font-weight: 500;
}

.picker-view {
  padding: 20rpx 16rpx;
  background-color: #fff;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #2c3e50;
  text-align: center;
  transition: all 0.3s ease;
}

.picker-view:active {
  border-color: #08C160;
  box-shadow: 0 0 0 4rpx rgba(8, 193, 96, 0.1);
}

.param-inputs {
  display: flex;
  gap: 16rpx;
}

.param-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.param-input {
  padding: 20rpx 16rpx;
  background-color: #fff;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #2c3e50;
  text-align: center;
  transition: all 0.3s ease;
}

.param-input:focus {
  border-color: #08C160;
  box-shadow: 0 0 0 4rpx rgba(8, 193, 96, 0.1);
}

.zone-selector {
  margin-top: 8rpx;
}

.zone-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
  margin-top: 12rpx;
}

.zone-option {
  padding: 16rpx 12rpx;
  background-color: #fff;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.zone-option:active {
  transform: scale(0.96);
}

.zone-option.selected {
  background-color: #e8f8ec;
  border-color: #08C160;
  color: #08C160;
}

.zone-name {
  font-size: 24rpx;
  font-weight: 500;
}

/* 按钮样式 */
.btn {
  padding: 20rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(8, 193, 96, 0.3);
}

.btn-primary:active {
  box-shadow: 0 2rpx 8rpx rgba(8, 193, 96, 0.4);
  transform: scale(0.98);
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-mini {
  padding: 8rpx 16rpx;
  font-size: 22rpx;
}

.btn-danger {
  background-color: #dc3545;
  color: #fff;
  border: 2rpx solid #dc3545;
}

.btn-danger:active {
  background-color: #c82333;
  color: #fff;
  border-color: #c82333;
  transform: scale(0.96);
}

.btn-large {
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  flex: 1;
  max-width: 280rpx;
}

.btn-outline {
  background-color: #fff;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.btn-outline:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

/* 操作按钮区域 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background-color: #fff;
  border-top: 1rpx solid #e9ecef;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 16rpx;
  justify-content: center;
}