/* 传感器详情页样式 */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2E7D32;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.update-time {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

/* 设备头部信息 */
.device-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.device-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: linear-gradient(45deg, #4CAF50, #2E7D32);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.device-icon {
  font-size: 60rpx;
}

.device-basic {
  flex: 1;
}

.device-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.device-type {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
}

.device-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
}

.device-status.online .status-dot {
  background-color: #4CAF50;
}

.device-status.warning .status-dot {
  background-color: #FF9800;
}

.device-status.offline .status-dot {
  background-color: #F44336;
}

.status-text {
  font-size: 28rpx;
  color: #666;
}

/* 信号强度 */
.signal-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.signal-bars {
  display: flex;
  align-items: flex-end;
  margin-bottom: 8rpx;
}

.signal-bar {
  width: 6rpx;
  margin-right: 4rpx;
  background-color: #ddd;
  border-radius: 3rpx;
}

.signal-bar:nth-child(1) { height: 12rpx; }
.signal-bar:nth-child(2) { height: 18rpx; }
.signal-bar:nth-child(3) { height: 24rpx; }
.signal-bar:nth-child(4) { height: 30rpx; }

.signal-bars.excellent .signal-bar {
  background-color: #4CAF50;
}

.signal-bars.good .signal-bar:nth-child(-n+3) {
  background-color: #8BC34A;
}

.signal-bars.weak .signal-bar:nth-child(-n+2) {
  background-color: #FF9800;
}

.signal-text {
  font-size: 20rpx;
  color: #999;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.info-item {
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  text-align: center;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 实时数据 */
.realtime-data {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.data-item {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 24rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid #4CAF50;
}

.data-item.warning {
  border-left-color: #FF9800;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}

.data-item.danger {
  border-left-color: #F44336;
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
}

.data-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.data-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.data-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.data-value {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
}

.value-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #2E7D32;
  margin-right: 8rpx;
}

.value-unit {
  font-size: 24rpx;
  color: #666;
}

.data-status {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.status-indicator {
  width: 8rpx;
  height: 8rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}

.status-indicator.normal {
  background-color: #4CAF50;
}

.status-indicator.warning {
  background-color: #FF9800;
}

.status-indicator.danger {
  background-color: #F44336;
}

.data-range {
  font-size: 20rpx;
  color: #999;
}

/* 趋势图表 */
.trend-chart {
  margin-top: 20rpx;
}

.picker-btn {
  background-color: #f0f2f5;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.chart-container {
  background-color: #fafafa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin: 20rpx 0;
}

.simple-chart {
  position: relative;
  height: 400rpx;
  display: flex;
}

.chart-y-axis {
  width: 80rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-right: 20rpx;
}

.y-label {
  font-size: 20rpx;
  color: #999;
  text-align: right;
  line-height: 1;
}

.chart-content {
  flex: 1;
  position: relative;
  border-left: 2rpx solid #e0e0e0;
  border-bottom: 2rpx solid #e0e0e0;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.grid-line {
  height: 1rpx;
  background-color: #f0f0f0;
  width: 100%;
}

.chart-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.chart-point {
  position: absolute;
  transform: translate(-50%, 50%);
}

.point-dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #2E7D32;
  border-radius: 6rpx;
  border: 3rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(46, 125, 50, 0.3);
}

.point-value {
  position: absolute;
  top: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: #2E7D32;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  white-space: nowrap;
}

.point-value::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6rpx solid transparent;
  border-top-color: #2E7D32;
}

.chart-x-axis {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
  padding-left: 20rpx;
}

.x-label {
  font-size: 20rpx;
  color: #999;
  flex: 1;
  text-align: center;
}

.chart-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e0e0e0;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #2E7D32;
}

/* 预警设置 */
.alarm-settings {
  margin-top: 20rpx;
}

.alarm-item {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.alarm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.alarm-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.alarm-config {
  border-top: 1rpx solid #e0e0e0;
  padding-top: 20rpx;
}

.threshold-group {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.threshold-label {
  font-size: 24rpx;
  color: #666;
  width: 120rpx;
}

.threshold-input {
  flex: 1;
  background-color: white;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  margin: 0 16rpx;
}

.threshold-unit {
  font-size: 24rpx;
  color: #999;
  width: 80rpx;
}

.alarm-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

/* 数据记录 */
.record-filter {
  margin-left: auto;
}

.data-table {
  background-color: #fafafa;
  border-radius: 12rpx;
  overflow: hidden;
  margin-top: 20rpx;
}

.table-header {
  display: flex;
  background-color: #e8f5e8;
  padding: 20rpx 0;
  font-weight: 600;
  color: #2E7D32;
}

.header-cell {
  flex: 1;
  text-align: center;
  font-size: 24rpx;
}

.header-cell.time {
  flex: 1.2;
}

.table-body {
  max-height: 600rpx;
}

.table-row {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  text-align: center;
  font-size: 24rpx;
  color: #333;
}

.table-cell.time {
  flex: 1.2;
  color: #666;
}

.table-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

/* 维护信息 */
.maintenance-info {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 20rpx;
}

.maintenance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
}

.maintenance-item:last-child {
  border-bottom: none;
}

.maintenance-label {
  font-size: 28rpx;
  color: #333;
}

.maintenance-value {
  font-size: 28rpx;
  color: #666;
}

.maintenance-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

/* 按钮样式 */
.btn {
  padding: 20rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(45deg, #4CAF50, #2E7D32);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #2E7D32;
  color: #2E7D32;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-mini {
  padding: 8rpx 16rpx;
  font-size: 22rpx;
}