Page({
  data: {
    searchKeyword: '',
    categories: [
      {
        id: 'general',
        name: '常规问题',
        icon: '❓',
        count: 8
      },
      {
        id: 'device',
        name: '设备相关',
        icon: '📱',
        count: 12
      },
      {
        id: 'monitor',
        name: '监测数据',
        icon: '📊',
        count: 6
      },
      {
        id: 'account',
        name: '账户管理',
        icon: '👤',
        count: 4
      }
    ],
    faqList: [
      {
        id: 1,
        category: 'general',
        question: '如何开始使用智慧农业小程序？',
        answer: '首先需要微信授权登录，然后完善个人信息，添加您的设备，就可以开始监测和控制了。建议先查看使用教程。',
        tags: ['入门', '登录'],
        helpful: 45
      },
      {
        id: 2,
        category: 'device',
        question: '设备显示离线怎么办？',
        answer: '请检查：1)设备电源是否正常 2)网络连接是否稳定 3)设备是否在信号覆盖范围内。如仍无法解决，请联系技术支持。',
        tags: ['设备', '离线', '故障'],
        helpful: 38
      },
      {
        id: 3,
        category: 'device',
        question: '如何绑定新设备？',
        answer: '进入设备管理页面，点击"添加设备"，扫描设备二维码或手动输入设备ID即可完成绑定。',
        tags: ['设备', '绑定'],
        helpful: 52
      },
      {
        id: 4,
        category: 'monitor',
        question: '为什么监测数据不更新？',
        answer: '可能原因：1)传感器故障 2)网络连接问题 3)设备电量不足。请检查传感器状态和网络连接。',
        tags: ['监测', '数据', '故障'],
        helpful: 31
      },
      {
        id: 5,
        category: 'general',
        question: '支持哪些类型的设备？',
        answer: '目前支持主流品牌的水肥一体机、土壤传感器、环境监测站等设备。详细兼容列表请查看设备说明。',
        tags: ['设备', '兼容性'],
        helpful: 29
      },
      {
        id: 6,
        category: 'device',
        question: '定时任务设置失败怎么办？',
        answer: '检查：1)时间设置是否合理 2)设备是否在线 3)权限是否充足。确保设备连接稳定后重新设置。',
        tags: ['定时', '任务', '设置'],
        helpful: 22
      },
      {
        id: 7,
        category: 'monitor',
        question: '如何设置预警阈值？',
        answer: '进入监测页面，点击"预警设置"，根据作物类型和生长阶段设置合适的阈值范围。',
        tags: ['预警', '阈值', '设置'],
        helpful: 41
      },
      {
        id: 8,
        category: 'account',
        question: '如何修改个人信息？',
        answer: '进入"我的"页面，点击"编辑资料"，修改相关信息后保存即可。',
        tags: ['个人信息', '修改'],
        helpful: 18
      },
      {
        id: 9,
        category: 'general',
        question: '数据会同步到云端吗？',
        answer: '是的，所有数据都会自动同步到云端，确保数据安全。您可以在任何设备上查看历史数据。',
        tags: ['数据', '同步', '云端'],
        helpful: 35
      },
      {
        id: 10,
        category: 'device',
        question: '设备控制延迟很大怎么办？',
        answer: '检查网络信号强度，确保设备在良好的网络环境中。如果问题持续，请尝试重启设备或联系技术支持。',
        tags: ['控制', '延迟', '网络'],
        helpful: 27
      }
    ],
    filteredFaqs: [],
    selectedCategory: 'all',
    expandedItems: []
  },

  onLoad() {
    this.setData({
      filteredFaqs: this.data.faqList
    });
  },

  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    this.filterFaqs();
  },

  selectCategory(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      selectedCategory: category
    });
    this.filterFaqs();
  },

  filterFaqs() {
    const { faqList, searchKeyword, selectedCategory } = this.data;
    let filtered = faqList;

    // 按分类筛选
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    // 按关键词搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(faq => 
        faq.question.toLowerCase().includes(keyword) ||
        faq.answer.toLowerCase().includes(keyword) ||
        faq.tags.some(tag => tag.toLowerCase().includes(keyword))
      );
    }

    this.setData({
      filteredFaqs: filtered
    });
  },

  toggleExpand(e) {
    const faqId = e.currentTarget.dataset.id;
    const { expandedItems } = this.data;
    
    const index = expandedItems.indexOf(faqId);
    if (index > -1) {
      expandedItems.splice(index, 1);
    } else {
      expandedItems.push(faqId);
    }
    
    this.setData({
      expandedItems: [...expandedItems]
    });
  },

  markHelpful(e) {
    const faqId = e.currentTarget.dataset.id;
    const { faqList } = this.data;
    
    const faqIndex = faqList.findIndex(faq => faq.id === faqId);
    if (faqIndex > -1) {
      faqList[faqIndex].helpful += 1;
      this.setData({
        faqList: [...faqList]
      });
      this.filterFaqs();
      
      wx.showToast({
        title: '感谢您的反馈',
        icon: 'success'
      });
    }
  },

  contactSupport() {
    wx.showModal({
      title: '联系技术支持',
      content: '客服电话：************\n工作时间：9:00-18:00\n\n或者您可以通过意见反馈提交问题',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        }
      }
    });
  }
})