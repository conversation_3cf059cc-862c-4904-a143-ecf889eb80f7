package com.agriculture.iot.service;

import com.agriculture.iot.entity.IrrigationRecord;
import com.agriculture.iot.entity.IrrigationSchedule;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 灌溉服务接口
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface IrrigationService extends IService<IrrigationRecord> {

    /**
     * 开始灌溉
     * 
     * @param startRequest 启动请求
     * @return 启动结果
     */
    Map<String, Object> startIrrigation(IrrigationRecord.StartRequest startRequest);

    /**
     * 停止灌溉
     * 
     * @param stopRequest 停止请求
     * @return 停止结果
     */
    Map<String, Object> stopIrrigation(IrrigationRecord.StopRequest stopRequest);

    /**
     * 分页获取灌溉记录
     * 
     * @param page 分页对象
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 灌溉记录列表
     */
    IPage<IrrigationRecord> getIrrigationRecords(Page<IrrigationRecord> page, Long deviceId, Long plotId, 
                                               LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取灌溉状态
     * 
     * @param deviceId 设备ID
     * @return 状态信息
     */
    IrrigationRecord.StatusInfo getIrrigationStatus(Long deviceId);

    /**
     * 分页获取灌溉计划
     * 
     * @param page 分页对象
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @param status 状态
     * @return 灌溉计划列表
     */
    IPage<IrrigationSchedule> getIrrigationSchedules(Page<IrrigationSchedule> page, Long deviceId, Long plotId, Integer status);

    /**
     * 创建灌溉计划
     * 
     * @param schedule 灌溉计划
     * @return 创建的计划
     */
    IrrigationSchedule createIrrigationSchedule(IrrigationSchedule schedule);

    /**
     * 更新灌溉计划
     * 
     * @param schedule 灌溉计划
     * @return 更新的计划
     */
    IrrigationSchedule updateIrrigationSchedule(IrrigationSchedule schedule);

    /**
     * 删除灌溉计划
     * 
     * @param id 计划ID
     * @return 删除结果
     */
    boolean deleteIrrigationSchedule(Long id);

    /**
     * 执行灌溉计划
     * 
     * @param scheduleId 计划ID
     * @return 执行结果
     */
    Map<String, Object> executeIrrigationSchedule(Long scheduleId);

    /**
     * 更新计划状态
     * 
     * @param scheduleId 计划ID
     * @param status 状态
     * @return 更新结果
     */
    boolean updateScheduleStatus(Long scheduleId, Integer status);

    /**
     * 获取灌溉统计信息
     * 
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @param days 统计天数
     * @return 统计信息
     */
    IrrigationRecord.Statistics getIrrigationStatistics(Long deviceId, Long plotId, Integer days);

    /**
     * 获取灌溉效率分析
     * 
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 效率数据
     */
    IrrigationRecord.EfficiencyData getIrrigationEfficiency(Long deviceId, Long plotId, 
                                                           LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取灌溉报表
     * 
     * @param reportType 报表类型
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报表数据
     */
    IrrigationRecord.ReportData getIrrigationReport(String reportType, Long deviceId, Long plotId, 
                                                   LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取灌溉模板
     * 
     * @return 模板列表
     */
    List<IrrigationSchedule.Template> getIrrigationTemplates();

    /**
     * 应用灌溉模板
     * 
     * @param templateId 模板ID
     * @param deviceId 设备ID
     * @param templateParams 模板参数
     * @return 创建的计划
     */
    IrrigationSchedule applyIrrigationTemplate(Long templateId, Long deviceId, 
                                              IrrigationSchedule.TemplateParams templateParams);

    /**
     * 批量控制灌溉
     * 
     * @param batchRequest 批量请求
     * @return 控制结果
     */
    Map<String, Object> batchControlIrrigation(IrrigationRecord.BatchControlRequest batchRequest);

    /**
     * 获取灌溉趋势
     * 
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @param trendType 趋势类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 趋势数据
     */
    IrrigationRecord.TrendData getIrrigationTrends(Long deviceId, Long plotId, String trendType, 
                                                  LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 设置灌溉阈值
     * 
     * @param thresholdConfig 阈值配置
     * @return 设置结果
     */
    boolean setIrrigationThreshold(IrrigationRecord.ThresholdConfig thresholdConfig);

    /**
     * 获取灌溉阈值配置
     * 
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @return 阈值配置列表
     */
    List<IrrigationRecord.ThresholdConfig> getIrrigationThresholds(Long deviceId, Long plotId);
}