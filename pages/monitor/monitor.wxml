<!--监测数据页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 传感器状态 -->
    <view class="card">
      <view class="card-title">传感器状态</view>
      <view class="sensor-grid">
        <view class="sensor-item" wx:for="{{sensorList}}" wx:key="id">
          <view class="sensor-header">
            <text class="sensor-name">{{item.name}}</text>
            <view class="sensor-status {{item.status}}">
              <text class="status-dot"></text>
            </view>
          </view>
          <text class="sensor-location">{{item.location}}</text>
          <text class="sensor-update">{{item.lastUpdate}}</text>
        </view>
      </view>
    </view>

    <!-- 显示模式切换 -->
    <view class="card">
      <view class="card-title">
        <text>监测面板</text>
        <view class="view-mode-tabs">
          <view class="mode-tab {{viewMode === 'dashboard' ? 'active' : ''}}" bindtap="switchViewMode" data-mode="dashboard">仪表盘</view>
          <view class="mode-tab {{viewMode === 'map' ? 'active' : ''}}" bindtap="switchViewMode" data-mode="map">地图</view>
          <view class="mode-tab {{viewMode === 'depth' ? 'active' : ''}}" bindtap="switchViewMode" data-mode="depth">深度</view>
        </view>
      </view>

      <!-- 仪表盘模式 -->
      <view wx:if="{{viewMode === 'dashboard'}}" class="dashboard-mode">
        <view class="gauge-grid">
          <view class="gauge-item" wx:for="{{realTimeData}}" wx:key="type">
            <view class="gauge-container">
              <view class="gauge-circle">
                <view class="gauge-fill {{item.status}}" style="transform: rotate({{item.gaugeAngle}}deg);"></view>
                <view class="gauge-center">
                  <text class="gauge-value">{{item.value}}</text>
                  <text class="gauge-unit">{{item.unit}}</text>
                </view>
              </view>
              <view class="gauge-info">
                <text class="gauge-icon">{{item.icon}}</text>
                <text class="gauge-name">{{item.name}}</text>
                <text class="gauge-range">{{item.range}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 地图模式 -->
      <view wx:if="{{viewMode === 'map'}}" class="map-mode">
        <view class="sensor-map">
          <view class="map-area">
            <image src="/images/farm-layout.png" class="farm-image" mode="aspectFit"></image>
            <view class="sensor-pin {{sensor.status}}" 
                  wx:for="{{sensorMapData}}" wx:key="id" wx:for-item="sensor"
                  style="left: {{sensor.x}}%; top: {{sensor.y}}%;"
                  bindtap="viewSensorMapDetail" data-id="{{sensor.id}}">
              <view class="pin-dot"></view>
              <view class="pin-label">{{sensor.name}}</view>
              <view class="pin-value">{{sensor.mainValue}}</view>
            </view>
          </view>
          <view class="map-legend">
            <view class="legend-item">
              <view class="legend-dot online"></view>
              <text>在线正常</text>
            </view>
            <view class="legend-item">
              <view class="legend-dot warning"></view>
              <text>预警状态</text>
            </view>
            <view class="legend-item">
              <view class="legend-dot offline"></view>
              <text>离线故障</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 深度监测模式 -->
      <view wx:if="{{viewMode === 'depth'}}" class="depth-mode">
        <view class="depth-selector">
          <view class="depth-tab {{selectedDepth === '10cm' ? 'active' : ''}}" bindtap="switchDepth" data-depth="10cm">10cm</view>
          <view class="depth-tab {{selectedDepth === '20cm' ? 'active' : ''}}" bindtap="switchDepth" data-depth="20cm">20cm</view>
          <view class="depth-tab {{selectedDepth === '30cm' ? 'active' : ''}}" bindtap="switchDepth" data-depth="30cm">30cm</view>
        </view>
        <view class="depth-data">
          <view class="depth-grid">
            <view class="depth-card" wx:for="{{depthData[selectedDepth]}}" wx:key="type">
              <view class="depth-header">
                <text class="depth-icon">{{item.icon}}</text>
                <text class="depth-name">{{item.name}}</text>
              </view>
              <view class="depth-value {{item.status}}">
                <text class="value-number">{{item.value}}</text>
                <text class="value-unit">{{item.unit}}</text>
              </view>
              <view class="depth-trend">
                <text class="trend-text">{{item.trend > 0 ? '↗' : item.trend < 0 ? '↘' : '→'}} {{item.trendText}}</text>
              </view>
              <view class="mini-chart">
                <view class="mini-line">
                  <view class="mini-point" wx:for="{{item.miniChart}}" wx:key="index" wx:for-item="point"
                        style="left: {{point.x}}%; bottom: {{point.y}}%;"></view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 趋势图表 -->
    <view class="card chart-card">
      <view class="card-title">
        <text>数据趋势</text>
        <view class="chart-controls">
          <picker range="{{timeRangeOptions}}" value="{{selectedTimeRange}}" bindchange="onTimeRangeChange">
            <view class="picker-view">{{timeRangeOptions[selectedTimeRange]}}</view>
          </picker>
        </view>
      </view>
      <view class="chart-container">
        <view class="chart-tabs">
          <view class="chart-tab {{selectedChart === 'humidity' ? 'active' : ''}}" 
                bindtap="switchChart" data-chart="humidity">
            土壤湿度
          </view>
          <view class="chart-tab {{selectedChart === 'temperature' ? 'active' : ''}}" 
                bindtap="switchChart" data-chart="temperature">
            土壤温度
          </view>
          <view class="chart-tab {{selectedChart === 'ph' ? 'active' : ''}}" 
                bindtap="switchChart" data-chart="ph">
            pH值
          </view>
          <view class="chart-tab {{selectedChart === 'ec' ? 'active' : ''}}" 
                bindtap="switchChart" data-chart="ec">
            EC值
          </view>
        </view>
        <view class="chart-content">
          <!-- 简化图表 -->
          <view class="chart-wrapper">
            <view class="chart-title">{{chartData.title}}</view>
            <view class="chart-mock">
              <view class="chart-points">
                <view 
                  class="chart-point" 
                  wx:for="{{chartData.points}}" 
                  wx:key="index"
                  style="left: {{item.x}}%; bottom: {{item.y}}%;">
                </view>
              </view>
            </view>
            <view class="chart-info">
              <text>最新值: {{chartData.latestValue}}</text>
              <text>平均值: {{chartData.avgValue}}</text>
            </view>
          </view>
          
          <!-- 图表信息和操作 -->
          <view class="chart-info-section">
            <view class="chart-stats-row">
              <view class="stat-card">
                <text class="stat-label">最新值</text>
                <text class="stat-value">{{chartData.latestValue}}</text>
              </view>
              <view class="stat-card">
                <text class="stat-label">平均值</text>
                <text class="stat-value">{{chartData.avgValue}}</text>
              </view>
            </view>
          </view>
          
        </view>
      </view>
    </view>

    <!-- 多级预警系统 -->
    <view class="card alarm-card">
      <view class="card-title">
        <text>智能预警系统</text>
        <view class="alarm-summary">
          <text class="alarm-count light">{{alarmStats.lightCount}}轻度</text>
          <text class="alarm-count moderate">{{alarmStats.moderateCount}}中度</text>
          <text class="alarm-count severe">{{alarmStats.severeCount}}重度</text>
          <text class="alarm-count continuous">{{alarmStats.continuousCount}}持续</text>
        </view>
      </view>
      
      <view class="alarm-filter">
        <view class="filter-tabs">
          <view class="filter-tab active">全部</view>
          <view class="filter-tab">紧急</view>
          <view class="filter-tab">今日</view>
          <view class="filter-tab">已处理</view>
        </view>
      </view>
      
      <view class="alarm-list">
        <view class="alarm-item {{item.level}}" wx:for="{{alarmList}}" wx:key="id">
          <view class="alarm-indicator">
            <view class="alarm-level-badge {{item.level}}">
              <text class="level-text">{{item.level === 'light' ? '轻' : item.level === 'moderate' ? '中' : item.level === 'severe' ? '重' : '续'}}</text>
            </view>
            <view class="priority-tag {{item.priority}}">{{item.priority === 'low' ? '低' : item.priority === 'medium' ? '中' : item.priority === 'high' ? '高' : '急'}}</view>
          </view>
          
          <view class="alarm-content">
            <view class="alarm-header">
              <text class="alarm-title">{{item.title}}</text>
              <text class="alarm-time">{{item.time}}</text>
            </view>
            
            <view class="alarm-details">
              <text class="alarm-desc">{{item.description}}</text>
              <view class="alarm-meta">
                <text class="alarm-sensor">传感器: {{item.sensor}}</text>
                <text class="alarm-duration">{{item.duration}}</text>
              </view>
              <view class="alarm-data">
                <text class="current-value">当前值: {{item.value}}</text>
                <text class="threshold-range">正常范围: {{item.threshold}}</text>
              </view>
            </view>
            
            <view class="alarm-suggestion">
              <text class="suggestion-label">处理建议:</text>
              <text class="suggestion-text">{{item.suggestion}}</text>
            </view>
          </view>
          
          <view class="alarm-actions">
            <button class="btn btn-mini btn-primary" bindtap="handleAlarmQuick" data-id="{{item.id}}">
              快速处理
            </button>
            <button class="btn btn-mini btn-outline" bindtap="viewAlarmDetail" data-id="{{item.id}}">
              详情
            </button>
            <button class="btn btn-mini btn-text" bindtap="ignoreAlarm" data-id="{{item.id}}">
              忽略
            </button>
          </view>
        </view>
        
        <view class="no-alarm" wx:if="{{alarmList.length === 0}}">
          <view class="no-alarm-icon">✅</view>
          <text class="text-muted">系统运行正常，暂无预警信息</text>
        </view>
      </view>
      
      <view class="alarm-settings-entry">
        <button class="btn btn-outline" bindtap="alarmSettings">
          ⚙️ 预警设置
        </button>
        <button class="btn btn-outline" bindtap="viewAlarmHistory">
          📋 历史记录
        </button>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="card">
      <view class="card-title">数据操作</view>
      <view class="data-actions">
        <button class="btn btn-outline" bindtap="exportData">
          📊 导出数据
        </button>
        <button class="btn btn-outline" bindtap="viewHistory">
          📈 历史分析
        </button>
        <button class="btn btn-outline" bindtap="alarmSettings">
          ⚙️ 预警设置
        </button>
        <button class="btn btn-outline" bindtap="manageSensors">
          📡 传感器管理
        </button>
      </view>
    </view>

  </view>
</view>