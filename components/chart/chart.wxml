<!--图表组件-->
<view class="chart-container">
  <view class="chart-header" wx:if="{{title}}">
    <text class="chart-title">{{title}}</text>
    <view class="chart-actions" wx:if="{{showActions}}">
      <text class="chart-action" bindtap="onExport">导出</text>
      <text class="chart-action" bindtap="onFullscreen">全屏</text>
    </view>
  </view>
  
  <view class="chart-wrapper">
    <canvas 
      class="chart-canvas" 
      canvas-id="{{canvasId}}" 
      id="{{canvasId}}"
      type="2d"
      style="width: {{width}}px; height: {{height}}px;"
      bindtouchstart="onTouchStart"
      bindtouchmove="onTouchMove"
      bindtouchend="onTouchEnd">
    </canvas>
    
    <view class="chart-loading" wx:if="{{loading}}">
      <text>图表加载中...</text>
    </view>
    
    <view class="chart-empty" wx:if="{{!loading && isEmpty}}">
      <image src="/images/empty-chart.png" mode="aspectFit"></image>
      <text>暂无数据</text>
    </view>
  </view>
  
  <view class="chart-legend" wx:if="{{legend && legend.length > 0}}">
    <view class="legend-item" wx:for="{{legend}}" wx:key="name">
      <view class="legend-color" style="background-color: {{item.color}};"></view>
      <text class="legend-text">{{item.name}}</text>
    </view>
  </view>
</view>