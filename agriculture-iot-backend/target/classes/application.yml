server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: agriculture-iot-backend
  
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    url: **********************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: AgricultureHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    # 日志配置
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 下划线转驼峰
    map-underscore-to-camel-case: true
    # 缓存配置
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: none
  global-config:
    db-config:
      # 主键类型
      id-type: ASSIGN_ID
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 字段策略
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_null
  # 扫描XML文件
  mapper-locations: classpath*:mapper/**/*.xml

# 日志配置
logging:
  level:
    com.agriculture.iot: debug
    org.springframework.web: info
    com.baomidou.mybatisplus: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/agriculture-iot.log
    max-size: 100MB
    max-history: 30

# 自定义配置
agriculture:
  # 文件上传路径
  upload:
    path: /Users/<USER>/develop/agriculture-mini/uploads/
    url-prefix: /uploads/
  
  # 设备配置
  device:
    heartbeat-interval: 30000 # 心跳间隔（毫秒）
    offline-timeout: 60000 # 离线超时（毫秒）
    command-timeout: 5000 # 命令超时（毫秒）
  
  # 传感器数据配置
  sensor:
    # 数据保留天数
    data-retention-days: 90
    # 批量插入大小
    batch-size: 1000
    # 数据采集间隔（毫秒）
    data-collection-interval: 60000
    # 告警检查间隔（毫秒）
    alert-check-interval: 300000
  
  # 灌溉配置
  irrigation:
    default-duration: 30 # 默认灌溉时长（分钟）
    max-duration: 120 # 最大灌溉时长（分钟）
    default-water-amount: 50.0 # 默认用水量（升）
    safety-check-interval: 60000 # 安全检查间隔（毫秒）
  
  # 施肥配置
  fertilizer:
    default-amount: 10.0 # 默认施肥量（升）
    max-amount: 50.0 # 最大施肥量（升）
    default-concentration: 0.5 # 默认浓度（%）
    safety-check-interval: 60000 # 安全检查间隔（毫秒）
  
  # 报警配置
  alarm:
    notification-methods: email,sms,push # 通知方式
    high-priority-check-interval: 60000 # 高优先级检查间隔（毫秒）
    cleanup-days: 90 # 清理天数
  
  # 定时任务配置
  schedule:
    # 传感器数据清理任务
    data-cleanup:
      enabled: true
      cron: "0 0 2 * * ?"  # 每天凌晨2点执行
    # 设备状态检查任务
    device-status-check:
      enabled: true
      cron: "0 */5 * * * ?"  # 每5分钟执行一次
    # 计划执行检查任务
    schedule-execution-check:
      enabled: true
      cron: "0 * * * * ?"  # 每分钟执行一次
    # 逾期计划检查任务
    overdue-schedule-check:
      enabled: true
      cron: "0 0 * * * ?"  # 每小时执行一次
    # 每日报告生成任务
    daily-report:
      enabled: true
      cron: "0 0 8 * * ?"  # 每天早上8点执行
    # 线程池配置
    thread-pool:
      core-size: 5
      max-size: 10
      queue-capacity: 100
      keep-alive-seconds: 60

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true