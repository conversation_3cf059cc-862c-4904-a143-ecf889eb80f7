<!--首页 - 农业设备控制页面-->
<view class="page-container">
  <!-- 顶部背景图片区域 -->
  <view class="header-bg-section">
    <!-- 背景图片 -->
    <image src="/assets/images/首页头背景.png" class="header-bg" mode="aspectFill"></image>
    
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <text class="navbar-title">智慧农业</text>
    </view>

    <!-- 农场信息区域 -->
    <view class="farm-info-section">
      <view class="farm-info">
        <view class="farm-name">
          <text class="name-text">重庆江北农场</text>
          <text class="switch-icon">⇆</text>
        </view>
        <view class="farm-location">
          <text class="location-icon">📍</text>
          <text class="location-text">重庆江北</text>
          <text class="area-text">50亩</text>
        </view>
      </view>
      
      <!-- 天气信息区域 -->
      <view class="weather-section">
        <view class="weather-visual">
          <canvas 
            type="2d" 
            id="weatherCanvas" 
            style="width: 240rpx; height: 160rpx;"
          ></canvas>
          <view class="weather-info">
            <text class="temperature">{{weatherData.temperature}}°</text>
            <text class="weather-desc">{{weatherData.weather}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 设备控制区域 -->
  <view class="device-section">
    <!-- 设备类型切换 -->
    <view class="device-tabs">
      <view class="tab-item {{currentTab === 'all' ? 'active' : ''}}" bindtap="switchTab" data-tab="all">
        <text>全部设备</text>
        <view class="tab-indicator" wx:if="{{currentTab === 'all'}}"></view>
      </view>
      <view class="tab-item {{currentTab === 'fertilizer' ? 'active' : ''}}" bindtap="switchTab" data-tab="fertilizer">
        <text>水肥机</text>
        <view class="tab-indicator" wx:if="{{currentTab === 'fertilizer'}}"></view>
      </view>
      <view class="tab-item {{currentTab === 'valve' ? 'active' : ''}}" bindtap="switchTab" data-tab="valve">
        <text>球阀</text>
        <view class="tab-indicator" wx:if="{{currentTab === 'valve'}}"></view>
      </view>
    </view>

    <!-- 设备列表 - 卡片式两列布局 -->
    <view class="device-list">
      <!-- 水肥一体机设备卡片 -->
      <view class="device-item" wx:for="{{deviceList}}" wx:key="id">
        <view class="device-control">
          <view class="power-button {{item.isOn ? 'on' : 'off'}}" bindtap="toggleDevice" data-id="{{item.id}}">
            <text class="power-icon">⏻</text>
          </view>
        </view>
        <view class="device-icon">
          <image src="{{item.icon}}" class="device-image" mode="aspectFit"></image>
        </view>
        <view class="device-info">
          <text class="device-name">{{item.name}}</text>
          <text class="device-status">{{item.status}}</text>
        </view>
      </view>
      <!-- 球阀控制卡片 -->
      <view class="valve-control" wx:if="{{currentTab === 'all' || currentTab === 'valve'}}">
        <view class="valve-info">
          <view class="valve-icon">
            <image src="/assets/images/球阀.png" class="valve-image" mode="aspectFit"></image>
          </view>
          <view class="valve-details">
            <text class="valve-name">电动球阀</text>
            <text class="valve-specs">灌溉中 | 流速400ps</text>
          </view>
        </view>
        <view class="valve-controls">
          <view class="channel-buttons">
            <view class="channel-btn active">
              <text class="channel-icon">⏻</text>
              <text class="channel-text">A 通道</text>
            </view>
            <view class="channel-btn">
              <text class="channel-icon">⏻</text>
              <text class="channel-text">B 通道</text>
            </view>
          </view>
          <view class="valve-slider">
            <slider 
              value="{{valveOpenness}}" 
              min="0" 
              max="100" 
              step="1"
              activeColor="#13BD9D"
              backgroundColor="#E5E5E5"
              bindchange="onValveSliderChange"
              block-size="20"
            />
            <view class="slider-info">
              <text class="slider-label">开度</text>
              <text class="slider-value">{{valveOpenness}}%</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>