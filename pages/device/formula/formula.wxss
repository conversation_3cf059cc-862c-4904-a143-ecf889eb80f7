/* 配方编辑页面样式 */

/* 表单列表 */
.form-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.form-input {
  padding: 20rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: white;
}

.form-picker {
  padding: 20rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  background-color: white;
}

.picker-view {
  font-size: 26rpx;
  color: #333333;
}

.form-textarea {
  padding: 20rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: white;
  min-height: 120rpx;
}

/* 营养成分配置 */
.nutrient-section {
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #E0E0E0;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.section-desc {
  font-size: 24rpx;
  color: #666666;
}

.nutrient-item {
  margin-bottom: 40rpx;
}

.nutrient-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.nutrient-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
}

.nutrient-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #08C160;
}

.nutrient-slider {
  margin: 20rpx 0;
}

.slider-range {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}

.range-min,
.range-max {
  font-size: 22rpx;
  color: #999999;
}

/* 微量元素 */
.micro-section {
  margin-top: 40rpx;
}

.micro-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.micro-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.micro-label {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

.micro-input {
  padding: 15rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 6rpx;
  font-size: 24rpx;
  background-color: white;
}

.micro-unit {
  font-size: 22rpx;
  color: #666666;
  text-align: center;
}

/* 目标参数 */
.target-params {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.param-item {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.param-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.param-input-group {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.param-input {
  flex: 1;
  padding: 20rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: white;
}

.param-unit {
  font-size: 24rpx;
  color: #666666;
  min-width: 80rpx;
}

.param-range {
  margin-top: 8rpx;
}

.range-text {
  font-size: 22rpx;
  color: #999999;
}

/* 使用说明 */
.usage-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.usage-item {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.usage-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.usage-picker {
  padding: 20rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  background-color: white;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.usage-input {
  flex: 1;
  padding: 20rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: white;
}

.input-unit {
  font-size: 24rpx;
  color: #666666;
}

.season-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.season-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.season-text {
  font-size: 26rpx;
  color: #333333;
}

.usage-textarea {
  padding: 20rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: white;
  min-height: 120rpx;
}

/* 配方预览 */
.preview-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.preview-item {
  display: flex;
  gap: 15rpx;
}

.preview-label {
  font-size: 26rpx;
  color: #666666;
  min-width: 140rpx;
}

.preview-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  flex: 1;
}

/* 操作按钮 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 40rpx;
  padding-bottom: 40rpx;
}

.btn-full {
  width: 100%;
  padding: 25rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 输入焦点状态 */
.form-input:focus,
.param-input:focus,
.usage-input:focus,
.micro-input:focus {
  border-color: #08C160;
  box-shadow: 0 0 0 2rpx rgba(8, 193, 96, 0.2);
}

/* ======= 自定义营养元素滑块样式 ======= */

/* 自定义营养元素滑块容器 */
.custom-nutrient-slider {
  margin: 20rpx 0;
}

.custom-nutrient-slider .slider-track {
  position: relative;
  height: 8rpx;
  background-color: #E0E0E0;
  border-radius: 4rpx;
  margin: 0 20rpx;
}

.custom-nutrient-slider .slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.1s ease;
}

.custom-nutrient-slider .slider-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 40rpx;
  height: 40rpx;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.custom-nutrient-slider .thumb-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
}

/* 氮素滑块 - 绿色 */
.custom-nutrient-slider.nitrogen .slider-fill {
  background: linear-gradient(90deg, #08C160 0%, #08C160 100%);
}

.custom-nutrient-slider.nitrogen .thumb-inner {
  background-color: #08C160;
}

/* 磷素滑块 - 橙色 */
.custom-nutrient-slider.phosphorus .slider-fill {
  background: linear-gradient(90deg, #FF9800 0%, #F57C00 100%);
}

.custom-nutrient-slider.phosphorus .thumb-inner {
  background-color: #FF9800;
}

/* 钾素滑块 - 蓝色 */
.custom-nutrient-slider.potassium .slider-fill {
  background: linear-gradient(90deg, #2196F3 0%, #1976D2 100%);
}

.custom-nutrient-slider.potassium .thumb-inner {
  background-color: #2196F3;
}

/* 滑块范围标签 */
.custom-nutrient-slider .slider-range {
  display: flex;
  justify-content: space-between;
  margin: 15rpx 20rpx 0;
}

.custom-nutrient-slider .range-min,
.custom-nutrient-slider .range-max {
  font-size: 22rpx;
  color: #666666;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .micro-grid {
    grid-template-columns: 1fr;
  }
  
  .param-input-group {
    flex-direction: column;
    align-items: stretch;
    gap: 10rpx;
  }
  
  .param-unit {
    text-align: center;
  }
}