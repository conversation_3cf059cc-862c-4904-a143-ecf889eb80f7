<!--使用教程页面-->
<view class="page-container">
  <!-- 教程列表 -->
  <view wx:if="{{!currentTutorial}}" class="tutorial-list">
    <view class="page-header">
      <text class="page-title">使用教程</text>
      <text class="page-desc">学习如何使用智慧农业管理系统</text>
    </view>

    <view class="tutorial-grid">
      <view 
        wx:for="{{tutorials}}" 
        wx:key="id" 
        class="tutorial-card"
        bindtap="selectTutorial"
        data-id="{{item.id}}"
      >
        <view class="tutorial-icon">{{item.icon}}</view>
        <view class="tutorial-info">
          <text class="tutorial-title">{{item.title}}</text>
          <text class="tutorial-desc">{{item.description}}</text>
          <view class="tutorial-meta">
            <text class="step-count">{{item.steps.length}}个步骤</text>
            <text class="tutorial-arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <view class="help-tip">
      <view class="tip-icon">💡</view>
      <view class="tip-content">
        <text class="tip-title">小提示</text>
        <text class="tip-text">建议新用户从"快速入门"开始学习，逐步掌握各项功能。</text>
      </view>
    </view>
  </view>

  <!-- 教程详情 -->
  <view wx:if="{{currentTutorial}}" class="tutorial-detail">
    <!-- 头部导航 -->
    <view class="detail-header">
      <view class="back-btn" bindtap="backToList">
        <text class="back-icon">←</text>
        <text>返回</text>
      </view>
      <view class="tutorial-progress">
        <text class="progress-text">{{currentStep + 1}}/{{currentTutorial.steps.length}}</text>
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            style="width: {{((currentStep + 1) / currentTutorial.steps.length) * 100}}%"
          ></view>
        </view>
      </view>
    </view>

    <!-- 教程内容 -->
    <view class="tutorial-content">
      <view class="tutorial-header">
        <view class="tutorial-icon large">{{currentTutorial.icon}}</view>
        <text class="tutorial-title">{{currentTutorial.title}}</text>
        <text class="tutorial-desc">{{currentTutorial.description}}</text>
      </view>

      <!-- 步骤内容 -->
      <view class="step-content">
        <view class="step-header">
          <view class="step-number">{{currentStep + 1}}</view>
          <text class="step-title">{{currentTutorial.steps[currentStep].title}}</text>
        </view>
        
        <view class="step-body">
          <text class="step-text">{{currentTutorial.steps[currentStep].content}}</text>
          
          <!-- 步骤图片占位 -->
          <view class="step-image-placeholder">
            <text class="image-icon">🖼️</text>
            <text class="image-text">演示图片</text>
          </view>
        </view>
      </view>

      <!-- 步骤导航点 -->
      <view class="step-dots">
        <view 
          wx:for="{{currentTutorial.steps}}" 
          wx:key="index" 
          class="step-dot {{index === currentStep ? 'active' : ''}}"
          bindtap="goToStep"
          data-step="{{index}}"
        ></view>
      </view>
    </view>

    <!-- 底部导航 -->
    <view class="tutorial-footer">
      <button 
        class="btn btn-secondary" 
        bindtap="prevStep"
        disabled="{{currentStep === 0}}"
      >
        上一步
      </button>
      
      <button 
        class="btn btn-primary" 
        bindtap="nextStep"
        wx:if="{{currentStep < currentTutorial.steps.length - 1}}"
      >
        下一步
      </button>
      
      <button 
        class="btn btn-success" 
        bindtap="backToList"
        wx:if="{{currentStep === currentTutorial.steps.length - 1}}"
      >
        完成
      </button>
    </view>
  </view>
</view>