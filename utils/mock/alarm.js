/**
 * 预警相关Mock API
 */

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 验证Token
function verifyToken(token) {
  // 在Mock环境中，简化Token验证，始终返回有效的payload
  return {
    userId: 'user_001',
    username: 'testuser',
    exp: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
  };
}

// 生成预警数据
function generateAlarms() {
  const alarmTypes = [
    { level: 'critical', title: '系统严重故障', description: '水泵系统出现严重故障', sensorId: 'device_001' },
    { level: 'warning', title: '土壤湿度过低', description: '1号地块土壤湿度低于阈值', sensorId: 'sensor_001' },
    { level: 'warning', title: '肥料储量不足', description: '肥料A储量低于20%', sensorId: 'device_002' },
    { level: 'info', title: '设备维护提醒', description: '设备需要例行维护', sensorId: 'device_003' },
    { level: 'critical', title: '水压异常', description: '系统水压持续低于安全阈值', sensorId: 'device_001' },
    { level: 'warning', title: 'pH值偏高', description: '2号区域土壤pH值连续3天超过7.5', sensorId: 'sensor_002' }
  ];

  const alarms = [];
  for (let i = 0; i < alarmTypes.length; i++) {
    const alarmType = alarmTypes[i];
    const time = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000); // 最近24小时
    
    alarms.push({
      id: `alarm_${String(i + 1).padStart(3, '0')}`,
      level: alarmType.level,
      title: alarmType.title,
      description: alarmType.description,
      sensorId: alarmType.sensorId,
      time: time.toISOString(),
      resolved: Math.random() > 0.7, // 30% 已解决
      currentValue: Math.random() * 100,
      thresholdValue: Math.random() * 50 + 25,
      suggestion: `建议检查${alarmType.sensorId}设备状态`,
      acknowledgedAt: null,
      resolvedAt: null
    });
  }

  return alarms.sort((a, b) => new Date(b.time) - new Date(a.time));
}

module.exports = {
  init() { 
    console.log('预警模块初始化完成'); 
  },
  
  async list(token, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { level, status, page = 1, pageSize = 20 } = params;
    
    let alarms = generateAlarms();
    
    // 级别过滤
    if (level && level !== 'all') {
      alarms = alarms.filter(alarm => alarm.level === level);
    }
    
    // 状态过滤
    if (status) {
      if (status === 'active') {
        alarms = alarms.filter(alarm => !alarm.resolved);
      } else if (status === 'resolved') {
        alarms = alarms.filter(alarm => alarm.resolved);
      }
    }
    
    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedAlarms = alarms.slice(startIndex, endIndex);
    
    return createApiResponse({
      alarms: paginatedAlarms,
      stats: {
        total: alarms.length,
        critical: alarms.filter(a => a.level === 'critical').length,
        warning: alarms.filter(a => a.level === 'warning').length,
        info: alarms.filter(a => a.level === 'info').length,
        resolved: alarms.filter(a => a.resolved).length,
        active: alarms.filter(a => !a.resolved).length
      },
      pagination: {
        page,
        pageSize,
        total: alarms.length,
        hasMore: endIndex < alarms.length
      }
    });
  },
  
  async acknowledge(token, alarmId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    return createApiResponse({
      alarmId: alarmId,
      acknowledgedAt: new Date().toISOString(),
      acknowledgedBy: payload.userId
    }, '预警已确认');
  },
  
  async resolve(token, alarmId, solution) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    return createApiResponse({
      alarmId: alarmId,
      solution: solution,
      resolvedAt: new Date().toISOString(),
      resolvedBy: payload.userId
    }, '预警已解决');
  },
  
  async getConfig(token) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    return createApiResponse({
      thresholds: {
        moisture: { min: 30, max: 80 },
        temperature: { min: 10, max: 35 },
        ph: { min: 6.0, max: 7.5 },
        ec: { min: 800, max: 2000 }
      },
      notifications: {
        email: true,
        sms: false,
        push: true
      }
    });
  },
  
  async updateConfig(token, config) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    return createApiResponse({
      config: config,
      updatedAt: new Date().toISOString(),
      updatedBy: payload.userId
    }, '配置已更新');
  }
};