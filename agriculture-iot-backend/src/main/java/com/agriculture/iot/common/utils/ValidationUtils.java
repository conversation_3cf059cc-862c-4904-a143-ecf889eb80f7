package com.agriculture.iot.common.utils;

import java.util.regex.Pattern;

/**
 * 验证工具类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public class ValidationUtils {

    /**
     * 手机号正则表达式
     */
    private static final String MOBILE_REGEX = "^1[3-9]\\d{9}$";
    
    /**
     * 邮箱正则表达式
     */
    private static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$";
    
    /**
     * 身份证号正则表达式
     */
    private static final String ID_CARD_REGEX = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
    
    /**
     * IP地址正则表达式
     */
    private static final String IP_REGEX = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";
    
    /**
     * MAC地址正则表达式
     */
    private static final String MAC_REGEX = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$";
    
    /**
     * 设备编号正则表达式
     */
    private static final String DEVICE_CODE_REGEX = "^[A-Za-z0-9]{6,20}$";

    /**
     * 验证手机号
     */
    public static boolean isValidMobile(String mobile) {
        return StringUtils.isNotEmpty(mobile) && Pattern.matches(MOBILE_REGEX, mobile);
    }

    /**
     * 验证邮箱
     */
    public static boolean isValidEmail(String email) {
        return StringUtils.isNotEmpty(email) && Pattern.matches(EMAIL_REGEX, email);
    }

    /**
     * 验证身份证号
     */
    public static boolean isValidIdCard(String idCard) {
        return StringUtils.isNotEmpty(idCard) && Pattern.matches(ID_CARD_REGEX, idCard);
    }

    /**
     * 验证IP地址
     */
    public static boolean isValidIp(String ip) {
        return StringUtils.isNotEmpty(ip) && Pattern.matches(IP_REGEX, ip);
    }

    /**
     * 验证MAC地址
     */
    public static boolean isValidMac(String mac) {
        return StringUtils.isNotEmpty(mac) && Pattern.matches(MAC_REGEX, mac);
    }

    /**
     * 验证设备编号
     */
    public static boolean isValidDeviceCode(String deviceCode) {
        return StringUtils.isNotEmpty(deviceCode) && Pattern.matches(DEVICE_CODE_REGEX, deviceCode);
    }

    /**
     * 验证密码强度
     * 至少8位，包含大小写字母、数字和特殊字符
     */
    public static boolean isValidPassword(String password) {
        if (StringUtils.isEmpty(password) || password.length() < 8) {
            return false;
        }
        
        boolean hasLower = false, hasUpper = false, hasDigit = false, hasSpecial = false;
        
        for (char c : password.toCharArray()) {
            if (Character.isLowerCase(c)) {
                hasLower = true;
            } else if (Character.isUpperCase(c)) {
                hasUpper = true;
            } else if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (!Character.isLetterOrDigit(c)) {
                hasSpecial = true;
            }
        }
        
        return hasLower && hasUpper && hasDigit && hasSpecial;
    }

    /**
     * 验证数值范围
     */
    public static boolean isInRange(double value, double min, double max) {
        return value >= min && value <= max;
    }

    /**
     * 验证字符串长度
     */
    public static boolean isValidLength(String str, int minLength, int maxLength) {
        if (str == null) {
            return false;
        }
        int length = str.length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * 验证是否为正数
     */
    public static boolean isPositive(Number number) {
        return number != null && number.doubleValue() > 0;
    }

    /**
     * 验证是否为非负数
     */
    public static boolean isNonNegative(Number number) {
        return number != null && number.doubleValue() >= 0;
    }

    /**
     * 验证温度范围（摄氏度）
     */
    public static boolean isValidTemperature(double temperature) {
        return isInRange(temperature, -50.0, 60.0);
    }

    /**
     * 验证湿度范围（百分比）
     */
    public static boolean isValidHumidity(double humidity) {
        return isInRange(humidity, 0.0, 100.0);
    }

    /**
     * 验证pH值范围
     */
    public static boolean isValidPH(double ph) {
        return isInRange(ph, 0.0, 14.0);
    }

    /**
     * 验证EC值范围（电导率）
     */
    public static boolean isValidEC(double ec) {
        return isNonNegative(ec) && ec <= 20.0;
    }

    /**
     * 验证光照强度范围
     */
    public static boolean isValidLightIntensity(double intensity) {
        return isNonNegative(intensity) && intensity <= 200000.0;
    }

    /**
     * 验证用水量范围（升）
     */
    public static boolean isValidWaterAmount(double amount) {
        return isPositive(amount) && amount <= 10000.0;
    }

    /**
     * 验证施肥量范围（升）
     */
    public static boolean isValidFertilizerAmount(double amount) {
        return isPositive(amount) && amount <= 1000.0;
    }

    /**
     * 验证施肥浓度范围（百分比）
     */
    public static boolean isValidFertilizerConcentration(double concentration) {
        return isInRange(concentration, 0.0, 10.0);
    }

    /**
     * 验证灌溉时长范围（分钟）
     */
    public static boolean isValidIrrigationDuration(int duration) {
        return duration > 0 && duration <= 720; // 最大12小时
    }

    /**
     * 验证设备电量范围（百分比）
     */
    public static boolean isValidBatteryLevel(int batteryLevel) {
        return batteryLevel >= 0 && batteryLevel <= 100;
    }

    /**
     * 验证信号强度范围（百分比）
     */
    public static boolean isValidSignalStrength(int signalStrength) {
        return signalStrength >= 0 && signalStrength <= 100;
    }
}