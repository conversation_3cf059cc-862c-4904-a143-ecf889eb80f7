# 智慧农业物联网小程序产品需求说明书（修订版）

> 本文档基于实际代码分析更新，移除了区域概念，保留了实际实现的功能

## 文档信息
- **产品名称**：智慧农业物联网小程序
- **文档版本**：V2.0（基于代码实际功能修订）
- **修订日期**：2024年07月15日
- **修订说明**：根据实际代码实现调整功能描述，简化管理模式

---

## 1. 产品概述

### 1.1 产品背景
智慧农业物联网小程序是一个**专注于设备控制和数据监测的农业管理平台**，通过简化的地块管理模式，为农户提供高效便捷的农业管理工具。

### 1.2 核心功能定位
- **设备远程控制**：水肥一体机远程启停和参数调节
- **实时数据监测**：土壤传感器数据实时展示和预警
- **定时任务管理**：灵活的灌溉和施肥计划设置
- **作物档案管理**：完整的作物生长记录和收获管理
- **操作记录追踪**：设备操作和任务执行的完整日志

### 1.3 简化的管理模式
取消复杂的区域概念，采用**地块直接绑定设备**的简化模式：
```
农场 → 地块 → 设备（直接绑定）
```

---

## 2. 核心功能模块

### 2.1 首页概览模块

#### 功能描述
提供农场整体状况的一站式展示，支持快速操作和状态监控。

#### 主要功能
- **农场信息展示**：名称、面积、地块数量、设备统计
- **天气信息**：实时天气和农业建议
- **设备状态概览**：在线设备数、异常设备提醒
- **今日数据统计**：灌溉时长、施肥量、预警次数
- **快速操作面板**：一键灌溉、设备状态查看
- **消息通知**：预警信息、任务完成提醒

### 2.2 设备控制模块

#### 2.2.1 设备管理
- **设备列表**：显示所有水肥一体机和传感器设备
- **设备状态**：实时在线状态、工作状态、电池电量
- **设备绑定**：扫码或手动添加设备到地块

#### 2.2.2 灌溉控制
**实时控制：**
- 手动启停灌溉设备
- 流量调节（滑块控制）
- 灌溉时长设置
- 紧急停止功能

**定时控制：**
- 复杂的日历视图管理
- 多时段灌溉计划（支持一天多次）
- 重复模式设置（每天/工作日/自定义星期）
- 快速模板应用（晨间灌溉、傍晚灌溉等）

#### 2.2.3 施肥管理
- **配方管理**：预设配方和自定义配方
- **施肥控制**：与灌溉结合的水肥一体化
- **浓度调节**：NPK比例和EC值设置

#### 2.2.4 操作记录
- **操作日志**：所有手动和自动操作记录
- **运行报告**：日/周/月运行统计和效率分析
- **异常记录**：设备故障和数据异常记录
- **数据导出**：支持PDF/Excel格式导出

### 2.3 监测数据模块

#### 2.3.1 实时监测
- **多视图模式**：仪表盘/地图/深度监测视图
- **核心指标**：
  - 土壤湿度（%）
  - 土壤温度（°C）
  - pH值（酸碱度）
  - EC值（电导率）
  - 氮磷钾含量（高级传感器）

#### 2.3.2 历史数据分析
- **ECharts图表**：趋势线、柱状图、饼图展示
- **时间范围**：24小时/7天/30天/自定义
- **数据对比**：多传感器数据对比分析
- **统计信息**：最值、均值、异常统计

#### 2.3.3 预警系统
- **三级预警**：
  - 🟡 轻度预警：数值接近边界
  - 🟠 中度预警：超出正常范围
  - 🔴 重度预警：严重偏离
- **预警处理**：确认预警、解决方案建议
- **通知推送**：小程序内通知、微信模板消息

### 2.4 地块管理模块

#### 2.4.1 地块信息管理
- **基本信息**：名称、面积、坐标、土壤类型
- **地块地图**：可视化地块布局和状态
- **地块编辑**：面积、坐标、土壤类型等信息更新

#### 2.4.2 直接设备绑定（简化模式）
```javascript
// 简化的数据结构
{
  plotId: 'plot_001',
  name: '1号大棚',
  area: 50.0,
  devices: [
    { id: 'device_001', type: 'irrigation', name: '水肥一体机' },
    { id: 'sensor_001', type: 'sensor', name: '土壤传感器' }
  ]
}
```

### 2.5 作物管理模块

#### 2.5.1 作物档案
- **完整的CRUD功能**：添加、查看、编辑、删除作物档案
- **作物信息**：品种、种植日期、预计收获、生长阶段
- **状态管理**：生长中、收获中、已完成、种植失败

#### 2.5.2 生长记录
- **记录类型**：浇水💧、施肥🌱、病虫害防治🐛、修剪✂️、收获🌾
- **详细信息**：操作内容、天气条件、温湿度、操作员
- **图片记录**：支持拍照记录生长状况

#### 2.5.3 收获管理
- **收获记录**：数量、品质等级、价格、收入
- **品质分级**：A级（优）、B级（良）、C级（中）
- **销售记录**：买家、销售渠道、收益统计

---

## 3. 技术实现架构

### 3.1 数据模型（简化版）

#### 农场模型
```javascript
{
  id: 'farm_001',
  name: '张三的有机农场',
  location: '重庆市江北区',
  totalArea: 120.5,
  plotCount: 3,
  deviceCount: 6
}
```

#### 地块模型（移除区域概念）
```javascript
{
  id: 'plot_001',
  farmId: 'farm_001',
  name: '1号大棚',
  area: 50.0,
  coordinates: { latitude: 29.5647, longitude: 106.5507 },
  soilType: '壤土',
  irrigationType: '滴灌',
  currentCrop: {
    name: '番茄',
    plantDate: '2024-03-15',
    progress: 75
  },
  // 直接绑定设备，无区域层级
  devices: ['device_001', 'sensor_001']
}
```

#### 设备模型
```javascript
{
  id: 'device_001',
  type: 'irrigation', // irrigation/sensor
  name: '1号水肥一体机',
  farmId: 'farm_001',
  plotId: 'plot_001', // 直接绑定到地块
  status: {
    online: true,
    working: false,
    batteryLevel: 85
  },
  capabilities: {
    flowRateRange: [10, 200],
    supportsFertilizer: true
  }
}
```

### 3.2 API接口设计

#### 核心接口列表
```javascript
// 设备控制
POST /api/devices/{deviceId}/control
{
  action: 'start_irrigation|stop_irrigation|start_fertilizer',
  parameters: {
    duration: 1800,
    flowRate: 80,
    fertilizer: { enabled: true, formula: 'tomato_bloom' }
  }
}

// 定时任务管理
GET /api/schedules
POST /api/schedules
PUT /api/schedules/{id}
DELETE /api/schedules/{id}

// 操作记录
GET /api/logs/operations?type=irrigation&startDate=xxx
GET /api/logs/reports?period=daily
```

---

## 4. 页面结构和导航

### 4.1 主要页面结构
```
pages/
├── index/index                 # 首页概览
├── device/                     # 设备管理模块
│   ├── device                  # 设备列表
│   ├── irrigation/             # 灌溉控制
│   ├── schedule/               # 定时计划
│   ├── logs/                   # 操作记录
│   └── config/                 # 设备配置
├── monitor/                    # 监测数据模块
│   ├── monitor                 # 实时监测
│   ├── history/                # 历史数据
│   └── alarm/                  # 预警管理
├── farm/                       # 地块管理模块
│   ├── farm                    # 地块列表
│   ├── plot/                   # 地块详情
│   ├── crop/                   # 作物管理
│   └── init/                   # 农场初始化
└── profile/                    # 个人中心
```

### 4.2 底部导航
- **首页**：数据概览和快速操作
- **设备**：设备管理和控制
- **监测**：传感器数据和预警
- **地块**：地块和作物管理
- **我的**：个人设置和帮助

---

## 5. 关键功能特性

### 5.1 定时任务系统
- **可视化日历**：月历视图显示所有计划
- **复杂规则支持**：
  - 多时段：一天内多次灌溉
  - 自定义星期：灵活的重复模式
  - 参数控制：流量、时长、施肥配方
- **快速模板**：晨间灌溉、傍晚灌溉、多时段等预设

### 5.2 完整的操作记录
- **操作日志**：成功/失败状态、详细参数、时间戳
- **运行报告**：效率统计、用量分析、成本核算
- **异常处理**：设备故障记录、预警处理历史

### 5.3 作物全生命周期管理
- **档案管理**：品种、种植时间、预期收获
- **生长记录**：6种操作类型的详细记录
- **收获统计**：产量、品质、收益的完整追踪

---

## 6. 验收标准

### 6.1 核心功能验收
- [ ] 设备可以成功启停和参数调节
- [ ] 定时计划可以正常创建和执行
- [ ] 传感器数据实时展示和历史查询
- [ ] 预警系统正常触发和处理
- [ ] 作物档案和记录完整管理
- [ ] 操作日志准确记录所有操作

### 6.2 用户体验验收
- [ ] 新用户10分钟内完成基本操作
- [ ] 核心操作路径不超过3步
- [ ] 页面加载时间小于3秒
- [ ] 设备控制响应时间小于5秒

---

## 7. 主要变更说明

### 7.1 移除的功能
- ❌ **复杂的区域概念**：取消地块下的区域划分
- ❌ **多阀门控制**：简化为地块级别的设备控制
- ❌ **管理模式选择**：统一使用简化模式

### 7.2 保留并强化的功能
- ✅ **设备直接绑定**：地块直接绑定设备，简化层级
- ✅ **完整的定时系统**：支持复杂的灌溉计划
- ✅ **详细的操作记录**：三种类型的完整记录
- ✅ **作物全周期管理**：从播种到收获的完整追踪

### 7.3 数据结构调整
```javascript
// 旧版本（复杂）
plot: {
  zones: [
    { id: 'zone_001', devices: [...] },
    { id: 'zone_002', devices: [...] }
  ]
}

// 新版本（简化）
plot: {
  devices: ['device_001', 'sensor_001']
}
```

这个修订版PRD准确反映了代码的实际实现，移除了未实现的复杂功能，强化了已经完整实现的核心功能。