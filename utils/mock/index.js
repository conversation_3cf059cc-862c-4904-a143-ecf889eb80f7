/**
 * Mock API 统一入口文件
 * 整合所有模块的Mock数据和API方法
 */

// 导入各个模块
const authModule = require('./auth');
const userModule = require('./user');
const farmModule = require('./farm');
const deviceModule = require('./device');
const sensorModule = require('./sensor');
const scheduleModule = require('./schedule');
const logModule = require('./log');
const alarmModule = require('./alarm');
const notificationModule = require('./notification');
const weatherModule = require('./weather');
const fertilizerModule = require('./fertilizer');

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

// 错误响应格式
function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 验证Token
function verifyToken(token) {
  // 在Mock环境中，简化Token验证，始终返回有效的payload
  return {
    userId: 'user_001',
    username: 'testuser',
    exp: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
  };
}

// 模拟API响应延迟
function simulateDelay(minMs = 200, maxMs = 800) {
  const delay = Math.random() * (maxMs - minMs) + minMs;
  return new Promise(resolve => setTimeout(resolve, delay));
}

// 统一的API处理函数
async function handleApiCall(apiFunction, ...args) {
  try {
    await simulateDelay();
    const result = await apiFunction(...args);
    return result;
  } catch (error) {
    console.error('API调用错误:', error);
    return createErrorResponse(error.message || '服务器内部错误');
  }
}

// 导出统一的Mock API
module.exports = {
  // 初始化
  init() {
    console.log('Mock API 初始化完成');
    // 初始化各个模块
    authModule.init();
    userModule.init();
    farmModule.init();
    deviceModule.init();
    sensorModule.init();
    scheduleModule.init();
    logModule.init();
    alarmModule.init();
    notificationModule.init();
    weatherModule.init();
    fertilizerModule.init();
  },

  // 认证相关API
  auth: {
    login: (data) => handleApiCall(authModule.login, data),
    phoneLogin: (data) => handleApiCall(authModule.phoneLogin, data),
    sendCode: (data) => handleApiCall(authModule.sendCode, data),
    refreshToken: (refreshToken) => handleApiCall(authModule.refreshToken, refreshToken),
    logout: (token) => handleApiCall(authModule.logout, token)
  },

  // 用户相关API
  user: {
    getProfile: (token) => handleApiCall(userModule.getProfile, token),
    updateProfile: (token, data) => handleApiCall(userModule.updateProfile, token, data)
  },

  // 农场相关API
  farms: {
    list: (token) => handleApiCall(farmModule.list, token),
    create: (token, data) => handleApiCall(farmModule.create, token, data),
    getDetail: (token, farmId) => handleApiCall(farmModule.getDetail, token, farmId),
    update: (token, farmId, data) => handleApiCall(farmModule.update, token, farmId, data),
    delete: (token, farmId) => handleApiCall(farmModule.delete, token, farmId)
  },

  // 地块相关API
  plots: {
    list: (token, farmId) => handleApiCall(farmModule.getPlots, token, farmId),
    create: (token, data) => handleApiCall(farmModule.createPlot, token, data),
    getDetail: (token, plotId) => handleApiCall(farmModule.getPlotDetail, token, plotId),
    update: (token, plotId, data) => handleApiCall(farmModule.updatePlot, token, plotId, data),
    delete: (token, plotId) => handleApiCall(farmModule.deletePlot, token, plotId)
  },

  // 设备相关API
  devices: {
    list: (token, params = {}) => handleApiCall(deviceModule.list, token, params),
    getStatus: (token, deviceId) => handleApiCall(deviceModule.getStatus, token, deviceId),
    getStatusSummary: (token) => handleApiCall(deviceModule.getStatusSummary, token),
    control: (token, deviceId, action, parameters) => handleApiCall(deviceModule.control, token, deviceId, action, parameters),
    getConfig: (token, deviceId) => handleApiCall(deviceModule.getConfig, token, deviceId),
    updateConfig: (token, deviceId, config) => handleApiCall(deviceModule.updateConfig, token, deviceId, config)
  },

  // 传感器相关API
  sensors: {
    list: (token, params = {}) => handleApiCall(sensorModule.list, token, params),
    getLatest: (token, sensorId) => handleApiCall(sensorModule.getLatest, token, sensorId),
    getLatestAll: (token) => handleApiCall(sensorModule.getLatestAll, token),
    getHistory: (token, sensorId, params) => handleApiCall(sensorModule.getHistory, token, sensorId, params),
    getRealtimeData: (token, sensorId) => handleApiCall(sensorModule.getRealtimeData, token, sensorId)
  },

  // 调度相关API
  schedules: {
    list: (token, params = {}) => handleApiCall(scheduleModule.list, token, params),
    create: (token, data) => handleApiCall(scheduleModule.create, token, data),
    update: (token, scheduleId, data) => handleApiCall(scheduleModule.update, token, scheduleId, data),
    delete: (token, scheduleId) => handleApiCall(scheduleModule.delete, token, scheduleId),
    duplicate: (token, scheduleId) => handleApiCall(scheduleModule.duplicate, token, scheduleId),
    toggle: (token, scheduleId, enabled) => handleApiCall(scheduleModule.toggle, token, scheduleId, enabled),
    getCalendarData: (token, year, month) => handleApiCall(scheduleModule.getCalendarData, token, year, month),
    getDaySchedules: (token, date) => handleApiCall(scheduleModule.getDaySchedules, token, date),
    getTemplates: (token) => handleApiCall(scheduleModule.getTemplates, token)
  },

  // 日志相关API
  logs: {
    getOperationLogs: (token, params = {}) => handleApiCall(logModule.getOperationLogs, token, params),
    getReports: (token, params = {}) => handleApiCall(logModule.getReports, token, params),
    getAlerts: (token, params = {}) => handleApiCall(logModule.getAlerts, token, params),
    resolveAlert: (token, alertId, solution) => handleApiCall(logModule.resolveAlert, token, alertId, solution),
    exportLogs: (token, params) => handleApiCall(logModule.exportLogs, token, params),
    retryOperation: (token, logId) => handleApiCall(logModule.retryOperation, token, logId)
  },

  // 预警相关API
  alarms: {
    list: (token, params = {}) => handleApiCall(alarmModule.list, token, params),
    acknowledge: (token, alarmId) => handleApiCall(alarmModule.acknowledge, token, alarmId),
    resolve: (token, alarmId, solution) => handleApiCall(alarmModule.resolve, token, alarmId, solution),
    getConfig: (token) => handleApiCall(alarmModule.getConfig, token),
    updateConfig: (token, config) => handleApiCall(alarmModule.updateConfig, token, config)
  },

  // 通知相关API
  notifications: {
    list: (token, params = {}) => handleApiCall(notificationModule.list, token, params),
    markAsRead: (token, notificationId) => handleApiCall(notificationModule.markAsRead, token, notificationId),
    markAllAsRead: (token) => handleApiCall(notificationModule.markAllAsRead, token),
    delete: (token, notificationId) => handleApiCall(notificationModule.delete, token, notificationId)
  },

  // 天气相关API
  weather: {
    getCurrent: (token) => handleApiCall(weatherModule.getCurrent, token),
    getForecast: (token, days = 3) => handleApiCall(weatherModule.getForecast, token, days)
  },

  // 施肥配方相关API
  fertilizer: {
    getFormulas: (token, params) => handleApiCall(fertilizerModule.getFormulas, token, params),
    createFormula: (token, data) => handleApiCall(fertilizerModule.createFormula, token, data),
    updateFormula: (token, formulaId, data) => handleApiCall(fertilizerModule.updateFormula, token, formulaId, data),
    deleteFormula: (token, formulaId) => handleApiCall(fertilizerModule.deleteFormula, token, formulaId),
    getRecords: (token, params) => handleApiCall(fertilizerModule.getRecords, token, params),
    startFertilization: (token, data) => handleApiCall(fertilizerModule.startFertilization, token, data),
    stopFertilization: (token, recordId) => handleApiCall(fertilizerModule.stopFertilization, token, recordId),
    getRecommendations: (token, params) => handleApiCall(fertilizerModule.getRecommendations, token, params)
  },

  // 统计数据API
  getTodayData: (token) => handleApiCall(() => {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    // 获取今日统计数据
    const todayStats = require('./data').getTodayStats();
    return createApiResponse(todayStats);
  }),

  // 任务相关API
  getTasks: (token, params = {}) => handleApiCall(() => {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const tasks = require('./data').getTasks(params);
    return createApiResponse(tasks);
  }),

  createTask: (token, data) => handleApiCall(() => {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const task = require('./data').createTask(data);
    return createApiResponse(task);
  }),

  // 上传相关API
  upload: {
    image: (token, data) => handleApiCall(() => {
      const payload = verifyToken(token);
      if (!payload) {
        return createErrorResponse('无效的访问令牌', 401);
      }

      // 模拟图片上传
      const uploadResult = {
        url: `/mock/images/uploaded_${Date.now()}.jpg`,
        size: Math.floor(Math.random() * 1000000) + 100000,
        type: 'image/jpeg',
        uploadTime: new Date().toISOString()
      };

      return createApiResponse(uploadResult);
    })
  },

  // 辅助函数
  createApiResponse,
  createErrorResponse,
  verifyToken,
  simulateDelay,
  handleApiCall
};