package com.agriculture.iot.service;

import com.agriculture.iot.entity.Plot;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 地块服务接口
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface PlotService extends IService<Plot> {

    /**
     * 分页获取地块列表
     * 
     * @param page 分页对象
     * @param farmId 农场ID
     * @param name 地块名称
     * @param cropType 作物类型
     * @param status 状态
     * @return 地块列表
     */
    IPage<Plot> getPlotList(Page<Plot> page, Long farmId, String name, String cropType, Integer status);

    /**
     * 根据ID获取地块详情
     * 
     * @param id 地块ID
     * @return 地块详情
     */
    Plot getPlotById(Long id);

    /**
     * 创建地块
     * 
     * @param plot 地块信息
     * @return 创建的地块
     */
    Plot createPlot(Plot plot);

    /**
     * 更新地块
     * 
     * @param plot 地块信息
     * @return 更新的地块
     */
    Plot updatePlot(Plot plot);

    /**
     * 删除地块
     * 
     * @param id 地块ID
     * @return 删除结果
     */
    boolean deletePlot(Long id);

    /**
     * 根据农场ID获取地块列表
     * 
     * @param farmId 农场ID
     * @return 地块列表
     */
    List<Plot> getPlotsByFarmId(Long farmId);

    /**
     * 获取地块绑定的设备列表
     * 
     * @param plotId 地块ID
     * @return 设备列表
     */
    List<Object> getPlotDevices(Long plotId);

    /**
     * 为地块绑定设备
     * 
     * @param plotId 地块ID
     * @param deviceId 设备ID
     * @return 绑定结果
     */
    boolean bindDeviceToPlot(Long plotId, Long deviceId);

    /**
     * 解绑设备
     * 
     * @param plotId 地块ID
     * @param deviceId 设备ID
     * @return 解绑结果
     */
    boolean unbindDeviceFromPlot(Long plotId, Long deviceId);

    /**
     * 获取地块统计信息
     * 
     * @param plotId 地块ID
     * @return 统计信息
     */
    Plot.Statistics getPlotStatistics(Long plotId);

    /**
     * 更新地块状态
     * 
     * @param plotId 地块ID
     * @param status 状态
     * @return 更新结果
     */
    boolean updatePlotStatus(Long plotId, Integer status);

    /**
     * 获取地块作物信息
     * 
     * @param plotId 地块ID
     * @return 作物信息
     */
    Plot.CropInfo getPlotCropInfo(Long plotId);

    /**
     * 更新地块作物信息
     * 
     * @param plotId 地块ID
     * @param cropInfo 作物信息
     * @return 更新结果
     */
    boolean updatePlotCropInfo(Long plotId, Plot.CropInfo cropInfo);
}