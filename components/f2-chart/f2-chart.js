/**
 * F2 Chart Component for WeChat Mini-Program
 * 基于AntV F2的移动端图表组件
 */

const F2Chart = require('../../libs/f2-canvas/f2.min.js');

Component({
  properties: {
    // 图表配置
    chartConfig: {
      type: Object,
      value: {}
    },
    // 图表数据
    chartData: {
      type: Array,
      value: []
    },
    // 图表类型
    chartType: {
      type: String,
      value: 'line' // line, area, interval, point
    },
    // 图表尺寸
    width: {
      type: Number,
      value: 350
    },
    height: {
      type: Number,
      value: 300
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 颜色配置
    colors: {
      type: Array,
      value: ['#1890FF', '#13C2C2', '#52C41A', '#FAAD14', '#F5222D', '#722ED1']
    }
  },

  data: {
    canvasId: '',
    isCanvasReady: false,
    chart: null
  },

  lifetimes: {
    attached() {
      this.data.canvasId = `f2-chart-${Math.random().toString(36).substr(2, 9)}`;
      this.setData({
        canvasId: this.data.canvasId
      });
    },

    ready() {
      this.initChart();
    },

    detached() {
      this.destroyChart();
    }
  },

  observers: {
    'chartData, chartType, chartConfig': function(data, type, config) {
      if (this.data.isCanvasReady && data && data.length > 0) {
        this.updateChart();
      }
    }
  },

  methods: {
    // 初始化图表
    initChart() {
      // 优先使用Canvas 2D API
      if (wx.createCanvasContext) {
        this.initWithCanvas2D();
      } else {
        this.initWithLegacyCanvas();
      }
    },

    // 使用Canvas 2D API初始化
    initWithCanvas2D() {
      const query = this.createSelectorQuery();
      query.select(`#${this.data.canvasId}`)
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res[0]) {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');
            
            // 设置画布尺寸
            const dpr = wx.getSystemInfoSync().pixelRatio;
            canvas.width = this.data.width * dpr;
            canvas.height = this.data.height * dpr;
            ctx.scale(dpr, dpr);

            // 创建图表实例
            this.data.chart = new F2Chart({
              canvas: canvas,
              context: ctx,
              width: this.data.width,
              height: this.data.height,
              padding: [20, 20, 50, 40]
            });

            this.setData({
              isCanvasReady: true
            });

            this.updateChart();
          }
        });
    },

    // 使用旧版Canvas API初始化
    initWithLegacyCanvas() {
      const ctx = wx.createCanvasContext(this.data.canvasId, this);
      
      // 创建图表实例
      this.data.chart = new F2Chart({
        context: ctx,
        width: this.data.width,
        height: this.data.height,
        padding: [20, 20, 50, 40]
      });

      this.setData({
        isCanvasReady: true
      });

      this.updateChart();
    },

    // 更新图表
    updateChart() {
      if (!this.data.chart || !this.data.chartData.length) return;

      try {
        const { chartData, chartType, chartConfig, colors } = this.data;
        
        // 设置数据源
        this.data.chart.source(chartData, chartConfig);
        
        // 根据图表类型配置
        let geom;
        switch (chartType) {
          case 'line':
            geom = this.data.chart.line();
            break;
          case 'area':
            geom = this.data.chart.area();
            break;
          case 'interval':
            geom = this.data.chart.interval();
            break;
          case 'point':
            geom = this.data.chart.point();
            break;
          default:
            geom = this.data.chart.line();
        }

        // 配置位置和颜色
        const xField = chartConfig.xField || 'x';
        const yField = chartConfig.yField || 'y';
        const colorField = chartConfig.colorField;
        
        geom.position(xField, yField);
        
        if (colorField) {
          geom.color(colorField, colors);
        } else {
          geom.color(colors[0]);
        }

        // 渲染图表
        this.data.chart.render();

        // 如果是旧版Canvas API，需要手动draw
        if (this.data.chart.ctx && this.data.chart.ctx.draw) {
          this.data.chart.ctx.draw();
        }

        this.triggerEvent('chartReady', { chart: this.data.chart });
      } catch (error) {
        console.error('Chart render error:', error);
        this.triggerEvent('chartError', { error });
      }
    },

    // 销毁图表
    destroyChart() {
      if (this.data.chart) {
        this.data.chart.destroy();
        this.data.chart = null;
      }
    },

    // 导出图表
    exportChart() {
      if (!this.data.chart) return;

      wx.canvasToTempFilePath({
        canvasId: this.data.canvasId,
        success: (res) => {
          this.triggerEvent('chartExported', { tempFilePath: res.tempFilePath });
        },
        fail: (error) => {
          this.triggerEvent('chartError', { error });
        }
      }, this);
    },

    // 图表点击事件
    onChartTap(e) {
      if (this.data.disabled) return;
      
      const { x, y } = e.detail;
      this.triggerEvent('chartTap', { x, y });
    },

    // 图表长按事件
    onChartLongPress(e) {
      if (this.data.disabled) return;
      
      const { x, y } = e.detail;
      this.triggerEvent('chartLongPress', { x, y });
    }
  }
});