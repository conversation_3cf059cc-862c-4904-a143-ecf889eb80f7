<!--常见问题页面-->
<view class="page-container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <view class="search-icon">🔍</view>
      <input 
        class="search-input" 
        placeholder="搜索问题关键词..."
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
      />
    </view>
  </view>

  <!-- 分类筛选 -->
  <view class="category-section">
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-list">
        <view 
          class="category-item {{selectedCategory === 'all' ? 'active' : ''}}"
          bindtap="selectCategory"
          data-category="all"
        >
          <text class="category-name">全部</text>
          <text class="category-count">{{faqList.length}}</text>
        </view>
        
        <view 
          wx:for="{{categories}}" 
          wx:key="id"
          class="category-item {{selectedCategory === item.id ? 'active' : ''}}"
          bindtap="selectCategory"
          data-category="{{item.id}}"
        >
          <text class="category-icon">{{item.icon}}</text>
          <text class="category-name">{{item.name}}</text>
          <text class="category-count">{{item.count}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- FAQ列表 -->
  <view class="faq-section">
    <view wx:if="{{filteredFaqs.length === 0}}" class="empty-state">
      <view class="empty-icon">🤔</view>
      <text class="empty-title">没有找到相关问题</text>
      <text class="empty-desc">试试其他关键词，或联系技术支持</text>
      <button class="btn btn-primary" bindtap="contactSupport">
        联系客服
      </button>
    </view>

    <view wx:else class="faq-list">
      <view 
        wx:for="{{filteredFaqs}}" 
        wx:key="id"
        class="faq-item {{expandedItems.includes(item.id) ? 'expanded' : ''}}"
      >
        <view class="faq-question" bindtap="toggleExpand" data-id="{{item.id}}">
          <view class="question-content">
            <text class="question-text">{{item.question}}</text>
            <view class="question-tags">
              <text 
                wx:for="{{item.tags}}" 
                wx:for-item="tag" 
                wx:key="*this"
                class="tag"
              >
                {{tag}}
              </text>
            </view>
          </view>
          <view class="expand-icon {{expandedItems.includes(item.id) ? 'rotated' : ''}}">
            ▼
          </view>
        </view>

        <view class="faq-answer {{expandedItems.includes(item.id) ? 'show' : ''}}">
          <text class="answer-text">{{item.answer}}</text>
          
          <view class="answer-actions">
            <view class="helpful-section">
              <button 
                class="helpful-btn" 
                bindtap="markHelpful" 
                data-id="{{item.id}}"
              >
                <text class="helpful-icon">👍</text>
                <text class="helpful-text">有帮助 ({{item.helpful}})</text>
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系支持 -->
  <view class="support-section">
    <view class="support-card">
      <view class="support-icon">🎧</view>
      <view class="support-content">
        <text class="support-title">没有找到答案？</text>
        <text class="support-desc">联系我们的技术支持团队获取帮助</text>
      </view>
      <button class="btn btn-outline" bindtap="contactSupport">
        联系客服
      </button>
    </view>
  </view>
</view>