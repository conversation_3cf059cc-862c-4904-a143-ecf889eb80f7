/* 农场初始化页面样式 */

/* 位置输入组件样式 */
.location-input-group {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.location-input {
  flex: 1;
}

.location-btn {
  height: 70rpx;
  padding: 0 20rpx;
  background-color: #1976D2;
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.location-btn:active {
  background-color: #1565C0;
}

.location-coords {
  margin-top: 10rpx;
  padding: 10rpx 15rpx;
  background-color: #E8F5E8;
  border-radius: 6rpx;
  border: 1rpx solid #C8E6C9;
}

.coords-text {
  font-size: 24rpx;
  color: #2E7D32;
  font-family: monospace;
}
.page-container {
  background-color: #F5F5F5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.container {
  padding: 20rpx;
}

/* 进度指示器 */
.progress-header {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.progress-bar {
  height: 8rpx;
  background: #E0E0E0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #2E7D32);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  display: block;
}

/* 步骤内容 */
.step-content {
  margin-bottom: 20rpx;
}

.card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #F0F0F0;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

/* 表单样式 */
.form-section {
  margin-top: 20rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background: white;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #4CAF50;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background: white;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #4CAF50;
}

/* 地块列表 */
.plot-list, .device-list {
  margin-top: 20rpx;
}

.plot-item, .device-item {
  background: #F8F8F8;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  border: 2rpx solid #E8E8E8;
}

.plot-header, .device-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.plot-icon, .device-icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.plot-info, .device-info {
  flex: 1;
  margin-left: 10rpx;
}

.plot-name, .device-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.plot-area, .device-serial {
  font-size: 24rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 30rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
}

/* 区域配置预览 */
.zone-preview {
  margin-top: 20rpx;
}

.preview-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

.summary-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.summary-item {
  background: #F8F8F8;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid #E8E8E8;
}

.summary-value {
  font-size: 32rpx;
  color: #2E7D32;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.setup-complete {
  background: linear-gradient(135deg, #E8F5E8, #F1F8E9);
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  border: 2rpx solid #C8E6C9;
}

.complete-icon {
  font-size: 60rpx;
  display: block;
  margin-bottom: 15rpx;
}

.complete-title {
  font-size: 30rpx;
  color: #2E7D32;
  font-weight: 600;
  display: block;
  margin-bottom: 10rpx;
}

.complete-desc {
  font-size: 26rpx;
  color: #558B2F;
  display: block;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #E0E0E0;
  display: flex;
  gap: 20rpx;
  align-items: center;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.1);
}

.action-buttons .btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: #4CAF50;
  color: white;
}

.btn-outline {
  background: white;
  color: #4CAF50;
  border: 2rpx solid #4CAF50;
}

.btn-text {
  background: transparent;
  color: #999;
  flex: none;
  padding: 0 20rpx;
}

.btn-small {
  height: 60rpx;
  padding: 0 20rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
}

.btn-mini {
  height: 50rpx;
  padding: 0 15rpx;
  font-size: 22rpx;
  border-radius: 4rpx;
}

.btn-danger {
  background: #F44336;
  color: white;
}