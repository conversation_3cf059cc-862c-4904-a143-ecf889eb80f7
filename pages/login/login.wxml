<!--智慧农业登录页面 - 简化版本-->
<view class="login-container">
  <!-- 顶部Logo区域 -->
  <view class="logo-section">
    <view class="logo-canvas-center">
      <canvas
        id="loginLottieCanvas"
        type="2d"
        class="logo-image"
       
      ></canvas>
    </view>
    <text class="app-title">智慧农业</text>
    <text class="app-subtitle">Smart Agriculture IoT</text>
  </view>

  <!-- 登录方式选择 -->
  <view class="login-methods">
    
    <!-- 微信授权登录 -->
    <view class="login-method-card">
      <button 
        type="primary" 
        size="default"
        bindtap="onWxLogin"
        loading="{{wxLoginLoading}}"
        class="wx-login-btn"
      >
        <image src="/assets/wx.png" mode="aspectFit" class="btn-icon" />
        微信授权登录
      </button>
    </view>

    <!-- 手机号登录 -->
    <view class="login-method-card" wx:if="{{showPhoneLogin}}">
      <view class="phone-login-form">
        <input
          placeholder="请输入手机号"
          type="number"
          maxlength="11"
          bindinput="onPhoneChange"
          value="{{phone}}"
          class="phone-input"
        />
        
        <view class="code-input-group">
          <input
            placeholder="验证码"
            type="number"
            maxlength="6"
            bindinput="onCodeChange"
            value="{{code}}"
            class="code-input"
          />
          <button 
            type="default" 
            size="mini"
            bindtap="sendCode"
            disabled="{{!canSendCode}}"
            loading="{{sendingCode}}"
            class="send-code-btn"
          >
            {{codeButtonText}}
          </button>
        </view>

        <button 
          type="primary" 
          size="default"
          bindtap="onPhoneLogin"
          loading="{{phoneLoginLoading}}"
          class="phone-login-btn"
        >
          手机号登录
        </button>
      </view>
    </view>

    <!-- 切换登录方式 -->
    <view class="switch-login-method">
      <button 
        type="default" 
        size="mini"
        bindtap="toggleLoginMethod"
        class="switch-btn"
      >
        {{showPhoneLogin ? '使用微信登录' : '使用手机号登录'}}
      </button>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer-info">
    <text class="version-text">版本 {{version}}</text>
    <text class="copyright-text">© 2024 智慧农业科技</text>
  </view>
</view>