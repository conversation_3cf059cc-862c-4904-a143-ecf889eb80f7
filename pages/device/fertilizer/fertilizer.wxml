<!--施肥管理页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 标签导航 -->
    <view class="tab-nav">
      <view class="tab-item {{activeTab === 'control' ? 'active' : ''}}" 
            bindtap="switchTab" data-tab="control">
        实时控制
      </view>
      <view class="tab-item {{activeTab === 'schedule' ? 'active' : ''}}" 
            bindtap="switchTab" data-tab="schedule">
        定时任务
      </view>
      <view class="tab-item {{activeTab === 'formula' ? 'active' : ''}}" 
            bindtap="switchTab" data-tab="formula">
        配方管理
      </view>
    </view>
    
    <!-- 设备状态面板 -->
    <view class="card">
      <view class="device-status-panel">
        <view class="status-header">
          <text class="device-name">施肥系统-01</text>
          <view class="status-indicator {{deviceStatus.class}}">
            <text class="status-dot"></text>
            <text class="status-text">{{deviceStatus.text}}</text>
          </view>
        </view>
        <view class="status-info">
          <view class="info-item">
            <text class="info-label">肥料A余量</text>
            <text class="info-value">{{deviceInfo.fertilizerA}}%</text>
          </view>
          <view class="info-item">
            <text class="info-label">肥料B余量</text>
            <text class="info-value">{{deviceInfo.fertilizerB}}%</text>
          </view>
          <view class="info-item">
            <text class="info-label">EC值</text>
            <text class="info-value">{{deviceInfo.ecValue}} mS/cm</text>
          </view>
          <view class="info-item">
            <text class="info-label">pH值</text>
            <text class="info-value">{{deviceInfo.phValue}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 实时控制标签页 -->
    <view wx:if="{{activeTab === 'control'}}">
      <view class="card">
        <view class="card-title">实时施肥控制</view>
        <view class="control-panel">
          
          <!-- 主控制开关 -->
          <view class="main-control">
            <view class="control-header">
              <text class="control-title">施肥系统</text>
              <switch checked="{{fertilizerRunning}}" bindchange="onFertilizerToggle" 
                      disabled="{{!deviceStatus.online}}" class="main-switch"/>
            </view>
            <view class="control-status" wx:if="{{fertilizerRunning}}">
              <text class="running-text">🌱 系统运行中...</text>
              <text class="remaining-time">剩余时间: {{remainingTime}}</text>
            </view>
          </view>

          <!-- 控制参数 -->
          <view class="control-params" wx:if="{{fertilizerRunning}}">
            
            <!-- 配方选择 -->
            <view class="param-item">
              <text class="param-label">施肥配方</text>
              <picker range="{{formulaList}}" range-key="name" value="{{selectedFormula}}" 
                      bindchange="onFormulaChange" class="formula-picker">
                <view class="picker-view">
                  {{formulaList[selectedFormula].name}}
                </view>
              </picker>
            </view>

            <!-- 浓度控制 -->
            <view class="param-item concentration-control">
              <view class="param-header">
                <text class="param-label">施肥浓度</text>
                <view class="concentration-display">
                  <text class="concentration-value">{{concentration}}</text>
                  <text class="concentration-unit">%</text>
                </view>
              </view>
              <view class="custom-slider">
                <view class="slider-track">
                  <view class="slider-fill" style="width: {{(concentration - 10) / 90 * 100}}%"></view>
                  <view class="slider-thumb" 
                        style="left: {{(concentration - 10) / 90 * 100}}%" 
                        bindtouchstart="onConcentrationTouchStart"
                        bindtouchmove="onConcentrationTouchMove"
                        bindtouchend="onConcentrationTouchEnd">
                    <view class="thumb-inner"></view>
                  </view>
                </view>
                <view class="slider-labels">
                  <text class="slider-min">10%</text>
                  <text class="slider-max">100%</text>
                </view>
              </view>
            </view>

            <!-- 施肥时长 -->
            <view class="param-item">
              <text class="param-label">施肥时长</text>
              <view class="param-control">
                <input type="number" value="{{duration}}" bindinput="onDurationInput" 
                       placeholder="输入分钟" class="duration-input"/>
                <text class="param-unit">分钟</text>
              </view>
            </view>

            <!-- EC/pH目标值 -->
            <view class="target-values">
              <view class="target-item">
                <text class="target-label">目标EC值</text>
                <input type="digit" value="{{targetEC}}" bindinput="onTargetECInput" 
                       placeholder="1.5" class="target-input"/>
                <text class="target-unit">mS/cm</text>
              </view>
              <view class="target-item">
                <text class="target-label">目标pH值</text>
                <input type="digit" value="{{targetPH}}" bindinput="onTargetPHInput" 
                       placeholder="6.5" class="target-input"/>
              </view>
            </view>

            <!-- 快捷设置 -->
            <view class="quick-settings">
              <text class="section-title">快捷设置</text>
              <view class="preset-buttons">
                <button class="preset-btn" bindtap="applyPreset" data-preset="seedling">
                  幼苗期配方
                </button>
                <button class="preset-btn" bindtap="applyPreset" data-preset="growth">
                  生长期配方
                </button>
                <button class="preset-btn" bindtap="applyPreset" data-preset="flowering">
                  开花期配方
                </button>
                <button class="preset-btn" bindtap="applyPreset" data-preset="fruiting">
                  结果期配方
                </button>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="control-actions">
              <button class="btn btn-outline" bindtap="pauseFertilizer">
                ⏸️ 暂停
              </button>
              <button class="btn btn-secondary" bindtap="flushSystem">
                🚿 冲洗
              </button>
              <button class="btn btn-danger" bindtap="stopFertilizer">
                ⏹️ 停止
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 定时任务标签页 -->
    <view wx:if="{{activeTab === 'schedule'}}">
      <view class="card">
        <view class="card-title">
          <text>定时施肥任务</text>
          <button class="btn btn-small btn-primary" bindtap="addSchedule">
            ➕ 新增任务
          </button>
        </view>
        
        <view class="schedule-list" wx:if="{{scheduleList.length > 0}}">
          <view class="schedule-item" wx:for="{{scheduleList}}" wx:key="id">
            <view class="schedule-header">
              <view class="schedule-info">
                <text class="schedule-name">{{item.name}}</text>
                <text class="schedule-time">{{item.time}} | {{item.days.join(', ')}}</text>
              </view>
              <switch checked="{{item.enabled}}" bindchange="toggleSchedule" data-id="{{item.id}}"/>
            </view>
            <view class="schedule-details">
              <view class="detail-row">
                <text class="detail-label">配方:</text>
                <text class="detail-value">{{item.formulaName}}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">浓度:</text>
                <text class="detail-value">{{item.concentration}}%</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">时长:</text>
                <text class="detail-value">{{item.duration}}分钟</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">下次运行:</text>
                <text class="detail-value">{{item.nextRun}}</text>
              </view>
            </view>
            <view class="schedule-actions">
              <button class="btn btn-small btn-outline" bindtap="editSchedule" data-id="{{item.id}}">
                编辑
              </button>
              <button class="btn btn-small btn-danger" bindtap="deleteSchedule" data-id="{{item.id}}">
                删除
              </button>
            </view>
          </view>
        </view>
        
        <view class="no-schedule" wx:else>
          <text>暂无定时任务</text>
        </view>
      </view>
    </view>

    <!-- 配方管理标签页 -->
    <view wx:if="{{activeTab === 'formula'}}">
      <view class="card">
        <view class="card-title">
          <text>施肥配方库</text>
          <button class="btn btn-small btn-primary" bindtap="addFormula">
            ➕ 新增配方
          </button>
        </view>
        
        <view class="formula-list">
          <view class="formula-item {{item.active ? 'active' : ''}}" 
                wx:for="{{formulaList}}" wx:key="id"
                bindtap="selectFormula" data-id="{{item.id}}">
            <view class="formula-header">
              <view class="formula-info">
                <text class="formula-name">{{item.name}}</text>
                <text class="formula-crop">{{item.crop}}</text>
              </view>
              <view class="formula-type {{item.stage}}">
                {{item.stageText}}
              </view>
            </view>
            <view class="formula-details">
              <view class="nutrient-ratio">
                <view class="nutrient-item">
                  <text class="nutrient-label">N</text>
                  <text class="nutrient-value">{{item.nitrogen}}%</text>
                </view>
                <view class="nutrient-item">
                  <text class="nutrient-label">P</text>
                  <text class="nutrient-value">{{item.phosphorus}}%</text>
                </view>
                <view class="nutrient-item">
                  <text class="nutrient-label">K</text>
                  <text class="nutrient-value">{{item.potassium}}%</text>
                </view>
              </view>
              <view class="formula-params">
                <text class="param-text">EC: {{item.ec}} mS/cm</text>
                <text class="param-text">pH: {{item.ph}}</text>
              </view>
            </view>
            <view class="formula-actions">
              <button class="btn btn-small btn-outline" bindtap="editFormula" data-id="{{item.id}}">
                编辑
              </button>
              <button class="btn btn-small btn-outline" bindtap="duplicateFormula" data-id="{{item.id}}">
                复制
              </button>
              <button class="btn btn-small btn-danger" bindtap="deleteFormula" data-id="{{item.id}}" 
                      wx:if="{{!item.preset}}">
                删除
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 定时任务编辑弹窗 -->
    <view class="schedule-modal" wx:if="{{showScheduleEdit}}">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">{{currentSchedule.id ? '编辑' : '新增'}}定时任务</text>
          <button class="btn btn-text" bindtap="cancelScheduleEdit">✕</button>
        </view>
        
        <view class="modal-body">
          <view class="form-group">
            <text class="form-label">任务名称</text>
            <input class="form-input" value="{{currentSchedule.name}}" 
                   bindinput="onScheduleNameInput" placeholder="请输入任务名称"/>
          </view>
          
          <view class="form-group">
            <text class="form-label">执行时间</text>
            <picker mode="time" value="{{currentSchedule.time}}" 
                    bindchange="onScheduleTimeChange" class="time-picker">
              <view class="picker-view">{{currentSchedule.time}}</view>
            </picker>
          </view>
          
          <view class="form-group">
            <text class="form-label">选择配方</text>
            <picker range="{{formulaList}}" range-key="name" 
                    value="{{currentSchedule.formulaId}}" 
                    bindchange="onScheduleFormulaChange" class="formula-picker">
              <view class="picker-view">{{currentSchedule.formulaName}}</view>
            </picker>
          </view>
          
          <view class="form-group">
            <text class="form-label">施肥浓度</text>
            <view class="slider-group">
              <slider value="{{currentSchedule.concentration}}" min="10" max="100" step="5" 
                      bindchanging="onScheduleConcentrationChange" show-value/>
              <text class="unit-text">%</text>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label">施肥时长</text>
            <view class="input-group">
              <input class="form-input" type="number" value="{{currentSchedule.duration}}" 
                     bindinput="onScheduleDurationInput" placeholder="输入分钟"/>
              <text class="unit-text">分钟</text>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label">执行天数</text>
            <checkbox-group bindchange="onScheduleDaysChange">
              <view class="checkbox-row">
                <label class="checkbox-item" wx:for="{{['周一', '周二', '周三', '周四', '周五', '周六', '周日']}}" wx:key="*this">
                  <checkbox value="{{index}}" checked="{{currentSchedule.days.indexOf(item) !== -1}}"/>
                  <text class="checkbox-text">{{item}}</text>
                </label>
              </view>
            </checkbox-group>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="btn btn-outline" bindtap="cancelScheduleEdit">取消</button>
          <button class="btn btn-primary" bindtap="saveSchedule">保存</button>
        </view>
      </view>
    </view>

  </view>
</view>