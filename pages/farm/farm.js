const api = require('../../utils/api.js');
const app = getApp();

Page({
  data: {
    currentTab: 'plots', // plots, crops, devices
    showMap: true,
    selectedPlotId: 1, // 当前选中的地块ID（用于地图高亮等）
    
    farmInfo: {
      name: '绿野生态农场',
      owner: '张三',
      location: '江苏省苏州市吴中区',
      totalArea: 120,
      plotCount: 4,
      cropTypes: 3,
      deviceCount: 8
    },
    
    weatherInfo: {
      temperature: 26,
      description: '晴',
      icon: '☀️'
    },

    // 地块地图数据
    plotMapData: [
      {
        id: 1,
        name: '1号大棚',
        area: 25,
        status: 'growing',
        x: 10, y: 15, width: 35, height: 30
      },
      {
        id: 2,
        name: '2号大棚', 
        area: 30,
        status: 'flowering',
        x: 50, y: 15, width: 35, height: 30
      },
      {
        id: 3,
        name: '露天菜地A',
        area: 20,
        status: 'ready',
        x: 10, y: 55, width: 25, height: 35
      },
      {
        id: 4,
        name: '果园区',
        area: 35,
        status: 'harvest',
        x: 40, y: 55, width: 45, height: 35
      }
    ],

    plotList: [
      {
        id: 1,
        name: '1号大棚',
        area: 25,
        coordinates: '31.2304°N, 120.5853°E',
        soilType: '壤土',
        drainage: '良好',
        irrigationType: '滴灌',
        slope: '平地',
        status: 'growing',
        statusText: '生长期',
        currentCrop: {
          name: '番茄',
          plantDate: '2024-03-15',
          harvestDate: '2024-07-15',
          progress: 75
        },
        devices: [
          { id: 1, name: '水肥一体机-01', icon: '💧', status: 'online', statusText: '在线' },
          { id: 2, name: '土壤传感器-01', icon: '📡', status: 'online', statusText: '在线' }
        ]
      },
      {
        id: 2,
        name: '2号大棚',
        area: 30,
        coordinates: '31.2308°N, 120.5860°E',
        soilType: '沙壤土',
        drainage: '良好',
        irrigationType: '喷灌',
        slope: '微坡',
        status: 'flowering',
        statusText: '开花期',
        currentCrop: {
          name: '黄瓜',
          plantDate: '2024-04-01',
          harvestDate: '2024-08-01',
          progress: 60
        },
        devices: [
          { id: 3, name: '水肥一体机-02', icon: '💧', status: 'online', statusText: '在线' },
          { id: 4, name: '土壤传感器-02', icon: '📡', status: 'warning', statusText: '信号弱' },
          { id: 5, name: '环境监测站', icon: '🌡️', status: 'online', statusText: '在线' }
        ]
      },
      {
        id: 3,
        name: '露天菜地A',
        area: 20,
        coordinates: '31.2312°N, 120.5845°E',
        soilType: '沙壤土',
        drainage: '良好',
        irrigationType: '滴灌',
        slope: '平地',
        status: 'ready',
        statusText: '待播种',
        currentCrop: {
          name: '未种植',
          plantDate: '--',
          harvestDate: '--',
          progress: 0
        },
        devices: [
          { id: 6, name: '土壤传感器-03', icon: '📡', status: 'offline', statusText: '离线' }
        ]
      },
      {
        id: 4,
        name: '果园区',
        area: 35,
        coordinates: '31.2298°N, 120.5870°E',
        soilType: '壤土',
        drainage: '良好',
        irrigationType: '喷灌',
        slope: '缓坡',
        status: 'harvest',
        statusText: '收获期',
        currentCrop: {
          name: '草莓',
          plantDate: '2023-10-15',
          harvestDate: '2024-06-15',
          progress: 95
        },
        devices: [
          { id: 7, name: '环境监测站-02', icon: '🌡️', status: 'online', statusText: '在线' },
          { id: 8, name: '土壤传感器-04', icon: '📡', status: 'online', statusText: '在线' }
        ]
      }
    ],

    // 设备绑定数据
    plotDeviceMap: [
      {
        plotId: 1,
        plotName: '1号大棚',
        deviceCount: 2,
        categories: [
          {
            type: 'irrigation',
            name: '灌溉设备',
            icon: '💧',
            devices: [
              {
                id: 1,
                name: '水肥一体机-01',
                model: 'WF-2000',
                location: '大棚北侧',
                status: 'online',
                statusText: '在线'
              }
            ]
          },
          {
            type: 'sensor',
            name: '传感设备',
            icon: '📡',
            devices: [
              {
                id: 2,
                name: '土壤传感器-01',
                model: 'SS-5TM',
                location: '中央区域',
                status: 'online',
                statusText: '在线'
              }
            ]
          }
        ]
      }
    ],

    // 权限设置
    devicePermissions: [
      {
        userId: 1,
        userName: '张三',
        role: '农场主',
        avatar: '👨‍🌾',
        permissions: {
          control: true,
          view: true,
          config: true
        }
      },
      {
        userId: 2,
        userName: '李四',
        role: '技术员',
        avatar: '👨‍🔧',
        permissions: {
          control: true,
          view: true,
          config: false
        }
      },
      {
        userId: 3,
        userName: '王五',
        role: '工人',
        avatar: '👨‍💼',
        permissions: {
          control: false,
          view: true,
          config: false
        }
      }
    ],

  },

  onLoad: function() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.loadFarmData();
    this.updateFarmStats();
    this.updateSelectedPlotData();
  },

  onShow: function() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.refreshData();
  },

  onPullDownRefresh: function() {
    var that = this;
    this.refreshData();
    setTimeout(function() {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  loadFarmData: function() {
    console.log('加载农场数据');
    
    // 获取农场列表
    api.getFarms().then(res => {
      if (res.data && res.data.farms && res.data.farms.length > 0) {
        const farm = res.data.farms[0]; // 使用第一个农场
        this.setData({
          farmInfo: {
            name: farm.name,
            owner: farm.ownerId,
            location: farm.location,
            totalArea: farm.totalArea,
            plotCount: farm.plotCount,
            deviceCount: farm.deviceCount,
            cropTypes: 3 // 默认值，可以根据实际情况计算
          }
        });
        
        // 加载地块数据
        return api.getPlots(farm.id);
      }
    }).then(res => {
      if (res && res.data && res.data.plots) {
        // 转换地块数据格式
        const plotMapData = res.data.plots.map((plot, index) => ({
          id: plot.id,
          name: plot.name,
          area: plot.area,
          status: plot.currentCrop ? 'growing' : 'empty',
          x: 10 + (index % 2) * 40, // 简单的布局计算
          y: 15 + Math.floor(index / 2) * 40,
          width: 35,
          height: 30,
          crop: plot.currentCrop
        }));
        
        this.setData({
          plotMapData: plotMapData
        });
      }
    }).catch(err => {
      console.error('加载农场数据失败:', err);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },

  refreshData: function() {
    this.loadFarmData();
    this.updateFarmStats();
    this.updateSelectedPlotData();
  },

  // 更新农场统计数据
  updateFarmStats: function() {
    var totalArea = 0;
    var totalDevices = 0;
    var activeCrops = 0;
    
    // 计算总面积和设备数量
    this.data.plotList.forEach(function(plot) {
      totalArea += plot.area;
      totalDevices += plot.devices.length;
      if (plot.currentCrop.name !== '未种植') {
        activeCrops++;
      }
    });
    
    var farmInfo = Object.assign({}, this.data.farmInfo, {
      totalArea: totalArea,
      plotCount: this.data.plotList.length,
      cropTypes: activeCrops,
      deviceCount: totalDevices
    });
    
    this.setData({
      farmInfo: farmInfo
    });
  },

  // 更新选中地块的数据（用于内部逻辑，不显示在UI上）
  updateSelectedPlotData: function() {
    var that = this;
    var selectedPlot = this.data.plotList.find(function(plot) {
      return plot.id === that.data.selectedPlotId;
    });
    
    if (selectedPlot) {
      this.setData({
        selectedPlot: selectedPlot
      });
    }
  },

  // 切换Tab
  switchTab: function(e) {
    var tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  // 切换地图/列表视图
  toggleMapView: function() {
    this.setData({
      showMap: !this.data.showMap
    });
  },

  // 地块选择
  selectPlot: function(e) {
    var plotId = parseInt(e.currentTarget.dataset.id);
    this.setData({
      selectedPlotId: plotId
    });
    
    var that = this;
    var plot = this.data.plotList.find(function(p) {
      return p.id === plotId;
    });
    
    if (plot) {
      wx.showModal({
        title: plot.name + ' - 快速查看',
        content: '面积: ' + plot.area + '亩\n状态: ' + plot.statusText + '\n当前作物: ' + plot.currentCrop.name + '\n设备数量: ' + plot.devices.length + '台',
        confirmText: '详细信息',
        cancelText: '关闭',
        success: function(res) {
          if (res.confirm) {
            that.viewPlotDetail(e);
          }
        }
      });
    }
  },

  // 地块管理相关方法
  addPlot: function() {
    // 直接跳转到地块配置页面新增模式
    wx.navigateTo({
      url: '/pages/farm/plot/plot?mode=add'
    });
  },


  viewPlotDetail: function(e) {
    var that = this;
    var plotId = parseInt(e.currentTarget.dataset.id);
    var plot = this.data.plotList.find(function(p) {
      return p.id === plotId;
    });
    
    if (plot) {
      wx.showModal({
        title: plot.name + ' - 详细信息',
        content: '面积: ' + plot.area + '亩\n坐标: ' + plot.coordinates + '\n土壤: ' + plot.soilType + '\n排水: ' + plot.drainage + '\n灌溉: ' + plot.irrigationType + '\n状态: ' + plot.statusText + '\n当前作物: ' + plot.currentCrop.name,
        showCancel: false
      });
    }
  },

  editPlotInfo: function(e) {
    var plotId = parseInt(e.currentTarget.dataset.id);
    var that = this;
    var plot = this.data.plotList.find(function(p) {
      return p.id === plotId;
    });
    
    wx.showActionSheet({
      itemList: ['编辑基本信息', '更新地理位置', '修改灌溉配置', '调整种植规划'],
      success: function(res) {
        switch(res.tapIndex) {
          case 0:
            that.editBasicInfo(plotId, plot);
            break;
          case 1:
            that.editLocation(plotId, plot);
            break;
          case 2:
            that.editIrrigation(plotId, plot);
            break;
          case 3:
            that.editPlanting(plotId, plot);
            break;
        }
      }
    });
  },

  editBasicInfo: function(plotId, plot) {
    var that = this;
    wx.showModal({
      title: '编辑' + plot.name + '基本信息',
      editable: true,
      placeholderText: '请输入新的地块面积(亩)',
      success: function(res) {
        if (res.confirm && res.content) {
          var newArea = parseFloat(res.content);
          if (!isNaN(newArea) && newArea > 0) {
            var plotList = that.data.plotList.map(function(p) {
              if (p.id === plotId) {
                return Object.assign({}, p, { area: newArea });
              }
              return p;
            });
            
            that.setData({ plotList: plotList });
            that.updateFarmStats();
            that.updateSelectedPlotData();
            
            wx.showToast({
              title: '面积更新成功',
              icon: 'success'
            });
          } else {
            wx.showToast({
              title: '请输入有效的面积数值',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  editLocation: function(plotId, plot) {
    var that = this;
    wx.showActionSheet({
      itemList: ['手动输入坐标', '地图选择位置', '修改土壤类型', '更新排水状况'],
      success: function(res) {
        switch(res.tapIndex) {
          case 0:
            that.inputCoordinates(plotId, plot);
            break;
          case 1:
            wx.showToast({ title: '地图选择功能开发中', icon: 'none' });
            break;
          case 2:
            that.editSoilType(plotId, plot);
            break;
          case 3:
            that.editDrainage(plotId, plot);
            break;
        }
      }
    });
  },

  inputCoordinates: function(plotId, plot) {
    var that = this;
    wx.showModal({
      title: '输入地理坐标',
      editable: true,
      placeholderText: '格式: 31.2304°N, 120.5853°E',
      success: function(res) {
        if (res.confirm && res.content) {
          var plotList = that.data.plotList.map(function(p) {
            if (p.id === plotId) {
              return Object.assign({}, p, { coordinates: res.content });
            }
            return p;
          });
          
          that.setData({ plotList: plotList });
          that.updateSelectedPlotData();
          
          wx.showToast({
            title: '坐标更新成功',
            icon: 'success'
          });
        }
      }
    });
  },

  editSoilType: function(plotId, plot) {
    var that = this;
    wx.showActionSheet({
      itemList: ['壤土', '沙壤土', '黏土', '沙土', '腐殖土', '其他'],
      success: function(res) {
        var soilTypes = ['壤土', '沙壤土', '黏土', '沙土', '腐殖土', '其他'];
        var selectedSoil = soilTypes[res.tapIndex];
        
        if (selectedSoil === '其他') {
          wx.showModal({
            title: '自定义土壤类型',
            editable: true,
            placeholderText: '请输入土壤类型',
            success: function(modalRes) {
              if (modalRes.confirm && modalRes.content) {
                that.updatePlotSoilType(plotId, modalRes.content);
              }
            }
          });
        } else {
          that.updatePlotSoilType(plotId, selectedSoil);
        }
      }
    });
  },

  updatePlotSoilType: function(plotId, soilType) {
    var plotList = this.data.plotList.map(function(p) {
      if (p.id === plotId) {
        return Object.assign({}, p, { soilType: soilType });
      }
      return p;
    });
    
    this.setData({ plotList: plotList });
    this.updateSelectedPlotData();
    
    wx.showToast({
      title: '土壤类型更新成功',
      icon: 'success'
    });
  },

  editDrainage: function(plotId, plot) {
    var that = this;
    wx.showActionSheet({
      itemList: ['良好', '一般', '较差', '需要改善'],
      success: function(res) {
        var drainageStatus = ['良好', '一般', '较差', '需要改善'];
        var selectedDrainage = drainageStatus[res.tapIndex];
        
        var plotList = that.data.plotList.map(function(p) {
          if (p.id === plotId) {
            return Object.assign({}, p, { drainage: selectedDrainage });
          }
          return p;
        });
        
        that.setData({ plotList: plotList });
        that.updateSelectedPlotData();
        
        wx.showToast({
          title: '排水状况更新成功',
          icon: 'success'
        });
      }
    });
  },

  editIrrigation: function(plotId, plot) {
    var that = this;
    wx.showActionSheet({
      itemList: ['滴灌', '喷灌', '漫灌', '微喷', '管道灌溉', '其他'],
      success: function(res) {
        var irrigationTypes = ['滴灌', '喷灌', '漫灌', '微喷', '管道灌溉', '其他'];
        var selectedType = irrigationTypes[res.tapIndex];
        
        if (selectedType === '其他') {
          wx.showModal({
            title: '自定义灌溉方式',
            editable: true,
            placeholderText: '请输入灌溉方式',
            success: function(modalRes) {
              if (modalRes.confirm && modalRes.content) {
                that.updatePlotIrrigation(plotId, modalRes.content);
              }
            }
          });
        } else {
          that.updatePlotIrrigation(plotId, selectedType);
        }
      }
    });
  },

  updatePlotIrrigation: function(plotId, irrigationType) {
    var plotList = this.data.plotList.map(function(p) {
      if (p.id === plotId) {
        return Object.assign({}, p, { irrigationType: irrigationType });
      }
      return p;
    });
    
    this.setData({ plotList: plotList });
    this.updateSelectedPlotData();
    
    wx.showToast({
      title: '灌溉方式更新成功',
      icon: 'success'
    });
  },

  editPlanting: function(plotId, plot) {
    var that = this;
    wx.showActionSheet({
      itemList: ['更换作物', '设置种植时间', '调整收获期', '查看种植历史'],
      success: function(res) {
        switch(res.tapIndex) {
          case 0:
            that.changeCrop(plotId, plot);
            break;
          case 1:
            that.setPlantingTime(plotId, plot);
            break;
          case 2:
            that.setHarvestTime(plotId, plot);
            break;
          case 3:
            that.viewPlantingHistory(plotId, plot);
            break;
        }
      }
    });
  },

  changeCrop: function(plotId, plot) {
    var that = this;
    wx.showActionSheet({
      itemList: ['番茄', '黄瓜', '茄子', '辣椒', '草莓', '清空作物', '其他'],
      success: function(res) {
        var crops = ['番茄', '黄瓜', '茄子', '辣椒', '草莓', '', '其他'];
        var selectedCrop = crops[res.tapIndex];
        
        if (selectedCrop === '其他') {
          wx.showModal({
            title: '自定义作物',
            editable: true,
            placeholderText: '请输入作物名称',
            success: function(modalRes) {
              if (modalRes.confirm && modalRes.content) {
                that.updatePlotCrop(plotId, modalRes.content);
              }
            }
          });
        } else if (selectedCrop === '') {
          that.updatePlotCrop(plotId, '未种植');
        } else {
          that.updatePlotCrop(plotId, selectedCrop);
        }
      }
    });
  },

  updatePlotCrop: function(plotId, cropName) {
    var plotList = this.data.plotList.map(function(p) {
      if (p.id === plotId) {
        var currentCrop = cropName === '未种植' ? 
          { name: '未种植', plantDate: '--', harvestDate: '--', progress: 0 } :
          { name: cropName, plantDate: new Date().toISOString().split('T')[0], harvestDate: '待设定', progress: 10 };
        
        return Object.assign({}, p, { 
          currentCrop: currentCrop,
          status: cropName === '未种植' ? 'ready' : 'growing',
          statusText: cropName === '未种植' ? '待播种' : '生长期'
        });
      }
      return p;
    });
    
    this.setData({ plotList: plotList });
    this.updateFarmStats();
    this.updateSelectedPlotData();
    
    wx.showToast({
      title: '作物更新成功',
      icon: 'success'
    });
  },

  setPlantingTime: function(plotId, plot) {
    var that = this;
    wx.showModal({
      title: '设置种植时间',
      content: '当前种植时间: ' + plot.currentCrop.plantDate,
      editable: true,
      placeholderText: '格式: 2024-07-08',
      success: function(res) {
        if (res.confirm && res.content) {
          var plotList = that.data.plotList.map(function(p) {
            if (p.id === plotId) {
              var currentCrop = Object.assign({}, p.currentCrop, { plantDate: res.content });
              return Object.assign({}, p, { currentCrop: currentCrop });
            }
            return p;
          });
          
          that.setData({ plotList: plotList });
          that.updateSelectedPlotData();
          
          wx.showToast({
            title: '种植时间更新成功',
            icon: 'success'
          });
        }
      }
    });
  },

  setHarvestTime: function(plotId, plot) {
    var that = this;
    wx.showModal({
      title: '设置收获时间',
      content: '当前收获时间: ' + plot.currentCrop.harvestDate,
      editable: true,
      placeholderText: '格式: 2024-10-08',
      success: function(res) {
        if (res.confirm && res.content) {
          var plotList = that.data.plotList.map(function(p) {
            if (p.id === plotId) {
              var currentCrop = Object.assign({}, p.currentCrop, { harvestDate: res.content });
              return Object.assign({}, p, { currentCrop: currentCrop });
            }
            return p;
          });
          
          that.setData({ plotList: plotList });
          that.updateSelectedPlotData();
          
          wx.showToast({
            title: '收获时间更新成功',
            icon: 'success'
          });
        }
      }
    });
  },

  viewPlantingHistory: function(plotId, plot) {
    wx.showModal({
      title: plot.name + ' - 种植历史',
      content: '历史记录:\n- 2024年春季: 番茄 (已收获)\n- 2023年秋季: 黄瓜 (已收获)\n- 2023年夏季: 茄子 (已收获)\n\n当前: ' + plot.currentCrop.name,
      showCancel: false
    });
  },

  viewPlotMap: function(e) {
    var plotId = parseInt(e.currentTarget.dataset.id);
    var that = this;
    var plot = this.data.plotList.find(function(p) {
      return p.id === plotId;
    });
    
    wx.showModal({
      title: '地块位置信息',
      content: '地块名称: ' + plot.name + '\n地理坐标: ' + plot.coordinates + '\n地形坡度: ' + plot.slope + '\n排水状况: ' + plot.drainage + '\n\n提示: 点击确定在地图中查看详细位置',
      confirmText: '查看地图',
      success: function(res) {
        if (res.confirm) {
          wx.showToast({
            title: '地图功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  managePlotDevices: function(e) {
    var plotId = parseInt(e.currentTarget.dataset.id);
    var that = this;
    var plot = this.data.plotList.find(function(p) {
      return p.id === plotId;
    });
    
    if (plot.devices.length === 0) {
      wx.showModal({
        title: '设备管理',
        content: '该地块暂无关联设备，是否绑定新设备？',
        confirmText: '绑定设备',
        success: function(res) {
          if (res.confirm) {
            that.bindDeviceToPlot(plotId);
          }
        }
      });
    } else {
      wx.showActionSheet({
        itemList: ['查看设备状态', '添加新设备', '设备配置', '解绑设备'],
        success: function(res) {
          var actions = ['设备状态', '添加设备', '设备配置', '解绑设备'];
          wx.showToast({
            title: actions[res.tapIndex] + '操作',
            icon: 'none'
          });
        }
      });
    }
  },

  bindDeviceToPlot: function(plotId) {
    var that = this;
    wx.showActionSheet({
      itemList: ['水肥一体机', '土壤传感器', '环境监测站', '摄像头'],
      success: function(res) {
        var deviceTypes = ['水肥一体机', '土壤传感器', '环境监测站', '摄像头'];
        var icons = ['💧', '📡', '🌡️', '📹'];
        
        wx.showToast({
          title: '正在绑定' + deviceTypes[res.tapIndex],
          icon: 'loading'
        });
        
        setTimeout(function() {
          var newDevice = {
            id: Date.now(),
            name: deviceTypes[res.tapIndex] + '-' + String(Date.now()).slice(-2),
            icon: icons[res.tapIndex],
            status: 'online',
            statusText: '在线'
          };
          
          var plotList = that.data.plotList.map(function(plot) {
            if (plot.id === plotId) {
              return Object.assign({}, plot, {
                devices: plot.devices.concat([newDevice])
              });
            }
            return plot;
          });
          
          that.setData({
            plotList: plotList
          });
          
          that.updateFarmStats();
          that.updateSelectedPlotData();
          
          wx.showToast({
            title: '设备绑定成功',
            icon: 'success'
          });
        }, 1500);
      }
    });
  },

  manageDevices: function() {
    wx.switchTab({
      url: '/pages/device/device'
    });
  },


  // 设备绑定相关方法
  bindDevice: function() {
    var that = this;
    wx.showActionSheet({
      itemList: ['扫描设备二维码', '手动添加设备'],
      success: function(res) {
        switch(res.tapIndex) {
          case 0:
            that.scanDeviceQR();
            break;
          case 1:
            that.manualAddDevice();
            break;
        }
      }
    });
  },

  scanDeviceQR: function() {
    wx.scanCode({
      success: function(res) {
        wx.showToast({
          title: '设备扫描成功',
          icon: 'success'
        });
        console.log('扫描结果:', res.result);
      },
      fail: function() {
        wx.showToast({
          title: '扫描失败',
          icon: 'none'
        });
      }
    });
  },

  manualAddDevice: function() {
    var that = this;
    wx.showModal({
      title: '手动添加设备',
      editable: true,
      placeholderText: '请输入设备ID或序列号',
      success: function(res) {
        if (res.confirm && res.content) {
          that.addDeviceById(res.content);
        }
      }
    });
  },

  addDeviceById: function(deviceId) {
    var that = this;
    wx.showLoading({
      title: '正在添加设备...',
      mask: true
    });
    
    setTimeout(function() {
      wx.hideLoading();
      
      var deviceTypes = ['水肥一体机', '土壤传感器', '环境监测站', '摄像头'];
      var icons = ['💧', '📡', '🌡️', '📹'];
      var randomType = Math.floor(Math.random() * deviceTypes.length);
      
      var newDevice = {
        id: deviceId,
        name: deviceTypes[randomType] + '-' + deviceId,
        model: 'Model-' + deviceId,
        location: '待配置',
        type: deviceTypes[randomType],
        icon: icons[randomType],
        status: 'online',
        statusText: '在线'
      };
      
      wx.showToast({
        title: '设备添加成功',
        icon: 'success'
      });
      
      console.log('新设备:', newDevice);
    }, 1500);
  },


  configDevice: function(e) {
    var deviceId = e.currentTarget.dataset.id;
    var that = this;
    
    wx.showActionSheet({
      itemList: ['基本配置', '网络设置', '传感器校准', '报警设置', '数据采集频率'],
      success: function(res) {
        var configs = ['基本配置', '网络设置', '传感器校准', '报警设置', '数据采集频率'];
        var selectedConfig = configs[res.tapIndex];
        
        switch(res.tapIndex) {
          case 0:
            that.configBasicSettings(deviceId);
            break;
          case 1:
            that.configNetworkSettings(deviceId);
            break;
          case 2:
            that.calibrateSensor(deviceId);
            break;
          case 3:
            that.configAlarmSettings(deviceId);
            break;
          case 4:
            that.configDataFrequency(deviceId);
            break;
        }
      }
    });
  },

  configBasicSettings: function(deviceId) {
    var that = this;
    wx.showModal({
      title: '基本配置',
      editable: true,
      placeholderText: '请输入设备名称',
      success: function(res) {
        if (res.confirm && res.content) {
          wx.showToast({
            title: '设备名称已更新',
            icon: 'success'
          });
        }
      }
    });
  },

  configNetworkSettings: function(deviceId) {
    wx.showModal({
      title: '网络设置',
      content: '当前网络状态:\n- IP地址: *************\n- 信号强度: -45dBm\n- 网络类型: WiFi\n- 连接状态: 已连接',
      confirmText: '重新配置',
      success: function(res) {
        if (res.confirm) {
          wx.showToast({
            title: '网络重配置中...',
            icon: 'loading'
          });
        }
      }
    });
  },

  calibrateSensor: function(deviceId) {
    wx.showModal({
      title: '传感器校准',
      content: '校准操作将重置传感器数据\n建议在标准环境下进行校准\n\n是否开始校准？',
      confirmText: '开始校准',
      success: function(res) {
        if (res.confirm) {
          wx.showLoading({
            title: '正在校准...',
            mask: true
          });
          
          setTimeout(function() {
            wx.hideLoading();
            wx.showToast({
              title: '校准完成',
              icon: 'success'
            });
          }, 3000);
        }
      }
    });
  },

  configAlarmSettings: function(deviceId) {
    wx.showModal({
      title: '报警设置',
      content: '当前报警配置:\n- 温度上限: 35°C\n- 温度下限: 5°C\n- 湿度上限: 80%\n- 湿度下限: 20%\n- 故障报警: 开启',
      confirmText: '修改设置',
      success: function(res) {
        if (res.confirm) {
          wx.showToast({
            title: '报警设置修改中...',
            icon: 'none'
          });
        }
      }
    });
  },

  configDataFrequency: function(deviceId) {
    wx.showActionSheet({
      itemList: ['1分钟', '5分钟', '15分钟', '30分钟', '1小时'],
      success: function(res) {
        var frequencies = ['1分钟', '5分钟', '15分钟', '30分钟', '1小时'];
        wx.showToast({
          title: '采集频率设为' + frequencies[res.tapIndex],
          icon: 'success'
        });
      }
    });
  },

  unbindDevice: function(e) {
    var deviceId = e.currentTarget.dataset.id;
    var that = this;
    
    wx.showModal({
      title: '解绑设备',
      content: '解绑后设备将从该地块移除\n但设备本身不会被删除\n\n确定要解绑此设备吗？',
      confirmText: '确定解绑',
      cancelText: '取消',
      success: function(res) {
        if (res.confirm) {
          wx.showLoading({
            title: '正在解绑...',
            mask: true
          });
          
          setTimeout(function() {
            wx.hideLoading();
            
            // 更新设备列表，移除已解绑的设备
            var plotDeviceMap = that.data.plotDeviceMap.map(function(plotDevice) {
              var updatedCategories = plotDevice.categories.map(function(category) {
                return Object.assign({}, category, {
                  devices: category.devices.filter(function(device) {
                    return device.id !== deviceId;
                  })
                });
              });
              
              var totalDevices = updatedCategories.reduce(function(sum, category) {
                return sum + category.devices.length;
              }, 0);
              
              return Object.assign({}, plotDevice, {
                categories: updatedCategories,
                deviceCount: totalDevices
              });
            });
            
            that.setData({
              plotDeviceMap: plotDeviceMap
            });
            
            wx.showToast({
              title: '设备解绑成功',
              icon: 'success'
            });
          }, 1500);
        }
      }
    });
  },

  bindNewDevice: function(e) {
    var plotId = e.currentTarget.dataset.plotId;
    var that = this;
    
    wx.showActionSheet({
      itemList: ['扫码添加设备', '手动输入设备'],
      success: function(res) {
        switch(res.tapIndex) {
          case 0:
            that.scanDeviceForPlot(plotId);
            break;
          case 1:
            that.manualAddDeviceToPlot(plotId);
            break;
        }
      }
    });
  },

  scanDeviceForPlot: function(plotId) {
    var that = this;
    wx.scanCode({
      success: function(res) {
        try {
          var deviceInfo = JSON.parse(res.result);
          that.addDeviceToPlot(plotId, deviceInfo);
        } catch (error) {
          that.addDeviceToPlot(plotId, {
            serialNumber: res.result,
            name: '设备-' + res.result.slice(-4),
            type: 'irrigation'
          });
        }
      },
      fail: function() {
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        });
      }
    });
  },

  manualAddDeviceToPlot: function(plotId) {
    var that = this;
    wx.showModal({
      title: '手动添加设备',
      editable: true,
      placeholderText: '请输入设备序列号或ID',
      success: function(res) {
        if (res.confirm && res.content) {
          var deviceInfo = {
            serialNumber: res.content,
            name: '设备-' + res.content.slice(-4),
            type: that.inferDeviceType(res.content)
          };
          
          that.addDeviceToPlot(plotId, deviceInfo);
        }
      }
    });
  },


  addDeviceToPlot: function(plotId, deviceInfo) {
    var that = this;
    // 更新地块设备映射
    var plotDeviceMap = this.data.plotDeviceMap.map(function(plotDevice) {
      if (plotDevice.plotId === plotId) {
        var newDevice = {
          id: Date.now(),
          name: deviceInfo.name,
          model: deviceInfo.model || 'Unknown',
          location: '地块内',
          status: 'online',
          statusText: '在线'
        };
        
        // 找到对应的设备类型分组
        var categories = plotDevice.categories.map(function(category) {
          if (category.type === deviceInfo.type) {
            return Object.assign({}, category, {
              devices: category.devices.concat([newDevice])
            });
          }
          return category;
        });
        
        return Object.assign({}, plotDevice, {
          categories: categories,
          deviceCount: plotDevice.deviceCount + 1
        });
      }
      return plotDevice;
    });
    
    this.setData({
      plotDeviceMap: plotDeviceMap
    });
    
    wx.showToast({
      title: '设备添加成功',
      icon: 'success'
    });
  },

  inferDeviceType: function(serialNumber) {
    var serial = serialNumber.toUpperCase();
    if (serial.includes('WF') || serial.includes('IRR')) {
      return 'irrigation';
    } else if (serial.includes('SNS') || serial.includes('SOIL')) {
      return 'sensor';
    } else if (serial.includes('WTH') || serial.includes('ENV')) {
      return 'weather';
    }
    return 'sensor'; // 默认为传感器
  },

  optimizeDeviceLayout: function(e) {
    var plotId = e.currentTarget.dataset.plotId;
    var that = this;
    
    wx.showModal({
      title: '优化布局',
      content: '系统将根据以下因素优化布局:\n1. 设备覆盖范围\n2. 网络信号强度\n3. 电源分配\n4. 维护便利性\n\n是否开始优化？',
      confirmText: '开始优化',
      success: function(res) {
        if (res.confirm) {
          wx.showLoading({
            title: '正在优化布局...',
            mask: true
          });
          
          setTimeout(function() {
            wx.hideLoading();
            
            wx.showModal({
              title: '布局优化完成',
              content: '优化结果:\n- 设备覆盖率提升15%\n- 网络连接稳定性提升20%\n- 维护路径优化\n- 预计节省成本5%\n\n是否应用优化方案？',
              confirmText: '应用方案',
              success: function(applyRes) {
                if (applyRes.confirm) {
                  wx.showToast({
                    title: '优化方案已应用',
                    icon: 'success'
                  });
                }
              }
            });
          }, 2000);
        }
      }
    });
  },

  updatePermission: function(e) {
    var userId = e.currentTarget.dataset.userId;
    var type = e.currentTarget.dataset.type;
    var value = e.detail.value;
    
    wx.showToast({
      title: '权限已' + (value ? '开启' : '关闭'),
      icon: 'success'
    });
  },

});