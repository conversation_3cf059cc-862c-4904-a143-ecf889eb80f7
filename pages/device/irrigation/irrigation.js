const app = getApp();
const api = require('../../../utils/api.js');

Page({
  data: {
    // 从设备选择页面传入的参数
    selectedDeviceIds: [],
    operationMode: 'normal', // normal | quick
    multiDeviceMode: false,
    
    // 设备状态
    deviceStatus: {
      online: true,
      class: 'online',
      text: '在线运行'
    },
    deviceInfo: {
      waterLevel: 85,
      fertilizerLevel: 72,
      pressure: 2.3,
      runTime: '2小时15分'
    },
    
    // 实时数据
    currentFlow: 0,
    currentPressure: 0,
    sliderDragging: false,
    sliderStartX: 0,
    sliderWidth: 0,
    
    // 灌溉控制参数
    irrigationRunning: false,
    flowRate: 45,
    duration: 30,
    remainingTime: '25分30秒',
    
    // 地块和区域数据
    plotList: [],
    selectedPlots: [],
    
    // 调度数据
    scheduleList: [],
    todaySchedules: [],
    todayScheduleCount: 0,
    nextIrrigationTime: '--:--',
    
    // 智能控制设置
    smartControl: {
      humidity: {
        enabled: true,
        threshold: 45,
        duration: 20
      },
      waterSaving: {
        enabled: false
      }
    },
    
    // 今日记录
    todayRecords: [],
    loading: false,
    refreshing: false
  },

  onLoad(options) {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }

    // 处理从设备选择页面传入的参数
    if (options.devices) {
      const deviceIds = options.devices.split(',').map(id => parseInt(id));
      const mode = options.mode || 'normal';
      
      this.setData({
        selectedDeviceIds: deviceIds,
        operationMode: mode,
        multiDeviceMode: deviceIds.length > 1
      });
      
      // 设置页面标题
      if (mode === 'quick') {
        wx.setNavigationBarTitle({
          title: deviceIds.length > 1 ? '批量灌溉控制' : '快速灌溉'
        });
      }
      
      // 加载选中设备的信息
      this.loadSelectedDevices(deviceIds);
    }
    
    this.initData();
  },

  onShow() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.refreshData();
  },

  onUnload() {
    this.clearTimer();
  },

  onPullDownRefresh() {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化数据
  async initData() {
    this.setData({ loading: true });
    
    try {
      await Promise.all([
        this.loadDeviceData(),
        this.loadPlotData(),
        this.loadScheduleData(),
        this.loadTodayRecords()
      ]);
    } catch (error) {
      console.error('初始化数据失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 刷新数据
  async refreshData() {
    this.setData({ refreshing: true });
    
    try {
      await Promise.all([
        this.loadDeviceStatus(),
        this.loadPlotData(),
        this.loadScheduleData(),
        this.loadTodayRecords()
      ]);
    } catch (error) {
      console.error('刷新数据失败:', error);
    } finally {
      this.setData({ refreshing: false });
    }
  },

  // 加载设备数据
  async loadDeviceData() {
    try {
      const response = await api.getDeviceList();
      
      if (response.code === 200) {
        const devices = response.data.devices || [];
        const irrigationDevices = devices.filter(device => device.type === '智能灌溉设备');
        
        if (irrigationDevices.length > 0) {
          const device = irrigationDevices[0];
          
          // 更新设备状态
          this.setData({
            deviceStatus: {
              online: device.status === 'online',
              class: device.status === 'online' ? 'online' : 'offline',
              text: device.statusText || (device.status === 'online' ? '在线运行' : '离线')
            },
            deviceInfo: {
              waterLevel: Math.round(Math.random() * 30 + 70), // 模拟水位 70-100%
              fertilizerLevel: Math.round(Math.random() * 40 + 60), // 模拟肥料液位 60-100%
              pressure: (Math.random() * 1 + 2).toFixed(1), // 模拟压力 2.0-3.0 bar
              runTime: this.calculateRunTime()
            }
          });
        }
      }
    } catch (error) {
      console.error('加载设备数据失败:', error);
    }
  },

  // 加载设备状态
  async loadDeviceStatus() {
    try {
      const response = await api.getDeviceStatus();
      
      if (response.code === 200) {
        const deviceData = response.data;
        const summary = deviceData.summary || {};
        
        // 更新设备状态
        let deviceStatus = {
          online: summary.online > 0,
          class: summary.online > 0 ? 'online' : 'offline',
          text: summary.online > 0 ? '在线运行' : '离线'
        };
        
        if (summary.working > 0) {
          deviceStatus.text = '运行中';
          this.setData({ irrigationRunning: true });
        }
        
        this.setData({ deviceStatus });
      }
    } catch (error) {
      console.error('加载设备状态失败:', error);
    }
  },

  // 加载选中的设备信息
  async loadSelectedDevices(deviceIds) {
    console.log('加载选中设备:', deviceIds);
    
    if (this.data.operationMode === 'quick') {
      const deviceCount = deviceIds.length;
      const deviceText = deviceCount > 1 ? `${deviceCount}台设备` : '1台设备';
      
      wx.showToast({
        title: `已选择${deviceText}`,
        icon: 'success',
        duration: 1500
      });
    }
    
    try {
      // 这里可以调用批量获取设备信息的API
      const response = await api.getDeviceList();
      
      if (response.code === 200) {
        const allDevices = response.data.devices || [];
        const selectedDevices = allDevices.filter(device => 
          deviceIds.includes(parseInt(device.id.replace('device_', '')))
        );
        
        if (selectedDevices.length > 0) {
          // 更新多设备信息显示
          this.setData({
            multiDeviceInfo: deviceIds.length > 1 ? {
              deviceCount: selectedDevices.length,
              onlineCount: selectedDevices.filter(d => d.status === 'online').length,
              devices: selectedDevices
            } : null
          });
        }
      }
    } catch (error) {
      console.error('获取设备信息失败:', error);
    }
  },

  // 加载地块数据
  async loadPlotData() {
    try {
      const response = await api.getPlots('farm_001'); // 使用当前农场ID
      
      if (response.code === 200) {
        const plots = response.data.plots || [];
        
        // 转换为灌溉控制所需的格式
        const plotList = plots.map(plot => ({
          id: plot.id,
          name: plot.name,
          area: plot.area,
          type: plot.currentCrop?.type || '未种植',
          selected: false,
          status: plot.status,
          soilMoisture: this.generateSoilMoisture(), // 从传感器数据获取
          lastWatering: this.formatLastWatering(plot.updatedAt),
          devices: plot.devices || [],
          deviceCount: plot.deviceCount || 0,
          onlineDevices: plot.onlineDevices || 0,
          zones: this.generateZoneData(plot.id)
        }));
        
        this.setData({
          plotList: plotList,
          selectedPlots: plotList.filter(plot => plot.selected)
        });
      }
    } catch (error) {
      console.error('加载地块数据失败:', error);
    }
  },

  // 加载调度数据
  async loadScheduleData() {
    try {
      const [schedulesResponse, todayResponse] = await Promise.all([
        api.getSchedules({ type: 'irrigation' }),
        api.getDaySchedules(new Date().toISOString().split('T')[0])
      ]);
      
      if (schedulesResponse.code === 200) {
        const schedules = schedulesResponse.data.schedules || [];
        
        // 处理调度列表格式
        const scheduleList = schedules.map(schedule => ({
          id: schedule.id,
          name: schedule.name,
          time: schedule.timeDisplay || '--:--',
          repeat: schedule.weekdaysText || '每天',
          duration: schedule.executionSummary?.estimatedDailyDuration || 0,
          plots: this.formatPlotNames(schedule.plotIds),
          enabled: schedule.enabled
        }));
        
        this.setData({ scheduleList });
      }
      
      if (todayResponse.code === 200) {
        const todayData = todayResponse.data;
        const todaySchedules = todayData.schedules || [];
        
        // 处理今日计划
        const processedTodaySchedules = todaySchedules.map(schedule => ({
          id: schedule.id,
          time: schedule.startTime,
          name: schedule.scheduleName,
          details: `${this.formatPlotNames(schedule.plotIds)} • ${schedule.duration}分钟`,
          status: this.determineScheduleStatus(schedule.startTime),
          statusText: this.getScheduleStatusText(schedule.startTime)
        }));
        
        // 计算下次灌溉时间
        const nextSchedule = todaySchedules.find(s => this.determineScheduleStatus(s.startTime) === 'pending');
        const nextIrrigationTime = nextSchedule ? nextSchedule.startTime : '--:--';
        
        this.setData({
          todaySchedules: processedTodaySchedules,
          todayScheduleCount: todaySchedules.length,
          nextIrrigationTime: nextIrrigationTime
        });
      }
    } catch (error) {
      console.error('加载调度数据失败:', error);
    }
  },

  // 加载今日记录
  async loadTodayRecords() {
    try {
      const today = new Date().toISOString().split('T')[0];
      const response = await api.getOperationLogs({
        type: 'irrigation',
        startDate: today,
        endDate: today,
        limit: 10
      });
      
      if (response.code === 200) {
        const logs = response.data.logs || [];
        
        const todayRecords = logs.map(log => ({
          id: log.id,
          time: this.formatTime(log.time),
          action: log.title,
          details: log.description,
          status: log.status,
          statusText: log.statusText
        }));
        
        this.setData({ todayRecords });
      }
    } catch (error) {
      console.error('加载今日记录失败:', error);
    }
  },

  // 灌溉控制相关方法
  onIrrigationToggle(e) {
    const checked = e.detail.value;
    
    if (checked) {
      this.startIrrigation();
    } else {
      this.stopIrrigation();
    }
  },

  startIrrigation() {
    const selectedPlots = this.data.plotList.filter(plot => plot.selected);
    
    if (selectedPlots.length === 0) {
      wx.showToast({
        title: '请选择灌溉地块',
        icon: 'none'
      });
      return;
    }

    // 检查选中地块是否都有设备
    const plotsWithoutDevices = selectedPlots.filter(plot => plot.deviceCount === 0);
    if (plotsWithoutDevices.length > 0) {
      wx.showModal({
        title: '设备缺失',
        content: `地块"${plotsWithoutDevices.map(p => p.name).join('、')}"没有关联的灌溉设备，无法执行灌溉。`,
        showCancel: false
      });
      return;
    }

    wx.showModal({
      title: '启动灌溉',
      content: `确定启动灌溉吗？\n地块：${selectedPlots.map(p => p.name).join('、')}\n时长：${this.data.duration}分钟\n流量：${this.data.flowRate}L/min`,
      success: (res) => {
        if (res.confirm) {
          this.executeStartIrrigation(selectedPlots);
        }
      }
    });
  },

  async executeStartIrrigation(selectedPlots) {
    try {
      wx.showLoading({ title: '启动中...' });
      
      // 获取第一个有设备的地块的设备ID
      const firstPlot = selectedPlots[0];
      const deviceId = firstPlot.devices[0] || 'device_001';
      
      const response = await api.controlIrrigation({
        action: 'start_irrigation',
        deviceId: deviceId,
        parameters: {
          duration: this.data.duration * 60, // 转换为秒
          flowRate: this.data.flowRate,
          zones: selectedPlots.map(plot => plot.zones).flat(),
          plotIds: selectedPlots.map(plot => plot.id)
        }
      });
      
      if (response.code === 200) {
        this.setData({
          irrigationRunning: true,
          remainingTime: `${this.data.duration}分00秒`,
          'deviceStatus.text': '运行中',
          'deviceStatus.class': 'online'
        });
        
        // 更新选中地块的最后灌溉时间
        this.updatePlotIrrigationTime(selectedPlots);
        this.startTimer();
        
        wx.showToast({
          title: '灌溉启动成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('启动灌溉失败:', error);
      wx.showToast({
        title: error.message || '启动失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  stopIrrigation() {
    wx.showModal({
      title: '停止灌溉',
      content: '确定要停止当前灌溉吗？',
      success: (res) => {
        if (res.confirm) {
          this.executeStopIrrigation();
        }
      }
    });
  },

  async executeStopIrrigation() {
    try {
      wx.showLoading({ title: '停止中...' });
      
      const response = await api.controlIrrigation({
        action: 'stop_irrigation',
        deviceId: 'device_001', // 使用当前设备ID
        parameters: {}
      });
      
      if (response.code === 200) {
        this.setData({
          irrigationRunning: false,
          currentFlow: 0,
          currentPressure: 0,
          'deviceStatus.text': '在线运行',
          'deviceStatus.class': 'online'
        });
        
        this.clearTimer();
        
        wx.showToast({
          title: '灌溉停止成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('停止灌溉失败:', error);
      wx.showToast({
        title: error.message || '停止失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 地块选择相关方法
  togglePlot(e) {
    const plotId = e.currentTarget.dataset.id;
    const plotList = this.data.plotList.map(plot => {
      if (plot.id === plotId) {
        plot.selected = !plot.selected;
      }
      return plot;
    });
    
    const selectedPlots = plotList.filter(p => p.selected);
    
    this.setData({
      plotList: plotList,
      selectedPlots: selectedPlots
    });
    
    if (selectedPlots.length > 0) {
      const plotNames = selectedPlots.map(p => p.name).join('、');
      wx.showToast({
        title: `已选择：${plotNames}`,
        icon: 'none',
        duration: 1500
      });
    }
  },

  toggleAllPlots() {
    const availablePlots = this.data.plotList.filter(plot => plot.deviceCount > 0);
    const allSelected = availablePlots.every(plot => plot.selected);
    
    const plotList = this.data.plotList.map(plot => {
      if (plot.deviceCount > 0) {
        plot.selected = !allSelected;
      }
      return plot;
    });
    
    this.setData({
      plotList: plotList,
      selectedPlots: plotList.filter(p => p.selected)
    });
    
    wx.showToast({
      title: allSelected ? '已取消全选' : '已全选可用地块',
      icon: 'success'
    });
  },

  smartSelectPlots() {
    const plotList = this.data.plotList.map(plot => {
      // 选择土壤湿度低于50%且有设备的地块
      if (plot.deviceCount > 0 && plot.soilMoisture < 50) {
        plot.selected = true;
      } else {
        plot.selected = false;
      }
      return plot;
    });
    
    const selectedCount = plotList.filter(p => p.selected).length;
    
    this.setData({
      plotList: plotList,
      selectedPlots: plotList.filter(p => p.selected)
    });
    
    wx.showToast({
      title: `智能选择了${selectedCount}个地块`,
      icon: 'success'
    });
  },

  // 参数设置
  onFlowRateChange(e) {
    this.setData({
      flowRate: e.detail.value
    });
  },

  onDurationInput(e) {
    this.setData({
      duration: e.detail.value
    });
  },

  // 预设参数
  applyPreset(e) {
    const preset = e.currentTarget.dataset.preset;
    let duration, flowRate;
    
    switch (preset) {
      case 'light':
        duration = 10;
        flowRate = 30;
        break;
      case 'medium':
        duration = 20;
        flowRate = 45;
        break;
      case 'heavy':
        duration = 40;
        flowRate = 60;
        break;
    }
    
    this.setData({
      duration: duration,
      flowRate: flowRate
    });
    
    wx.showToast({
      title: '参数设置已应用',
      icon: 'success'
    });
  },

  // 智能控制
  toggleSmartHumidity(e) {
    this.setData({
      'smartControl.humidity.enabled': e.detail.value
    });
  },

  onHumidityThresholdChange(e) {
    this.setData({
      'smartControl.humidity.threshold': e.detail.value
    });
  },

  onSmartDurationInput(e) {
    this.setData({
      'smartControl.humidity.duration': e.detail.value
    });
  },

  toggleWaterSaving(e) {
    this.setData({
      'smartControl.waterSaving.enabled': e.detail.value
    });
  },

  // 定时器相关
  startTimer() {
    if (this.data.irrigationRunning) {
      this.timer = setInterval(() => {
        this.updateRemainingTime();
        this.updateRealtimeData();
      }, 1000);
    }
  },

  clearTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },

  updateRemainingTime() {
    // 更新剩余时间逻辑
    const currentTime = this.data.remainingTime;
    const parts = currentTime.split(/[分秒]/);
    let minutes = parseInt(parts[0]) || 0;
    let seconds = parseInt(parts[1]) || 0;
    
    seconds--;
    if (seconds < 0) {
      seconds = 59;
      minutes--;
    }
    
    if (minutes <= 0 && seconds <= 0) {
      this.setData({
        irrigationRunning: false,
        remainingTime: '00分00秒'
      });
      this.clearTimer();
      wx.showToast({
        title: '灌溉完成',
        icon: 'success'
      });
    } else {
      this.setData({
        remainingTime: `${minutes.toString().padStart(2, '0')}分${seconds.toString().padStart(2, '0')}秒`
      });
    }
  },

  updateRealtimeData() {
    if (this.data.irrigationRunning) {
      const baseFlow = this.data.flowRate;
      const currentFlow = baseFlow + (Math.random() - 0.5) * 10; // ±5 L/min 波动
      const currentPressure = this.data.deviceInfo.pressure + (Math.random() - 0.5) * 0.4; // ±0.2 bar 波动
      
      this.setData({
        currentFlow: Math.max(0, currentFlow).toFixed(1),
        currentPressure: Math.max(0, currentPressure).toFixed(1)
      });
    }
  },

  // 辅助方法
  generateSoilMoisture() {
    return Math.round(Math.random() * 40 + 30); // 30-70%
  },

  formatLastWatering(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    const hours = Math.floor(diff / 3600000);
    
    if (hours < 24) {
      return `${hours}小时前`;
    } else {
      const days = Math.floor(hours / 24);
      return `${days}天前`;
    }
  },

  generateZoneData(plotId) {
    // 根据地块生成区域数据
    return [`${plotId}_zone_1`];
  },

  formatPlotNames(plotIds) {
    const plotList = this.data.plotList;
    const plotNames = plotIds.map(id => {
      const plot = plotList.find(p => p.id === id);
      return plot ? plot.name : id;
    });
    return plotNames.join('、');
  },

  determineScheduleStatus(startTime) {
    const now = new Date();
    const [hour, minute] = startTime.split(':').map(Number);
    const scheduleTime = new Date(now);
    scheduleTime.setHours(hour, minute, 0, 0);
    
    if (scheduleTime > now) {
      return 'pending';
    } else if (scheduleTime <= now && now - scheduleTime < 3600000) { // 1小时内
      return 'completed';
    } else {
      return 'completed';
    }
  },

  getScheduleStatusText(startTime) {
    const status = this.determineScheduleStatus(startTime);
    return status === 'pending' ? '待执行' : '已完成';
  },

  formatTime(timeString) {
    const time = new Date(timeString);
    return time.toLocaleTimeString().substring(0, 5);
  },

  calculateRunTime() {
    const hours = Math.floor(Math.random() * 5);
    const minutes = Math.floor(Math.random() * 60);
    return `${hours}小时${minutes}分`;
  },

  updatePlotIrrigationTime(selectedPlots) {
    const currentTime = new Date().toLocaleString();
    const plotList = this.data.plotList.map(plot => {
      const selectedPlot = selectedPlots.find(sp => sp.id === plot.id);
      if (selectedPlot) {
        plot.lastWatering = '刚刚';
      }
      return plot;
    });
    
    this.setData({ plotList });
  },

  // 导航方法
  addSchedule() {
    wx.navigateTo({
      url: '/pages/device/schedule/add/add?type=irrigation'
    });
  },

  editSchedule(e) {
    const scheduleId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/device/schedule/add/add?action=edit&id=${scheduleId}`
    });
  },

  deleteSchedule(e) {
    const scheduleId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '删除计划',
      content: '确定要删除这个定时计划吗？',
      success: (res) => {
        if (res.confirm) {
          this.executeDeleteSchedule(scheduleId);
        }
      }
    });
  },

  async executeDeleteSchedule(scheduleId) {
    try {
      const response = await api.deleteSchedule(scheduleId);
      
      if (response.code === 200) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        this.loadScheduleData();
      }
    } catch (error) {
      console.error('删除计划失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  toggleSchedule(e) {
    const scheduleId = e.currentTarget.dataset.id;
    const enabled = e.detail.value;
    
    api.toggleSchedule(scheduleId, enabled).then(res => {
      if (res.code === 200) {
        wx.showToast({
          title: enabled ? '计划已启用' : '计划已禁用',
          icon: 'success'
        });
      }
    }).catch(err => {
      console.error('切换计划状态失败:', err);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    });
  },

  viewAllRecords() {
    wx.navigateTo({
      url: '/pages/device/logs/logs?type=irrigation'
    });
  },

  openAdvancedSchedule() {
    wx.navigateTo({
      url: '/pages/device/schedule/schedule'
    });
  }
});