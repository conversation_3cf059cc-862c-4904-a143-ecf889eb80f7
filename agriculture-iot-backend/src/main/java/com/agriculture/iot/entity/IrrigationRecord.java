package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 灌溉记录实体类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("irrigation_records")
public class IrrigationRecord {

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private Long deviceId;

    /**
     * 地块ID
     */
    @TableField("plot_id")
    private Long plotId;

    /**
     * 灌溉开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 灌溉结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 灌溉持续时间(分钟)
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 用水量(升)
     */
    @TableField("water_amount")
    private BigDecimal waterAmount;

    /**
     * 触发方式: manual-手动, auto-自动, scheduled-定时
     */
    @TableField("trigger_type")
    private String triggerType;

    /**
     * 触发条件
     */
    @TableField("trigger_condition")
    private String triggerCondition;

    /**
     * 执行状态: pending-待执行, running-执行中, completed-已完成, failed-失败, cancelled-已取消
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("notes")
    private String notes;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // 关联对象
    /**
     * 关联设备信息
     */
    @TableField(exist = false)
    private Device device;

    /**
     * 关联地块信息
     */
    @TableField(exist = false)
    private Plot plot;

    /**
     * 灌溉启动请求内部类
     */
    @Data
    public static class StartRequest {
        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 灌溉持续时间（分钟）
         */
        private Integer duration;

        /**
         * 流量设置（L/min）
         */
        private BigDecimal flowRate;

        /**
         * 触发方式
         */
        private String triggerType;

        /**
         * 备注
         */
        private String notes;
    }

    /**
     * 灌溉停止请求内部类
     */
    @Data
    public static class StopRequest {
        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 停止原因
         */
        private String reason;

        /**
         * 备注
         */
        private String notes;
    }

    /**
     * 灌溉状态信息内部类
     */
    @Data
    public static class StatusInfo {
        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 当前状态
         */
        private String currentStatus;

        /**
         * 是否正在灌溉
         */
        private Boolean isIrrigating;

        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 预计结束时间
         */
        private LocalDateTime expectedEndTime;

        /**
         * 已运行时间（分钟）
         */
        private Integer runningTime;

        /**
         * 当前流量
         */
        private BigDecimal currentFlowRate;

        /**
         * 已用水量
         */
        private BigDecimal usedWaterAmount;
    }

    /**
     * 灌溉统计信息内部类
     */
    @Data
    public static class Statistics {
        /**
         * 总灌溉次数
         */
        private Integer totalCount;

        /**
         * 总灌溉时长（小时）
         */
        private BigDecimal totalDuration;

        /**
         * 总用水量（升）
         */
        private BigDecimal totalWaterAmount;

        /**
         * 平均灌溉时长（分钟）
         */
        private BigDecimal avgDuration;

        /**
         * 平均用水量（升）
         */
        private BigDecimal avgWaterAmount;

        /**
         * 本月灌溉次数
         */
        private Integer monthlyCount;

        /**
         * 本月用水量
         */
        private BigDecimal monthlyWaterAmount;

        /**
         * 成功率（%）
         */
        private BigDecimal successRate;
    }

    /**
     * 灌溉效率数据内部类
     */
    @Data
    public static class EfficiencyData {
        /**
         * 水效率评分
         */
        private BigDecimal waterEfficiencyScore;

        /**
         * 单位面积用水量
         */
        private BigDecimal waterPerArea;

        /**
         * 灌溉频次分析
         */
        private String frequencyAnalysis;

        /**
         * 建议优化措施
         */
        private java.util.List<String> recommendations;

        /**
         * 节水潜力（%）
         */
        private BigDecimal waterSavingPotential;
    }

    /**
     * 灌溉报表数据内部类
     */
    @Data
    public static class ReportData {
        /**
         * 报表标题
         */
        private String title;

        /**
         * 统计数据
         */
        private Statistics statistics;

        /**
         * 详细记录
         */
        private java.util.List<IrrigationRecord> records;

        /**
         * 图表数据
         */
        private java.util.List<ChartData> chartData;

        /**
         * 生成时间
         */
        private LocalDateTime generatedAt;

        @Data
        public static class ChartData {
            private String date;
            private BigDecimal waterAmount;
            private Integer duration;
            private Integer count;
        }
    }

    /**
     * 灌溉趋势数据内部类
     */
    @Data
    public static class TrendData {
        /**
         * 趋势数据点
         */
        private java.util.List<TrendPoint> trendPoints;

        /**
         * 趋势分析
         */
        private String trendAnalysis;

        /**
         * 预测数据
         */
        private java.util.List<TrendPoint> predictedData;

        @Data
        public static class TrendPoint {
            private LocalDateTime timestamp;
            private BigDecimal waterAmount;
            private Integer frequency;
            private BigDecimal efficiency;
        }
    }

    /**
     * 灌溉阈值配置内部类
     */
    @Data
    public static class ThresholdConfig {
        /**
         * 配置ID
         */
        private Long configId;

        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 土壤湿度阈值（%）
         */
        private BigDecimal soilMoistureThreshold;

        /**
         * 自动灌溉时长（分钟）
         */
        private Integer autoIrrigationDuration;

        /**
         * 自动灌溉流量
         */
        private BigDecimal autoIrrigationFlowRate;

        /**
         * 是否启用自动灌溉
         */
        private Boolean autoIrrigationEnabled;

        /**
         * 灌溉间隔（小时）
         */
        private Integer irrigationInterval;

        /**
         * 创建时间
         */
        private LocalDateTime createdAt;
    }

    /**
     * 批量控制请求内部类
     */
    @Data
    public static class BatchControlRequest {
        /**
         * 设备ID列表
         */
        private java.util.List<Long> deviceIds;

        /**
         * 操作类型
         */
        private String operation;

        /**
         * 灌溉参数
         */
        private IrrigationParams irrigationParams;

        @Data
        public static class IrrigationParams {
            private Integer duration;
            private BigDecimal flowRate;
            private String notes;
        }
    }
}