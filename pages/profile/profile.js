const api = require('../../utils/api.js');
const app = getApp();

Page({
  data: {
    userInfo: {
      name: '张三',
      phone: '138****1234',
      role: '农场主'
    },
    userStats: {
      loginDays: 45,
      operationCount: 328,
      dataPoints: 1256,
      alarmHandled: 23
    },
    roleOptions: [
      { value: '农场主', label: '农场主', desc: '拥有所有权限，可以管理整个农场' },
      { value: '技术员', label: '技术员', desc: '负责设备维护和技术支持' },
      { value: '工人', label: '工人', desc: '负责日常农场作业' }
    ]
  },

  onLoad() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.loadUserInfo();
  },

  onShow() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.refreshUserData();
  },

  loadUserInfo() {
    // 从API加载用户信息
    api.getUserProfile().then(res => {
      if (res.data) {
        this.setData({
          userInfo: {
            name: res.data.realName || res.data.nickname,
            phone: res.data.phone,
            email: res.data.email,
            avatar: res.data.avatarUrl,
            role: '农场主' // 可以根据实际情况设置
          }
        });
        
        // 更新本地存储
        wx.setStorageSync('userInfo', res.data);
      }
    }).catch(err => {
      console.error('加载用户信息失败:', err);
      // 降级到本地存储
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({
          userInfo: userInfo
        });
      }
    });
  },

  refreshUserData() {
    this.loadUserInfo();
    // 加载用户统计数据
  },

  changeAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        wx.showLoading({
          title: '上传中...'
        });
        
        // 使用API上传头像
        api.uploadImage(tempFilePath).then(result => {
          // 更新用户头像
          return api.updateUserProfile({
            avatarUrl: result.data.url
          });
        }).then(res => {
          wx.hideLoading();
          wx.showToast({
            title: '头像更换成功',
            icon: 'success'
          });
          
          // 刷新用户数据
          this.loadUserInfo();
        }).catch(err => {
          wx.hideLoading();
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          });
          console.error('头像上传失败:', err);
        });
      }
    });
  },

  editProfile() {
    wx.navigateTo({
      url: '/pages/profile/edit/edit'
    });
  },

  changeRole() {
    const that = this;
    const roleNames = this.data.roleOptions.map(role => role.label);
    
    wx.showActionSheet({
      itemList: roleNames,
      success: (res) => {
        const selectedRole = that.data.roleOptions[res.tapIndex];
        
        wx.showModal({
          title: '切换角色',
          content: `确定要切换到"${selectedRole.label}"吗？\n\n${selectedRole.desc}`,
          success: (modalRes) => {
            if (modalRes.confirm) {
              const newUserInfo = {
                ...that.data.userInfo,
                role: selectedRole.value
              };
              
              that.setData({
                userInfo: newUserInfo
              });
              
              // 保存到本地存储
              wx.setStorageSync('userInfo', newUserInfo);
              
              wx.showToast({
                title: `已切换到${selectedRole.label}`,
                icon: 'success'
              });
            }
          }
        });
      }
    });
  },

  navigateToFarmInit() {
    wx.navigateTo({
      url: '/pages/farm/init/init'
    });
  },

  navigateToSettings() {
    wx.navigateTo({
      url: '/pages/profile/settings/settings'
    });
  },

  navigateToDeviceManage() {
    wx.switchTab({
      url: '/pages/device/device'
    });
  },

  navigateToDataExport() {
    wx.showModal({
      title: '数据导出',
      content: '选择要导出的数据类型',
      showCancel: true,
      cancelText: '取消',
      confirmText: '导出全部',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '正在导出...',
            icon: 'loading'
          });
        }
      }
    });
  },

  navigateToAlarmSettings() {
    wx.navigateTo({
      url: '/pages/monitor/alarm/alarm'
    });
  },

  viewHelp(e) {
    const type = e.currentTarget.dataset.type;
    let url = '';
    
    switch (type) {
      case 'tutorial':
        url = '/pages/help/tutorial/tutorial';
        break;
      case 'faq':
        url = '/pages/help/faq/faq';
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
        return;
    }
    
    wx.navigateTo({
      url: url
    });
  },

  contactSupport() {
    wx.showModal({
      title: '技术支持',
      content: '客服电话：************\n工作时间：9:00-18:00',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        }
      }
    });
  },

  viewAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },

  clearCache() {
    wx.showModal({
      title: '清理缓存',
      content: '确定要清理本地缓存数据吗？',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorage({
            success: () => {
              wx.showToast({
                title: '缓存清理成功',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  checkUpdate() {
    wx.showToast({
      title: '检查更新中...',
      icon: 'loading'
    });
    
    // 模拟检查更新
    setTimeout(() => {
      wx.showToast({
        title: '已是最新版本',
        icon: 'success'
      });
    }, 2000);
  },

  feedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  logout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出当前账号吗？',
      success: (res) => {
        if (res.confirm) {
          // 清理用户数据
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      }
    });
  }
})