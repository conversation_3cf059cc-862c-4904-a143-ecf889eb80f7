package com.agriculture.iot.mapper;

import com.agriculture.iot.entity.FertilizerSchedule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 施肥计划Mapper接口
 *
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface FertilizerScheduleMapper extends BaseMapper<FertilizerSchedule> {

    /**
     * 查找活跃的施肥计划
     */
    @Select("SELECT * FROM fertilizer_schedule " +
            "WHERE status = 'ACTIVE' " +
            "AND start_time <= #{now} " +
            "AND end_time >= #{now}")
    List<FertilizerSchedule> findActiveSchedules(@Param("now") LocalDateTime now);
}