const app = getApp();

Page({
  data: {
    loading: false,
    isRunning: false,
    progress: 0,
    
    // 设备状态
    irrigationStatus: {
      class: 'status-offline',
      text: '停止',
      action: '启动灌溉'
    },
    fertilizerStatus: {
      class: 'status-offline', 
      text: '停止',
      action: '启动施肥'
    },
    
    // 设备参数
    waterLevel: 85,
    fertilizerLevel: 72,
    currentFlow: 0,
    currentPressure: 0,
    currentEC: 0,
    runningTime: '00:00:00',
    
    // 今日统计
    todayStats: {
      irrigationTime: 125,
      waterUsage: 890,
      fertilizerUsage: 45.2,
      operationCount: 8
    }
  },

  onLoad: function (options) {
    this.loadDeviceStatus();
    this.loadTodayStats();
  },

  onShow: function () {
    this.refreshStatus();
  },

  onPullDownRefresh: function () {
    this.refreshStatus();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 加载设备状态
  loadDeviceStatus: function () {
    wx.request({
      url: 'https://api.your-domain.com/device/irrigation/status',
      success: (res) => {
        if (res.data.code === 200) {
          const data = res.data.data;
          
          let irrigationStatus = {
            class: 'status-offline',
            text: '停止',
            action: '启动灌溉'
          };
          
          if (data.irrigation && data.irrigation.online) {
            if (data.irrigation.running) {
              irrigationStatus = {
                class: 'status-online',
                text: '运行中', 
                action: '停止灌溉'
              };
              this.setData({ isRunning: true });
              this.startProgressUpdate();
            } else {
              irrigationStatus = {
                class: 'status-warning',
                text: '待机',
                action: '启动灌溉'
              };
            }
          }

          let fertilizerStatus = {
            class: 'status-offline',
            text: '停止', 
            action: '启动施肥'
          };
          
          if (data.fertilizer && data.fertilizer.online) {
            if (data.fertilizer.running) {
              fertilizerStatus = {
                class: 'status-online',
                text: '运行中',
                action: '停止施肥'
              };
            } else {
              fertilizerStatus = {
                class: 'status-warning', 
                text: '待机',
                action: '启动施肥'
              };
            }
          }

          this.setData({
            irrigationStatus: irrigationStatus,
            fertilizerStatus: fertilizerStatus,
            waterLevel: data.waterLevel || 85,
            fertilizerLevel: data.fertilizerLevel || 72,
            currentFlow: data.currentFlow || 0,
            currentPressure: data.currentPressure || 0,
            currentEC: data.currentEC || 0
          });
        }
      },
      fail: (err) => {
        console.error('获取设备状态失败:', err);
      }
    });
  },

  // 加载今日统计
  loadTodayStats: function () {
    wx.request({
      url: 'https://api.your-domain.com/device/irrigation/today-stats',
      success: (res) => {
        if (res.data.code === 200) {
          this.setData({
            todayStats: res.data.data
          });
        }
      },
      fail: (err) => {
        console.error('获取今日统计失败:', err);
      }
    });
  },

  // 刷新状态
  refreshStatus: function () {
    this.loadDeviceStatus();
    this.loadTodayStats();
  },

  // 启动进度更新
  startProgressUpdate: function () {
    if (this.progressTimer) {
      clearInterval(this.progressTimer);
    }
    
    this.progressTimer = setInterval(() => {
      // 模拟进度更新
      let progress = this.data.progress + 2;
      if (progress >= 100) {
        progress = 100;
        this.setData({ 
          progress: progress,
          isRunning: false 
        });
        clearInterval(this.progressTimer);
        this.refreshStatus();
      } else {
        this.setData({ progress: progress });
      }
      
      // 更新运行时间
      this.updateRunningTime();
    }, 1000);
  },

  // 更新运行时间
  updateRunningTime: function () {
    // 这里应该从服务器获取实际运行时间
    // 现在使用模拟数据
    const now = new Date();
    const startTime = this.data.startTime || now;
    const diff = Math.floor((now - startTime) / 1000);
    
    const hours = Math.floor(diff / 3600);
    const minutes = Math.floor((diff % 3600) / 60);
    const seconds = diff % 60;
    
    const runningTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    this.setData({ runningTime });
  },

  // 切换灌溉状态
  toggleIrrigation: function () {
    const currentStatus = this.data.irrigationStatus;
    
    if (currentStatus.class === 'status-offline') {
      wx.showToast({
        title: '设备离线',
        icon: 'none'
      });
      return;
    }
    
    const isRunning = currentStatus.class === 'status-online' && currentStatus.text === '运行中';
    const action = isRunning ? 'stop' : 'start';
    
    wx.showModal({
      title: '确认操作',
      content: `确定要${isRunning ? '停止' : '启动'}灌溉吗？`,
      success: (res) => {
        if (res.confirm) {
          this.executeControl('irrigation', action);
        }
      }
    });
  },

  // 切换施肥状态
  toggleFertilizer: function () {
    const currentStatus = this.data.fertilizerStatus;
    
    if (currentStatus.class === 'status-offline') {
      wx.showToast({
        title: '设备离线',
        icon: 'none'
      });
      return;
    }
    
    const isRunning = currentStatus.class === 'status-online' && currentStatus.text === '运行中';
    const action = isRunning ? 'stop' : 'start';
    
    wx.showModal({
      title: '确认操作',
      content: `确定要${isRunning ? '停止' : '启动'}施肥吗？`,
      success: (res) => {
        if (res.confirm) {
          this.executeControl('fertilizer', action);
        }
      }
    });
  },

  // 紧急停止
  emergencyStop: function () {
    wx.showModal({
      title: '紧急停止',
      content: '确定要停止所有运行中的设备吗？此操作不可撤销！',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.executeControl('all', 'emergency_stop');
        }
      }
    });
  },

  // 系统冲洗
  systemFlush: function () {
    wx.showModal({
      title: '系统冲洗',
      content: '执行系统冲洗会清洗管道内残留的肥料，预计需要5分钟。',
      success: (res) => {
        if (res.confirm) {
          this.executeControl('system', 'flush');
        }
      }
    });
  },

  // 执行控制命令
  executeControl: function (device, action) {
    this.setData({ loading: true });
    
    wx.request({
      url: 'https://api.your-domain.com/device/irrigation/control',
      method: 'POST',
      data: {
        device: device,
        action: action
      },
      success: (res) => {
        this.setData({ loading: false });
        
        if (res.data.code === 200) {
          wx.showToast({
            title: '操作成功',
            icon: 'success'
          });
          
          // 如果是启动操作，设置开始时间
          if (action === 'start') {
            this.setData({ 
              startTime: new Date(),
              progress: 0
            });
          }
          
          setTimeout(() => {
            this.refreshStatus();
          }, 1000);
        } else {
          wx.showToast({
            title: res.data.message || '操作失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        this.setData({ loading: false });
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 页面跳转方法
  goToIrrigation: function () {
    wx.navigateTo({
      url: '/pages/device/irrigation/irrigation'
    });
  },

  goToFertilizer: function () {
    wx.navigateTo({
      url: '/pages/device/fertilizer/fertilizer'
    });
  },

  goToSchedule: function () {
    wx.navigateTo({
      url: '/pages/device/schedule/schedule'
    });
  },

  goToFormula: function () {
    wx.navigateTo({
      url: '/pages/device/formula/formula'
    });
  },

  goToLogs: function () {
    wx.navigateTo({
      url: '/pages/device/logs/logs'
    });
  },

  goToConfig: function () {
    wx.navigateTo({
      url: '/pages/device/config/config'
    });
  },

  onUnload: function () {
    if (this.progressTimer) {
      clearInterval(this.progressTimer);
    }
  }
});