/* 全局样式 */
page {
  background-color: #F5F5F5;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 主色调 */
.primary-color {
  color: #08C160;
}

.primary-bg {
  background-color: #08C160;
}

/* 辅助色 */
.secondary-color {
  color: #1976D2;
}

.secondary-bg {
  background-color: #1976D2;
}

/* 状态颜色 */
.success-color {
  color: #08C160;
}

.warning-color {
  color: #FF9800;
}

.danger-color {
  color: #D32F2F;
}

.success-bg {
  background-color: #08C160;
}

.warning-bg {
  background-color: #FF9800;
}

.danger-bg {
  background-color: #D32F2F;
}

/* 通用布局 */
.container {
  padding: 20rpx;
}

.page-container {
  min-height: 100vh;
  background-color: #F5F5F5;
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

/* 按钮样式 */
.btn {
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #08C160;
  color: #ffffff;
}

.btn-secondary {
  background-color: #1976D2;
  color: #ffffff;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #08C160;
  color: #08C160;
}

.btn-small {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 30rpx 60rpx;
  font-size: 32rpx;
}

/* 文本样式 */
.text-primary {
  color: #333333;
}

.text-secondary {
  color: #666666;
}

.text-muted {
  color: #999999;
}

.text-small {
  font-size: 24rpx;
}

.text-large {
  font-size: 36rpx;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 间距工具类 */
.m-10 {
  margin: 10rpx;
}

.m-20 {
  margin: 20rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.p-10 {
  padding: 10rpx;
}

.p-20 {
  padding: 20rpx;
}

.pt-20 {
  padding-top: 20rpx;
}

.pb-20 {
  padding-bottom: 20rpx;
}

/* 状态指示器 */
.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  display: inline-block;
  margin-right: 10rpx;
}

.status-online {
  background-color: #08C160;
}

.status-offline {
  background-color: #F44336;
}

.status-warning {
  background-color: #FF9800;
}

/* 数据展示 */
.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #E0E0E0;
}

.data-item:last-child {
  border-bottom: none;
}

.data-label {
  font-size: 28rpx;
  color: #666666;
}

.data-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.data-unit {
  font-size: 24rpx;
  color: #999999;
  margin-left: 10rpx;
}

/* 图表容器 */
.chart-container {
  width: 100%;
  height: 400rpx;
  margin: 20rpx 0;
}

/* 设备控制面板 */
.control-panel {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.control-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}

.switch-label {
  font-size: 28rpx;
  color: #333333;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 15rpx;
  }
  
  .card {
    padding: 20rpx;
  }
  
  .btn {
    font-size: 26rpx;
    padding: 18rpx 36rpx;
  }
}