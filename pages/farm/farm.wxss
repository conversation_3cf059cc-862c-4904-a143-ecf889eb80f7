/* 智慧农业地块管理页面样式 */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 卡片基础样式 */
.card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #08C160;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 农场概览 */
.farm-overview {
  background: linear-gradient(135deg, #E8F8EC 0%, #C8F2D6 100%);
  border-radius: 16rpx;
  padding: 30rpx;
}


.farm-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.farm-basic {
  flex: 1;
}

.farm-name {
  font-size: 40rpx;
  font-weight: 700;
  color: #08C160;
  display: block;
  margin-bottom: 12rpx;
}

.farm-owner {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
}

.farm-location {
  display: flex;
  align-items: center;
}

.location-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.location-text {
  font-size: 24rpx;
  color: #888;
}

.farm-weather {
  text-align: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  padding: 20rpx;
  min-width: 120rpx;
}

.weather-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.weather-temp {
  font-size: 32rpx;
  font-weight: 600;
  color: #08C160;
  margin-bottom: 4rpx;
}

.weather-desc {
  font-size: 20rpx;
  color: #666;
}

.farm-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stat-item {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
}

.stat-icon {
  font-size: 32rpx;
  display: block;
  margin-bottom: 12rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #08C160;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #666;
}

/* 功能导航 */
.function-nav {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.nav-item {
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  border: 3rpx solid transparent;
  transition: all 0.3s ease;
}

.nav-item.active {
  background: linear-gradient(135deg, #E8F8EC 0%, #C8F2D6 100%);
  border-color: #2E7D32;
}

.nav-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.nav-item.active .nav-text {
  color: #08C160;
  font-weight: 600;
}

/* 内容区域 */
.content-section {
  margin-top: 20rpx;
}

/* 地块地图视图 */
.plot-map-container {
  margin-bottom: 30rpx;
}

.map-view {
  background-color: #f0f8f0;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.farm-layout {
  position: relative;
  height: 400rpx;
  background: linear-gradient(135deg, #E8F8EC 0%, #F1FAF3 100%);
  border-radius: 12rpx;
  border: 2rpx dashed #C8F2D6;
}

.plot-map-item {
  position: absolute;
  border-radius: 8rpx;
  border: 2rpx solid #08C160;
  background-color: rgba(8, 193, 96, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.plot-map-item.growing {
  border-color: #08C160;
  background-color: rgba(8, 193, 96, 0.15);
}

.plot-map-item.flowering {
  border-color: #FF9800;
  background-color: rgba(255, 152, 0, 0.15);
}

.plot-map-item.ready {
  border-color: #2196F3;
  background-color: rgba(33, 150, 243, 0.15);
}

.plot-map-item.harvest {
  border-color: #9C27B0;
  background-color: rgba(156, 39, 176, 0.15);
}

.plot-map-item.selected {
  border-width: 4rpx;
  border-color: #FF9800;
  box-shadow: 0 0 0 2rpx rgba(255, 152, 0, 0.3);
  transform: scale(1.05);
  z-index: 10;
}

.selected-indicator {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  background-color: #FF9800;
  color: white;
  border-radius: 10rpx;
  font-size: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.plot-info-overlay {
  text-align: center;
}

.plot-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.plot-area {
  font-size: 20rpx;
  color: #666;
}

.map-controls {
  display: flex;
  gap: 20rpx;
}

/* 地块列表视图 */
.plot-list {
  margin-top: 30rpx;
}

.plot-item {
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #08C160;
}

.plot-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.plot-basic {
  flex: 1;
}

.plot-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.plot-area {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.plot-coordinates {
  display: flex;
  align-items: center;
}

.coord-label {
  font-size: 22rpx;
  color: #888;
  margin-right: 8rpx;
}

.coord-value {
  font-size: 22rpx;
  color: #666;
}

.plot-status {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 6rpx;
  margin-right: 8rpx;
}

.plot-status.growing .status-dot {
  background-color: #08C160;
}

.plot-status.flowering .status-dot {
  background-color: #FF9800;
}

.plot-status.ready .status-dot {
  background-color: #2196F3;
}

.plot-status.harvest .status-dot {
  background-color: #9C27B0;
}

.status-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 地块详细信息 */
.plot-details {
  margin-bottom: 24rpx;
}

.detail-section {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #08C160;
  margin-bottom: 16rpx;
  display: block;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.detail-item {
  background-color: white;
  padding: 16rpx;
  border-radius: 8rpx;
}

.detail-label {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 种植规划 */
.planting-plan {
  background-color: white;
  padding: 20rpx;
  border-radius: 12rpx;
}

.current-crop {
  margin-bottom: 16rpx;
}

.crop-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #08C160;
  margin-right: 16rpx;
}

.plant-date, .harvest-date {
  font-size: 22rpx;
  color: #666;
  margin-right: 16rpx;
}

.growth-progress {
  margin-top: 12rpx;
}

.progress-bar {
  height: 8rpx;
  background-color: #e0e0e0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, #4CAF50, #2E7D32);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 20rpx;
  color: #888;
}

/* 设备管理 */
.plot-devices {
  margin-bottom: 24rpx;
}

.devices-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.device-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.device-tag {
  background-color: white;
  border: 2rpx solid #08C160;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  display: flex;
  align-items: center;
  font-size: 20rpx;
}

.device-tag.online {
  border-color: #08C160;
  background-color: rgba(8, 193, 96, 0.1);
}

.device-tag.warning {
  border-color: #FF9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.device-tag.offline {
  border-color: #F44336;
  background-color: rgba(244, 67, 54, 0.1);
}

.device-icon {
  margin-right: 8rpx;
}

.device-name {
  margin-right: 8rpx;
  color: #333;
}

.device-status {
  color: #666;
}

/* 地块操作 */
.plot-actions {
  display: flex;
  gap: 12rpx;
}

/* 作物档案 */
.crop-archives {
  margin-top: 20rpx;
}

.crop-archive-item {
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #08C160;
}

.crop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.crop-basic {
  display: flex;
  align-items: center;
  flex: 1;
}

.crop-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.crop-info {
  flex: 1;
}

.crop-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.crop-variety {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 4rpx;
}

.crop-plot {
  font-size: 22rpx;
  color: #888;
}

.crop-status {
  background-color: white;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
}

.crop-details {
  margin-bottom: 24rpx;
}

.detail-tabs {
  display: flex;
  background-color: #e0e0e0;
  border-radius: 12rpx;
  padding: 6rpx;
  margin-bottom: 24rpx;
}

.detail-tab {
  flex: 1;
  text-align: center;
  padding: 12rpx;
  font-size: 24rpx;
  color: #666;
  border-radius: 8rpx;
}

.detail-tab.active {
  background-color: #2E7D32;
  color: white;
}

/* 生长记录 */
.growth-records {
  margin-bottom: 24rpx;
}

.growth-stage {
  display: flex;
  margin-bottom: 24rpx;
}

.stage-indicator {
  width: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
}

.stage-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 10rpx;
  background-color: #e0e0e0;
  margin-bottom: 12rpx;
}

.stage-dot.completed {
  background-color: #08C160;
}

.stage-dot.current {
  background-color: #FF9800;
}

.stage-dot.pending {
  background-color: #e0e0e0;
}

.stage-line {
  width: 2rpx;
  height: 40rpx;
  background-color: #e0e0e0;
}

.stage-line.completed {
  background-color: #08C160;
}

.stage-content {
  flex: 1;
}

.stage-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.stage-date {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.stage-desc {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
}

/* 收获统计 */
.harvest-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  background-color: white;
  padding: 20rpx;
  border-radius: 12rpx;
}

/* 作物操作 */
.crop-actions {
  display: flex;
  gap: 12rpx;
}

/* 设备绑定 */
.device-binding {
  margin-top: 20rpx;
}

.plot-device-map {
  margin-bottom: 30rpx;
}

.plot-device-item {
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.plot-device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.plot-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.device-count {
  font-size: 24rpx;
  color: #666;
}

/* 设备分类 */
.device-categories {
  margin-bottom: 30rpx;
}

.device-category {
  margin-bottom: 24rpx;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 16rpx;
  background-color: white;
  border-radius: 12rpx;
}

.category-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.category-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.category-count {
  font-size: 22rpx;
  color: #666;
}

.device-item {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  border-left: 6rpx solid #4CAF50;
}

.device-item.warning {
  border-left-color: #FF9800;
}

.device-item.offline {
  border-left-color: #F44336;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.device-model {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.device-location {
  font-size: 20rpx;
  color: #888;
}

.device-status {
  margin: 0 20rpx;
  text-align: center;
}

.device-actions {
  display: flex;
  gap: 8rpx;
}

/* 区域划分 */
.zone-division {
  background-color: white;
  padding: 24rpx;
  border-radius: 12rpx;
}

.zone-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.zone-map {
  position: relative;
  height: 300rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx dashed #e0e0e0;
  margin-bottom: 16rpx;
}

.zone-item {
  position: absolute;
  border: 2rpx solid #08C160;
  border-radius: 8rpx;
  background-color: rgba(8, 193, 96, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
}

.zone-item.has-device {
  background-color: rgba(76, 175, 80, 0.2);
}

.zone-item.no-device {
  border-color: #e0e0e0;
  background-color: rgba(224, 224, 224, 0.1);
}

.zone-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.zone-device-count {
  color: #666;
}

.zone-controls {
  display: flex;
  gap: 12rpx;
}

/* 权限设置 */
.permission-settings {
  background-color: white;
  padding: 24rpx;
  border-radius: 12rpx;
}

.permission-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.permission-list {
  margin-top: 20rpx;
}

.permission-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 20rpx;
}

.user-avatar {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.user-role {
  font-size: 22rpx;
  color: #666;
}

.permission-controls {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.permission-group {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.permission-label {
  font-size: 22rpx;
  color: #666;
  min-width: 120rpx;
}

/* 种植规划 */
.planning-calendar {
  background-color: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.current-month {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.calendar-grid {
  margin-bottom: 20rpx;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2rpx;
  margin-bottom: 10rpx;
}

.weekday {
  text-align: center;
  font-size: 22rpx;
  color: #666;
  padding: 8rpx;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  position: relative;
  cursor: pointer;
}

.calendar-day.has-plans {
  background-color: #e8f5e8;
}

.calendar-day.today {
  background-color: #2E7D32;
  color: white;
}

.day-number {
  font-size: 24rpx;
  font-weight: 500;
}

.day-plans {
  position: absolute;
  bottom: 4rpx;
  display: flex;
  gap: 2rpx;
}

.plan-dot {
  width: 6rpx;
  height: 6rpx;
  border-radius: 3rpx;
  background-color: #08C160;
}

.plan-dot.harvest {
  background-color: #FF9800;
}

.plan-dot.planting {
  background-color: #2196F3;
}

.plan-dot.fertilizing {
  background-color: #9C27B0;
}

/* 时间线 */
.planning-timeline {
  background-color: white;
  border-radius: 12rpx;
  padding: 24rpx;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.timeline-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.timeline-filters {
  display: flex;
  gap: 12rpx;
}

.filter-item {
  padding: 8rpx 16rpx;
  background-color: #f0f0f0;
  border-radius: 16rpx;
  font-size: 22rpx;
  color: #666;
}

.filter-item.active {
  background-color: #2E7D32;
  color: white;
}

.timeline-content {
  margin-top: 20rpx;
}

.timeline-item {
  display: flex;
  margin-bottom: 30rpx;
}

.timeline-date {
  width: 80rpx;
  text-align: center;
  margin-right: 20rpx;
}

.month {
  font-size: 20rpx;
  color: #666;
  display: block;
}

.day {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.timeline-connector {
  width: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
}

.timeline-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background-color: #08C160;
  margin-bottom: 12rpx;
}

.timeline-dot.harvest {
  background-color: #FF9800;
}

.timeline-dot.planting {
  background-color: #2196F3;
}

.timeline-dot.fertilizing {
  background-color: #9C27B0;
}

.timeline-line {
  width: 2rpx;
  height: 60rpx;
  background-color: #e0e0e0;
}

.timeline-details {
  flex: 1;
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.plan-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.plan-type {
  font-size: 20rpx;
  color: #666;
  background-color: white;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

.plan-description {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.plan-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.plan-plot,
.plan-crop,
.plan-worker {
  font-size: 22rpx;
  color: #666;
}

.plan-actions {
  display: flex;
  gap: 8rpx;
}

/* 按钮样式 */
.btn {
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(45deg, #08C160, #08C160);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #08C160;
  color: #08C160;
}

.btn-info {
  background-color: #2196F3;
  color: white;
}

.btn-success {
  background-color: #08C160;
  color: white;
}

.btn-warning {
  background-color: #FF9800;
  color: white;
}

.btn-small {
  padding: 12rpx 20rpx;
  font-size: 22rpx;
}

.btn-mini {
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  border-radius: 8rpx;
}