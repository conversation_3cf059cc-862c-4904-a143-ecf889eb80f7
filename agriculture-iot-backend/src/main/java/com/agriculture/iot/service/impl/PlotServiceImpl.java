package com.agriculture.iot.service.impl;

import com.agriculture.iot.entity.Plot;
import com.agriculture.iot.mapper.PlotMapper;
import com.agriculture.iot.service.PlotService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 地块服务实现类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
public class PlotServiceImpl extends ServiceImpl<PlotMapper, Plot> implements PlotService {

    private final PlotMapper plotMapper;

    @Override
    public IPage<Plot> getPlotList(Page<Plot> page, Long farmId, String name, String cropType, Integer status) {
        QueryWrapper<Plot> wrapper = new QueryWrapper<>();
        
        if (farmId != null) {
            wrapper.eq("farm_id", farmId);
        }
        if (name != null && !name.trim().isEmpty()) {
            wrapper.like("name", name);
        }
        if (cropType != null && !cropType.trim().isEmpty()) {
            wrapper.eq("crop_type", cropType);
        }
        if (status != null) {
            wrapper.eq("status", status);
        }
        
        wrapper.orderByDesc("created_at");
        return this.page(page, wrapper);
    }

    @Override
    public Plot getPlotById(Long id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Plot createPlot(Plot plot) {
        // 设置默认状态
        if (plot.getStatus() == null) {
            plot.setStatus(0); // 默认为休耕状态
        }
        
        boolean saved = this.save(plot);
        return saved ? plot : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Plot updatePlot(Plot plot) {
        if (this.getById(plot.getId()) == null) {
            return null;
        }
        
        boolean updated = this.updateById(plot);
        return updated ? plot : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePlot(Long id) {
        if (this.getById(id) == null) {
            return false;
        }
        
        // TODO: 检查是否有关联的设备，如果有需要先解绑
        return this.removeById(id);
    }

    @Override
    public List<Plot> getPlotsByFarmId(Long farmId) {
        QueryWrapper<Plot> wrapper = new QueryWrapper<>();
        wrapper.eq("farm_id", farmId);
        wrapper.orderByDesc("created_at");
        return this.list(wrapper);
    }

    @Override
    public List<Object> getPlotDevices(Long plotId) {
        // TODO: 实现获取地块绑定的设备列表
        // 这里需要查询device表，根据plot_id获取设备列表
        return plotMapper.getPlotDevices(plotId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindDeviceToPlot(Long plotId, Long deviceId) {
        // 检查地块是否存在
        if (this.getById(plotId) == null) {
            return false;
        }
        
        // TODO: 检查设备是否存在
        // TODO: 更新设备的plot_id字段
        return plotMapper.bindDeviceToPlot(plotId, deviceId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindDeviceFromPlot(Long plotId, Long deviceId) {
        // TODO: 将设备的plot_id字段设为null
        return plotMapper.unbindDeviceFromPlot(plotId, deviceId) > 0;
    }

    @Override
    public Plot.Statistics getPlotStatistics(Long plotId) {
        Plot plot = this.getById(plotId);
        if (plot == null) {
            return null;
        }
        
        Plot.Statistics statistics = new Plot.Statistics();
        statistics.setArea(plot.getArea());
        
        // TODO: 统计设备数量
        statistics.setDeviceCount(plotMapper.getDeviceCountByPlotId(plotId));
        statistics.setOnlineDeviceCount(plotMapper.getOnlineDeviceCountByPlotId(plotId));
        statistics.setSensorCount(plotMapper.getSensorCountByPlotId(plotId));
        
        // TODO: 统计本月操作次数
        statistics.setIrrigationCount(plotMapper.getIrrigationCountByPlotId(plotId));
        statistics.setFertilizerCount(plotMapper.getFertilizerCountByPlotId(plotId));
        statistics.setAlarmCount(plotMapper.getAlarmCountByPlotId(plotId));
        
        // TODO: 获取最新传感器数据
        statistics.setSoilHumidity(plotMapper.getLatestSoilHumidity(plotId));
        statistics.setSoilTemperature(plotMapper.getLatestSoilTemperature(plotId));
        statistics.setPhValue(plotMapper.getLatestPhValue(plotId));
        
        // 计算种植天数
        if (plot.getPlantingDate() != null) {
            long plantingDays = ChronoUnit.DAYS.between(plot.getPlantingDate(), LocalDate.now());
            statistics.setPlantingDays((int) plantingDays);
        }
        
        // 计算预计收获天数
        if (plot.getExpectedHarvestDate() != null) {
            long harvestDays = ChronoUnit.DAYS.between(LocalDate.now(), plot.getExpectedHarvestDate());
            statistics.setExpectedHarvestDays((int) harvestDays);
        }
        
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlotStatus(Long plotId, Integer status) {
        Plot plot = this.getById(plotId);
        if (plot == null) {
            return false;
        }
        
        plot.setStatus(status);
        return this.updateById(plot);
    }

    @Override
    public Plot.CropInfo getPlotCropInfo(Long plotId) {
        Plot plot = this.getById(plotId);
        if (plot == null) {
            return null;
        }
        
        Plot.CropInfo cropInfo = new Plot.CropInfo();
        cropInfo.setCropType(plot.getCropType());
        cropInfo.setPlantingDate(plot.getPlantingDate());
        cropInfo.setExpectedHarvestDate(plot.getExpectedHarvestDate());
        cropInfo.setPlantingArea(plot.getArea());
        
        // TODO: 从其他表获取更详细的作物信息
        // 计算生长进度
        if (plot.getPlantingDate() != null && plot.getExpectedHarvestDate() != null) {
            long totalDays = ChronoUnit.DAYS.between(plot.getPlantingDate(), plot.getExpectedHarvestDate());
            long passedDays = ChronoUnit.DAYS.between(plot.getPlantingDate(), LocalDate.now());
            if (totalDays > 0) {
                int progress = (int) ((passedDays * 100) / totalDays);
                cropInfo.setProgress(Math.max(0, Math.min(100, progress)));
            }
        }
        
        // 根据状态设置生长状态
        switch (plot.getStatus()) {
            case 0:
                cropInfo.setGrowthStatus("休耕");
                cropInfo.setGrowthStage("未种植");
                break;
            case 1:
                cropInfo.setGrowthStatus("生长中");
                // TODO: 根据种植时间计算生长阶段
                cropInfo.setGrowthStage("生长期");
                break;
            case 2:
                cropInfo.setGrowthStatus("收获期");
                cropInfo.setGrowthStage("成熟期");
                break;
            default:
                cropInfo.setGrowthStatus("未知");
                cropInfo.setGrowthStage("未知");
        }
        
        return cropInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlotCropInfo(Long plotId, Plot.CropInfo cropInfo) {
        Plot plot = this.getById(plotId);
        if (plot == null) {
            return false;
        }
        
        // 更新地块的作物信息
        plot.setCropType(cropInfo.getCropType());
        plot.setPlantingDate(cropInfo.getPlantingDate());
        plot.setExpectedHarvestDate(cropInfo.getExpectedHarvestDate());
        
        // 如果更新了种植信息，状态改为种植中
        if (cropInfo.getPlantingDate() != null && cropInfo.getCropType() != null) {
            plot.setStatus(1);
        }
        
        return this.updateById(plot);
    }
}