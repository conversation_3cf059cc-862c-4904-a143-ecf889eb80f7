<!--地块绘制页面-->
<view class="container">
  
  <!-- 顶部栏 -->
  <view class="header">
    <button class="back-btn" bindtap="goBack">返回</button>
    <text class="title">地块绘制</text>
    <text class="area">{{area && area > 0 ? area.toFixed(2) + '亩' : ''}}</text>
  </view>
  
  <!-- 搜索栏 -->
  <view class="search-bar">
    <input class="search-input" 
           placeholder="搜索地点定位" 
           value="{{searchKeyword}}"
           bindinput="onSearchInput"
           bindconfirm="searchLocation" />
    <button class="search-btn" bindtap="searchLocation">搜索</button>
  </view>
  
  <!-- 地图 -->
  <view class="map-container">
    <view class="map-box">
      <map wx:if="{{locationReady}}"
        class="map"
        latitude="{{latitude}}"
        longitude="{{longitude}}"
        scale="15"
        markers="{{markers}}"
        polygons="{{polygons}}"
        show-location="true"
        enable-auto-center="false"
        enable-scroll="true"
        enable-zoom="true"
        enable-rotate="false"
        bindtap="onMapTap">
      </map>
      <view wx:else class="loading">正在获取位置...</view>
    </view>
    
    <!-- 浮动工具栏 -->
    <view class="floating-toolbar">
      <text class="tip">
        {{isDrawing ? '点击地图添加边界点 (' + points.length + '个)' : '点击开始绘制地块边界'}}
      </text>
      
      <view class="buttons">
        <button wx:if="{{!isDrawing}}" class="btn start" bindtap="startDrawing">
          开始绘制
        </button>
        
        <button wx:if="{{isDrawing && points.length >= 3}}" class="btn finish" bindtap="finishDrawing">
          完成绘制
        </button>
        
        <button wx:if="{{points.length > 0}}" class="btn clear" bindtap="clearDrawing">
          清除
        </button>
      </view>
    </view>
  </view>
  
</view>