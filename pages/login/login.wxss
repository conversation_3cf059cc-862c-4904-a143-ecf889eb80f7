/* 智慧农业登录页面样式 - 原生版本 */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
  display: flex;
  flex-direction: column;
  padding: 60rpx 40rpx 40rpx;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 80rpx;
  padding-top: 60rpx;
}

.logo-canvas-center {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-bottom: -100rpx;
}
.logo-canvas {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.logo-image {
  width: 400rpx;
  height: 380rpx;
  
  
 
}

.app-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #08C160;
  margin-bottom: 16rpx;
  letter-spacing: 2rpx;
}

.app-subtitle {
  display: block;
  font-size: 28rpx;
  color: #08C160;
  font-weight: 400;
  letter-spacing: 1rpx;
}

/* 登录方式区域 */
.login-methods {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.login-method-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(46, 125, 50, 0.1);
}

/* 微信登录按钮 */
.wx-login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg,rgb(1, 215, 105) 0%,rgb(8, 218, 15) 100%);
  border: none;
  border-radius: 12rpx;
  color: white;
  font-size: 30rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.wx-login-btn:after {
  border: none;
}

.btn-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 手机号登录表单 */
.phone-login-form {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.phone-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background: #FAFAFA;
  border: 1rpx solid #E0E0E0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.code-input-group {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.code-input {
  flex: 1;
  height: 88rpx;
  padding: 0 24rpx;
  background: #FAFAFA;
  border: 1rpx solid #E0E0E0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.send-code-btn {
  min-width: 140rpx;
  height: 88rpx;
  background: white;
  border: 1rpx solid #2E7D32;
  border-radius: 12rpx;
  color: #2E7D32;
  font-size: 24rpx;
  font-weight: 500;
}

.send-code-btn:after {
  border: none;
}

.send-code-btn[disabled] {
  background: #f0f0f0;
  color: #ccc;
  border-color: #e0e0e0;
}

.phone-login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  border: none;
  border-radius: 12rpx;
  color: white;
  font-size: 30rpx;
  font-weight: 600;
  margin-top: 16rpx;
}

.phone-login-btn:after {
  border: none;
}

.phone-login-btn[disabled] {
  background: #ccc;
}

/* 切换登录方式 */
.switch-login-method {
  text-align: center;
  margin: 20rpx 0;
}

.switch-btn {
  background: transparent;
  border: none;
  color: #2E7D32;
  font-size: 26rpx;
  font-weight: 500;
  text-decoration: underline;
}

.switch-btn:after {
  border: none;
}

/* 用户协议区域 */
.agreement-section {
  margin: 40rpx 0;
  padding: 0 20rpx;
}

.agreement-check {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.6;
  flex: 1;
}

.link-text {
  color: #2E7D32;
  text-decoration: underline;
  font-weight: 500;
}

/* 底部信息 */
.footer-info {
  text-align: center;
  margin-top: auto;
  padding-top: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.version-text {
  font-size: 22rpx;
  color: #999999;
}

.copyright-text {
  font-size: 22rpx;
  color: #BBBBBB;
}

/* 协议弹窗 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-content {
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.dialog-header {
  padding: 32rpx 32rpx 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.dialog-close {
  font-size: 48rpx;
  color: #999;
  cursor: pointer;
}

.agreement-content {
  max-height: 60vh;
  padding: 32rpx;
  font-size: 26rpx;
  line-height: 1.6;
  color: #333333;
}

.dialog-footer {
  padding: 16rpx 32rpx 32rpx;
  display: flex;
  justify-content: center;
}

.dialog-footer button {
  width: 200rpx;
  height: 72rpx;
  background: #2E7D32;
  border: none;
  border-radius: 8rpx;
  color: white;
  font-size: 28rpx;
}

.dialog-footer button:after {
  border: none;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .login-container {
    padding: 40rpx 30rpx 30rpx;
  }
  
  .logo-section {
    padding-top: 40rpx;
    margin-bottom: 60rpx;
  }
  
  .app-title {
    font-size: 42rpx;
  }
  
  .login-method-card {
    padding: 30rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .login-container {
    background: linear-gradient(135deg, #1B5E20 0%, #2E7D32 100%);
  }
  
  .login-method-card {
    background: rgba(255, 255, 255, 0.95);
  }
  
  .app-title {
    color: white;
  }
  
  .app-subtitle {
    color: rgba(255, 255, 255, 0.8);
  }
}