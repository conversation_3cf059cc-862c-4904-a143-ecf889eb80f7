/**
 * 日志相关Mock API
 */

const dataModule = require('./data');

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 验证Token
function verifyToken(token) {
  // 在Mock环境中，简化Token验证，始终返回有效的payload
  return {
    userId: 'user_001',
    username: 'testuser',
    exp: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
  };
}

// 生成模拟操作日志
function generateOperationLogs() {
  const logTypes = [
    { type: 'irrigation', title: '自动灌溉', icon: 'water', description: '自动灌溉任务完成' },
    { type: 'fertilization', title: '施肥操作', icon: 'fertilizer', description: '肥料配方施用完成' },
    { type: 'maintenance', title: '设备维护', icon: 'maintenance', description: '系统例行检查' },
    { type: 'config', title: '配置更新', icon: 'config', description: '设备配置已更新' },
    { type: 'alarm', title: '异常处理', icon: 'alarm', description: '异常情况已处理' }
  ];

  const statusTypes = ['success', 'failed', 'pending', 'warning'];
  const statusTexts = { success: '成功', failed: '失败', pending: '进行中', warning: '警告' };

  const logs = [];
  for (let i = 0; i < 20; i++) {
    const logType = logTypes[Math.floor(Math.random() * logTypes.length)];
    const status = statusTypes[Math.floor(Math.random() * statusTypes.length)];
    const time = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000); // 最近7天

    logs.push({
      id: `log_${String(i + 1).padStart(3, '0')}`,
      type: logType.type,
      title: logType.title,
      description: logType.description,
      icon: logType.icon,
      status: status,
      statusText: statusTexts[status],
      time: time.toISOString(),
      deviceId: `device_${String(Math.floor(Math.random() * 4) + 1).padStart(3, '0')}`,
      plotId: `plot_${String(Math.floor(Math.random() * 3) + 1).padStart(3, '0')}`,
      userId: 'user_001',
      details: [
        { label: '执行时长', value: `${Math.floor(Math.random() * 60) + 1}分钟` },
        { label: '操作结果', value: statusTexts[status] },
        { label: '设备状态', value: '正常' }
      ]
    });
  }

  return logs.sort((a, b) => new Date(b.time) - new Date(a.time));
}

module.exports = {
  init() { 
    console.log('日志模块初始化完成'); 
  },
  
  async getOperationLogs(token, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { type, startDate, endDate, page = 1, limit = 50 } = params;
    
    let logs = generateOperationLogs();
    
    // 类型过滤
    if (type && type !== 'all') {
      logs = logs.filter(log => log.type === type);
    }
    
    // 日期过滤
    if (startDate || endDate) {
      const start = startDate ? new Date(startDate) : new Date('1900-01-01');
      const end = endDate ? new Date(endDate) : new Date('2100-12-31');
      
      logs = logs.filter(log => {
        const logDate = new Date(log.time);
        return logDate >= start && logDate <= end;
      });
    }
    
    // 分页处理
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedLogs = logs.slice(startIndex, endIndex);
    
    return createApiResponse({
      logs: paginatedLogs,
      stats: {
        totalOperations: logs.length,
        successRate: Math.round((logs.filter(l => l.status === 'success').length / logs.length) * 100 * 10) / 10,
        todayOperations: logs.filter(l => {
          const today = new Date().toISOString().split('T')[0];
          const logDate = new Date(l.time).toISOString().split('T')[0];
          return logDate === today;
        }).length
      },
      pagination: {
        page,
        limit,
        total: logs.length,
        hasMore: endIndex < logs.length
      }
    });
  },
  
  async getReports(token, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { type = 'daily' } = params;
    
    const reportData = {
      type: type,
      period: type === 'daily' ? '今日' : type === 'weekly' ? '本周' : '本月',
      totalRunTime: '24小时30分钟',
      irrigationCount: 8,
      fertilizerCount: 3,
      waterUsage: 450,
      fertilizerUsage: 12.5,
      efficiency: 95.2,
      irrigationEfficiency: 92,
      fertilizerEfficiency: 88,
      systemUptime: 99,
      generatedAt: new Date().toISOString()
    };
    
    return createApiResponse(reportData);
  },
  
  async getAlerts(token, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { level } = params;
    
    const alerts = [
      {
        id: 'alert_001',
        level: 'error',
        title: '水压异常',
        description: '系统水压持续低于安全阈值',
        time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        resolved: false
      },
      {
        id: 'alert_002', 
        level: 'warning',
        title: '肥料余量不足',
        description: '肥料B储量低于20%',
        time: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        resolved: false
      }
    ];
    
    let filteredAlerts = alerts;
    if (level && level !== 'all') {
      filteredAlerts = alerts.filter(alert => alert.level === level);
    }
    
    return createApiResponse({
      alerts: filteredAlerts,
      stats: {
        warnings: alerts.filter(a => a.level === 'warning').length,
        errors: alerts.filter(a => a.level === 'error').length,
        resolved: alerts.filter(a => a.resolved).length
      }
    });
  },
  
  async resolveAlert(token, alertId, solution) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    return createApiResponse({
      alertId: alertId,
      solution: solution,
      resolvedAt: new Date().toISOString(),
      resolvedBy: payload.userId
    }, '异常已标记为已解决');
  },
  
  async exportLogs(token, params) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    return createApiResponse({
      exportId: `export_${Date.now()}`,
      format: params.format || 'excel',
      fileName: `operation_logs_${new Date().toISOString().split('T')[0]}.xlsx`,
      downloadUrl: `/api/downloads/export_${Date.now()}.xlsx`,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    }, '日志导出任务已创建');
  },
  
  async retryOperation(token, logId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const success = Math.random() > 0.3; // 70% 成功率
    
    return createApiResponse({
      logId: logId,
      success: success,
      newLogId: success ? `log_retry_${Date.now()}` : null,
      message: success ? '操作重试成功' : '操作重试失败，请检查设备状态'
    }, success ? '重试成功' : '重试失败');
  }
};