package com.agriculture.iot.service.impl;

import com.agriculture.iot.entity.IrrigationRecord;
import com.agriculture.iot.entity.IrrigationSchedule;
import com.agriculture.iot.mapper.IrrigationRecordMapper;
import com.agriculture.iot.mapper.IrrigationScheduleMapper;
import com.agriculture.iot.service.IrrigationService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 灌溉服务实现类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IrrigationServiceImpl extends ServiceImpl<IrrigationRecordMapper, IrrigationRecord> implements IrrigationService {

    private final IrrigationRecordMapper irrigationRecordMapper;
    private final IrrigationScheduleMapper irrigationScheduleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> startIrrigation(IrrigationRecord.StartRequest startRequest) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建灌溉记录
            IrrigationRecord record = new IrrigationRecord();
            record.setDeviceId(startRequest.getDeviceId());
            record.setPlotId(startRequest.getPlotId());
            record.setStartTime(LocalDateTime.now());
            record.setDuration(startRequest.getDuration());
            record.setWaterAmount(startRequest.getWaterAmount());
            record.setMode(startRequest.getMode());
            record.setStatus(1); // 运行中
            record.setTriggerSource(startRequest.getTriggerSource());
            record.setNotes(startRequest.getNotes());
            
            boolean saved = this.save(record);
            if (saved) {
                result.put("success", true);
                result.put("message", "灌溉启动成功");
                result.put("recordId", record.getId());
                result.put("operationId", UUID.randomUUID().toString());
                
                // TODO: 发送设备控制指令
                // 这里应该调用设备控制服务启动实际的灌溉设备
                
                log.info("灌溉启动成功，设备ID: {}, 地块ID: {}, 记录ID: {}", 
                        startRequest.getDeviceId(), startRequest.getPlotId(), record.getId());
            } else {
                result.put("success", false);
                result.put("message", "灌溉启动失败");
            }
            
        } catch (Exception e) {
            log.error("灌溉启动失败", e);
            result.put("success", false);
            result.put("message", "灌溉启动失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> stopIrrigation(IrrigationRecord.StopRequest stopRequest) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            IrrigationRecord record = this.getById(stopRequest.getRecordId());
            if (record == null) {
                result.put("success", false);
                result.put("message", "灌溉记录不存在");
                return result;
            }
            
            // 更新灌溉记录
            record.setEndTime(LocalDateTime.now());
            record.setStatus(2); // 已完成
            record.setNotes(stopRequest.getNotes());
            
            boolean updated = this.updateById(record);
            if (updated) {
                result.put("success", true);
                result.put("message", "灌溉停止成功");
                result.put("recordId", record.getId());
                result.put("operationId", UUID.randomUUID().toString());
                
                // TODO: 发送设备控制指令
                // 这里应该调用设备控制服务停止实际的灌溉设备
                
                log.info("灌溉停止成功，记录ID: {}", stopRequest.getRecordId());
            } else {
                result.put("success", false);
                result.put("message", "灌溉停止失败");
            }
            
        } catch (Exception e) {
            log.error("灌溉停止失败", e);
            result.put("success", false);
            result.put("message", "灌溉停止失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public IPage<IrrigationRecord> getIrrigationRecords(Page<IrrigationRecord> page, Long deviceId, Long plotId, 
                                                       LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<IrrigationRecord> wrapper = new QueryWrapper<>();
        
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        if (startTime != null) {
            wrapper.ge("start_time", startTime);
        }
        if (endTime != null) {
            wrapper.le("end_time", endTime);
        }
        
        wrapper.orderByDesc("start_time");
        return this.page(page, wrapper);
    }

    @Override
    public IrrigationRecord.StatusInfo getIrrigationStatus(Long deviceId) {
        // 获取设备当前的灌溉状态
        QueryWrapper<IrrigationRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("device_id", deviceId);
        wrapper.eq("status", 1); // 运行中
        wrapper.orderByDesc("start_time");
        wrapper.last("LIMIT 1");
        
        IrrigationRecord currentRecord = this.getOne(wrapper);
        
        IrrigationRecord.StatusInfo statusInfo = new IrrigationRecord.StatusInfo();
        statusInfo.setDeviceId(deviceId);
        statusInfo.setUpdateTime(LocalDateTime.now());
        
        if (currentRecord != null) {
            statusInfo.setIsRunning(true);
            statusInfo.setCurrentRecordId(currentRecord.getId());
            statusInfo.setStartTime(currentRecord.getStartTime());
            statusInfo.setDuration(currentRecord.getDuration());
            statusInfo.setWaterAmount(currentRecord.getWaterAmount());
            statusInfo.setMode(currentRecord.getMode());
            
            // 计算运行时间
            long runningMinutes = java.time.Duration.between(currentRecord.getStartTime(), LocalDateTime.now()).toMinutes();
            statusInfo.setRunningTime((int) runningMinutes);
        } else {
            statusInfo.setIsRunning(false);
        }
        
        return statusInfo;
    }

    @Override
    public IPage<IrrigationSchedule> getIrrigationSchedules(Page<IrrigationSchedule> page, Long deviceId, Long plotId, Integer status) {
        QueryWrapper<IrrigationSchedule> wrapper = new QueryWrapper<>();
        
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        if (status != null) {
            wrapper.eq("status", status);
        }
        
        wrapper.orderByDesc("created_at");
        return irrigationScheduleMapper.selectPage(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IrrigationSchedule createIrrigationSchedule(IrrigationSchedule schedule) {
        schedule.setCreatedAt(LocalDateTime.now());
        schedule.setUpdatedAt(LocalDateTime.now());
        
        if (schedule.getStatus() == null) {
            schedule.setStatus(1); // 默认启用
        }
        
        int result = irrigationScheduleMapper.insert(schedule);
        return result > 0 ? schedule : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IrrigationSchedule updateIrrigationSchedule(IrrigationSchedule schedule) {
        schedule.setUpdatedAt(LocalDateTime.now());
        
        int result = irrigationScheduleMapper.updateById(schedule);
        return result > 0 ? schedule : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIrrigationSchedule(Long id) {
        return irrigationScheduleMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeIrrigationSchedule(Long scheduleId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            IrrigationSchedule schedule = irrigationScheduleMapper.selectById(scheduleId);
            if (schedule == null) {
                result.put("success", false);
                result.put("message", "灌溉计划不存在");
                return result;
            }
            
            if (schedule.getStatus() != 1) {
                result.put("success", false);
                result.put("message", "灌溉计划已禁用");
                return result;
            }
            
            // 创建启动请求
            IrrigationRecord.StartRequest startRequest = new IrrigationRecord.StartRequest();
            startRequest.setDeviceId(schedule.getDeviceId());
            startRequest.setPlotId(schedule.getPlotId());
            startRequest.setDuration(schedule.getDuration());
            startRequest.setWaterAmount(schedule.getWaterAmount());
            startRequest.setMode(schedule.getMode());
            startRequest.setTriggerSource("schedule");
            startRequest.setNotes("计划执行：" + schedule.getName());
            
            // 执行灌溉
            Map<String, Object> startResult = startIrrigation(startRequest);
            
            if ((Boolean) startResult.get("success")) {
                // 更新计划的最后执行时间
                schedule.setLastExecuteTime(LocalDateTime.now());
                schedule.setExecuteCount(schedule.getExecuteCount() + 1);
                irrigationScheduleMapper.updateById(schedule);
                
                result.put("success", true);
                result.put("message", "计划执行成功");
                result.put("scheduleId", scheduleId);
                result.put("recordId", startResult.get("recordId"));
            } else {
                result.put("success", false);
                result.put("message", "计划执行失败：" + startResult.get("message"));
            }
            
        } catch (Exception e) {
            log.error("执行灌溉计划失败", e);
            result.put("success", false);
            result.put("message", "计划执行失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateScheduleStatus(Long scheduleId, Integer status) {
        IrrigationSchedule schedule = irrigationScheduleMapper.selectById(scheduleId);
        if (schedule == null) {
            return false;
        }
        
        schedule.setStatus(status);
        schedule.setUpdatedAt(LocalDateTime.now());
        
        return irrigationScheduleMapper.updateById(schedule) > 0;
    }

    @Override
    public IrrigationRecord.Statistics getIrrigationStatistics(Long deviceId, Long plotId, Integer days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        LocalDateTime endTime = LocalDateTime.now();
        
        QueryWrapper<IrrigationRecord> wrapper = new QueryWrapper<>();
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        wrapper.ge("start_time", startTime);
        wrapper.le("start_time", endTime);
        
        List<IrrigationRecord> records = this.list(wrapper);
        
        IrrigationRecord.Statistics statistics = new IrrigationRecord.Statistics();
        statistics.setDeviceId(deviceId);
        statistics.setPlotId(plotId);
        statistics.setStatisticsDays(days);
        statistics.setTotalRecords(records.size());
        
        if (!records.isEmpty()) {
            // 计算统计数据
            int totalDuration = 0;
            double totalWaterAmount = 0.0;
            int successCount = 0;
            
            for (IrrigationRecord record : records) {
                totalDuration += record.getDuration() != null ? record.getDuration() : 0;
                totalWaterAmount += record.getWaterAmount() != null ? record.getWaterAmount().doubleValue() : 0.0;
                if (record.getStatus() == 2) { // 已完成
                    successCount++;
                }
            }
            
            statistics.setTotalDuration(totalDuration);
            statistics.setTotalWaterAmount(totalWaterAmount);
            statistics.setSuccessCount(successCount);
            statistics.setSuccessRate(records.size() > 0 ? (double) successCount / records.size() : 0.0);
            statistics.setAvgDuration(records.size() > 0 ? totalDuration / records.size() : 0);
            statistics.setAvgWaterAmount(records.size() > 0 ? totalWaterAmount / records.size() : 0.0);
        }
        
        return statistics;
    }

    @Override
    public IrrigationRecord.EfficiencyData getIrrigationEfficiency(Long deviceId, Long plotId, 
                                                                  LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<IrrigationRecord> wrapper = new QueryWrapper<>();
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        wrapper.ge("start_time", startTime);
        wrapper.le("end_time", endTime);
        wrapper.eq("status", 2); // 已完成
        
        List<IrrigationRecord> records = this.list(wrapper);
        
        IrrigationRecord.EfficiencyData efficiencyData = new IrrigationRecord.EfficiencyData();
        efficiencyData.setDeviceId(deviceId);
        efficiencyData.setPlotId(plotId);
        efficiencyData.setStartTime(startTime);
        efficiencyData.setEndTime(endTime);
        
        if (!records.isEmpty()) {
            // 计算效率数据
            double totalWaterAmount = 0.0;
            int totalDuration = 0;
            
            for (IrrigationRecord record : records) {
                totalWaterAmount += record.getWaterAmount() != null ? record.getWaterAmount().doubleValue() : 0.0;
                totalDuration += record.getDuration() != null ? record.getDuration() : 0;
            }
            
            efficiencyData.setTotalWaterAmount(totalWaterAmount);
            efficiencyData.setTotalDuration(totalDuration);
            efficiencyData.setWaterEfficiency(totalDuration > 0 ? totalWaterAmount / totalDuration * 60 : 0.0); // 每小时用水量
            efficiencyData.setRecordCount(records.size());
        }
        
        return efficiencyData;
    }

    @Override
    public IrrigationRecord.ReportData getIrrigationReport(String reportType, Long deviceId, Long plotId, 
                                                          LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<IrrigationRecord> wrapper = new QueryWrapper<>();
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        wrapper.ge("start_time", startTime);
        wrapper.le("end_time", endTime);
        wrapper.orderByAsc("start_time");
        
        List<IrrigationRecord> records = this.list(wrapper);
        
        IrrigationRecord.ReportData reportData = new IrrigationRecord.ReportData();
        reportData.setReportType(reportType);
        reportData.setDeviceId(deviceId);
        reportData.setPlotId(plotId);
        reportData.setStartTime(startTime);
        reportData.setEndTime(endTime);
        reportData.setRecords(records);
        
        // 生成报表摘要
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalRecords", records.size());
        summary.put("generateTime", LocalDateTime.now());
        
        if (!records.isEmpty()) {
            double totalWaterAmount = 0.0;
            int totalDuration = 0;
            
            for (IrrigationRecord record : records) {
                totalWaterAmount += record.getWaterAmount() != null ? record.getWaterAmount().doubleValue() : 0.0;
                totalDuration += record.getDuration() != null ? record.getDuration() : 0;
            }
            
            summary.put("totalWaterAmount", totalWaterAmount);
            summary.put("totalDuration", totalDuration);
            summary.put("avgWaterAmount", totalWaterAmount / records.size());
            summary.put("avgDuration", totalDuration / records.size());
        }
        
        reportData.setSummary(summary);
        
        return reportData;
    }

    @Override
    public List<IrrigationSchedule.Template> getIrrigationTemplates() {
        return irrigationScheduleMapper.selectIrrigationTemplates();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IrrigationSchedule applyIrrigationTemplate(Long templateId, Long deviceId, 
                                                     IrrigationSchedule.TemplateParams templateParams) {
        IrrigationSchedule.Template template = irrigationScheduleMapper.selectTemplateById(templateId);
        if (template == null) {
            return null;
        }
        
        // 根据模板创建计划
        IrrigationSchedule schedule = new IrrigationSchedule();
        schedule.setName(template.getName() + " - " + LocalDateTime.now().toLocalDate());
        schedule.setDeviceId(deviceId);
        schedule.setPlotId(templateParams.getPlotId());
        schedule.setDuration(templateParams.getDuration() != null ? templateParams.getDuration() : template.getDuration());
        schedule.setWaterAmount(templateParams.getWaterAmount() != null ? templateParams.getWaterAmount() : template.getWaterAmount());
        schedule.setMode(template.getMode());
        schedule.setScheduleType(template.getScheduleType());
        schedule.setStartTime(templateParams.getStartTime());
        schedule.setEndTime(templateParams.getEndTime());
        schedule.setRepeatDays(templateParams.getRepeatDays());
        schedule.setStatus(1); // 启用
        schedule.setCreatedAt(LocalDateTime.now());
        schedule.setUpdatedAt(LocalDateTime.now());
        
        int result = irrigationScheduleMapper.insert(schedule);
        return result > 0 ? schedule : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchControlIrrigation(IrrigationRecord.BatchControlRequest batchRequest) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            int successCount = 0;
            int failedCount = 0;
            
            for (IrrigationRecord.StartRequest startRequest : batchRequest.getStartRequests()) {
                Map<String, Object> startResult = startIrrigation(startRequest);
                if ((Boolean) startResult.get("success")) {
                    successCount++;
                } else {
                    failedCount++;
                }
            }
            
            result.put("success", true);
            result.put("message", "批量控制完成");
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("totalCount", batchRequest.getStartRequests().size());
            
        } catch (Exception e) {
            log.error("批量控制灌溉失败", e);
            result.put("success", false);
            result.put("message", "批量控制失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public IrrigationRecord.TrendData getIrrigationTrends(Long deviceId, Long plotId, String trendType, 
                                                         LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<IrrigationRecord> wrapper = new QueryWrapper<>();
        if (deviceId != null) {
            wrapper.eq("device_id", deviceId);
        }
        if (plotId != null) {
            wrapper.eq("plot_id", plotId);
        }
        wrapper.ge("start_time", startTime);
        wrapper.le("end_time", endTime);
        wrapper.orderByAsc("start_time");
        
        List<IrrigationRecord> records = this.list(wrapper);
        
        IrrigationRecord.TrendData trendData = new IrrigationRecord.TrendData();
        trendData.setDeviceId(deviceId);
        trendData.setPlotId(plotId);
        trendData.setTrendType(trendType);
        trendData.setStartTime(startTime);
        trendData.setEndTime(endTime);
        trendData.setRecords(records);
        
        return trendData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setIrrigationThreshold(IrrigationRecord.ThresholdConfig thresholdConfig) {
        // TODO: 实现阈值配置的保存
        // 这里应该保存到阈值配置表
        return true;
    }

    @Override
    public List<IrrigationRecord.ThresholdConfig> getIrrigationThresholds(Long deviceId, Long plotId) {
        // TODO: 实现阈值配置的查询
        // 这里应该从阈值配置表查询
        return irrigationRecordMapper.selectIrrigationThresholds(deviceId, plotId);
    }
}