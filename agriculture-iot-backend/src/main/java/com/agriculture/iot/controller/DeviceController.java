package com.agriculture.iot.controller;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.Device;
import com.agriculture.iot.entity.DeviceType;
import com.agriculture.iot.service.DeviceService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 设备管理控制器
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Tag(name = "设备管理", description = "设备信息的增删改查和控制操作")
@RestController
@RequestMapping("/api/devices")
@RequiredArgsConstructor
public class DeviceController {

    private final DeviceService deviceService;

    @Operation(summary = "获取设备列表", description = "分页获取设备列表，支持按类型、状态筛选")
    @GetMapping
    public Result<IPage<Device>> getDeviceList(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "设备名称") @RequestParam(required = false) String name,
            @Parameter(description = "设备类型ID") @RequestParam(required = false) Long deviceTypeId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "在线状态") @RequestParam(required = false) Integer onlineStatus,
            @Parameter(description = "设备状态") @RequestParam(required = false) Integer status) {
        
        Page<Device> page = new Page<>(current, size);
        IPage<Device> result = deviceService.getDeviceList(page, name, deviceTypeId, plotId, onlineStatus, status);
        return Result.success(result);
    }

    @Operation(summary = "获取设备详情", description = "根据ID获取设备详细信息")
    @GetMapping("/{id}")
    public Result<Device> getDeviceById(
            @Parameter(description = "设备ID") @PathVariable Long id) {
        
        Device device = deviceService.getDeviceById(id);
        if (device == null) {
            return Result.notFound("设备不存在");
        }
        return Result.success(device);
    }

    @Operation(summary = "创建设备", description = "创建新的设备")
    @PostMapping
    public Result<Device> createDevice(@Valid @RequestBody Device device) {
        Device createdDevice = deviceService.createDevice(device);
        return Result.success("设备创建成功", createdDevice);
    }

    @Operation(summary = "更新设备", description = "更新设备信息")
    @PutMapping("/{id}")
    public Result<Device> updateDevice(
            @Parameter(description = "设备ID") @PathVariable Long id,
            @Valid @RequestBody Device device) {
        
        device.setId(id);
        Device updatedDevice = deviceService.updateDevice(device);
        if (updatedDevice == null) {
            return Result.notFound("设备不存在");
        }
        return Result.success("设备更新成功", updatedDevice);
    }

    @Operation(summary = "删除设备", description = "根据ID删除设备")
    @DeleteMapping("/{id}")
    public Result<Void> deleteDevice(
            @Parameter(description = "设备ID") @PathVariable Long id) {
        
        boolean deleted = deviceService.deleteDevice(id);
        if (!deleted) {
            return Result.notFound("设备不存在");
        }
        return Result.success("设备删除成功", null);
    }

    @Operation(summary = "设备控制", description = "对设备进行控制操作（启停、参数调节）")
    @PostMapping("/{id}/control")
    public Result<Map<String, Object>> controlDevice(
            @Parameter(description = "设备ID") @PathVariable Long id,
            @Valid @RequestBody Device.ControlRequest controlRequest) {
        
        Map<String, Object> result = deviceService.controlDevice(id, controlRequest);
        if (result == null) {
            return Result.error("设备控制失败");
        }
        return Result.success("设备控制成功", result);
    }

    @Operation(summary = "获取设备实时状态", description = "获取设备的实时运行状态")
    @GetMapping("/{id}/status")
    public Result<Device.StatusInfo> getDeviceStatus(
            @Parameter(description = "设备ID") @PathVariable Long id) {
        
        Device.StatusInfo statusInfo = deviceService.getDeviceStatus(id);
        if (statusInfo == null) {
            return Result.notFound("设备不存在");
        }
        return Result.success(statusInfo);
    }

    @Operation(summary = "获取设备类型列表", description = "获取所有设备类型")
    @GetMapping("/types")
    public Result<List<DeviceType>> getDeviceTypes() {
        List<DeviceType> deviceTypes = deviceService.getDeviceTypes();
        return Result.success(deviceTypes);
    }

    @Operation(summary = "绑定设备到地块", description = "将设备绑定到指定地块")
    @PostMapping("/{id}/bind")
    public Result<Void> bindDeviceToPlot(
            @Parameter(description = "设备ID") @PathVariable Long id,
            @Parameter(description = "地块ID") @RequestParam Long plotId) {
        
        boolean success = deviceService.bindDeviceToPlot(id, plotId);
        if (!success) {
            return Result.error("设备绑定失败");
        }
        return Result.success("设备绑定成功", null);
    }

    @Operation(summary = "解绑设备", description = "将设备从地块解绑")
    @DeleteMapping("/{id}/bind")
    public Result<Void> unbindDevice(
            @Parameter(description = "设备ID") @PathVariable Long id) {
        
        boolean success = deviceService.unbindDevice(id);
        if (!success) {
            return Result.error("设备解绑失败");
        }
        return Result.success("设备解绑成功", null);
    }

    @Operation(summary = "获取设备操作记录", description = "获取设备的操作历史记录")
    @GetMapping("/{id}/logs")
    public Result<IPage<Object>> getDeviceLogs(
            @Parameter(description = "设备ID") @PathVariable Long id,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "操作类型") @RequestParam(required = false) String operationType) {
        
        Page<Object> page = new Page<>(current, size);
        IPage<Object> result = deviceService.getDeviceLogs(id, page, operationType);
        return Result.success(result);
    }

    @Operation(summary = "获取设备统计信息", description = "获取设备的统计数据")
    @GetMapping("/{id}/statistics")
    public Result<Device.Statistics> getDeviceStatistics(
            @Parameter(description = "设备ID") @PathVariable Long id) {
        
        Device.Statistics statistics = deviceService.getDeviceStatistics(id);
        if (statistics == null) {
            return Result.notFound("设备不存在");
        }
        return Result.success(statistics);
    }

    @Operation(summary = "批量更新设备状态", description = "批量更新多个设备的状态")
    @PatchMapping("/batch/status")
    public Result<Void> batchUpdateDeviceStatus(
            @Parameter(description = "设备ID列表") @RequestParam List<Long> deviceIds,
            @Parameter(description = "状态") @RequestParam Integer status) {
        
        boolean success = deviceService.batchUpdateDeviceStatus(deviceIds, status);
        if (!success) {
            return Result.error("批量更新失败");
        }
        return Result.success("批量更新成功", null);
    }

    @Operation(summary = "设备健康检查", description = "检查设备健康状态")
    @PostMapping("/{id}/health-check")
    public Result<Device.HealthInfo> deviceHealthCheck(
            @Parameter(description = "设备ID") @PathVariable Long id) {
        
        Device.HealthInfo healthInfo = deviceService.deviceHealthCheck(id);
        if (healthInfo == null) {
            return Result.notFound("设备不存在");
        }
        return Result.success(healthInfo);
    }

    @Operation(summary = "获取设备参数配置", description = "获取设备的参数配置信息")
    @GetMapping("/{id}/config")
    public Result<Device.ConfigInfo> getDeviceConfig(
            @Parameter(description = "设备ID") @PathVariable Long id) {
        
        Device.ConfigInfo configInfo = deviceService.getDeviceConfig(id);
        if (configInfo == null) {
            return Result.notFound("设备不存在");
        }
        return Result.success(configInfo);
    }

    @Operation(summary = "更新设备参数配置", description = "更新设备的参数配置")
    @PutMapping("/{id}/config")
    public Result<Void> updateDeviceConfig(
            @Parameter(description = "设备ID") @PathVariable Long id,
            @Valid @RequestBody Device.ConfigInfo configInfo) {
        
        boolean success = deviceService.updateDeviceConfig(id, configInfo);
        if (!success) {
            return Result.error("配置更新失败");
        }
        return Result.success("配置更新成功", null);
    }
}