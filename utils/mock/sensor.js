/**
 * 传感器相关Mock API
 */

const dataModule = require('./data');

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 验证Token
function verifyToken(token) {
  // 在Mock环境中，简化Token验证，始终返回有效的payload
  return {
    userId: 'user_001',
    username: 'testuser',
    exp: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
  };
}

module.exports = {
  init() {
    console.log('传感器模块初始化完成');
  },

  // 获取传感器列表
  async list(token, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { farmId, plotId, type, status, page = 1, pageSize = 20 } = params;
    
    let sensors = dataModule.getSensors();

    // 过滤条件
    if (farmId) {
      sensors = sensors.filter(sensor => sensor.farmId === farmId);
    }
    
    if (plotId) {
      sensors = sensors.filter(sensor => sensor.plotId === plotId);
    }
    
    if (type) {
      sensors = sensors.filter(sensor => sensor.type === type);
    }
    
    if (status) {
      sensors = sensors.filter(sensor => sensor.status.online === (status === 'online'));
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedSensors = sensors.slice(startIndex, endIndex);

    return createApiResponse({
      sensors: paginatedSensors,
      pagination: {
        page,
        pageSize,
        total: sensors.length,
        totalPages: Math.ceil(sensors.length / pageSize),
        hasNext: endIndex < sensors.length,
        hasPrev: page > 1
      },
      summary: {
        total: sensors.length,
        online: sensors.filter(s => s.status.online).length,
        offline: sensors.filter(s => !s.status.online).length,
        lowBattery: sensors.filter(s => s.status.batteryLevel < 20).length,
        weakSignal: sensors.filter(s => s.status.signalStrength < -60).length
      }
    });
  },

  // 获取最新传感器数据
  async getLatestAll(token) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const sensors = dataModule.getSensors();
    
    const latestData = sensors.map(sensor => {
      const plot = dataModule.getPlotById(sensor.plotId);
      
      return {
        sensorId: sensor.id,
        sensorName: sensor.name,
        sensorType: sensor.type,
        plotId: sensor.plotId,
        plotName: plot ? plot.name : '未知地块',
        status: sensor.status,
        currentReading: sensor.currentReading,
        thresholds: sensor.thresholds,
        alerts: checkSensorAlerts(sensor),
        lastUpdate: sensor.currentReading.timestamp
      };
    });

    return createApiResponse({
      sensors: latestData,
      summary: {
        total: sensors.length,
        online: sensors.filter(s => s.status.online).length,
        normal: latestData.filter(s => s.alerts.length === 0).length,
        warning: latestData.filter(s => s.alerts.some(a => a.level === 'warning')).length,
        critical: latestData.filter(s => s.alerts.some(a => a.level === 'critical')).length
      }
    });
  },

  // 获取单个传感器最新数据
  async getLatest(token, sensorId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const sensor = dataModule.getSensorById(sensorId);
    if (!sensor) {
      return createErrorResponse('传感器不存在', 404);
    }

    const plot = dataModule.getPlotById(sensor.plotId);
    
    return createApiResponse({
      sensor: {
        id: sensor.id,
        name: sensor.name,
        type: sensor.type,
        model: sensor.model,
        plotId: sensor.plotId,
        plotName: plot ? plot.name : '未知地块',
        installLocation: sensor.installLocation,
        installDepth: sensor.installDepth,
        status: sensor.status,
        capabilities: sensor.capabilities,
        currentReading: sensor.currentReading,
        thresholds: sensor.thresholds,
        alerts: checkSensorAlerts(sensor)
      }
    });
  },

  // 获取传感器历史数据
  async getHistory(token, sensorId, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const sensor = dataModule.getSensorById(sensorId);
    if (!sensor) {
      return createErrorResponse('传感器不存在', 404);
    }

    const { 
      parameter = 'all', 
      startDate, 
      endDate, 
      interval = 'hour', 
      page = 1, 
      pageSize = 100 
    } = params;

    // 获取历史数据
    let historyData = dataModule.getSensorHistoryData(sensorId);

    // 日期范围过滤
    if (startDate || endDate) {
      const start = startDate ? new Date(startDate) : new Date('1900-01-01');
      const end = endDate ? new Date(endDate) : new Date('2100-12-31');
      
      historyData = historyData.filter(item => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= start && itemDate <= end;
      });
    }

    // 参数过滤
    if (parameter !== 'all') {
      historyData = historyData.map(item => ({
        timestamp: item.timestamp,
        sensorId: item.sensorId,
        [parameter]: item[parameter]
      }));
    }

    // 间隔处理（简化处理）
    if (interval === 'day') {
      // 按天聚合数据
      const dailyData = {};
      historyData.forEach(item => {
        const date = item.timestamp.split('T')[0];
        if (!dailyData[date]) {
          dailyData[date] = [];
        }
        dailyData[date].push(item);
      });

      historyData = Object.keys(dailyData).map(date => {
        const dayData = dailyData[date];
        const avgData = { timestamp: date + 'T12:00:00.000Z', sensorId };
        
        // 计算平均值
        Object.keys(dayData[0]).forEach(key => {
          if (key !== 'timestamp' && key !== 'sensorId' && typeof dayData[0][key] === 'number') {
            avgData[key] = dayData.reduce((sum, item) => sum + (item[key] || 0), 0) / dayData.length;
          }
        });
        
        return avgData;
      });
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = historyData.slice(startIndex, endIndex);

    return createApiResponse({
      sensorId,
      parameter,
      interval,
      data: paginatedData,
      pagination: {
        page,
        pageSize,
        total: historyData.length,
        totalPages: Math.ceil(historyData.length / pageSize),
        hasNext: endIndex < historyData.length,
        hasPrev: page > 1
      },
      statistics: calculateStatistics(historyData, parameter)
    });
  },

  // 获取传感器实时数据
  async getRealtimeData(token, sensorId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const sensor = dataModule.getSensorById(sensorId);
    if (!sensor) {
      return createErrorResponse('传感器不存在', 404);
    }

    // 模拟实时数据变化
    const currentReading = { ...sensor.currentReading };
    
    // 添加微小的随机变化
    Object.keys(currentReading).forEach(key => {
      if (typeof currentReading[key] === 'number' && key !== 'timestamp') {
        const variation = (Math.random() - 0.5) * 0.1; // ±5%的变化
        currentReading[key] = Math.max(0, currentReading[key] * (1 + variation));
      }
    });

    currentReading.timestamp = new Date().toISOString();

    return createApiResponse({
      sensorId,
      data: currentReading,
      alerts: checkSensorAlerts({ ...sensor, currentReading }),
      status: sensor.status
    });
  }
};

// 检查传感器预警
function checkSensorAlerts(sensor) {
  const alerts = [];
  const reading = sensor.currentReading;
  const thresholds = sensor.thresholds;

  Object.keys(reading).forEach(param => {
    if (param === 'timestamp' || param === 'sensorId') return;
    
    const value = reading[param];
    const threshold = thresholds[param];
    
    if (threshold && typeof value === 'number') {
      if (value < threshold.min) {
        alerts.push({
          parameter: param,
          level: 'critical',
          message: `${param}值过低: ${value}`,
          threshold: threshold.min,
          value
        });
      } else if (value > threshold.max) {
        alerts.push({
          parameter: param,
          level: 'critical',
          message: `${param}值过高: ${value}`,
          threshold: threshold.max,
          value
        });
      } else if (threshold.optimal) {
        const [optimalMin, optimalMax] = threshold.optimal;
        if (value < optimalMin || value > optimalMax) {
          alerts.push({
            parameter: param,
            level: 'warning',
            message: `${param}值偏离最佳范围: ${value}`,
            threshold: threshold.optimal,
            value
          });
        }
      }
    }
  });

  return alerts;
}

// 计算统计信息
function calculateStatistics(data, parameter) {
  if (data.length === 0) return null;

  const stats = {};
  
  // 获取所有数值参数
  const numericParams = Object.keys(data[0]).filter(key => 
    key !== 'timestamp' && key !== 'sensorId' && typeof data[0][key] === 'number'
  );

  // 如果指定了参数，只计算该参数
  const paramsToCalculate = parameter !== 'all' ? [parameter] : numericParams;

  paramsToCalculate.forEach(param => {
    const values = data.map(item => item[param]).filter(val => val !== undefined);
    
    if (values.length > 0) {
      const sortedValues = values.sort((a, b) => a - b);
      
      stats[param] = {
        min: Math.min(...values),
        max: Math.max(...values),
        avg: values.reduce((sum, val) => sum + val, 0) / values.length,
        median: sortedValues[Math.floor(sortedValues.length / 2)],
        count: values.length,
        latest: data[data.length - 1][param]
      };
    }
  });

  return stats;
}