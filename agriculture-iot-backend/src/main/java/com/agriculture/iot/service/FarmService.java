package com.agriculture.iot.service;

import com.agriculture.iot.entity.Farm;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 农场服务接口
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface FarmService {

    /**
     * 分页获取农场列表
     */
    IPage<Farm> getFarmList(Page<Farm> page, String name, Integer status);

    /**
     * 根据ID获取农场详情
     */
    Farm getFarmById(Long id);

    /**
     * 创建农场
     */
    Farm createFarm(Farm farm);

    /**
     * 更新农场信息
     */
    Farm updateFarm(Farm farm);

    /**
     * 删除农场
     */
    boolean deleteFarm(Long id);

    /**
     * 获取所有启用状态的农场
     */
    List<Farm> getAllActiveFarms();

    /**
     * 获取农场统计信息
     */
    Farm.Statistics getFarmStatistics(Long id);

    /**
     * 更新农场状态
     */
    boolean updateFarmStatus(Long id, Integer status);
}