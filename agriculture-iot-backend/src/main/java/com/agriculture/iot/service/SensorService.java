package com.agriculture.iot.service;

import com.agriculture.iot.entity.SensorData;
import com.agriculture.iot.entity.SensorType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 传感器服务接口
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface SensorService extends IService<SensorData> {

    /**
     * 分页获取传感器数据列表
     * 
     * @param page 分页对象
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @param sensorTypeId 传感器类型ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 传感器数据列表
     */
    IPage<SensorData> getSensorDataList(Page<SensorData> page, Long deviceId, Long plotId, 
                                       Long sensorTypeId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取最新传感器数据
     * 
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @return 最新传感器数据
     */
    List<SensorData> getLatestSensorData(Long deviceId, Long plotId);

    /**
     * 获取历史传感器数据
     * 
     * @param deviceId 设备ID
     * @param sensorType 传感器类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param interval 数据间隔（分钟）
     * @return 历史数据
     */
    List<SensorData> getHistorySensorData(Long deviceId, String sensorType, 
                                         LocalDateTime startTime, LocalDateTime endTime, Integer interval);

    /**
     * 获取传感器数据统计
     * 
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @param sensorType 传感器类型
     * @param days 统计天数
     * @return 统计信息
     */
    SensorData.Statistics getSensorDataStatistics(Long deviceId, Long plotId, String sensorType, Integer days);

    /**
     * 上传传感器数据
     * 
     * @param sensorData 传感器数据
     * @return 保存的数据
     */
    SensorData uploadSensorData(SensorData sensorData);

    /**
     * 批量上传传感器数据
     * 
     * @param sensorDataList 传感器数据列表
     * @return 上传结果
     */
    boolean batchUploadSensorData(List<SensorData> sensorDataList);

    /**
     * 获取指定设备的传感器数据
     * 
     * @param deviceId 设备ID
     * @param hours 时间范围（小时）
     * @return 设备传感器数据
     */
    Map<String, Object> getDeviceSensorData(Long deviceId, Integer hours);

    /**
     * 获取指定地块的传感器数据
     * 
     * @param plotId 地块ID
     * @param hours 时间范围（小时）
     * @return 地块传感器数据
     */
    Map<String, Object> getPlotSensorData(Long plotId, Integer hours);

    /**
     * 获取传感器类型列表
     * 
     * @return 传感器类型列表
     */
    List<SensorType> getSensorTypes();

    /**
     * 获取传感器数据趋势
     * 
     * @param deviceId 设备ID
     * @param sensorType 传感器类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param trendType 趋势类型
     * @return 趋势数据
     */
    SensorData.TrendData getSensorDataTrend(Long deviceId, String sensorType, 
                                           LocalDateTime startTime, LocalDateTime endTime, String trendType);

    /**
     * 对比传感器数据
     * 
     * @param deviceIds 设备ID列表
     * @param sensorType 传感器类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 对比数据
     */
    SensorData.CompareData compareSensorData(List<Long> deviceIds, String sensorType, 
                                           LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取传感器数据报表
     * 
     * @param plotId 地块ID
     * @param deviceIds 设备ID列表
     * @param reportType 报表类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报表数据
     */
    SensorData.ReportData getSensorDataReport(Long plotId, List<Long> deviceIds, String reportType, 
                                            LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 导出传感器数据
     * 
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param format 导出格式
     * @return 导出文件URL
     */
    String exportSensorData(Long deviceId, Long plotId, LocalDateTime startTime, 
                           LocalDateTime endTime, String format);

    /**
     * 获取传感器预警信息
     * 
     * @param deviceId 设备ID
     * @param plotId 地块ID
     * @param alertLevel 预警级别
     * @return 预警信息列表
     */
    List<SensorData.AlertInfo> getSensorAlerts(Long deviceId, Long plotId, String alertLevel);

    /**
     * 设置传感器阈值
     * 
     * @param thresholdConfig 阈值配置
     * @return 设置结果
     */
    boolean setSensorThreshold(SensorData.ThresholdConfig thresholdConfig);

    /**
     * 获取传感器阈值配置
     * 
     * @param deviceId 设备ID
     * @param sensorType 传感器类型
     * @return 阈值配置列表
     */
    List<SensorData.ThresholdConfig> getSensorThresholds(Long deviceId, String sensorType);
}