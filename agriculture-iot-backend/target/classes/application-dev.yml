# 开发环境配置
spring:
  # 数据源配置
  datasource:
    url: **************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 2
      maximum-pool-size: 10

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

# 日志配置
logging:
  level:
    root: INFO
    com.agriculture.iot: DEBUG
    org.springframework.web: INFO
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"

# 自定义配置
agriculture:
  # 文件上传路径
  upload:
    path: /Users/<USER>/develop/agriculture-mini/uploads/
    url-prefix: /uploads/
  
  # 设备配置
  device:
    heartbeat-interval: 30000 # 心跳间隔（毫秒）
    offline-timeout: 60000 # 离线超时（毫秒）
    command-timeout: 5000 # 命令超时（毫秒）
  
  # 定时任务配置
  schedule:
    # 传感器数据清理任务
    data-cleanup:
      enabled: true
      cron: "0 0 2 * * ?"  # 每天凌晨2点执行
    # 设备状态检查任务
    device-status-check:
      enabled: true
      cron: "0 */5 * * * ?"  # 每5分钟执行一次
    # 计划执行检查任务
    schedule-execution-check:
      enabled: true
      cron: "0 * * * * ?"  # 每分钟执行一次
    # 逾期计划检查任务
    overdue-schedule-check:
      enabled: true
      cron: "0 0 * * * ?"  # 每小时执行一次
    # 每日报告生成任务
    daily-report:
      enabled: false  # 开发环境禁用
      cron: "0 0 8 * * ?"  # 每天早上8点执行