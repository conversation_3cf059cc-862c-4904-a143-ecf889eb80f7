/* 地块配置页面样式 */

.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

.container {
  padding: 20rpx;
  padding-bottom: 120rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  padding: 20rpx 0 30rpx 0;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #08C160;
}

/* 配置卡片 */
.config-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 25rpx;
  border-left: 6rpx solid #08C160;
  padding-left: 15rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 25rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.required {
  color: #E53E3E;
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #E2E8F0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  background-color: #FFFFFF;
}

.form-input:focus {
  border-color: #08C160;
}

.form-input[disabled] {
  background-color: #F7FAFC;
  color: #A0AEC0;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #E2E8F0;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  background-color: #FFFFFF;
}

.form-textarea:focus {
  border-color: #08C160;
}

.form-textarea[disabled] {
  background-color: #F7FAFC;
  color: #A0AEC0;
}

/* 带单位的输入框 */
.input-with-unit {
  display: flex;
  align-items: center;
  border: 2rpx solid #E2E8F0;
  border-radius: 12rpx;
  background-color: #FFFFFF;
  overflow: hidden;
}

.input-with-unit .form-input {
  flex: 1;
  border: none;
  border-radius: 0;
  margin: 0;
}

.unit-label {
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #666666;
  background-color: #F7FAFC;
  border-left: 2rpx solid #E2E8F0;
  min-width: 60rpx;
  text-align: center;
}

/* 选择器字段 */
.picker-field {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  border: 2rpx solid #E2E8F0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  background-color: #FFFFFF;
}

.picker-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
}

.picker-arrow {
  font-size: 28rpx;
  color: #A0AEC0;
  margin-left: 10rpx;
}

/* GPS坐标相关 */
.coordinate-input {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.coordinate-field {
  flex: 1;
}

.gps-btn {
  min-width: 120rpx;
  height: 80rpx;
  background-color: #2E7D32;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.gps-btn[disabled] {
  background-color: #CBD5E0;
  color: #A0AEC0;
}

.search-btn {
  width: 100%;
  height: 80rpx;
  background-color: #1976D2;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15rpx;
}

.search-btn:active {
  background-color: #1565C0;
}

.gps-details {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #F0FDF4;
  border-radius: 12rpx;
  border: 2rpx solid #BBF7D0;
}

.gps-item {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.gps-item:last-child {
  margin-bottom: 0;
}

.gps-label {
  font-size: 26rpx;
  color: #166534;
  margin-right: 10rpx;
  font-weight: 500;
}

.gps-value {
  font-size: 26rpx;
  color: #15803D;
  font-family: monospace;
}

/* 设备列表 */
.device-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.device-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  border: 2rpx solid #E9ECEF;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  display: block;
  margin-bottom: 5rpx;
}

.device-type {
  font-size: 24rpx;
  color: #666666;
}

.device-status {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #CBD5E0;
}

.device-status.online .status-dot {
  background-color: #10B981;
}

.status-text {
  font-size: 24rpx;
  color: #666666;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx;
  border-top: 2rpx solid #E2E8F0;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #2E7D32;
  color: #FFFFFF;
}

.btn-primary:active {
  background-color: #1B5E20;
}

.btn-secondary {
  background-color: #FFFFFF;
  color: #666666;
  border: 2rpx solid #E2E8F0;
}

.btn-secondary:active {
  background-color: #F7FAFC;
}

/* 作物配置相关样式 */
.crop-details {
  margin-top: 20rpx;
  padding: 25rpx;
  background-color: #F8FDF8;
  border-radius: 12rpx;
  border: 2rpx solid #D1FAE5;
}

.growth-progress {
  margin-top: 25rpx;
  padding: 20rpx;
  background-color: #ECFDF5;
  border-radius: 12rpx;
  border: 2rpx solid #A7F3D0;
}

.progress-label {
  display: block;
  font-size: 26rpx;
  color: #065F46;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: #E5E7EB;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10B981 0%, #059669 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #047857;
  font-weight: 500;
}

.empty-crop {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx;
  text-align: center;
}

.empty-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #9CA3AF;
  font-weight: 400;
}

/* 地块类型选择样式 */
.picker-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.picker-icon {
  font-size: 32rpx;
}

/* 地图相关样式 */
.map-container {
  position: relative;
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid #E2E8F0;
}

.plot-map {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.map-btn {
  min-width: 120rpx;
  height: 60rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15rpx;
}

.map-btn.primary {
  background-color: #2E7D32;
  color: #FFFFFF;
}

.map-btn.success {
  background-color: #388E3C;
  color: #FFFFFF;
}

.map-btn.secondary {
  background-color: #F44336;
  color: #FFFFFF;
}

.map-btn:active {
  transform: scale(0.95);
}

.drawing-tip {
  position: absolute;
  bottom: 15rpx;
  left: 15rpx;
  right: 15rpx;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 15rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #333333;
  text-align: center;
  border: 2rpx solid #2E7D32;
}

/* 多边形信息样式 */
.polygon-info {
  display: flex;
  align-items: center;
  margin-top: 15rpx;
  padding: 15rpx;
  background-color: #E8F5E8;
  border-radius: 8rpx;
  border: 2rpx solid #C8E6C9;
}

.polygon-label {
  font-size: 26rpx;
  color: #2E7D32;
  margin-right: 10rpx;
  font-weight: 500;
}

.polygon-value {
  font-size: 26rpx;
  color: #388E3C;
  font-weight: 600;
}

/* 查看模式地图样式 */
.map-container.view-mode {
  height: 300rpx;
  position: relative;
}

.map-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 20rpx 15rpx 15rpx;
  border-radius: 0 0 12rpx 12rpx;
}

.map-overlay-text {
  color: #FFFFFF;
  font-size: 26rpx;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 地块绘制区域样式 */
.drawing-section {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.drawing-tip {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #E8F8EC 0%, #F0FDF4 100%);
  border-radius: 12rpx;
  border: 1rpx solid #C8F2D6;
}

.tip-icon {
  font-size: 32rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #333333;
  line-height: 1.4;
  flex: 1;
}

.draw-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  padding: 25rpx;
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  color: white;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(8, 193, 96, 0.3);
}

.draw-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(8, 193, 96, 0.4);
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 600;
}

.polygon-info {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  border: 1rpx solid #E2E8F0;
}

.polygon-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.polygon-label {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

.polygon-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
}

.polygon-value.completed {
  color: #08C160;
}

.polygon-value.pending {
  color: #FF9800;
}

.polygon-tip {
  font-size: 24rpx;
  color: #999999;
  text-align: center;
  padding: 10rpx 0;
  border-top: 1rpx solid #E2E8F0;
  margin-top: 10rpx;
}

/* 查看模式绘制区域 */
.view-drawing-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.drawing-summary {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-label {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

.summary-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #08C160;
}

.view-map-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  color: white;
  border-radius: 12rpx;
  border: none;
  font-size: 26rpx;
  font-weight: 600;
}

.view-map-btn:active {
  transform: translateY(2rpx);
}