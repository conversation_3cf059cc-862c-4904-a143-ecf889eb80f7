/**
 * 设备相关Mock API
 */

const dataModule = require('./data');

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 验证Token
function verifyToken(token) {
  // 在Mock环境中，简化Token验证，始终返回有效的payload
  return {
    userId: 'user_001',
    username: 'testuser',
    exp: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
  };
}

module.exports = {
  init() {
    console.log('设备模块初始化完成');
  },

  // 获取设备列表
  async list(token, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { farmId, plotId, type, status, page = 1, pageSize = 20 } = params;
    
    let devices = dataModule.getDevices();

    // 过滤条件
    if (farmId) {
      devices = devices.filter(device => device.farmId === farmId);
    }
    
    if (plotId) {
      devices = devices.filter(device => device.plotId === plotId);
    }
    
    if (type) {
      devices = devices.filter(device => device.type === type);
    }
    
    if (status) {
      devices = devices.filter(device => device.status.online === (status === 'online'));
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedDevices = devices.slice(startIndex, endIndex);

    // 添加地块信息
    const devicesWithPlotInfo = paginatedDevices.map(device => {
      const plot = dataModule.getPlotById(device.plotId);
      return {
        ...device,
        plotName: plot ? plot.name : '未知地块',
        plotArea: plot ? plot.area : 0,
        cropType: plot && plot.currentCrop ? plot.currentCrop.type : null
      };
    });

    return createApiResponse({
      devices: devicesWithPlotInfo,
      pagination: {
        page,
        pageSize,
        total: devices.length,
        totalPages: Math.ceil(devices.length / pageSize),
        hasNext: endIndex < devices.length,
        hasPrev: page > 1
      },
      summary: {
        total: devices.length,
        online: devices.filter(d => d.status.online).length,
        offline: devices.filter(d => !d.status.online).length,
        working: devices.filter(d => d.status.working).length,
        idle: devices.filter(d => d.status.online && !d.status.working).length
      }
    });
  },

  // 获取设备状态
  async getStatus(token, deviceId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const device = dataModule.getDeviceById(deviceId);
    if (!device) {
      return createErrorResponse('设备不存在', 404);
    }

    const plot = dataModule.getPlotById(device.plotId);
    
    // 获取设备相关的传感器数据
    const sensors = dataModule.getSensors(device.plotId);
    const sensorData = sensors.map(sensor => ({
      id: sensor.id,
      name: sensor.name,
      type: sensor.type,
      status: sensor.status,
      currentReading: sensor.currentReading
    }));

    return createApiResponse({
      device: {
        id: device.id,
        name: device.name,
        type: device.type,
        model: device.model,
        status: device.status,
        config: device.config,
        currentTask: device.currentTask,
        capabilities: device.capabilities,
        installLocation: device.installLocation,
        plotName: plot ? plot.name : '未知地块',
        plotArea: plot ? plot.area : 0,
        cropType: plot && plot.currentCrop ? plot.currentCrop.type : null
      },
      sensors: sensorData,
      stats: {
        totalRuntime: device.totalRuntime,
        totalWaterUsage: device.totalWaterUsage,
        totalFertilizerUsage: device.totalFertilizerUsage,
        lastMaintenance: device.maintenanceRecord.lastMaintenance,
        nextMaintenance: device.maintenanceRecord.nextMaintenance
      }
    });
  },

  // 获取设备状态汇总
  async getStatusSummary(token) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const devices = dataModule.getDevices();
    
    const summary = {
      total: devices.length,
      online: devices.filter(d => d.status.online).length,
      offline: devices.filter(d => !d.status.online).length,
      working: devices.filter(d => d.status.working).length,
      idle: devices.filter(d => d.status.online && !d.status.working).length,
      maintenance: devices.filter(d => {
        const nextMaintenance = new Date(d.maintenanceRecord.nextMaintenance);
        const now = new Date();
        return nextMaintenance <= now;
      }).length,
      lowBattery: devices.filter(d => d.status.batteryLevel < 20).length,
      weakSignal: devices.filter(d => d.status.signalStrength < -60).length
    };

    const devicesByType = devices.reduce((acc, device) => {
      const type = device.type;
      if (!acc[type]) {
        acc[type] = {
          total: 0,
          online: 0,
          working: 0
        };
      }
      acc[type].total++;
      if (device.status.online) acc[type].online++;
      if (device.status.working) acc[type].working++;
      return acc;
    }, {});

    const devicesByPlot = devices.reduce((acc, device) => {
      const plotId = device.plotId;
      const plot = dataModule.getPlotById(plotId);
      const plotName = plot ? plot.name : '未知地块';
      
      if (!acc[plotId]) {
        acc[plotId] = {
          plotName,
          total: 0,
          online: 0,
          working: 0
        };
      }
      acc[plotId].total++;
      if (device.status.online) acc[plotId].online++;
      if (device.status.working) acc[plotId].working++;
      return acc;
    }, {});

    return createApiResponse({
      summary,
      devicesByType,
      devicesByPlot: Object.values(devicesByPlot),
      alerts: devices.filter(d => {
        return !d.status.online || 
               d.status.batteryLevel < 20 || 
               d.status.signalStrength < -60;
      }).map(d => ({
        deviceId: d.id,
        deviceName: d.name,
        type: !d.status.online ? 'offline' : 
              d.status.batteryLevel < 20 ? 'low_battery' : 'weak_signal',
        message: !d.status.online ? '设备离线' : 
                 d.status.batteryLevel < 20 ? '电量不足' : '信号较弱',
        level: !d.status.online ? 'critical' : 'warning'
      }))
    });
  },

  // 控制设备
  async control(token, deviceId, action, parameters) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const devices = dataModule.getDevices();
    const deviceIndex = devices.findIndex(d => d.id === deviceId);
    
    if (deviceIndex === -1) {
      return createErrorResponse('设备不存在', 404);
    }

    const device = devices[deviceIndex];
    
    if (!device.status.online) {
      return createErrorResponse('设备离线，无法执行操作', 400);
    }

    // 模拟不同的控制操作
    let operationResult = null;
    
    switch (action) {
      case 'start_irrigation':
        if (device.status.working) {
          return createErrorResponse('设备正在工作中', 400);
        }
        
        // 更新设备状态
        device.status.working = true;
        device.currentTask = {
          taskId: `task_${Date.now()}`,
          type: 'irrigation',
          startTime: new Date().toISOString(),
          estimatedEndTime: new Date(Date.now() + (parameters.duration || 1800) * 1000).toISOString(),
          parameters: {
            duration: parameters.duration || 1800,
            flowRate: parameters.flowRate || 60,
            zones: parameters.zones || ['VALVE_1'],
            fertilizer: parameters.fertilizer || { enabled: false }
          }
        };
        
        // 更新阀门状态
        if (parameters.zones) {
          device.config.valves.forEach(valve => {
            if (parameters.zones.includes(valve.id)) {
              valve.status = 'open';
              valve.flowRate = parameters.flowRate || 60;
            }
          });
        }
        
        operationResult = {
          action: 'start_irrigation',
          status: 'success',
          taskId: device.currentTask.taskId,
          estimatedDuration: parameters.duration || 1800,
          message: '灌溉启动成功'
        };
        break;
        
      case 'stop_irrigation':
        if (!device.status.working) {
          return createErrorResponse('设备未在工作状态', 400);
        }
        
        // 更新设备状态
        device.status.working = false;
        device.currentTask = null;
        
        // 关闭所有阀门
        device.config.valves.forEach(valve => {
          valve.status = 'closed';
          valve.flowRate = 0;
        });
        
        operationResult = {
          action: 'stop_irrigation',
          status: 'success',
          message: '灌溉停止成功'
        };
        break;
        
      case 'start_fertilization':
        if (device.status.working) {
          return createErrorResponse('设备正在工作中', 400);
        }
        
        // 检查肥料罐液位
        const hasEnoughFertilizer = Object.values(device.config.fertilizer).some(tank => tank.level > 10);
        if (!hasEnoughFertilizer) {
          return createErrorResponse('肥料罐液位不足', 400);
        }
        
        // 更新设备状态
        device.status.working = true;
        device.currentTask = {
          taskId: `task_${Date.now()}`,
          type: 'fertilization',
          startTime: new Date().toISOString(),
          estimatedEndTime: new Date(Date.now() + (parameters.duration || 1800) * 1000).toISOString(),
          parameters: {
            duration: parameters.duration || 1800,
            flowRate: parameters.flowRate || 40,
            zones: parameters.zones || ['VALVE_1'],
            fertilizer: parameters.fertilizer || { enabled: true, formula: 'default', concentration: 15 }
          }
        };
        
        operationResult = {
          action: 'start_fertilization',
          status: 'success',
          taskId: device.currentTask.taskId,
          estimatedDuration: parameters.duration || 1800,
          message: '施肥启动成功'
        };
        break;
        
      case 'emergency_stop':
        // 紧急停止
        device.status.working = false;
        device.currentTask = null;
        
        // 关闭所有阀门
        device.config.valves.forEach(valve => {
          valve.status = 'closed';
          valve.flowRate = 0;
        });
        
        operationResult = {
          action: 'emergency_stop',
          status: 'success',
          message: '紧急停止成功'
        };
        break;
        
      default:
        return createErrorResponse('不支持的操作', 400);
    }

    // 更新设备时间戳
    device.updatedAt = new Date().toISOString();

    return createApiResponse({
      deviceId,
      operation: operationResult,
      deviceStatus: {
        online: device.status.online,
        working: device.status.working,
        currentTask: device.currentTask,
        valves: device.config.valves,
        fertilizer: device.config.fertilizer
      }
    });
  },

  // 获取设备配置
  async getConfig(token, deviceId) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const device = dataModule.getDeviceById(deviceId);
    if (!device) {
      return createErrorResponse('设备不存在', 404);
    }

    return createApiResponse({
      deviceId,
      config: device.config,
      capabilities: device.capabilities,
      maintenanceRecord: device.maintenanceRecord
    });
  },

  // 更新设备配置
  async updateConfig(token, deviceId, config) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const devices = dataModule.getDevices();
    const deviceIndex = devices.findIndex(d => d.id === deviceId);
    
    if (deviceIndex === -1) {
      return createErrorResponse('设备不存在', 404);
    }

    const device = devices[deviceIndex];
    
    // 验证配置更新权限
    if (device.status.working) {
      return createErrorResponse('设备工作中，无法更新配置', 400);
    }

    // 更新配置
    if (config.valves) {
      device.config.valves = config.valves;
    }
    
    if (config.fertilizer) {
      device.config.fertilizer = config.fertilizer;
    }
    
    if (config.pump) {
      device.config.pump = { ...device.config.pump, ...config.pump };
    }

    device.updatedAt = new Date().toISOString();

    return createApiResponse({
      deviceId,
      config: device.config,
      message: '设备配置更新成功'
    });
  },

  // 获取设备历史数据
  async getHistory(token, deviceId, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const device = dataModule.getDeviceById(deviceId);
    if (!device) {
      return createErrorResponse('设备不存在', 404);
    }

    const { type = 'all', startDate, endDate, page = 1, pageSize = 20 } = params;

    // 模拟历史数据
    const historyData = [];
    const now = new Date();
    
    for (let i = 0; i < 50; i++) {
      const timestamp = new Date(now.getTime() - i * 3600000); // 每小时一条记录
      
      historyData.push({
        timestamp: timestamp.toISOString(),
        type: i % 3 === 0 ? 'irrigation' : i % 3 === 1 ? 'fertilization' : 'maintenance',
        duration: 1800 + Math.random() * 1800, // 30-60分钟
        waterUsage: 100 + Math.random() * 200, // 100-300升
        fertilizerUsage: Math.random() * 10, // 0-10升
        efficiency: 90 + Math.random() * 10, // 90-100%
        status: Math.random() > 0.1 ? 'success' : 'failed',
        parameters: {
          flowRate: 40 + Math.random() * 40,
          zones: ['VALVE_1'],
          fertilizer: {
            enabled: i % 3 === 1,
            concentration: 15 + Math.random() * 10
          }
        }
      });
    }

    // 过滤类型
    let filteredData = historyData;
    if (type !== 'all') {
      filteredData = historyData.filter(item => item.type === type);
    }

    // 日期范围过滤
    if (startDate || endDate) {
      const start = startDate ? new Date(startDate) : new Date('1900-01-01');
      const end = endDate ? new Date(endDate) : new Date('2100-12-31');
      
      filteredData = filteredData.filter(item => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= start && itemDate <= end;
      });
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    return createApiResponse({
      deviceId,
      history: paginatedData,
      pagination: {
        page,
        pageSize,
        total: filteredData.length,
        totalPages: Math.ceil(filteredData.length / pageSize),
        hasNext: endIndex < filteredData.length,
        hasPrev: page > 1
      },
      statistics: {
        totalOperations: filteredData.length,
        successRate: filteredData.filter(item => item.status === 'success').length / filteredData.length * 100,
        averageEfficiency: filteredData.reduce((sum, item) => sum + item.efficiency, 0) / filteredData.length,
        totalWaterUsage: filteredData.reduce((sum, item) => sum + item.waterUsage, 0),
        totalFertilizerUsage: filteredData.reduce((sum, item) => sum + item.fertilizerUsage, 0)
      }
    });
  }
};