package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 报警记录实体类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("alarms")
public class Alarm {

    /**
     * 报警ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private Long deviceId;

    /**
     * 地块ID
     */
    @TableField("plot_id")
    private Long plotId;

    /**
     * 报警类型: sensor-传感器异常, device-设备故障, threshold-阈值超限, offline-设备离线
     */
    @TableField("alarm_type")
    private String alarmType;

    /**
     * 报警级别: low-低, medium-中, high-高, critical-严重
     */
    @TableField("alarm_level")
    private String alarmLevel;

    /**
     * 报警标题
     */
    @TableField("title")
    private String title;

    /**
     * 报警描述
     */
    @TableField("description")
    private String description;

    /**
     * 报警数据
     */
    @TableField("alarm_data")
    private String alarmData;

    /**
     * 报警状态: active-活跃, acknowledged-已确认, resolved-已解决
     */
    @TableField("status")
    private String status;

    /**
     * 确认时间
     */
    @TableField("acknowledged_at")
    private LocalDateTime acknowledgedAt;

    /**
     * 解决时间
     */
    @TableField("resolved_at")
    private LocalDateTime resolvedAt;

    /**
     * 处理备注
     */
    @TableField("resolution_notes")
    private String resolutionNotes;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // 关联对象
    /**
     * 关联设备信息
     */
    @TableField(exist = false)
    private Device device;

    /**
     * 关联地块信息
     */
    @TableField(exist = false)
    private Plot plot;

    // DTO内部类
    /**
     * 报警规则DTO
     */
    @Data
    public static class Rule {
        private Long id;
        private String name;
        private String description;
        private String type;
        private Long deviceId;
        private String deviceName;
        private Long plotId;
        private String plotName;
        private String parameter;
        private String condition;
        private BigDecimal thresholdValue;
        private BigDecimal minValue;
        private BigDecimal maxValue;
        private String level;
        private String levelText;
        private Boolean enabled;
        private String notificationMethods;
        private String notificationUsers;
        private Boolean autoAction;
        private String actionType;
        private Map<String, Object> actionParameters;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    /**
     * 报警统计DTO
     */
    @Data
    public static class Statistics {
        private LocalDate date;
        private Integer total;
        private Integer critical;
        private Integer warning;
        private Integer info;
        private Integer active;
        private Integer resolved;
        private Integer acknowledged;
        private BigDecimal resolutionRate;
        private Integer avgResolutionTime;
    }

    /**
     * 报警详情DTO
     */
    @Data
    public static class Detail {
        private Long id;
        private String alarmType;
        private String alarmTypeText;
        private String alarmLevel;
        private String alarmLevelText;
        private String title;
        private String description;
        private Long deviceId;
        private String deviceName;
        private Long plotId;
        private String plotName;
        private String parameter;
        private BigDecimal currentValue;
        private BigDecimal thresholdValue;
        private String unit;
        private String status;
        private String statusText;
        private String suggestion;
        private Map<String, Object> alarmData;
        private LocalDateTime acknowledgedAt;
        private String acknowledgedBy;
        private LocalDateTime resolvedAt;
        private String resolvedBy;
        private String resolutionNotes;
        private Integer duration;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    /**
     * 报警处理请求DTO
     */
    @Data
    public static class ProcessRequest {
        private String action;
        private String notes;
        private String solution;
        private Boolean notifyUsers;
    }

    /**
     * 报警趋势数据DTO
     */
    @Data
    public static class TrendData {
        private LocalDate date;
        private String period;
        private Integer count;
        private String level;
        private String type;
        private BigDecimal rate;
    }

    /**
     * 报警配置DTO
     */
    @Data
    public static class Config {
        private Long id;
        private String parameter;
        private String parameterName;
        private String parameterUnit;
        private Boolean enabled;
        private String level;
        private BigDecimal minValue;
        private BigDecimal maxValue;
        private BigDecimal warningThreshold;
        private BigDecimal criticalThreshold;
        private String condition;
        private Integer checkInterval;
        private Boolean notification;
        private String notificationMethods;
        private String notificationUsers;
        private Boolean autoAction;
        private String actionType;
        private Map<String, Object> actionConfig;
    }

    /**
     * 报警通知DTO
     */
    @Data
    public static class Notification {
        private Long id;
        private Long alarmId;
        private String method;
        private String recipient;
        private String title;
        private String content;
        private String status;
        private String errorMessage;
        private LocalDateTime sentAt;
        private LocalDateTime deliveredAt;
    }

    /**
     * 报警响应DTO
     */
    @Data
    public static class Response {
        private Long alarmId;
        private String responseType;
        private String responseAction;
        private Boolean success;
        private String message;
        private Map<String, Object> responseData;
        private LocalDateTime responseTime;
        private Integer duration;
    }

    /**
     * 报警仪表板DTO
     */
    @Data
    public static class Dashboard {
        private Statistics todayStats;
        private List<Detail> recentAlarms;
        private List<TrendData> trendData;
        private Map<String, Integer> alarmsByType;
        private Map<String, Integer> alarmsByLevel;
        private Map<String, Integer> alarmsByDevice;
        private List<Config> criticalRules;
    }

    /**
     * 报警报告DTO
     */
    @Data
    public static class Report {
        private String reportType;
        private LocalDate startDate;
        private LocalDate endDate;
        private Statistics summary;
        private List<TrendData> trends;
        private List<Detail> topAlarms;
        private Map<String, Object> analysis;
        private String recommendations;
        private LocalDateTime generatedAt;
    }
}