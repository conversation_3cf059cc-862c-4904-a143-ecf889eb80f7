Page({
  data: {
    sensorId: '',
    sensorInfo: {
      name: '土壤传感器-01',
      type: '土壤多参数传感器',
      model: 'SS-5TM',
      location: '1号大棚 A区 中央',
      status: 'online',
      statusText: '在线',
      signalStrength: -45,
      signalLevel: 'excellent',
      icon: '🌡️'
    },
    
    deviceDetails: [
      { label: '设备ID', value: 'SS001' },
      { label: '固件版本', value: 'v2.1.3' },
      { label: '安装日期', value: '2024-01-15' },
      { label: '制造商', value: '智农科技' },
      { label: '保修期', value: '2025-01-15' }
    ],
    
    lastUpdateTime: '2024-07-07 14:30:25',
    
    realtimeData: [
      {
        type: 'humidity',
        name: '土壤湿度',
        value: 65,
        unit: '%',
        icon: '💧',
        status: 'normal',
        statusText: '正常',
        normalRange: '50-80%'
      },
      {
        type: 'temperature', 
        name: '土壤温度',
        value: 23,
        unit: '°C',
        icon: '🌡️',
        status: 'normal',
        statusText: '正常',
        normalRange: '18-28°C'
      },
      {
        type: 'ph',
        name: 'PH值',
        value: 6.8,
        unit: '',
        icon: '🧪',
        status: 'normal',
        statusText: '正常',
        normalRange: '6.0-7.5'
      },
      {
        type: 'ec',
        name: 'EC值',
        value: 1.2,
        unit: 'mS/cm',
        icon: '⚡',
        status: 'normal',
        statusText: '正常',
        normalRange: '0.8-2.0'
      }
    ],
    
    selectedIndicator: 0,
    indicatorOptions: ['土壤湿度', '土壤温度', 'PH值', 'EC值'],
    
    chartData: {
      yLabels: ['80', '70', '60', '50', '40', '30'],
      xLabels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
      points: [
        { x: 5, y: 45, value: 58, showValue: false },
        { x: 20, y: 55, value: 62, showValue: false },
        { x: 35, y: 50, value: 60, showValue: false },
        { x: 50, y: 65, value: 68, showValue: true },
        { x: 65, y: 60, value: 65, showValue: false },
        { x: 80, y: 70, value: 72, showValue: false },
        { x: 95, y: 65, value: 65, showValue: false }
      ]
    },
    
    chartStats: {
      max: '72%',
      min: '58%',
      avg: '64%'
    },
    
    alarmSettings: [
      {
        type: 'humidity',
        name: '土壤湿度预警',
        enabled: true,
        maxValue: 80,
        minValue: 50,
        unit: '%'
      },
      {
        type: 'temperature',
        name: '土壤温度预警',
        enabled: true,
        maxValue: 28,
        minValue: 18,
        unit: '°C'
      },
      {
        type: 'ph',
        name: 'PH值预警',
        enabled: false,
        maxValue: 7.5,
        minValue: 6.0,
        unit: ''
      },
      {
        type: 'ec',
        name: 'EC值预警',
        enabled: true,
        maxValue: 2.0,
        minValue: 0.8,
        unit: 'mS/cm'
      }
    ],
    
    selectedTimeRange: 0,
    timeRangeOptions: ['今天', '最近3天', '最近7天', '最近30天'],
    
    tableHeaders: ['湿度(%)', '温度(°C)', 'PH值', 'EC值'],
    dataRecords: [
      {
        time: '14:30',
        values: ['65', '23.0', '6.8', '1.2']
      },
      {
        time: '14:00',
        values: ['63', '22.8', '6.7', '1.1']
      },
      {
        time: '13:30',
        values: ['68', '23.2', '6.9', '1.3']
      },
      {
        time: '13:00',
        values: ['66', '23.1', '6.8', '1.2']
      },
      {
        time: '12:30',
        values: ['64', '22.9', '6.7', '1.1']
      }
    ],
    
    maintenanceInfo: {
      lastCalibration: '2024-06-15',
      nextCalibration: '2024-08-15',
      batteryReplacement: '2024-01-15',
      warrantyExpiry: '2025-01-15'
    }
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        sensorId: options.id
      });
      this.loadSensorDetail(options.id);
    }
  },

  onShow() {
    this.refreshRealtimeData();
  },

  onPullDownRefresh() {
    this.refreshRealtimeData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  loadSensorDetail(sensorId) {
    // 根据传感器ID加载详细信息
    console.log('加载传感器详情:', sensorId);
    
    // 模拟根据ID加载不同传感器数据
    if (sensorId == 1) {
      // 设备页面传来的土壤传感器-01，对应传感器管理中的ID 1
      this.setData({
        'sensorInfo.name': '土壤传感器-01',
        'sensorInfo.type': '土壤多参数传感器',
        'sensorInfo.model': 'SS-5TM',
        'sensorInfo.location': '1号大棚 A区 中央',
        'sensorInfo.status': 'online',
        'sensorInfo.statusText': '在线',
        'sensorInfo.signalStrength': -45,
        'sensorInfo.signalLevel': 'excellent',
        'sensorInfo.icon': '🌡️'
      });
    } else if (sensorId == 2) {
      this.setData({
        'sensorInfo.name': '土壤传感器-02',
        'sensorInfo.type': '土壤温湿度传感器',
        'sensorInfo.location': '1号大棚 B区 南侧',
        'sensorInfo.signalStrength': -62,
        'sensorInfo.signalLevel': 'good'
      });
    } else if (sensorId == 3) {
      this.setData({
        'sensorInfo.name': '土壤传感器-03',
        'sensorInfo.type': 'NPK传感器',
        'sensorInfo.location': '2号大棚 东侧',
        'sensorInfo.status': 'warning',
        'sensorInfo.statusText': '信号弱',
        'sensorInfo.signalStrength': -78,
        'sensorInfo.signalLevel': 'weak',
        'sensorInfo.icon': '🌱'
      });
      
      // 更新NPK传感器的实时数据
      this.setData({
        realtimeData: [
          {
            type: 'nitrogen',
            name: '氮含量',
            value: 120,
            unit: 'mg/kg',
            icon: '🌿',
            status: 'normal',
            statusText: '正常',
            normalRange: '100-150mg/kg'
          },
          {
            type: 'phosphorus',
            name: '磷含量',
            value: 45,
            unit: 'mg/kg', 
            icon: '🌾',
            status: 'low',
            statusText: '偏低',
            normalRange: '50-80mg/kg'
          },
          {
            type: 'potassium',
            name: '钾含量',
            value: 180,
            unit: 'mg/kg',
            icon: '🌼',
            status: 'normal',
            statusText: '正常',
            normalRange: '150-200mg/kg'
          }
        ]
      });
    }
  },

  refreshRealtimeData() {
    // 刷新实时数据
    const currentTime = new Date().toLocaleString('zh-CN');
    this.setData({
      lastUpdateTime: currentTime
    });
    
    // 模拟数据更新
    const realtimeData = this.data.realtimeData.map(item => {
      const variation = (Math.random() - 0.5) * 2; // ±1的随机变化
      const newValue = Math.max(0, item.value + variation);
      return {
        ...item,
        value: Math.round(newValue * 10) / 10
      };
    });
    
    this.setData({
      realtimeData: realtimeData
    });
  },

  onIndicatorChange(e) {
    const index = e.detail.value;
    this.setData({
      selectedIndicator: index
    });
    
    // 更新图表数据
    this.updateChartData(index);
  },

  updateChartData(indicatorIndex) {
    // 根据选择的指标更新图表数据
    const indicator = this.data.indicatorOptions[indicatorIndex];
    let chartStats = {};
    let points = [];
    
    switch (indicatorIndex) {
      case 0: // 土壤湿度
        chartStats = { max: '72%', min: '58%', avg: '64%' };
        points = [
          { x: 5, y: 45, value: 58, showValue: false },
          { x: 20, y: 55, value: 62, showValue: false },
          { x: 35, y: 50, value: 60, showValue: false },
          { x: 50, y: 65, value: 68, showValue: true },
          { x: 65, y: 60, value: 65, showValue: false },
          { x: 80, y: 70, value: 72, showValue: false },
          { x: 95, y: 65, value: 65, showValue: false }
        ];
        break;
      case 1: // 土壤温度
        chartStats = { max: '25.2°C', min: '21.8°C', avg: '23.1°C' };
        points = [
          { x: 5, y: 40, value: 21.8, showValue: false },
          { x: 20, y: 50, value: 22.5, showValue: false },
          { x: 35, y: 55, value: 23.0, showValue: false },
          { x: 50, y: 60, value: 23.5, showValue: true },
          { x: 65, y: 58, value: 23.2, showValue: false },
          { x: 80, y: 65, value: 24.1, showValue: false },
          { x: 95, y: 70, value: 25.2, showValue: false }
        ];
        break;
      case 2: // PH值
        chartStats = { max: '7.1', min: '6.5', avg: '6.8' };
        points = [
          { x: 5, y: 50, value: 6.5, showValue: false },
          { x: 20, y: 55, value: 6.7, showValue: false },
          { x: 35, y: 60, value: 6.8, showValue: false },
          { x: 50, y: 65, value: 6.9, showValue: true },
          { x: 65, y: 58, value: 6.8, showValue: false },
          { x: 80, y: 62, value: 6.9, showValue: false },
          { x: 95, y: 68, value: 7.1, showValue: false }
        ];
        break;
      case 3: // EC值
        chartStats = { max: '1.5', min: '1.0', avg: '1.2' };
        points = [
          { x: 5, y: 45, value: 1.0, showValue: false },
          { x: 20, y: 50, value: 1.1, showValue: false },
          { x: 35, y: 55, value: 1.2, showValue: false },
          { x: 50, y: 60, value: 1.3, showValue: true },
          { x: 65, y: 58, value: 1.2, showValue: false },
          { x: 80, y: 65, value: 1.4, showValue: false },
          { x: 95, y: 70, value: 1.5, showValue: false }
        ];
        break;
    }
    
    this.setData({
      chartStats: chartStats,
      'chartData.points': points
    });
  },

  toggleAlarm(e) {
    const type = e.currentTarget.dataset.type;
    const alarmSettings = this.data.alarmSettings.map(item => {
      if (item.type === type) {
        return { ...item, enabled: e.detail.value };
      }
      return item;
    });
    
    this.setData({
      alarmSettings: alarmSettings
    });
  },

  onMaxValueInput(e) {
    const type = e.currentTarget.dataset.type;
    const value = parseFloat(e.detail.value) || 0;
    
    const alarmSettings = this.data.alarmSettings.map(item => {
      if (item.type === type) {
        return { ...item, maxValue: value };
      }
      return item;
    });
    
    this.setData({
      alarmSettings: alarmSettings
    });
  },

  onMinValueInput(e) {
    const type = e.currentTarget.dataset.type;
    const value = parseFloat(e.detail.value) || 0;
    
    const alarmSettings = this.data.alarmSettings.map(item => {
      if (item.type === type) {
        return { ...item, minValue: value };
      }
      return item;
    });
    
    this.setData({
      alarmSettings: alarmSettings
    });
  },

  saveAlarmSettings() {
    wx.showLoading({
      title: '保存中...'
    });
    
    // 模拟保存过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    }, 1500);
  },

  resetAlarmSettings() {
    wx.showModal({
      title: '重置预警设置',
      content: '确定要重置为默认预警设置吗？',
      success: (res) => {
        if (res.confirm) {
          // 重置为默认值
          this.setData({
            alarmSettings: [
              {
                type: 'humidity',
                name: '土壤湿度预警',
                enabled: true,
                maxValue: 80,
                minValue: 50,
                unit: '%'
              },
              {
                type: 'temperature',
                name: '土壤温度预警',
                enabled: true,
                maxValue: 28,
                minValue: 18,
                unit: '°C'
              },
              {
                type: 'ph',
                name: 'PH值预警',
                enabled: false,
                maxValue: 7.5,
                minValue: 6.0,
                unit: ''
              },
              {
                type: 'ec',
                name: 'EC值预警',
                enabled: true,
                maxValue: 2.0,
                minValue: 0.8,
                unit: 'mS/cm'
              }
            ]
          });
          
          wx.showToast({
            title: '已重置',
            icon: 'success'
          });
        }
      }
    });
  },

  onTimeRangeChange(e) {
    this.setData({
      selectedTimeRange: e.detail.value
    });
    
    // 根据时间范围重新加载数据记录
    this.loadDataRecords();
  },

  loadDataRecords() {
    // 根据选择的时间范围加载数据记录
    console.log('加载数据记录:', this.data.timeRangeOptions[this.data.selectedTimeRange]);
  },

  exportData() {
    wx.showActionSheet({
      itemList: ['导出为Excel', '导出为CSV', '导出为PDF'],
      success: (res) => {
        const formats = ['Excel', 'CSV', 'PDF'];
        const format = formats[res.tapIndex];
        
        wx.showLoading({
          title: `导出${format}中...`
        });
        
        setTimeout(() => {
          wx.hideLoading();
          wx.showToast({
            title: '导出成功',
            icon: 'success'
          });
        }, 2000);
      }
    });
  },

  viewMoreHistory() {
    wx.navigateTo({
      url: '/pages/monitor/history/history'
    });
  },

  calibrateSensor() {
    wx.showModal({
      title: '传感器校准',
      content: '确定要校准此传感器吗？校准过程将持续3-5分钟，期间可能影响数据准确性。',
      success: (res) => {
        if (res.confirm) {
          this.startCalibration();
        }
      }
    });
  },

  startCalibration() {
    wx.showLoading({
      title: '正在校准...'
    });
    
    // 模拟校准过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '校准完成',
        icon: 'success'
      });
      
      // 更新维护信息
      const today = new Date().toISOString().split('T')[0];
      const nextMonth = new Date();
      nextMonth.setMonth(nextMonth.getMonth() + 2);
      const nextCalibration = nextMonth.toISOString().split('T')[0];
      
      this.setData({
        'maintenanceInfo.lastCalibration': today,
        'maintenanceInfo.nextCalibration': nextCalibration
      });
    }, 3000);
  },

  scheduleMaintenance() {
    wx.showModal({
      title: '预约维护',
      content: '请联系技术人员预约传感器维护服务。',
      showCancel: false
    });
  }
})