<!--设备详情页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 设备基本信息 -->
    <view class="card">
      <view class="card-title">设备信息</view>
      <view class="device-info">
        <view class="device-header">
          <view class="device-avatar">
            <text class="device-icon">🌱</text>
          </view>
          <view class="device-basic">
            <text class="device-name">{{deviceInfo.name}}</text>
            <text class="device-type">{{deviceInfo.type}}</text>
            <view class="device-status {{deviceInfo.status}}">
              <text class="status-dot"></text>
              <text class="status-text">{{deviceInfo.statusText}}</text>
            </view>
          </view>
        </view>
        
        <view class="info-grid">
          <view class="info-item" wx:for="{{deviceDetails}}" wx:key="key">
            <text class="info-label">{{item.label}}</text>
            <text class="info-value">{{item.value}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 实时状态 -->
    <view class="card">
      <view class="card-title">实时状态</view>
      <view class="status-grid">
        <view class="status-item">
          <text class="status-label">水箱液位</text>
          <view class="progress-container">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{realtimeData.waterLevel}}%"></view>
            </view>
            <text class="progress-text">{{realtimeData.waterLevel}}%</text>
          </view>
        </view>
        
        <view class="status-item">
          <text class="status-label">肥料A余量</text>
          <view class="progress-container">
            <view class="progress-bar">
              <view class="progress-fill fertilizer" style="width: {{realtimeData.fertilizerA}}%"></view>
            </view>
            <text class="progress-text">{{realtimeData.fertilizerA}}%</text>
          </view>
        </view>
        
        <view class="status-item">
          <text class="status-label">肥料B余量</text>
          <view class="progress-container">
            <view class="progress-bar">
              <view class="progress-fill fertilizer" style="width: {{realtimeData.fertilizerB}}%"></view>
            </view>
            <text class="progress-text">{{realtimeData.fertilizerB}}%</text>
          </view>
        </view>
        
        <view class="status-item">
          <text class="status-label">系统压力</text>
          <text class="status-value">{{realtimeData.pressure}} bar</text>
        </view>
        
        <view class="status-item">
          <text class="status-label">流量</text>
          <text class="status-value">{{realtimeData.flowRate}} L/h</text>
        </view>
        
        <view class="status-item">
          <text class="status-label">温度</text>
          <text class="status-value">{{realtimeData.temperature}}°C</text>
        </view>
      </view>
    </view>

    <!-- 运行统计 -->
    <view class="card">
      <view class="card-title">运行统计</view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{runStats.totalRunTime}}</text>
          <text class="stat-label">总运行时间</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{runStats.waterUsed}}</text>
          <text class="stat-label">累计用水量</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{runStats.fertilizerUsed}}</text>
          <text class="stat-label">累计施肥量</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{runStats.taskCount}}</text>
          <text class="stat-label">执行任务数</text>
        </view>
      </view>
    </view>

    <!-- 最近运行记录 -->
    <view class="card">
      <view class="card-title">
        <text>最近运行记录</text>
        <text class="view-more" bindtap="viewAllLogs">查看全部</text>
      </view>
      <view class="log-list">
        <view class="log-item" wx:for="{{recentLogs}}" wx:key="id">
          <view class="log-icon {{item.type}}">
            <text>{{item.icon}}</text>
          </view>
          <view class="log-content">
            <text class="log-title">{{item.title}}</text>
            <text class="log-desc">{{item.description}}</text>
            <text class="log-time">{{item.time}}</text>
          </view>
          <view class="log-status {{item.status}}">
            <text>{{item.statusText}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="card">
      <view class="card-title">快捷操作</view>
      <view class="action-grid">
        <button class="action-btn" bindtap="navigateToIrrigation">
          <text class="btn-icon">💧</text>
          <text class="btn-text">灌溉控制</text>
        </button>
        <button class="action-btn" bindtap="navigateToFertilizer">
          <text class="btn-icon">🌱</text>
          <text class="btn-text">施肥管理</text>
        </button>
        <button class="action-btn" bindtap="navigateToConfig">
          <text class="btn-icon">⚙️</text>
          <text class="btn-text">设备配置</text>
        </button>
        <button class="action-btn" bindtap="navigateToLogs">
          <text class="btn-icon">📋</text>
          <text class="btn-text">运行记录</text>
        </button>
      </view>
    </view>

    <!-- 快速控制 -->
    <view class="card">
      <view class="card-title">快速控制</view>
      <view class="quick-controls">
        <view class="control-item">
          <view class="control-header">
            <text class="control-title">灌溉系统</text>
            <switch checked="{{irrigationRunning}}" bindchange="onIrrigationSwitch"/>
          </view>
          <view class="control-details" wx:if="{{irrigationRunning}}">
            <view class="control-param">
              <text class="param-label">流量</text>
              <slider value="{{irrigationFlow}}" min="0" max="100" bindchanging="onFlowChange" />
              <text class="param-value">{{irrigationFlow}}%</text>
            </view>
            <view class="control-param">
              <text class="param-label">时长</text>
              <input type="number" value="{{irrigationDuration}}" bindinput="onDurationInput" placeholder="分钟"/>
            </view>
          </view>
        </view>

        <view class="control-item">
          <view class="control-header">
            <text class="control-title">施肥系统</text>
            <switch checked="{{fertilizerRunning}}" bindchange="onFertilizerSwitch"/>
          </view>
          <view class="control-details" wx:if="{{fertilizerRunning}}">
            <view class="control-param">
              <text class="param-label">浓度</text>
              <slider value="{{fertilizerConcentration}}" min="0" max="100" bindchanging="onConcentrationChange" />
              <text class="param-value">{{fertilizerConcentration}}%</text>
            </view>
            <view class="control-param">
              <text class="param-label">配方</text>
              <picker range="{{formulaOptions}}" value="{{selectedFormula}}" bindchange="onFormulaChange">
                <view class="picker-view">{{formulaOptions[selectedFormula]}}</view>
              </picker>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 维护信息 -->
    <view class="card">
      <view class="card-title">维护信息</view>
      <view class="maintenance-list">
        <view class="maintenance-item">
          <text class="maintenance-label">上次维护</text>
          <text class="maintenance-value">{{maintenanceInfo.lastMaintenance}}</text>
        </view>
        <view class="maintenance-item">
          <text class="maintenance-label">下次维护</text>
          <text class="maintenance-value">{{maintenanceInfo.nextMaintenance}}</text>
        </view>
        <view class="maintenance-item">
          <text class="maintenance-label">保修期至</text>
          <text class="maintenance-value">{{maintenanceInfo.warrantyExpiry}}</text>
        </view>
      </view>
      <button class="btn btn-outline" bindtap="scheduleMaintenance">
        预约维护
      </button>
    </view>

  </view>
</view>