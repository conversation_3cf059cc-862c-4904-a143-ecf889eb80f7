package com.agriculture.iot.service;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.Alarm;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报警服务接口
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface AlarmService extends IService<Alarm> {

    /**
     * 分页获取报警列表
     */
    IPage<Alarm.Detail> getAlarms(
            Integer current, Integer size, String level, String status, 
            String type, Long plotId, Long deviceId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取最新报警
     */
    Result<List<Alarm.Detail>> getLatestAlarms(Integer limit);

    /**
     * 获取活跃报警
     */
    Result<List<Alarm.Detail>> getActiveAlarms(Long plotId, String level);

    /**
     * 获取报警详情
     */
    Result<Alarm.Detail> getAlarmDetail(Long id);

    /**
     * 确认报警
     */
    Result<Void> acknowledgeAlarm(Long id, String notes);

    /**
     * 解决报警
     */
    Result<Void> resolveAlarm(Long id, Alarm.ProcessRequest request);

    /**
     * 忽略报警
     */
    Result<Void> ignoreAlarm(Long id, String reason);

    /**
     * 删除报警记录
     */
    Result<Void> deleteAlarm(Long id);

    /**
     * 批量处理报警
     */
    Result<Void> batchProcessAlarms(List<Long> alarmIds, String action, String notes);

    /**
     * 获取报警统计
     */
    Result<Alarm.Statistics> getAlarmStatistics(
            LocalDate startDate, LocalDate endDate, Long plotId, String type);

    /**
     * 获取报警趋势数据
     */
    Result<List<Alarm.TrendData>> getAlarmTrends(
            LocalDate startDate, LocalDate endDate, String period, Long plotId, String type);

    /**
     * 获取报警仪表板数据
     */
    Result<Alarm.Dashboard> getAlarmDashboard(Long plotId);

    /**
     * 生成报警报告
     */
    Result<Alarm.Report> generateAlarmReport(
            String reportType, LocalDate startDate, LocalDate endDate, Long plotId);

    /**
     * 获取报警规则列表
     */
    Result<IPage<Alarm.Rule>> getAlarmRules(
            Integer current, Integer size, String type, Boolean enabled, Long plotId, Long deviceId);

    /**
     * 创建报警规则
     */
    Result<Alarm.Rule> createAlarmRule(Alarm.Rule rule);

    /**
     * 更新报警规则
     */
    Result<Alarm.Rule> updateAlarmRule(Long id, Alarm.Rule rule);

    /**
     * 删除报警规则
     */
    Result<Void> deleteAlarmRule(Long id);

    /**
     * 启用/禁用报警规则
     */
    Result<Void> toggleAlarmRule(Long id, Boolean enabled);

    /**
     * 测试报警规则
     */
    Result<Alarm.Response> testAlarmRule(Long ruleId, Long deviceId);

    /**
     * 获取报警配置
     */
    Result<List<Alarm.Config>> getAlarmConfigs(Long plotId, Long deviceId);

    /**
     * 更新报警配置
     */
    Result<Void> updateAlarmConfig(Long configId, Alarm.Config config);

    /**
     * 重置报警配置为默认值
     */
    Result<Void> resetAlarmConfig(Long plotId, Long deviceId);

    /**
     * 检查报警条件
     */
    void checkAlarmConditions(Long deviceId, String parameter, Object value);

    /**
     * 触发报警
     */
    Result<Alarm> triggerAlarm(
            Long deviceId, Long plotId, String type, String level, 
            String title, String description, Object currentValue, Object thresholdValue);

    /**
     * 发送报警通知
     */
    Result<List<Alarm.Notification>> sendAlarmNotifications(Long alarmId);

    /**
     * 执行自动响应动作
     */
    Result<Alarm.Response> executeAutoResponse(Long alarmId, Long ruleId);

    /**
     * 获取报警通知记录
     */
    Result<IPage<Alarm.Notification>> getAlarmNotifications(
            Integer current, Integer size, Long alarmId, String method, String status);

    /**
     * 重新发送报警通知
     */
    Result<Void> resendAlarmNotification(Long notificationId);

    /**
     * 获取报警响应记录
     */
    Result<IPage<Alarm.Response>> getAlarmResponses(
            Integer current, Integer size, Long alarmId, String responseType);

    /**
     * 获取逾期未处理报警
     */
    Result<List<Alarm.Detail>> getOverdueAlarms(Integer hours, Long plotId);

    /**
     * 获取高优先级报警
     */
    Result<List<Alarm.Detail>> getHighPriorityAlarms(Long plotId);

    /**
     * 导出报警数据
     */
    Result<String> exportAlarmData(
            LocalDate startDate, LocalDate endDate, String format, Long plotId);

    /**
     * 清理历史报警数据
     */
    Result<Void> cleanupAlarmHistory(Integer retentionDays);

    /**
     * 获取报警级联规则
     */
    Result<List<Alarm.Rule>> getCascadeRules(Long triggeredRuleId);

    /**
     * 执行级联报警检查
     */
    Result<List<Alarm>> executeCascadeAlarms(Long triggeredAlarmId);
}