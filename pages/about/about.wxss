/* pages/about/about.wxss */

.page-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 应用信息区域 */
.app-info-section {
  background: linear-gradient(135deg, #2E7D32, #4CAF50);
  color: white;
  padding: 60rpx 32rpx 40rpx;
  text-align: center;
}

.app-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
}

.app-logo {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.2));
}

.app-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.app-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.app-version {
  font-size: 28rpx;
  opacity: 0.9;
}

.app-build {
  font-size: 24rpx;
  opacity: 0.7;
}

.app-description {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 24rpx;
}

.description-text {
  font-size: 28rpx;
  line-height: 1.5;
  opacity: 0.95;
}

/* 通用区域样式 */
.section-title {
  padding: 32rpx 32rpx 24rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

/* 功能特性 */
.features-section {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  padding: 0 32rpx 32rpx;
}

.feature-item {
  text-align: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.feature-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.feature-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

/* 团队信息 */
.team-section {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.team-list {
  padding: 0 32rpx 32rpx;
}

.team-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.team-item:last-child {
  border-bottom: none;
}

.team-avatar {
  width: 64rpx;
  height: 64rpx;
  background: rgba(46, 125, 50, 0.1);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
}

.team-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.team-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.team-role {
  font-size: 24rpx;
  color: #2E7D32;
  background: rgba(46, 125, 50, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  align-self: flex-start;
  margin-bottom: 4rpx;
}

.team-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

/* 版本历史 */
.version-section {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.changelog-list {
  padding: 0 32rpx 32rpx;
}

.changelog-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.changelog-item:last-child {
  border-bottom: none;
}

.changelog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.changelog-version {
  font-size: 28rpx;
  font-weight: 600;
  color: #2E7D32;
}

.changelog-date {
  font-size: 24rpx;
  color: #666666;
}

.changelog-content {
  padding-left: 16rpx;
}

.change-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.change-bullet {
  color: #2E7D32;
  margin-right: 12rpx;
  margin-top: 2rpx;
}

.change-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

/* 联系方式 */
.contact-section {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.contact-list {
  padding: 0 32rpx 32rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.2s ease;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-item:active {
  background: #f8f8f8;
  margin: 0 -32rpx;
  padding: 24rpx 32rpx;
}

.contact-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  background: rgba(46, 125, 50, 0.1);
  border-radius: 12rpx;
  margin-right: 24rpx;
}

.contact-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.contact-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.contact-value {
  font-size: 26rpx;
  color: #666666;
}

.contact-action {
  margin-left: 16rpx;
}

.action-text {
  font-size: 24rpx;
  color: #2E7D32;
}

/* 操作按钮 */
.actions-section {
  padding: 20rpx;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.btn::after {
  border: none;
}

.btn-outline {
  background: transparent;
  border: 2rpx solid #2E7D32;
  color: #2E7D32;
}

.btn-outline:active {
  background: rgba(46, 125, 50, 0.1);
  transform: scale(0.98);
}

/* 法律信息 */
.legal-section {
  text-align: center;
  padding: 40rpx 32rpx;
}

.legal-links {
  margin-bottom: 24rpx;
}

.legal-link {
  font-size: 26rpx;
  color: #2E7D32;
  text-decoration: underline;
}

.legal-separator {
  margin: 0 16rpx;
  color: #cccccc;
}

.copyright {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.copyright-text {
  font-size: 24rpx;
  color: #999999;
}

/* 响应式适配 */
@media screen and (max-width: 400px) {
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}