# 智慧农业小程序业务流程与功能设计文档

## 目录
- [1. 业务流程图](#1-业务流程图)
- [2. 功能模块详细设计](#2-功能模块详细设计)
- [3. 用户决策点分析](#3-用户决策点分析)
- [4. 管理模式对比](#4-管理模式对比)
- [5. 页面结构与功能映射](#5-页面结构与功能映射)
- [6. 数据流设计](#6-数据流设计)
- [7. 实施建议](#7-实施建议)

## 1. 业务流程图

### 1.1 完整业务流程

```mermaid
graph TD
    A[用户登录小程序] --> B[首次登录?]
    
    B -->|是| C[初始化设置]
    B -->|否| D[进入首页]
    
    C --> C1[输入农场名称]
    C1 --> C2[选择管理模式]
    
    C2 --> C3[简单模式 - 地块级管理]
    C2 --> C4[精细模式 - 区域级管理]
    
    C3 --> D
    C4 --> D
    
    D[农场首页] --> E1[查看总览]
    D --> E2[快速操作]
    D --> E3[进入详细管理]
    
    E1 --> E1A[农场信息展示]
    E1 --> E1B[地块状态卡片]
    E1 --> E1C[今日数据统计]
    
    E2 --> E2A[一键灌溉]
    E2 --> E2B[查看监测数据]
    E2 --> E2C[查看设备状态]
    
    E3 --> F[选择功能模块]
    
    F --> G1[地块管理]
    F --> G2[设备控制]
    F --> G3[监测数据]
    F --> G4[设置中心]
    
    %% 地块管理流程
    G1 --> H1[查看地块列表]
    H1 --> H2[添加新地块]
    H1 --> H3[编辑现有地块]
    H1 --> H4[删除地块]
    
    H2 --> H2A[输入地块信息]
    H2A --> H2B[选择管理级别]
    
    H2B -->|简单模式| H2C[地块级设置]
    H2B -->|精细模式| H2D[区域级设置]
    
    H2C --> H2C1[绑定水肥一体机]
    H2C1 --> H2C2[绑定土壤传感器]
    H2C2 --> H2C3[设置种植作物]
    H2C3 --> H2C4[完成地块创建]
    
    H2D --> H2D1[添加区域信息]
    H2D1 --> H2D2[绑定设备到区域]
    H2D2 --> H2D3[设置区域作物]
    H2D3 --> H2D4[完成地块创建]
    
    %% 设备控制流程
    G2 --> I1[选择控制地块]
    I1 --> I2[简单模式地块]
    I1 --> I3[精细模式地块]
    
    I2 --> I2A[整体灌溉控制]
    I2 --> I2B[整体施肥控制]
    I2 --> I2C[定时计划设置]
    
    I3 --> I3A[选择控制区域]
    I3A --> I3B[区域灌溉控制]
    I3A --> I3C[区域施肥控制]
    I3A --> I3D[区域定时计划]
    
    I2A --> J1[手动控制]
    I2A --> J2[智能控制]
    I2B --> J3[配方选择]
    I2C --> J4[计划管理]
    
    %% 监测数据流程
    G3 --> K1[选择监测地块]
    K1 --> K2[实时数据查看]
    K1 --> K3[历史数据分析]
    K1 --> K4[报警设置]
    
    K2 --> K2A[土壤湿度]
    K2 --> K2B[土壤温度]
    K2 --> K2C[pH值]
    K2 --> K2D[EC值]
    
    K3 --> K3A[趋势图表]
    K3 --> K3B[数据对比]
    K3 --> K3C[报表导出]
    
    %% 设置中心流程
    G4 --> L1[农场设置]
    G4 --> L2[设备管理]
    G4 --> L3[用户设置]
    
    L1 --> L1A[修改农场信息]
    L1 --> L1B[管理模式切换]
    
    L2 --> L2A[设备绑定/解绑]
    L2 --> L2B[设备状态检查]
    L2 --> L2C[设备参数配置]
    
    %% 日常使用循环
    K2 --> M[发现异常?]
    M -->|是| N[处理异常]
    M -->|否| O[继续监控]
    
    N --> N1[调整灌溉参数]
    N --> N2[修改施肥配方]
    N --> N3[设置报警阈值]
    
    N1 --> O
    N2 --> O
    N3 --> O
    
    O --> D
```

### 1.2 关键流程说明

#### 1.2.1 初始化设置流程
用户首次使用小程序时的配置流程：
```
登录 → 农场名称 → 选择管理模式 → 配置地块/区域 → 绑定设备 → 开始使用
```

#### 1.2.2 日常使用流程
用户日常使用的核心路径：
```
首页总览 → 选择地块 → 查看监测数据 → 控制设备 → 返回首页
```

#### 1.2.3 异常处理流程
发现问题时的处理路径：
```
监测异常 → 分析原因 → 调整参数/手动干预 → 继续监控
```

## 2. 区域概念与设备配合机制

### 2.1 区域概念详解

#### 2.1.1 区域的定义
**区域**是智慧农业管理中的基本控制单位，代表一个地块内具有相同管理需求的种植区域。区域的划分基于以下因素：
- **作物类型**：不同作物有不同的水肥需求
- **生长阶段**：同一作物在不同生长期需要不同管理
- **土壤条件**：不同土质需要差异化管理
- **设备覆盖**：基于灌溉设备的物理覆盖范围

#### 2.1.2 区域层级结构
```
农场 (Farm)
└── 地块 (Plot) - 物理空间概念
    └── 区域 (Zone) - 管理控制概念
        ├── 作物信息 (Crop)
        ├── 设备绑定 (Device Binding)
        └── 管理参数 (Management Parameters)
```

#### 2.1.3 区域类型分类

| 区域类型 | 定义 | 应用场景 | 技术实现 |
|---------|------|---------|---------|
| **物理区域** | 基于物理管道/阀门划分 | 设备支持多路输出 | 硬件阀门控制 |
| **逻辑区域** | 基于管理需求划分 | 设备不支持多路输出 | 软件参数配置 |
| **混合区域** | 物理+逻辑的组合 | 复杂农场管理 | 硬件+软件结合 |

### 2.2 设备与区域的配合机制

#### 2.2.1 硬件层面配合

**方案1：多阀门控制系统（标准方案）**
```
水肥一体机 WF-001
├── 主控制器
├── 水泵系统
├── 肥料混合系统
└── 多路输出模块
    ├── 阀门1 (VALVE_1) → 管道A → 番茄区域
    ├── 阀门2 (VALVE_2) → 管道B → 黄瓜区域
    ├── 阀门3 (VALVE_3) → 管道C → 生菜区域
    └── 阀门4 (VALVE_4) → 管道D → 备用区域
```

**设备能力要求：**
- 支持4-8路独立阀门控制
- 具备远程通信能力（WiFi/4G/LoRa）
- 支持MQTT或HTTP协议
- 具备阀门状态反馈功能

**方案2：分布式设备系统**
```
1号大棚
├── 小型设备A (WF-001A) → 番茄区域 (200㎡)
├── 小型设备B (WF-001B) → 黄瓜区域 (150㎡)
└── 小型设备C (WF-001C) → 生菜区域 (100㎡)
```

**方案3：逻辑区域控制（兼容方案）**
```
单阀门设备 WF-001
├── 统一水肥输出
└── 软件逻辑区域
    ├── 区域A配置 (参数集1)
    ├── 区域B配置 (参数集2)
    └── 区域C配置 (参数集3)
```

#### 2.2.2 软件控制逻辑

**区域控制指令格式：**
```javascript
// 物理区域控制指令
const physicalZoneCommand = {
  deviceId: "WF-001",
  commandType: "ZONE_CONTROL",
  zones: [
    {
      zoneId: 1,
      valveId: "VALVE_1",
      action: "START",
      parameters: {
        duration: 1800,        // 30分钟
        flowRate: 60,          // L/min
        pressure: 2.5,         // bar
        fertilizer: {
          enabled: true,
          npkRatio: [20, 10, 10],
          concentration: 1.2   // EC值
        }
      }
    }
  ],
  timestamp: 1640995200000,
  userId: "user123"
};

// 逻辑区域控制指令
const logicalZoneCommand = {
  deviceId: "WF-001",
  commandType: "ZONE_PRESET",
  zoneProfile: "tomato_flowering", // 预设参数配置
  parameters: {
    duration: 1800,
    flowRate: 80,             // 番茄开花期需要更多水
    fertilizer: {
      enabled: true,
      preset: "tomato_bloom"  // 番茄开花期配方
    }
  }
};
```

**设备端处理逻辑：**
```javascript
// 设备端指令处理器
class DeviceController {
  constructor(deviceConfig) {
    this.config = deviceConfig;
    this.valves = this.initializeValves();
    this.sensors = this.initializeSensors();
  }

  // 处理区域控制指令
  handleZoneCommand(command) {
    switch(command.commandType) {
      case 'ZONE_CONTROL':
        return this.executePhysicalZoneControl(command);
      case 'ZONE_PRESET':
        return this.executeLogicalZoneControl(command);
      default:
        throw new Error('Unknown command type');
    }
  }

  // 物理区域控制
  executePhysicalZoneControl(command) {
    const { zones } = command;
    
    // 启动主系统
    this.startMainPump();
    
    zones.forEach(zone => {
      // 打开对应阀门
      this.openValve(zone.valveId);
      
      // 设置流量控制
      this.setFlowRate(zone.valveId, zone.parameters.flowRate);
      
      // 配置施肥系统
      if (zone.parameters.fertilizer.enabled) {
        this.configureFertilizer(zone.parameters.fertilizer);
      }
      
      // 设置定时关闭
      this.scheduleValveClose(zone.valveId, zone.parameters.duration);
    });
    
    return { success: true, taskId: this.generateTaskId() };
  }

  // 逻辑区域控制
  executeLogicalZoneControl(command) {
    const preset = this.loadZonePreset(command.zoneProfile);
    const mergedParams = { ...preset, ...command.parameters };
    
    // 应用预设参数
    this.applyParameters(mergedParams);
    
    return { success: true, taskId: this.generateTaskId() };
  }
}
```

#### 2.2.3 区域映射配置

**数据库中的区域配置表：**
```javascript
// 区域配置数据模型
const ZoneConfigModel = {
  id: String,                // 区域ID
  plotId: String,            // 所属地块
  name: String,              // 区域名称
  type: String,              // physical/logical/hybrid
  
  // 物理配置（物理区域必需）
  hardware: {
    deviceId: String,        // 绑定设备ID
    valveId: String,         // 阀门ID
    pipelineId: String,      // 管道ID
    sensorIds: Array,        // 绑定传感器ID列表
    coverage: {              // 覆盖范围
      coordinates: Array,    // 地理坐标
      area: Number          // 面积
    }
  },
  
  // 逻辑配置
  management: {
    crop: {                  // 作物信息
      type: String,          // 作物类型
      variety: String,       // 品种
      plantDate: Date,       // 种植日期
      growthStage: String    // 生长阶段
    },
    irrigation: {            // 灌溉参数
      defaultDuration: Number,
      defaultFlowRate: Number,
      schedule: Array        // 定时计划
    },
    fertilizer: {            // 施肥参数
      defaultFormula: String,
      concentration: Number,
      schedule: Array
    }
  },
  
  status: String,            // active/inactive/maintenance
  createdAt: Date,
  updatedAt: Date
};
```

#### 2.2.4 设备发现与绑定流程

**自动发现流程：**
```javascript
// 设备发现和区域绑定
class DeviceDiscovery {
  // 扫描局域网设备
  async scanDevices() {
    const devices = await this.networkScan();
    return devices.filter(device => 
      device.type === 'irrigation' && device.capabilities.includes('zone_control')
    );
  }

  // 获取设备能力
  async getDeviceCapabilities(deviceId) {
    const response = await this.sendCommand(deviceId, 'GET_CAPABILITIES');
    return {
      maxZones: response.maxZones || 1,
      valveTypes: response.valveTypes || ['standard'],
      communicationProtocols: response.protocols || ['mqtt'],
      sensors: response.sensors || []
    };
  }

  // 配置区域映射
  async configureZoneMapping(deviceId, zones) {
    const capabilities = await this.getDeviceCapabilities(deviceId);
    
    if (zones.length > capabilities.maxZones) {
      throw new Error(`设备最多支持 ${capabilities.maxZones} 个区域`);
    }
    
    const mapping = zones.map((zone, index) => ({
      zoneId: zone.id,
      valveIndex: index + 1,
      valveId: `VALVE_${index + 1}`,
      parameters: zone.defaultParameters
    }));
    
    return await this.sendCommand(deviceId, 'SET_ZONE_MAPPING', mapping);
  }
}
```

### 2.3 用户界面中的区域管理

#### 2.3.1 区域添加流程

**Step 1: 选择区域类型**
```xml
<view class="zone-type-selector">
  <view class="type-option" bindtap="selectZoneType" data-type="physical">
    <view class="type-icon">🔧</view>
    <text class="type-name">物理区域</text>
    <text class="type-desc">设备支持多阀门控制</text>
  </view>
  <view class="type-option" bindtap="selectZoneType" data-type="logical">
    <view class="type-icon">⚙️</view>
    <text class="type-name">逻辑区域</text>
    <text class="type-desc">基于参数配置管理</text>
  </view>
</view>
```

**Step 2: 配置区域信息**
```javascript
// 区域配置页面逻辑
Page({
  data: {
    zoneType: '',
    zoneInfo: {
      name: '',
      area: 0,
      crop: '',
      deviceId: '',
      valveId: ''  // 仅物理区域需要
    },
    availableDevices: [],
    availableValves: []
  },

  onZoneTypeSelect(e) {
    const zoneType = e.currentTarget.dataset.type;
    this.setData({ zoneType });
    
    if (zoneType === 'physical') {
      this.loadAvailableDevices();
    }
  },

  async loadAvailableDevices() {
    const devices = await this.getDevicesWithZoneSupport();
    this.setData({ availableDevices: devices });
  },

  async onDeviceSelect(e) {
    const deviceId = e.detail.value;
    const valves = await this.getAvailableValves(deviceId);
    this.setData({ 
      'zoneInfo.deviceId': deviceId,
      availableValves: valves 
    });
  }
});
```

#### 2.3.2 区域控制界面

**物理区域控制：**
```xml
<view class="zone-control-panel">
  <view class="zone-selector">
    <text class="title">选择控制区域</text>
    <view class="zone-grid">
      <view class="zone-item {{zone.selected ? 'selected' : ''}} {{zone.status}}"
            wx:for="{{physicalZones}}" wx:key="id"
            bindtap="toggleZone" data-id="{{zone.id}}">
        <view class="zone-header">
          <text class="zone-name">{{zone.name}}</text>
          <view class="zone-status {{zone.valveStatus}}"></view>
        </view>
        <text class="zone-crop">{{zone.crop}}</text>
        <text class="zone-info">阀门{{zone.valveId}} • {{zone.area}}㎡</text>
      </view>
    </view>
  </view>
  
  <view class="control-actions">
    <button class="btn-primary" bindtap="startSelectedZones">
      启动选中区域 ({{selectedZones.length}})
    </button>
  </view>
</view>
```

**逻辑区域控制：**
```xml
<view class="logical-zone-control">
  <view class="zone-preset-selector">
    <text class="title">选择区域配置</text>
    <picker range="{{zonePresets}}" range-key="name" 
            bindchange="onPresetChange">
      <view class="picker-display">
        <text>{{currentPreset.name}}</text>
        <text class="preset-desc">{{currentPreset.description}}</text>
      </view>
    </picker>
  </view>
  
  <view class="preset-parameters">
    <view class="param-item">
      <text class="param-label">灌溉时长</text>
      <input type="number" value="{{currentPreset.duration}}" 
             bindinput="onParamChange" data-field="duration"/>
      <text class="param-unit">分钟</text>
    </view>
  </view>
</view>
```

### 2.4 区域管理的技术实现要点

#### 2.4.1 设备兼容性处理
```javascript
// 设备兼容性检测
class DeviceCompatibility {
  static async checkZoneSupport(deviceId) {
    try {
      const capabilities = await DeviceAPI.getCapabilities(deviceId);
      return {
        supportsPhysicalZones: capabilities.maxValves > 1,
        maxZones: capabilities.maxValves || 1,
        supportsLogicalZones: true,  // 所有设备都支持逻辑区域
        recommendedMode: capabilities.maxValves > 1 ? 'physical' : 'logical'
      };
    } catch (error) {
      // 降级处理：假设只支持逻辑区域
      return {
        supportsPhysicalZones: false,
        maxZones: 1,
        supportsLogicalZones: true,
        recommendedMode: 'logical'
      };
    }
  }
}
```

#### 2.4.2 区域冲突检测
```javascript
// 区域冲突检测算法
class ZoneConflictDetector {
  static detectConflicts(zones, schedule) {
    const conflicts = [];
    
    // 检测物理冲突：同一阀门同时被多个区域使用
    const valveUsage = this.analyzeValveUsage(zones, schedule);
    valveUsage.forEach((usage, valveId) => {
      if (usage.length > 1) {
        conflicts.push({
          type: 'VALVE_CONFLICT',
          valveId: valveId,
          conflictingZones: usage,
          message: `阀门 ${valveId} 被多个区域同时使用`
        });
      }
    });
    
    // 检测资源冲突：水泵容量不足
    const pumpLoad = this.calculatePumpLoad(zones, schedule);
    if (pumpLoad.maxConcurrent > pumpLoad.capacity) {
      conflicts.push({
        type: 'PUMP_CAPACITY_CONFLICT',
        required: pumpLoad.maxConcurrent,
        available: pumpLoad.capacity,
        message: `水泵容量不足，需要 ${pumpLoad.maxConcurrent}L/min，但只有 ${pumpLoad.capacity}L/min`
      });
    }
    
    return conflicts;
  }
}
```

#### 2.4.3 区域性能优化
```javascript
// 区域操作性能优化
class ZoneOptimizer {
  // 批量区域操作
  static async batchZoneOperation(zones, operation) {
    // 按设备分组，减少通信次数
    const deviceGroups = this.groupByDevice(zones);
    
    const promises = Object.keys(deviceGroups).map(deviceId => {
      const deviceZones = deviceGroups[deviceId];
      return this.executeDeviceOperation(deviceId, deviceZones, operation);
    });
    
    return await Promise.all(promises);
  }
  
  // 智能调度：避免同时启动过多区域
  static optimizeSchedule(zones, schedule) {
    const optimized = [...schedule];
    
    // 错开启动时间，避免瞬时负载过高
    optimized.forEach((item, index) => {
      if (item.zones.length > 2) {
        item.staggeredStart = true;
        item.startDelay = index * 30; // 每个区域延迟30秒启动
      }
    });
    
    return optimized;
  }
}
```

## 3. 功能模块详细设计

### 3.1 首页模块（Dashboard）

#### 3.1.1 功能概述
- **目标**：提供农场整体状况的一览式展示
- **用户价值**：快速了解农场状态，进行紧急操作

#### 3.1.2 主要功能点

| 功能点 | 描述 | 对应页面文件 | 优先级 |
|-------|------|-------------|-------|
| 农场信息展示 | 显示农场名称、总面积、地块数量 | `/pages/index/index.wxml` | 高 |
| 地块状态卡片 | 显示各地块的设备状态和作物信息 | `/pages/index/index.js` (farmOverview) | 高 |
| 今日数据统计 | 灌溉时长、施肥量、预警次数等 | `/pages/index/index.js` (todayData) | 中 |
| 快速操作按钮 | 一键灌溉、查看监测、设备状态 | `/pages/index/index.wxml` (quickActions) | 高 |
| 消息通知中心 | 系统通知、预警信息、任务提醒 | `/pages/index/index.js` (notifications) | 中 |

#### 3.1.3 数据结构
```javascript
// 农场概览数据
farmOverview: {
  name: "张三的有机农场",
  totalArea: 1200,
  plotCount: 3,
  deviceCount: 6,
  onlineDevices: 5
}

// 地块状态数据
plotStatus: [
  {
    id: 1,
    name: "1号大棚",
    crop: "番茄",
    status: "normal", // normal/warning/error
    deviceStatus: "online",
    lastUpdate: "2024-01-20 14:30"
  }
]
```

### 3.2 地块管理模块

#### 3.2.1 功能概述
- **目标**：管理农场的地块信息和设备绑定
- **用户价值**：灵活配置农场结构，支持不同规模的管理需求

#### 3.2.2 主要功能点

| 功能点 | 描述 | 对应页面文件 | 优先级 |
|-------|------|-------------|-------|
| 地块列表查看 | 显示所有地块的基本信息 | `/pages/farm/farm.wxml` | 高 |
| 添加新地块 | 创建新的种植地块 | `/pages/farm/add-plot/add-plot.js` | 高 |
| 管理模式选择 | 简单模式 vs 精细模式 | `/pages/farm/add-plot/add-plot.js` | 高 |
| 设备绑定 | 关联水肥一体机和土壤传感器 | `/pages/farm/device-binding/device-binding.js` | 高 |
| 作物信息设置 | 记录种植的作物类型和时间 | `/pages/farm/crop-management/crop-management.js` | 中 |
| 区域管理 | 精细模式下的区域划分 | `/pages/farm/zone-management/zone-management.js` | 中 |

#### 3.2.3 管理模式对比

| 特性 | 简单模式 | 精细模式 |
|------|---------|---------|
| **适用场景** | 小农场，单一作物 | 大农场，多种作物 |
| **管理单位** | 地块 | 区域 |
| **设备绑定** | 地块绑定设备 | 区域绑定阀门/传感器 |
| **控制粒度** | 整个地块统一控制 | 不同区域分别控制 |
| **配置复杂度** | 简单，3步完成 | 复杂，需要详细配置 |
| **功能丰富度** | 基础功能 | 高级功能 |
| **学习成本** | 低 | 中等 |

#### 3.2.4 添加地块流程

**简单模式流程：**
```
1. 输入地块信息（名称、面积、位置）
2. 绑定水肥一体机
3. 绑定土壤传感器
4. 设置种植作物
5. 完成创建
```

**精细模式流程：**
```
1. 输入地块信息（名称、面积、位置）
2. 划分区域（区域名称、面积、作物）
3. 为每个区域绑定设备（阀门、传感器）
4. 设置区域参数（灌溉策略、施肥配方）
5. 完成创建
```

### 3.3 设备控制模块

#### 3.3.1 功能概述
- **目标**：实现对水肥一体机的精准控制
- **用户价值**：远程操作，提高管理效率

#### 3.3.2 主要功能点

| 功能点 | 描述 | 对应页面文件 | 优先级 |
|-------|------|-------------|-------|
| 实时控制面板 | 手动启停、参数调节 | `/pages/device/irrigation/irrigation.wxml` | 高 |
| 区域选择控制 | 选择特定区域进行操作 | `/pages/device/irrigation/irrigation.js` (zoneList) | 高 |
| 流量调节 | 灌溉流量的精确控制 | `/pages/device/irrigation/irrigation.wxml` (slider) | 高 |
| 定时计划管理 | 设置自动灌溉计划 | `/pages/device/schedule/schedule.js` | 高 |
| 施肥配方选择 | 选择和应用施肥配方 | `/pages/device/fertilizer/fertilizer.js` | 中 |
| 智能模式 | 基于传感器数据自动控制 | `/pages/device/irrigation/irrigation.js` (smartControl) | 中 |
| 紧急停止 | 紧急情况下的快速停止 | `/pages/device/irrigation/irrigation.wxml` | 高 |

#### 3.3.3 控制层级设计

**简单模式控制：**
```
地块 → 设备 → 全局控制
例：1号大棚 → WF-001水肥机 → 整体灌溉
```

**精细模式控制：**
```
地块 → 区域 → 设备 → 精确控制
例：1号大棚 → A区域 → 阀门1 → 番茄灌溉
```

#### 3.3.4 定时计划功能

| 功能 | 描述 | 实现方式 |
|------|------|---------|
| 计划创建 | 创建新的灌溉计划 | `/pages/device/schedule/add/add.js` |
| 重复模式 | 自定义星期执行 | 星期选择器 |
| 多时段支持 | 一天内多次执行 | 时间段数组 |
| 快速模板 | 预设的常用计划 | 模板应用功能 |
| 计划编辑 | 修改现有计划 | 编辑模式支持 |
| 冲突检测 | 避免计划时间冲突 | 智能检测算法 |

### 3.4 监测数据模块

#### 3.4.1 功能概述
- **目标**：实时监测土壤状况，提供数据分析
- **用户价值**：科学决策，精准农业

#### 3.4.2 主要功能点

| 功能点 | 描述 | 对应页面文件 | 优先级 |
|-------|------|-------------|-------|
| 实时数据展示 | 当前土壤指标显示 | `/pages/monitor/monitor.wxml` | 高 |
| 传感器地图 | 传感器位置和状态 | `/pages/monitor/monitor.js` (sensorMap) | 中 |
| 历史趋势图 | 数据变化趋势分析 | `/pages/monitor/history/history.js` | 高 |
| 多深度监测 | 不同深度的土壤数据 | `/pages/monitor/monitor.js` (depthData) | 中 |
| 预警设置 | 异常值阈值配置 | `/pages/monitor/alert/alert.js` | 高 |
| 数据导出 | 历史数据报表导出 | `/pages/monitor/export/export.js` | 低 |
| 对比分析 | 不同时期数据对比 | `/pages/monitor/analysis/analysis.js` | 低 |

#### 3.4.3 监测指标设计

| 指标 | 单位 | 正常范围 | 作用 |
|------|------|---------|------|
| 土壤湿度 | % | 40-70% | 指导灌溉决策 |
| 土壤温度 | ℃ | 15-30℃ | 监测根系环境 |
| pH值 | - | 6.0-7.5 | 影响养分吸收 |
| EC值 | ms/cm | 1.5-3.0 | 反映盐分浓度 |
| 氮含量 | mg/kg | 50-150 | 指导氮肥施用 |
| 磷含量 | mg/kg | 10-50 | 指导磷肥施用 |
| 钾含量 | mg/kg | 100-300 | 指导钾肥施用 |

#### 3.4.4 预警系统设计

**预警级别：**
- **轻度预警**：数值接近边界，黄色提示
- **中度预警**：超出正常范围，橙色警告
- **重度预警**：严重偏离，红色告警

**预警触发机制：**
```javascript
// 预警规则示例
const alertRules = {
  soilMoisture: {
    low: { threshold: 30, level: 'warning', action: 'suggest_irrigation' },
    critical: { threshold: 20, level: 'critical', action: 'auto_irrigation' }
  },
  soilPH: {
    low: { threshold: 5.5, level: 'warning', action: 'suggest_lime' },
    high: { threshold: 8.0, level: 'warning', action: 'suggest_sulfur' }
  }
}
```

### 3.5 设置中心模块

#### 3.5.1 功能概述
- **目标**：系统配置和用户管理
- **用户价值**：个性化设置，系统维护

#### 3.5.2 主要功能点

| 功能点 | 描述 | 对应页面文件 | 优先级 |
|-------|------|-------------|-------|
| 农场信息管理 | 修改农场基本信息 | `/pages/profile/farm-info/farm-info.js` | 中 |
| 管理模式切换 | 简单/精细模式切换 | `/pages/profile/settings/settings.js` | 中 |
| 设备管理 | 设备绑定、状态检查 | `/pages/profile/device-management/device-management.js` | 高 |
| 通知设置 | 预警通知配置 | `/pages/profile/notification/notification.js` | 中 |
| 数据导出 | 历史数据导出设置 | `/pages/profile/data-export/data-export.js` | 低 |
| 帮助中心 | 使用教程和FAQ | `/pages/profile/help/help.js` | 中 |
| 用户反馈 | 问题反馈和建议 | `/pages/profile/feedback/feedback.js` | 低 |

## 4. 用户决策点分析

### 4.1 关键决策点

#### 4.1.1 管理模式选择
**决策因素：**
- 农场规模（小于100㎡建议简单模式）
- 作物种类（单一作物建议简单模式）
- 技术水平（新手建议简单模式）
- 管理精度需求（精细化管理选择精细模式）

**决策支持：**
```javascript
// 模式推荐算法
function recommendMode(farmInfo) {
  const { area, cropTypes, userLevel, managementGoal } = farmInfo;
  
  let score = 0;
  if (area > 500) score += 2;
  if (cropTypes.length > 2) score += 2;
  if (userLevel === 'advanced') score += 1;
  if (managementGoal === 'precision') score += 2;
  
  return score >= 4 ? 'precision' : 'simple';
}
```

#### 4.1.2 异常处理决策
**触发条件：**
- 土壤湿度 < 30% 或 > 80%
- 设备离线超过30分钟
- pH值 < 5.5 或 > 8.0
- EC值异常波动

**处理策略：**
```javascript
// 异常处理策略
const emergencyActions = {
  low_moisture: {
    immediate: '立即灌溉',
    preventive: '调整灌溉计划',
    longterm: '检查灌溉系统'
  },
  device_offline: {
    immediate: '检查网络连接',
    preventive: '重启设备',
    longterm: '联系技术支持'
  }
}
```

### 4.2 用户行为路径分析

#### 4.2.1 新用户路径
```
注册登录 → 观看引导视频 → 选择模式 → 配置第一个地块 → 完成设备绑定 → 查看数据 → 首次控制操作
```

#### 4.2.2 日常用户路径
```
打开应用 → 查看首页概览 → 检查异常提醒 → 进入具体地块 → 查看监测数据 → 必要时进行控制操作
```

#### 4.2.3 高级用户路径
```
快速浏览状态 → 批量操作多个地块 → 查看趋势分析 → 调整自动化策略 → 导出数据报告
```

## 5. 管理模式对比

### 5.1 详细功能对比

| 功能领域 | 简单模式 | 精细模式 | 技术实现差异 |
|---------|---------|---------|------------|
| **地块管理** | 整体管理 | 区域细分 | 数据结构层级不同 |
| **设备控制** | 统一控制 | 分区控制 | 控制粒度不同 |
| **监测数据** | 平均值显示 | 分区数据 | 数据聚合方式不同 |
| **定时计划** | 地块级计划 | 区域级计划 | 计划关联对象不同 |
| **施肥管理** | 统一配方 | 区域配方 | 配方应用范围不同 |
| **用户界面** | 简化界面 | 完整界面 | UI组件显示逻辑不同 |

### 5.2 模式切换机制

#### 5.2.1 切换条件
- 用户主动切换
- 系统根据使用情况推荐切换
- 农场规模变化触发切换建议

#### 5.2.2 数据迁移策略
```javascript
// 简单模式 → 精细模式
function migrateToAdvanced(plotData) {
  return {
    ...plotData,
    zones: [{
      id: 1,
      name: '主区域',
      area: plotData.area,
      crop: plotData.crop,
      devices: plotData.devices
    }]
  };
}

// 精细模式 → 简单模式
function migrateToSimple(plotData) {
  return {
    ...plotData,
    area: plotData.zones.reduce((sum, zone) => sum + zone.area, 0),
    crop: plotData.zones[0].crop, // 取第一个区域的作物
    devices: plotData.zones.flatMap(zone => zone.devices)
  };
}
```

## 6. 页面结构与功能映射

### 6.1 小程序页面结构

```
src/
├── pages/
│   ├── index/                    # 首页模块
│   │   ├── index.js             # 农场概览、快速操作
│   │   ├── index.wxml           # 首页布局
│   │   └── index.wxss           # 首页样式
│   │
│   ├── farm/                     # 地块管理模块
│   │   ├── farm.js              # 地块列表
│   │   ├── add-plot/            # 添加地块
│   │   ├── plot-detail/         # 地块详情
│   │   ├── zone-management/     # 区域管理（精细模式）
│   │   └── device-binding/      # 设备绑定
│   │
│   ├── device/                   # 设备控制模块
│   │   ├── irrigation/          # 灌溉控制
│   │   │   ├── irrigation.js    # 控制逻辑
│   │   │   └── irrigation.wxml  # 控制界面
│   │   ├── fertilizer/          # 施肥管理
│   │   ├── schedule/            # 定时计划
│   │   │   ├── schedule.js      # 计划列表
│   │   │   └── add/             # 添加计划
│   │   └── config/              # 设备配置
│   │
│   ├── monitor/                  # 监测数据模块
│   │   ├── monitor.js           # 实时监测
│   │   ├── history/             # 历史数据
│   │   ├── analysis/            # 数据分析
│   │   └── alert/               # 预警设置
│   │
│   └── profile/                  # 设置中心模块
│       ├── profile.js           # 个人中心
│       ├── settings/            # 系统设置
│       ├── device-management/   # 设备管理
│       └── help/                # 帮助中心
│
├── components/                   # 公共组件
│   ├── chart/                   # 图表组件
│   ├── device-card/             # 设备卡片
│   ├── plot-card/               # 地块卡片
│   └── status-indicator/        # 状态指示器
│
└── utils/                        # 工具函数
    ├── api.js                   # API接口
    ├── device-control.js        # 设备控制
    └── data-analysis.js         # 数据分析
```

### 6.2 功能与页面映射表

| 功能模块 | 主要页面 | 关键组件 | API接口 |
|---------|---------|---------|---------|
| **农场概览** | `/pages/index/index` | `plot-card`, `status-indicator` | `/api/farm/overview` |
| **地块管理** | `/pages/farm/farm` | `plot-card`, `device-binding` | `/api/plots/*` |
| **设备控制** | `/pages/device/irrigation` | `device-card`, `control-panel` | `/api/devices/control` |
| **监测数据** | `/pages/monitor/monitor` | `chart`, `sensor-card` | `/api/sensors/data` |
| **定时计划** | `/pages/device/schedule` | `schedule-card`, `time-picker` | `/api/schedules/*` |
| **系统设置** | `/pages/profile/settings` | `setting-item`, `mode-switch` | `/api/settings/*` |

## 7. 数据流设计

### 7.1 数据流架构

```mermaid
graph LR
    A[传感器硬件] --> B[数据采集层]
    B --> C[数据处理层]
    C --> D[业务逻辑层]
    D --> E[数据展示层]
    
    F[控制指令] --> G[设备控制层]
    G --> H[硬件执行层]
    
    E --> I[用户界面]
    I --> F
    
    C --> J[数据存储]
    J --> C
```

### 7.2 核心数据模型

#### 7.2.1 农场数据模型
```javascript
const FarmModel = {
  id: String,              // 农场ID
  name: String,            // 农场名称
  ownerId: String,         // 农场主ID
  area: Number,            // 总面积
  location: {              // 位置信息
    latitude: Number,
    longitude: Number,
    address: String
  },
  managementMode: String,  // 管理模式：simple/precision
  plots: Array,            // 地块列表
  devices: Array,          // 设备列表
  createdAt: Date,
  updatedAt: Date
};
```

#### 7.2.2 地块数据模型
```javascript
const PlotModel = {
  id: String,              // 地块ID
  farmId: String,          // 所属农场ID
  name: String,            // 地块名称
  area: Number,            // 面积
  managementMode: String,  // 管理模式
  
  // 简单模式字段
  crop: Object,            // 种植作物
  devices: Array,          // 绑定设备
  
  // 精细模式字段
  zones: Array,            // 区域列表
  
  coordinates: Object,     // 地理坐标
  soilType: String,        // 土壤类型
  status: String,          // 状态
  createdAt: Date,
  updatedAt: Date
};
```

#### 7.2.3 设备数据模型
```javascript
const DeviceModel = {
  id: String,              // 设备ID
  serialNumber: String,    // 设备序列号
  type: String,            // 设备类型：irrigation/sensor
  name: String,            // 设备名称
  model: String,           // 设备型号
  farmId: String,          // 所属农场
  plotId: String,          // 所属地块
  zoneId: String,          // 所属区域（可选）
  
  status: {                // 设备状态
    online: Boolean,
    working: Boolean,
    lastSeen: Date,
    batteryLevel: Number
  },
  
  config: Object,          // 设备配置
  capabilities: Array,     // 设备能力
  createdAt: Date,
  updatedAt: Date
};
```

### 7.3 API接口设计

#### 7.3.1 农场管理接口
```javascript
// 获取农场列表
GET /api/farms
Response: { farms: [FarmModel], total: Number }

// 创建农场
POST /api/farms
Body: { name, location, managementMode }
Response: { farm: FarmModel, success: Boolean }

// 更新农场信息
PUT /api/farms/:farmId
Body: { name, managementMode, ... }
Response: { farm: FarmModel, success: Boolean }
```

#### 7.3.2 地块管理接口
```javascript
// 获取地块列表
GET /api/farms/:farmId/plots
Response: { plots: [PlotModel], total: Number }

// 创建地块
POST /api/farms/:farmId/plots
Body: { name, area, managementMode, crop, zones }
Response: { plot: PlotModel, success: Boolean }

// 更新地块信息
PUT /api/plots/:plotId
Body: { name, area, crop, zones, ... }
Response: { plot: PlotModel, success: Boolean }
```

#### 7.3.3 设备控制接口
```javascript
// 设备控制
POST /api/devices/:deviceId/control
Body: { action, parameters, duration }
Response: { taskId: String, success: Boolean }

// 获取设备状态
GET /api/devices/:deviceId/status
Response: { status: Object, lastUpdate: Date }

// 设置定时任务
POST /api/devices/:deviceId/schedules
Body: { name, schedule, parameters }
Response: { schedule: Object, success: Boolean }
```

#### 7.3.4 监测数据接口
```javascript
// 获取实时数据
GET /api/sensors/:sensorId/data/realtime
Response: { data: Object, timestamp: Date }

// 获取历史数据
GET /api/sensors/:sensorId/data/history
Query: { startDate, endDate, interval }
Response: { data: Array, statistics: Object }

// 设置预警规则
POST /api/sensors/:sensorId/alerts
Body: { rules: Array, notifications: Object }
Response: { alertId: String, success: Boolean }
```

## 8. 实施建议

### 8.1 开发阶段规划

#### Phase 1: 核心功能实现（4-6周）
**目标**：实现基础的监测和控制功能

**优先级1（必须完成）**：
- [ ] 用户登录和农场初始化
- [ ] 地块管理（添加、编辑、删除）
- [ ] 设备绑定和状态监控
- [ ] 基础的灌溉控制（手动）
- [ ] 实时监测数据展示
- [ ] 简单的预警功能

**优先级2（重要功能）**：
- [ ] 定时计划管理
- [ ] 施肥配方管理
- [ ] 历史数据查看
- [ ] 数据图表展示

#### Phase 2: 智能化功能（2-3周）
**目标**：增加自动化和智能化功能

- [ ] 智能灌溉（基于土壤湿度）
- [ ] 预警系统完善
- [ ] 数据分析和趋势预测
- [ ] 操作优化建议

#### Phase 3: 体验优化（1-2周）
**目标**：提升用户体验和系统稳定性

- [ ] 界面优化和交互改进
- [ ] 性能优化
- [ ] 错误处理完善
- [ ] 用户帮助和引导

### 8.2 技术实现重点

#### 8.2.1 数据同步策略
```javascript
// 实时数据同步
const realtimeSync = {
  interval: 30000, // 30秒同步一次
  sensors: ['soil_moisture', 'soil_temperature', 'ph', 'ec'],
  onUpdate: (data) => {
    updateUI(data);
    checkAlerts(data);
  }
};

// 设备状态同步
const deviceSync = {
  interval: 60000, // 1分钟检查设备状态
  onStatusChange: (device, status) => {
    updateDeviceStatus(device, status);
    if (!status.online) {
      triggerOfflineAlert(device);
    }
  }
};
```

#### 8.2.2 离线处理机制
```javascript
// 离线数据缓存
const offlineManager = {
  cacheData: (data) => {
    wx.setStorageSync('offline_data', data);
  },
  
  syncWhenOnline: () => {
    const offlineData = wx.getStorageSync('offline_data');
    if (offlineData) {
      syncToServer(offlineData);
      wx.removeStorageSync('offline_data');
    }
  }
};
```

#### 8.2.3 错误处理策略
```javascript
// 统一错误处理
const errorHandler = {
  network: (error) => {
    showToast('网络连接异常，请检查网络设置');
    enableOfflineMode();
  },
  
  device: (error) => {
    showToast('设备连接失败，请检查设备状态');
    updateDeviceStatus(error.deviceId, { online: false });
  },
  
  data: (error) => {
    showToast('数据异常，请刷新后重试');
    logError(error);
  }
};
```

### 8.3 测试策略

#### 8.3.1 功能测试重点
- **设备控制测试**：确保控制指令正确发送和执行
- **数据准确性测试**：验证监测数据的准确性和实时性
- **预警系统测试**：测试预警触发和通知机制
- **用户权限测试**：验证不同用户的操作权限

#### 7.3.2 性能测试重点
- **数据加载性能**：大量历史数据的加载速度
- **实时性能**：实时数据更新的响应时间
- **并发性能**：多设备同时操作的处理能力
- **网络适应性**：弱网环境下的应用表现

#### 7.3.3 用户体验测试
- **易用性测试**：新用户使用的学习成本
- **操作流程测试**：关键操作路径的流畅性
- **异常情况测试**：异常情况下的用户体验
- **设备适配测试**：不同手机型号的兼容性

### 7.4 部署和运维

#### 7.4.1 部署架构
```
用户端（微信小程序）
↓
API网关
↓
业务服务层
├── 用户服务
├── 设备服务
├── 数据服务
└── 通知服务
↓
数据存储层
├── 关系数据库（MySQL）
├── 时序数据库（InfluxDB）
└── 缓存（Redis）
```

#### 7.4.2 监控指标
- **业务指标**：活跃用户、设备在线率、数据准确率
- **技术指标**：接口响应时间、系统可用性、错误率
- **用户体验指标**：页面加载时间、操作成功率、用户满意度

---

*本文档为智慧农业小程序的完整业务流程和功能设计文档，为产品开发和迭代提供详细指导。*