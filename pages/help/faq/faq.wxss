/* pages/help/faq/faq.wxss */

.page-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 搜索栏 */
.search-section {
  padding: 20rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
}

.search-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

/* 分类筛选 */
.category-section {
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-scroll {
  padding: 20rpx 0;
}

.category-list {
  display: flex;
  padding: 0 20rpx;
  gap: 12rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.category-item.active {
  background: #2E7D32;
  color: #ffffff;
}

.category-icon {
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.category-name {
  font-size: 24rpx;
  font-weight: 500;
  margin-bottom: 2rpx;
}

.category-count {
  font-size: 20rpx;
  opacity: 0.7;
}

/* FAQ列表 */
.faq-section {
  padding: 20rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background: #ffffff;
  border-radius: 16rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 32rpx;
}

.faq-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.faq-item {
  border-bottom: 1rpx solid #f8f8f8;
  transition: all 0.3s ease;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  display: flex;
  align-items: center;
  padding: 32rpx;
  cursor: pointer;
}

.faq-question:active {
  background: #f8f8f8;
}

.question-content {
  flex: 1;
}

.question-text {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.question-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.tag {
  font-size: 20rpx;
  color: #2E7D32;
  background: rgba(46, 125, 50, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.expand-icon {
  font-size: 20rpx;
  color: #999999;
  transition: transform 0.3s ease;
  margin-left: 16rpx;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  background: #f9f9f9;
}

.faq-answer.show {
  max-height: 1000rpx;
  padding: 0 32rpx 32rpx;
}

.answer-text {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 24rpx;
  padding-top: 24rpx;
}

.answer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.helpful-section {
  display: flex;
  align-items: center;
}

.helpful-btn {
  display: flex;
  align-items: center;
  background: transparent;
  border: none;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #666666;
  transition: all 0.3s ease;
}

.helpful-btn::after {
  border: none;
}

.helpful-btn:active {
  background: rgba(46, 125, 50, 0.1);
  color: #2E7D32;
}

.helpful-icon {
  margin-right: 8rpx;
}

/* 联系支持 */
.support-section {
  padding: 20rpx;
  margin-top: 20rpx;
}

.support-card {
  display: flex;
  align-items: center;
  background: #ffffff;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.support-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.support-content {
  flex: 1;
}

.support-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.support-desc {
  font-size: 26rpx;
  color: #666666;
}

/* 按钮样式 */
.btn {
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  padding: 20rpx 32rpx;
  transition: all 0.3s ease;
}

.btn::after {
  border: none;
}

.btn-primary {
  background: #2E7D32;
  color: #ffffff;
}

.btn-primary:active {
  background: #1B5E20;
  transform: scale(0.98);
}

.btn-outline {
  background: transparent;
  border: 2rpx solid #2E7D32;
  color: #2E7D32;
}

.btn-outline:active {
  background: rgba(46, 125, 50, 0.1);
  transform: scale(0.98);
}

/* 响应式适配 */
@media screen and (max-width: 400px) {
  .faq-question,
  .faq-answer.show {
    padding: 24rpx 20rpx;
  }
  
  .support-card {
    padding: 24rpx 20rpx;
  }
}