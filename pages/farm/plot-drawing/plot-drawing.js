Page({
  data: {
    latitude: 36.651216,
    longitude: 117.120095,
    isDrawing: false,
    points: [],
    markers: [],
    polygons: [],
    area: 0,
    locationReady: false,
    searchKeyword: ''
  },

  onLoad() {
    console.log('地块绘制页面加载');
    this.getCurrentLocation();
  },

  // 获取当前位置（只获取一次，不动态更新）
  getCurrentLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('获取到位置:', res.latitude, res.longitude);
        this.setData({
          latitude: res.latitude,
          longitude: res.longitude,
          locationReady: true
        });
      },
      fail: (err) => {
        console.log('获取位置失败，使用默认位置');
        this.setData({
          locationReady: true
        });
      }
    });
  },

  // 开始绘制
  startDrawing() {
    this.setData({
      isDrawing: true,
      points: [],
      markers: [],
      polygons: [],
      area: 0
    });
    
    wx.showToast({
      title: '点击地图绘制边界',
      icon: 'none'
    });
  },

  // 地图点击
  onMapTap(e) {
    if (!this.data.isDrawing) return;

    const { latitude, longitude } = e.detail;
    const points = [...this.data.points, { latitude, longitude }];
    
    // 实时优化点的顺序（当点数>=3时）
    let optimizedPoints = points;
    if (points.length >= 3) {
      optimizedPoints = this.optimizePolygonPoints(points);
    }
    
    const markers = optimizedPoints.map((point, index) => ({
      id: index,
      latitude: point.latitude,
      longitude: point.longitude,
      width: 30,
      height: 30,
      callout: {
        content: `${index + 1}`,
        fontSize: 14,
        borderRadius: 6,
        bgColor: '#08C160',
        color: '#fff',
        padding: 6
      }
    }));

    let polygons = [];
    let area = 0;
    if (optimizedPoints.length >= 3) {
      polygons = [{
        points: optimizedPoints,
        fillColor: '#08C16030',
        strokeColor: '#08C160',
        strokeWidth: 3
      }];
      area = this.calculateArea(optimizedPoints);
    }

    this.setData({
      points: optimizedPoints,
      markers,
      polygons,
      area
    });
  },

  // 完成绘制
  finishDrawing() {
    if (this.data.points.length < 3) {
      wx.showToast({
        title: '至少需要3个点',
        icon: 'none'
      });
      return;
    }

    // 优化多边形点的顺序，避免交叉
    const optimizedPoints = this.optimizePolygonPoints(this.data.points);
    const optimizedArea = this.calculateArea(optimizedPoints);
    
    // 更新显示
    const polygons = [{
      points: optimizedPoints,
      fillColor: '#08C16030',
      strokeColor: '#08C160',
      strokeWidth: 3
    }];
    
    this.setData({
      points: optimizedPoints,
      polygons,
      area: optimizedArea
    });

    wx.showModal({
      title: '绘制完成',
      content: `面积: ${optimizedArea.toFixed(2)}亩`,
      success: (res) => {
        if (res.confirm) {
          this.saveResult();
        }
      }
    });
  },

  // 优化多边形点顺序，避免线条交叉
  optimizePolygonPoints(points) {
    if (points.length <= 3) return points;
    
    // 使用凸包算法的改进版本来排序点
    // 1. 找到最下方的点（latitude最小的点）
    let bottomPoint = points[0];
    let bottomIndex = 0;
    for (let i = 1; i < points.length; i++) {
      if (points[i].latitude < bottomPoint.latitude || 
          (points[i].latitude === bottomPoint.latitude && points[i].longitude < bottomPoint.longitude)) {
        bottomPoint = points[i];
        bottomIndex = i;
      }
    }
    
    // 2. 计算其他点相对于底部点的极角
    const otherPoints = points.filter((_, index) => index !== bottomIndex);
    const sortedOthers = otherPoints.map(point => {
      const angle = Math.atan2(
        point.latitude - bottomPoint.latitude,
        point.longitude - bottomPoint.longitude
      );
      const distance = Math.sqrt(
        Math.pow(point.latitude - bottomPoint.latitude, 2) + 
        Math.pow(point.longitude - bottomPoint.longitude, 2)
      );
      return { ...point, angle, distance };
    }).sort((a, b) => {
      if (a.angle !== b.angle) {
        return a.angle - b.angle;
      }
      return a.distance - b.distance; // 角度相同时，距离近的在前
    });
    
    // 3. 返回排序后的点（从底部点开始按逆时针顺序）
    const result = [bottomPoint, ...sortedOthers.map(({ angle, distance, ...point }) => point)];
    return result;
  },

  // 清除绘制
  clearDrawing() {
    this.setData({
      isDrawing: false,
      points: [],
      markers: [],
      polygons: [],
      area: 0
    });
  },

  // 计算面积
  calculateArea(points) {
    if (points.length < 3) return 0;
    
    let area = 0;
    for (let i = 0; i < points.length; i++) {
      const j = (i + 1) % points.length;
      area += points[i].longitude * points[j].latitude;
      area -= points[j].longitude * points[i].latitude;
    }
    area = Math.abs(area) / 2;
    return area * 111320 * 111320 * 0.0015; // 转换为亩
  },

  // 保存结果
  saveResult() {
    wx.setStorageSync('plotDrawingResult', {
      polygonPath: this.data.points,
      area: this.data.area.toFixed(2)
    });
    
    wx.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索地点
  searchLocation() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '搜索中...'
    });

    // 使用腾讯地图API搜索
    wx.request({
      url: 'https://apis.map.qq.com/ws/place/v1/search',
      data: {
        keyword: keyword,
        boundary: `nearby(${this.data.latitude},${this.data.longitude},10000)`,
        key: 'MTDBZ-RVJLB-AYLUH-NNYOY-5SJG3-UNF3M'
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.status === 0 && res.data.data.length > 0) {
          const results = res.data.data.slice(0, 5); // 只显示前5个结果
          const itemList = results.map(item => item.title + ' - ' + item.address);
          
          wx.showActionSheet({
            itemList: itemList,
            success: (actionRes) => {
              const selected = results[actionRes.tapIndex];
              this.moveToLocation(selected.location.lat, selected.location.lng, selected.title);
            }
          });
        } else {
          wx.showToast({
            title: '未找到相关地点',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 移动到指定位置（用户主动操作）
  moveToLocation(lat, lng, name) {
    wx.showModal({
      title: '移动到位置',
      content: `确定要移动到 ${name} 吗？`,
      success: (res) => {
        if (res.confirm) {
          this.setData({
            latitude: lat,
            longitude: lng
          });
          wx.showToast({
            title: '定位成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 返回
  goBack() {
    wx.navigateBack();
  }
});