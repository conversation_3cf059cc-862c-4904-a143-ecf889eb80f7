/* pages/feedback/feedback.wxss */

.page-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

.form-container {
  padding: 20rpx;
}

/* 表单区域 */
.form-section {
  background: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 8rpx;
}

.required {
  color: #ff4757;
  font-size: 24rpx;
}

.optional {
  color: #999999;
  font-size: 24rpx;
}

/* 类型选择器 */
.type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.type-item.selected {
  background: rgba(46, 125, 50, 0.1);
  border-color: #2E7D32;
  color: #2E7D32;
}

.type-item:active {
  transform: scale(0.95);
}

.type-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.type-label {
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
}

/* 输入框组 */
.input-group {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #2E7D32;
}

.input-counter {
  position: absolute;
  right: 16rpx;
  bottom: 16rpx;
  font-size: 20rpx;
  color: #999999;
}

/* 文本域组 */
.textarea-group {
  position: relative;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #2E7D32;
}

.textarea-counter {
  position: absolute;
  right: 16rpx;
  bottom: 16rpx;
  font-size: 20rpx;
  color: #999999;
  background: rgba(255, 255, 255, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 表单提示 */
.form-tip {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #f0f8f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #2E7D32;
}

.tip-text {
  font-size: 24rpx;
  color: #4a7c59;
  line-height: 1.4;
}

/* 图片上传 */
.image-upload {
  margin-top: 16rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  border: 2rpx solid #e0e0e0;
}

.image-remove {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

.remove-icon {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
}

.image-add {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #cccccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.image-add:active {
  border-color: #2E7D32;
  background: rgba(46, 125, 50, 0.05);
}

.add-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  opacity: 0.6;
}

.add-text {
  font-size: 22rpx;
  color: #666666;
}

.upload-tip {
  margin-top: 16rpx;
}

/* 操作按钮 */
.form-actions {
  padding: 32rpx;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.btn {
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.btn::after {
  border: none;
}

.btn-primary {
  background: #2E7D32;
  color: #ffffff;
  padding: 28rpx;
}

.btn-primary:active {
  background: #1B5E20;
  transform: scale(0.98);
}

.btn-primary[disabled] {
  background: #cccccc !important;
  color: #999999 !important;
  transform: none !important;
}

.btn-secondary {
  flex: 1;
  background: #f5f5f5;
  color: #666666;
  padding: 20rpx;
  font-size: 26rpx;
}

.btn-secondary:active {
  background: #e0e0e0;
  transform: scale(0.98);
}

.btn-large {
  width: 100%;
  font-size: 32rpx;
  font-weight: 600;
}

/* 反馈提示 */
.feedback-tips {
  padding: 20rpx;
}

.tips-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.tips-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.tips-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
}

.tip-bullet {
  color: #2E7D32;
  margin-right: 12rpx;
  margin-top: 2rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

/* 响应式适配 */
@media screen and (max-width: 400px) {
  .form-section {
    padding: 24rpx 20rpx;
  }
  
  .type-selector {
    gap: 12rpx;
  }
  
  .type-item {
    min-width: 100rpx;
    padding: 20rpx 16rpx;
  }
  
  .image-item,
  .image-add {
    width: 140rpx;
    height: 140rpx;
  }
}