<!--F2 Chart Component-->
<view class="f2-chart-container">
  <!-- 图表标题 -->
  <view class="chart-header" wx:if="{{chartConfig.title}}">
    <text class="chart-title">{{chartConfig.title}}</text>
    <view class="chart-actions" wx:if="{{!disabled}}">
      <button class="chart-action" bindtap="exportChart">
        <text class="iconfont icon-export"></text>
        <text>导出</text>
      </button>
    </view>
  </view>

  <!-- 图表画布 -->
  <view class="chart-wrapper" style="width: {{width}}px; height: {{height}}px;">
    <!-- Canvas 2D -->
    <canvas 
      type="2d" 
      id="{{canvasId}}" 
      class="chart-canvas"
      style="width: {{width}}px; height: {{height}}px;"
      bindtap="onChartTap"
      bindlongpress="onChartLongPress"
      wx:if="{{!disabled}}"
    ></canvas>
    
    <!-- 加载状态 -->
    <view class="chart-loading" wx:if="{{loading}}">
      <text class="iconfont icon-spin icon-refresh"></text>
      <text>加载中...</text>
    </view>
    
    <!-- 空数据状态 -->
    <view class="chart-empty" wx:if="{{!loading && (!chartData || chartData.length === 0)}}">
      <text class="iconfont icon-chart icon-xl icon-muted"></text>
      <text>暂无数据</text>
    </view>
    
    <!-- 禁用状态 -->
    <view class="chart-disabled" wx:if="{{disabled}}">
      <text class="iconfont icon-warning icon-xl icon-muted"></text>
      <text>图表已禁用</text>
    </view>
  </view>

  <!-- 图表说明 -->
  <view class="chart-description" wx:if="{{chartConfig.description}}">
    <text>{{chartConfig.description}}</text>
  </view>
</view>