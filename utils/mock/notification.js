/**
 * 通知相关Mock API
 */

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 验证Token
function verifyToken(token) {
  // 开发环境始终返回 mock 用户信息，跳过 token 校验
  return {
    userId: 'mock_user',
    username: 'testuser',
    exp: Math.floor(Date.now() / 1000) + 3600
  };
}

// 生成通知数据
function generateNotifications() {
  const notificationTypes = [
    { type: 'alarm', title: '系统预警', description: '土壤湿度过低，需要立即灌溉', icon: '⚠️' },
    { type: 'task', title: '任务提醒', description: '定时灌溉任务已完成', icon: '✅' },
    { type: 'system', title: '系统通知', description: '设备固件更新可用', icon: '🔧' },
    { type: 'alarm', title: '设备异常', description: '水泵设备连接中断', icon: '🚨' },
    { type: 'task', title: '维护提醒', description: '设备需要进行例行维护', icon: '🔧' },
    { type: 'system', title: '天气预报', description: '明天将有大雨，建议调整灌溉计划', icon: '🌧️' },
    { type: 'alarm', title: '肥料不足', description: '肥料A储量低于20%', icon: '⚠️' },
    { type: 'task', title: '任务完成', description: '施肥任务已成功完成', icon: '✅' }
  ];

  const notifications = [];
  for (let i = 0; i < notificationTypes.length; i++) {
    const notifType = notificationTypes[i];
    const time = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000); // 最近7天
    
    notifications.push({
      id: `notification_${String(i + 1).padStart(3, '0')}`,
      type: notifType.type,
      title: notifType.title,
      description: notifType.description,
      icon: notifType.icon,
      time: time.toISOString(),
      isRead: Math.random() > 0.4, // 60% 已读
      read: Math.random() > 0.4, // 兼容字段
      priority: notifType.type === 'alarm' ? 'high' : notifType.type === 'task' ? 'medium' : 'low',
      category: notifType.type === 'alarm' ? '预警' : notifType.type === 'task' ? '任务' : '系统',
      relatedId: notifType.type === 'alarm' ? `alarm_${String(i + 1).padStart(3, '0')}` : 
                 notifType.type === 'task' ? `task_${String(i + 1).padStart(3, '0')}` : null,
      actions: notifType.type === 'alarm' ? [
        { id: 'view', label: '查看详情', type: 'primary' },
        { id: 'ignore', label: '忽略', type: 'secondary' }
      ] : notifType.type === 'task' ? [
        { id: 'view', label: '查看任务', type: 'primary' }
      ] : [
        { id: 'view', label: '查看', type: 'primary' }
      ]
    });
  }

  return notifications.sort((a, b) => new Date(b.time) - new Date(a.time));
}

module.exports = {
  init() { 
    console.log('通知模块初始化完成'); 
  },
  
  async list(token, params = {}) {
    const payload = verifyToken(token);

    const { type, isRead, page = 1, pageSize = 20 } = params;
    
    let notifications = generateNotifications();
    
    // 类型过滤
    if (type && type !== 'all') {
      notifications = notifications.filter(notification => notification.type === type);
    }
    
    // 已读状态过滤
    if (isRead !== undefined) {
      notifications = notifications.filter(notification => notification.isRead === isRead);
    }
    
    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedNotifications = notifications.slice(startIndex, endIndex);
    
    return createApiResponse({
      notifications: paginatedNotifications,
      stats: {
        total: notifications.length,
        unread: notifications.filter(n => !n.isRead).length,
        alarm: notifications.filter(n => n.type === 'alarm').length,
        task: notifications.filter(n => n.type === 'task').length,
        system: notifications.filter(n => n.type === 'system').length
      },
      pagination: {
        page,
        pageSize,
        total: notifications.length,
        hasMore: endIndex < notifications.length
      }
    });
  },
  
  async markAsRead(token, params = {}) {
    const payload = verifyToken(token);

    const { notificationId } = params;
    
    if (!notificationId) {
      return createErrorResponse('缺少通知ID', 400);
    }

    return createApiResponse({
      notificationId: notificationId,
      isRead: true,
      readAt: new Date().toISOString(),
      readBy: payload.userId
    }, '通知已标记为已读');
  },
  
  async markAllAsRead(token) {
    const payload = verifyToken(token);

    const notifications = generateNotifications();
    const unreadCount = notifications.filter(n => !n.isRead).length;

    return createApiResponse({
      markedCount: unreadCount,
      readAt: new Date().toISOString(),
      readBy: payload.userId
    }, '所有通知已标记为已读');
  },
  
  async delete(token, params = {}) {
    const payload = verifyToken(token);

    const { notificationId } = params;
    
    if (!notificationId) {
      return createErrorResponse('缺少通知ID', 400);
    }

    return createApiResponse({
      notificationId: notificationId,
      deletedAt: new Date().toISOString(),
      deletedBy: payload.userId
    }, '通知已删除');
  },
  
  async getUnreadCount(token) {
    const payload = verifyToken(token);

    const notifications = generateNotifications();
    const unreadCount = notifications.filter(n => !n.isRead).length;

    return createApiResponse({
      unreadCount: unreadCount,
      totalCount: notifications.length,
      breakdown: {
        alarm: notifications.filter(n => n.type === 'alarm' && !n.isRead).length,
        task: notifications.filter(n => n.type === 'task' && !n.isRead).length,
        system: notifications.filter(n => n.type === 'system' && !n.isRead).length
      }
    });
  }
};