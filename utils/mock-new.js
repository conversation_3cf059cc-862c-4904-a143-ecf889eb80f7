/**
 * 智慧农业小程序 Mock 数据模块 - 完整版
 * 基于PRD文档重新设计，提供真实的API模拟数据
 */

// 辅助函数：生成下次运行时间
function getNextRunTime(time, days = [1, 2, 3, 4, 5, 6, 7]) {
  const now = new Date();
  const [hour, minute] = time.split(':').map(Number);
  
  let nextRun = new Date(now);
  nextRun.setHours(hour, minute, 0, 0);
  
  // 如果今天的时间已过，找下一个符合条件的日期
  if (nextRun <= now || !days.includes(now.getDay() || 7)) {
    do {
      nextRun.setDate(nextRun.getDate() + 1);
    } while (!days.includes(nextRun.getDay() || 7));
  }
  
  return nextRun.toISOString();
}

// 辅助函数：生成传感器历史数据
function generateSensorHistoryData(sensorId, hours = 24) {
  const data = [];
  const now = new Date();
  
  for (let i = hours; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
    
    // 根据传感器类型生成不同的数据
    const baseData = {
      timestamp: timestamp.toISOString(),
      sensorId
    };
    
    // 模拟不同传感器的数据变化
    if (sensorId === 'sensor_001') {
      data.push({
        ...baseData,
        moisture: 65 + Math.sin(i * 0.1) * 10 + Math.random() * 5,
        temperature: 24 + Math.sin(i * 0.2) * 3 + Math.random() * 2,
        ph: 6.8 + Math.sin(i * 0.05) * 0.3 + Math.random() * 0.1,
        ec: 1200 + Math.sin(i * 0.15) * 200 + Math.random() * 50,
        nitrogen: 80 + Math.sin(i * 0.08) * 15 + Math.random() * 10,
        phosphorus: 40 + Math.sin(i * 0.12) * 8 + Math.random() * 5,
        potassium: 170 + Math.sin(i * 0.18) * 25 + Math.random() * 15
      });
    } else if (sensorId === 'sensor_002') {
      data.push({
        ...baseData,
        moisture: 70 + Math.sin(i * 0.12) * 8 + Math.random() * 4,
        temperature: 25 + Math.sin(i * 0.18) * 2.5 + Math.random() * 1.5,
        ph: 6.9 + Math.sin(i * 0.06) * 0.2 + Math.random() * 0.08,
        ec: 1180 + Math.sin(i * 0.14) * 180 + Math.random() * 40
      });
    } else if (sensorId === 'sensor_003') {
      data.push({
        ...baseData,
        moisture: 55 + Math.sin(i * 0.14) * 12 + Math.random() * 6,
        temperature: 22 + Math.sin(i * 0.16) * 4 + Math.random() * 2.5
      });
    } else if (sensorId === 'sensor_004') {
      data.push({
        ...baseData,
        moisture: 35 + Math.sin(i * 0.10) * 15 + Math.random() * 8,
        temperature: 18 + Math.sin(i * 0.20) * 5 + Math.random() * 3
      });
    }
  }
  
  return data;
}

// 辅助函数：生成天气数据
function generateWeatherData() {
  const weathers = ['晴', '多云', '阴', '小雨', '中雨'];
  const icons = ['☀️', '⛅', '☁️', '🌦️', '🌧️'];
  const weatherIndex = Math.floor(Math.random() * weathers.length);
  
  return {
    temperature: 18 + Math.random() * 15, // 18-33度
    weather: weathers[weatherIndex],
    icon: icons[weatherIndex],
    humidity: 45 + Math.random() * 40, // 45-85%
    windSpeed: 2 + Math.random() * 12, // 2-14 m/s
    pressure: 1000 + Math.random() * 50, // 1000-1050 hPa
    uvIndex: Math.floor(Math.random() * 11), // 0-10
    visibility: 10 + Math.random() * 20, // 10-30 km
    forecast: [
      { date: '今天', weather: weathers[weatherIndex], temp: '18°/33°' },
      { date: '明天', weather: weathers[Math.floor(Math.random() * weathers.length)], temp: '16°/31°' },
      { date: '后天', weather: weathers[Math.floor(Math.random() * weathers.length)], temp: '20°/35°' }
    ]
  };
}

// Mock数据存储
let mockStorage = {
  // 用户数据
  users: [
    {
      id: 'user_001',
      openid: 'openid_123456',
      unionId: 'unionid_123456',
      nickname: '张三',
      avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4uBdhjwwdY1Q3L1JM8ibVN5iarJwzGtDc1Tg2W0s2f/132',
      phone: '13800138000',
      email: '<EMAIL>',
      realName: '张三',
      farmIds: ['farm_001'],
      role: 'farmer', // farmer, admin, technician
      permissions: ['device_control', 'data_view', 'schedule_manage'],
      status: 1,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    }
  ],
  
  // 农场数据
  farms: [
    {
      id: 'farm_001',
      ownerId: 'user_001',
      name: '张三的有机农场',
      location: '重庆市江北区北滨路100号',
      coordinates: {
        latitude: 29.5647,
        longitude: 106.5507
      },
      totalArea: 120.5,
      description: '专注有机蔬菜种植的现代化农场',
      status: 'active', // active, inactive, maintenance
      plotCount: 3,
      deviceCount: 6,
      sensorCount: 4,
      cropTypes: ['番茄', '生菜', '萝卜'],
      establishedDate: '2024-01-01',
      certification: '有机认证',
      contactPerson: '张三',
      contactPhone: '13800138000',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    }
  ],
  
  // 地块数据 - 采用简化模式，直接绑定设备
  plots: [
    {
      id: 'plot_001',
      farmId: 'farm_001',
      name: '1号大棚',
      area: 50.0,
      coordinates: {
        latitude: 29.5647,
        longitude: 106.5507
      },
      soilType: '壤土',
      drainageStatus: '良好',
      irrigationType: '滴灌',
      slopeLevel: '平地',
      status: 'active', // active, inactive, maintenance
      currentCrop: {
        type: '番茄',
        variety: '樱桃番茄',
        plantDate: '2024-01-10',
        growthStage: '开花期',
        progress: 75, // 生长进度 %
        expectedHarvestDate: '2024-04-10',
        plantingArea: 45.0, // 实际种植面积
        plantDensity: 4000 // 株/亩
      },
      // 直接绑定设备，无区域层级
      devices: ['device_001', 'sensor_001', 'sensor_002'],
      soilAnalysis: {
        ph: 6.8,
        organicMatter: 3.2, // %
        nitrogen: 1.8, // g/kg
        phosphorus: 0.8, // g/kg
        potassium: 2.1, // g/kg
        lastTestDate: '2024-01-01'
      },
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    },
    {
      id: 'plot_002',
      farmId: 'farm_001',
      name: '2号大棚',
      area: 45.0,
      coordinates: {
        latitude: 29.5650,
        longitude: 106.5510
      },
      soilType: '沙壤土',
      drainageStatus: '良好',
      irrigationType: '喷灌',
      slopeLevel: '缓坡',
      status: 'active',
      currentCrop: {
        type: '生菜',
        variety: '奶油生菜',
        plantDate: '2024-01-20',
        growthStage: '生长期',
        progress: 60,
        expectedHarvestDate: '2024-03-20',
        plantingArea: 42.0,
        plantDensity: 8000
      },
      devices: ['device_002', 'sensor_003'],
      soilAnalysis: {
        ph: 6.5,
        organicMatter: 2.8,
        nitrogen: 1.5,
        phosphorus: 0.6,
        potassium: 1.8,
        lastTestDate: '2024-01-05'
      },
      createdAt: '2024-01-05T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    },
    {
      id: 'plot_003',
      farmId: 'farm_001',
      name: '露天菜地',
      area: 25.5,
      coordinates: {
        latitude: 29.5645,
        longitude: 106.5505
      },
      soilType: '黏土',
      drainageStatus: '一般',
      irrigationType: '漫灌',
      slopeLevel: '平地',
      status: 'maintenance', // 维护中
      currentCrop: {
        type: '萝卜',
        variety: '白萝卜',
        plantDate: '2024-01-08',
        growthStage: '根部发育期',
        progress: 40,
        expectedHarvestDate: '2024-04-08',
        plantingArea: 22.0,
        plantDensity: 5000
      },
      devices: ['device_003', 'sensor_004'],
      soilAnalysis: {
        ph: 7.2,
        organicMatter: 3.8,
        nitrogen: 2.1,
        phosphorus: 1.0,
        potassium: 2.5,
        lastTestDate: '2024-01-03'
      },
      createdAt: '2024-01-03T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    }
  ],
  
  // 设备数据 - 水肥一体机
  devices: [
    {
      id: 'device_001',
      serialNumber: 'WF001-2024-001',
      type: 'irrigation',
      subType: 'water_fertilizer_integrated',
      name: '1号水肥一体机',
      model: 'WF-PRO-2000',
      manufacturer: '智慧农业科技有限公司',
      version: 'v2.1.0',
      farmId: 'farm_001',
      plotId: 'plot_001',
      installLocation: '1号大棚东侧',
      installDate: '2024-01-01',
      warrantyExpiry: '2027-01-01',
      status: {
        online: true,
        working: false,
        lastSeen: new Date().toISOString(),
        batteryLevel: 85,
        signalStrength: -45, // dBm
        temperature: 28.5, // 设备温度
        humidity: 65 // 设备湿度
      },
      capabilities: {
        maxZones: 4,
        valveTypes: ['solenoid', 'motorized'],
        flowRateRange: [10, 200], // L/min
        pressureRange: [1.0, 5.0], // bar
        fertilizerTanks: 3,
        supportedProtocols: ['mqtt', 'http', 'modbus'],
        supportedCrops: ['番茄', '黄瓜', '辣椒', '茄子'],
        automation: true,
        remoteControl: true,
        dataLogging: true
      },
      config: {
        valves: [
          { id: 'VALVE_1', name: 'A区域阀门', status: 'closed', flowRate: 0, maxFlowRate: 80 },
          { id: 'VALVE_2', name: 'B区域阀门', status: 'closed', flowRate: 0, maxFlowRate: 80 },
          { id: 'VALVE_3', name: '备用阀门3', status: 'closed', flowRate: 0, maxFlowRate: 60 },
          { id: 'VALVE_4', name: '备用阀门4', status: 'closed', flowRate: 0, maxFlowRate: 60 }
        ],
        fertilizer: {
          tank1: { type: 'N', name: '氮肥', concentration: 20, level: 75, capacity: 100 },
          tank2: { type: 'P', name: '磷肥', concentration: 10, level: 60, capacity: 100 },
          tank3: { type: 'K', name: '钾肥', concentration: 15, level: 80, capacity: 100 }
        },
        pump: {
          maxPressure: 5.0,
          currentPressure: 0,
          status: 'stopped',
          totalRuntime: 245.5 // 小时
        }
      },
      currentTask: null,
      totalRuntime: 1200, // 小时
      totalWaterUsage: 15000, // 升
      totalFertilizerUsage: 450, // 升
      maintenanceRecord: {
        lastMaintenance: '2024-01-01T00:00:00.000Z',
        nextMaintenance: '2024-04-01T00:00:00.000Z',
        maintenanceInterval: 90 // 天
      },
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'device_002',
      serialNumber: 'WF002-2024-002',
      type: 'irrigation',
      subType: 'water_fertilizer_integrated',
      name: '2号水肥一体机',
      model: 'WF-STANDARD-1500',
      manufacturer: '智慧农业科技有限公司',
      version: 'v2.0.3',
      farmId: 'farm_001',
      plotId: 'plot_002',
      installLocation: '2号大棚中央',
      installDate: '2024-01-02',
      warrantyExpiry: '2027-01-02',
      status: {
        online: true,
        working: true,
        lastSeen: new Date().toISOString(),
        batteryLevel: 92,
        signalStrength: -38,
        temperature: 26.8,
        humidity: 58
      },
      capabilities: {
        maxZones: 2,
        valveTypes: ['solenoid'],
        flowRateRange: [20, 150],
        pressureRange: [1.5, 4.0],
        fertilizerTanks: 2,
        supportedProtocols: ['mqtt', 'http'],
        supportedCrops: ['生菜', '菠菜', '小白菜'],
        automation: true,
        remoteControl: true,
        dataLogging: true
      },
      config: {
        valves: [
          { id: 'VALVE_1', name: '主阀门', status: 'open', flowRate: 80, maxFlowRate: 120 },
          { id: 'VALVE_2', name: '副阀门', status: 'closed', flowRate: 0, maxFlowRate: 100 }
        ],
        fertilizer: {
          tank1: { type: 'NPK', name: '复合肥', concentration: 25, level: 45, capacity: 80 },
          tank2: { type: 'Micro', name: '微量元素', concentration: 5, level: 70, capacity: 50 }
        },
        pump: {
          maxPressure: 4.0,
          currentPressure: 2.8,
          status: 'running',
          totalRuntime: 186.2
        }
      },
      currentTask: {
        taskId: 'task_001',
        type: 'irrigation',
        startTime: new Date(Date.now() - 1800000).toISOString(), // 30分钟前开始
        estimatedEndTime: new Date(Date.now() + 900000).toISOString(), // 15分钟后结束
        parameters: {
          duration: 2700, // 45分钟
          flowRate: 80,
          zones: ['VALVE_1'],
          fertilizer: {
            enabled: false
          }
        }
      },
      totalRuntime: 890,
      totalWaterUsage: 8500,
      totalFertilizerUsage: 280,
      maintenanceRecord: {
        lastMaintenance: '2024-01-02T00:00:00.000Z',
        nextMaintenance: '2024-04-02T00:00:00.000Z',
        maintenanceInterval: 90
      },
      createdAt: '2024-01-02T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'device_003',
      serialNumber: 'WF003-2024-003',
      type: 'irrigation',
      subType: 'simple_irrigation',
      name: '3号灌溉设备',
      model: 'WF-BASIC-800',
      manufacturer: '智慧农业科技有限公司',
      version: 'v1.5.2',
      farmId: 'farm_001',
      plotId: 'plot_003',
      installLocation: '露天菜地西侧',
      installDate: '2024-01-03',
      warrantyExpiry: '2027-01-03',
      status: {
        online: false, // 离线状态
        working: false,
        lastSeen: new Date(Date.now() - 1800000).toISOString(), // 30分钟前离线
        batteryLevel: 15,
        signalStrength: -72,
        temperature: 22.1,
        humidity: 45
      },
      capabilities: {
        maxZones: 1,
        valveTypes: ['solenoid'],
        flowRateRange: [30, 120],
        pressureRange: [2.0, 6.0],
        fertilizerTanks: 1,
        supportedProtocols: ['http'],
        supportedCrops: ['萝卜', '胡萝卜', '白菜'],
        automation: false,
        remoteControl: true,
        dataLogging: false
      },
      config: {
        valves: [
          { id: 'VALVE_1', name: '主阀门', status: 'closed', flowRate: 0, maxFlowRate: 100 }
        ],
        fertilizer: {
          tank1: { type: 'Compound', name: '复合肥', concentration: 15, level: 25, capacity: 60 }
        },
        pump: {
          maxPressure: 6.0,
          currentPressure: 0,
          status: 'stopped',
          totalRuntime: 156.8
        }
      },
      currentTask: null,
      totalRuntime: 350,
      totalWaterUsage: 4200,
      totalFertilizerUsage: 120,
      maintenanceRecord: {
        lastMaintenance: '2024-01-03T00:00:00.000Z',
        nextMaintenance: '2024-04-03T00:00:00.000Z',
        maintenanceInterval: 90
      },
      createdAt: '2024-01-03T00:00:00.000Z',
      updatedAt: new Date(Date.now() - 1800000).toISOString()
    }
  ],
  
  // 传感器数据 - 扩展完整的传感器配置
  sensors: [
    {
      id: 'sensor_001',
      serialNumber: 'SM001-2024-001',
      type: 'soil',
      subType: 'multi_parameter',
      name: '1号土壤传感器',
      model: 'SM-PRO-2000',
      manufacturer: '农业传感器有限公司',
      version: 'v3.1.2',
      farmId: 'farm_001',
      plotId: 'plot_001',
      installLocation: '1号大棚A区域',
      installDepth: 30, // cm
      installDate: '2024-01-01',
      warrantyExpiry: '2027-01-01',
      status: {
        online: true,
        batteryLevel: 78,
        signalStrength: -42,
        lastSeen: new Date().toISOString(),
        calibrationDate: '2024-01-01T00:00:00.000Z',
        temperature: 35.2 // 传感器温度
      },
      capabilities: {
        parameters: ['moisture', 'temperature', 'ph', 'ec', 'npk'],
        measurementRange: {
          moisture: [0, 100], // %
          temperature: [-20, 80], // °C
          ph: [0, 14],
          ec: [0, 5000], // μS/cm
          nitrogen: [0, 1000], // mg/kg
          phosphorus: [0, 500], // mg/kg
          potassium: [0, 2000] // mg/kg
        },
        accuracy: {
          moisture: 2, // %
          temperature: 0.5, // °C
          ph: 0.1,
          ec: 50, // μS/cm
          nitrogen: 10, // mg/kg
          phosphorus: 5, // mg/kg
          potassium: 20 // mg/kg
        },
        dataInterval: 300, // 秒
        batteryLife: 365 // 天
      },
      currentReading: {
        timestamp: new Date().toISOString(),
        moisture: 68.5, // %
        temperature: 24.2, // °C
        ph: 6.8,
        ec: 1250, // μS/cm
        nitrogen: 85, // mg/kg
        phosphorus: 45, // mg/kg
        potassium: 180 // mg/kg
      },
      thresholds: {
        moisture: { min: 40, max: 80, optimal: [60, 70] },
        temperature: { min: 15, max: 35, optimal: [20, 30] },
        ph: { min: 6.0, max: 7.5, optimal: [6.5, 7.0] },
        ec: { min: 800, max: 2000, optimal: [1000, 1500] },
        nitrogen: { min: 50, max: 200, optimal: [80, 120] },
        phosphorus: { min: 20, max: 100, optimal: [40, 60] },
        potassium: { min: 100, max: 300, optimal: [150, 250] }
      },
      alerts: {
        enabled: true,
        checkInterval: 600, // 秒
        notificationTypes: ['wechat', 'sms', 'push']
      },
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'sensor_002',
      serialNumber: 'SM002-2024-002',
      type: 'soil',
      subType: 'standard',
      name: '2号土壤传感器',
      model: 'SM-STANDARD-1500',
      manufacturer: '农业传感器有限公司',
      version: 'v2.8.1',
      farmId: 'farm_001',
      plotId: 'plot_001',
      installLocation: '1号大棚B区域',
      installDepth: 25, // cm
      installDate: '2024-01-01',
      warrantyExpiry: '2027-01-01',
      status: {
        online: true,
        batteryLevel: 82,
        signalStrength: -45,
        lastSeen: new Date().toISOString(),
        calibrationDate: '2024-01-01T00:00:00.000Z',
        temperature: 33.8
      },
      capabilities: {
        parameters: ['moisture', 'temperature', 'ph', 'ec'],
        measurementRange: {
          moisture: [0, 100],
          temperature: [-20, 80],
          ph: [0, 14],
          ec: [0, 5000]
        },
        accuracy: {
          moisture: 2,
          temperature: 0.5,
          ph: 0.1,
          ec: 50
        },
        dataInterval: 300,
        batteryLife: 365
      },
      currentReading: {
        timestamp: new Date().toISOString(),
        moisture: 72.3,
        temperature: 25.1,
        ph: 6.9,
        ec: 1180
      },
      thresholds: {
        moisture: { min: 40, max: 80, optimal: [60, 70] },
        temperature: { min: 15, max: 35, optimal: [20, 30] },
        ph: { min: 6.0, max: 7.5, optimal: [6.5, 7.0] },
        ec: { min: 800, max: 2000, optimal: [1000, 1500] }
      },
      alerts: {
        enabled: true,
        checkInterval: 600,
        notificationTypes: ['wechat', 'push']
      },
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'sensor_003',
      serialNumber: 'SM003-2024-003',
      type: 'soil',
      subType: 'basic',
      name: '3号土壤传感器',
      model: 'SM-BASIC-1000',
      manufacturer: '农业传感器有限公司',
      version: 'v1.9.5',
      farmId: 'farm_001',
      plotId: 'plot_002',
      installLocation: '2号大棚中央',
      installDepth: 20, // cm
      installDate: '2024-01-05',
      warrantyExpiry: '2027-01-05',
      status: {
        online: true,
        batteryLevel: 65,
        signalStrength: -50,
        lastSeen: new Date().toISOString(),
        calibrationDate: '2024-01-05T00:00:00.000Z',
        temperature: 31.5
      },
      capabilities: {
        parameters: ['moisture', 'temperature'],
        measurementRange: {
          moisture: [0, 100],
          temperature: [-10, 60]
        },
        accuracy: {
          moisture: 3,
          temperature: 1.0
        },
        dataInterval: 600,
        batteryLife: 180
      },
      currentReading: {
        timestamp: new Date().toISOString(),
        moisture: 58.7,
        temperature: 22.8
      },
      thresholds: {
        moisture: { min: 30, max: 75, optimal: [50, 65] },
        temperature: { min: 10, max: 40, optimal: [18, 32] }
      },
      alerts: {
        enabled: true,
        checkInterval: 1200,
        notificationTypes: ['wechat']
      },
      createdAt: '2024-01-05T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'sensor_004',
      serialNumber: 'SM004-2024-004',
      type: 'soil',
      subType: 'basic',
      name: '4号土壤传感器',
      model: 'SM-BASIC-1000',
      manufacturer: '农业传感器有限公司',
      version: 'v1.9.5',
      farmId: 'farm_001',
      plotId: 'plot_003',
      installLocation: '露天菜地中央',
      installDepth: 35, // cm
      installDate: '2024-01-03',
      warrantyExpiry: '2027-01-03',
      status: {
        online: false, // 离线状态
        batteryLevel: 8,
        signalStrength: -80,
        lastSeen: new Date(Date.now() - 3600000).toISOString(), // 1小时前
        calibrationDate: '2024-01-03T00:00:00.000Z',
        temperature: 28.2
      },
      capabilities: {
        parameters: ['moisture', 'temperature'],
        measurementRange: {
          moisture: [0, 100],
          temperature: [-10, 60]
        },
        accuracy: {
          moisture: 3,
          temperature: 1.0
        },
        dataInterval: 600,
        batteryLife: 180
      },
      currentReading: {
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        moisture: 35.2,
        temperature: 18.5
      },
      thresholds: {
        moisture: { min: 25, max: 70, optimal: [40, 60] },
        temperature: { min: 5, max: 45, optimal: [15, 35] }
      },
      alerts: {
        enabled: true,
        checkInterval: 1200,
        notificationTypes: ['wechat']
      },
      createdAt: '2024-01-03T00:00:00.000Z',
      updatedAt: new Date(Date.now() - 3600000).toISOString()
    }
  ],
  
  // 历史数据 - 用于图表展示
  sensorHistoryData: {
    'sensor_001': generateSensorHistoryData('sensor_001'),
    'sensor_002': generateSensorHistoryData('sensor_002'),
    'sensor_003': generateSensorHistoryData('sensor_003'),
    'sensor_004': generateSensorHistoryData('sensor_004')
  },
  
  // 预警配置和历史记录
  alarms: [
    {
      id: 'alarm_001',
      type: 'sensor',
      level: 'warning', // info, warning, critical
      title: '土壤湿度偏低',
      description: '4号传感器土壤湿度为35.2%，低于设定阈值40%',
      sensorId: 'sensor_004',
      deviceId: null,
      plotId: 'plot_003',
      farmId: 'farm_001',
      triggerValue: 35.2,
      thresholdValue: 40,
      parameter: 'moisture',
      unit: '%',
      status: 'active', // active, acknowledged, resolved
      priority: 'medium',
      category: 'environmental',
      impact: 'crop_growth',
      recommendation: '建议立即进行灌溉，确保土壤湿度保持在40-60%之间',
      createdAt: new Date(Date.now() - 900000).toISOString(), // 15分钟前
      acknowledgedAt: null,
      resolvedAt: null,
      acknowledgedBy: null,
      resolvedBy: null,
      solution: null,
      autoResolve: false,
      tags: ['irrigation', 'soil', 'urgent']
    },
    {
      id: 'alarm_002',
      type: 'device',
      level: 'critical',
      title: '设备离线',
      description: '3号灌溉设备已离线超过30分钟，可能存在网络或电源问题',
      sensorId: null,
      deviceId: 'device_003',
      plotId: 'plot_003',
      farmId: 'farm_001',
      triggerValue: null,
      thresholdValue: null,
      parameter: 'status',
      unit: null,
      status: 'active',
      priority: 'high',
      category: 'equipment',
      impact: 'irrigation_capability',
      recommendation: '请检查设备电源和网络连接，必要时联系技术支持',
      createdAt: new Date(Date.now() - 1800000).toISOString(), // 30分钟前
      acknowledgedAt: null,
      resolvedAt: null,
      acknowledgedBy: null,
      resolvedBy: null,
      solution: null,
      autoResolve: false,
      tags: ['device', 'offline', 'critical']
    },
    {
      id: 'alarm_003',
      type: 'sensor',
      level: 'warning',
      title: 'pH值异常',
      description: '1号传感器pH值为7.8，超出正常范围6.0-7.5',
      sensorId: 'sensor_001',
      deviceId: null,
      plotId: 'plot_001',
      farmId: 'farm_001',
      triggerValue: 7.8,
      thresholdValue: 7.5,
      parameter: 'ph',
      unit: '',
      status: 'resolved',
      priority: 'medium',
      category: 'environmental',
      impact: 'nutrient_absorption',
      recommendation: '调整施肥配方，添加酸性肥料降低pH值',
      createdAt: new Date(Date.now() - 7200000).toISOString(), // 2小时前
      acknowledgedAt: new Date(Date.now() - 6600000).toISOString(), // 1小时50分钟前
      resolvedAt: new Date(Date.now() - 3600000).toISOString(), // 1小时前
      acknowledgedBy: 'user_001',
      resolvedBy: 'user_001',
      solution: '已调整施肥配方，降低pH值至正常范围',
      autoResolve: false,
      tags: ['ph', 'soil', 'resolved']
    },
    {
      id: 'alarm_004',
      type: 'system',
      level: 'info',
      title: '定时任务完成',
      description: '晨间灌溉计划执行完成，用水量2.35m³',
      sensorId: null,
      deviceId: 'device_001',
      plotId: 'plot_001',
      farmId: 'farm_001',
      triggerValue: null,
      thresholdValue: null,
      parameter: 'task_completion',
      unit: null,
      status: 'resolved',
      priority: 'low',
      category: 'operational',
      impact: 'none',
      recommendation: '任务正常完成，无需额外操作',
      createdAt: new Date(Date.now() - 86400000 + 23400000).toISOString(), // 昨天早上6点30分
      acknowledgedAt: new Date(Date.now() - 86400000 + 23400000).toISOString(),
      resolvedAt: new Date(Date.now() - 86400000 + 23400000).toISOString(),
      acknowledgedBy: 'system',
      resolvedBy: 'system',
      solution: '任务自动完成',
      autoResolve: true,
      tags: ['task', 'irrigation', 'completed']
    }
  ],
  
  // 定时任务/调度计划
  schedules: [
    {
      id: 'schedule_001',
      name: '晨间灌溉计划',
      type: 'irrigation',
      farmId: 'farm_001',
      plotId: 'plot_001',
      deviceId: 'device_001',
      enabled: true,
      template: 'morning_irrigation',
      schedule: {
        type: 'daily', // daily, weekly, monthly, custom
        time: '06:00',
        duration: 1800, // 30分钟
        repeatDays: [1, 2, 3, 4, 5, 6, 7], // 每天
        timeZone: 'Asia/Shanghai',
        parameters: {
          flowRate: 80, // L/min
          zones: ['VALVE_1', 'VALVE_2'],
          totalVolume: 2400, // L
          fertilizer: {
            enabled: true,
            formula: 'tomato_growth',
            concentration: 15,
            duration: 1200 // 20分钟
          },
          conditions: {
            skipIfRaining: true,
            skipIfSoilMoist: true,
            soilMoistureThreshold: 75 // %
          }
        }
      },
      execution: {
        nextRun: getNextRunTime('06:00'),
        lastRun: new Date(Date.now() - 86400000 + 21600000).toISOString(), // 昨天早上6点
        lastResult: 'success',
        totalExecutions: 45,
        successfulExecutions: 44,
        failedExecutions: 1,
        averageDuration: 1785, // 秒
        totalWaterUsage: 105000, // L
        totalFertilizerUsage: 1800 // L
      },
      createdBy: 'user_001',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'schedule_002',
      name: '傍晚灌溉计划',
      type: 'irrigation',
      farmId: 'farm_001',
      plotId: 'plot_001',
      deviceId: 'device_001',
      enabled: true,
      template: 'evening_irrigation',
      schedule: {
        type: 'daily',
        time: '18:00',
        duration: 2400, // 40分钟
        repeatDays: [1, 2, 3, 4, 5, 6, 7],
        timeZone: 'Asia/Shanghai',
        parameters: {
          flowRate: 60,
          zones: ['VALVE_1', 'VALVE_2'],
          totalVolume: 2400,
          fertilizer: {
            enabled: false
          },
          conditions: {
            skipIfRaining: true,
            skipIfSoilMoist: false,
            soilMoistureThreshold: 80
          }
        }
      },
      execution: {
        nextRun: getNextRunTime('18:00'),
        lastRun: new Date(Date.now() - 86400000 + 64800000).toISOString(), // 昨天傍晚6点
        lastResult: 'success',
        totalExecutions: 45,
        successfulExecutions: 43,
        failedExecutions: 2,
        averageDuration: 2380,
        totalWaterUsage: 108000,
        totalFertilizerUsage: 0
      },
      createdBy: 'user_001',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'schedule_003',
      name: '周末施肥计划',
      type: 'fertilization',
      farmId: 'farm_001',
      plotId: 'plot_002',
      deviceId: 'device_002',
      enabled: true,
      template: 'weekend_fertilization',
      schedule: {
        type: 'weekly',
        time: '08:00',
        duration: 3600, // 1小时
        repeatDays: [6, 7], // 周六周日
        timeZone: 'Asia/Shanghai',
        parameters: {
          flowRate: 40,
          zones: ['VALVE_1'],
          totalVolume: 2400,
          fertilizer: {
            enabled: true,
            formula: 'lettuce_boost',
            concentration: 20,
            duration: 3600
          },
          conditions: {
            skipIfRaining: false,
            skipIfSoilMoist: false
          }
        }
      },
      execution: {
        nextRun: getNextRunTime('08:00', [6, 7]),
        lastRun: new Date(Date.now() - 86400000 * 7 + 28800000).toISOString(), // 上周六早上8点
        lastResult: 'success',
        totalExecutions: 12,
        successfulExecutions: 11,
        failedExecutions: 1,
        averageDuration: 3580,
        totalWaterUsage: 28800,
        totalFertilizerUsage: 2400
      },
      createdBy: 'user_001',
      createdAt: '2024-01-05T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    }
  ],
  
  // 操作日志
  operationLogs: [
    {
      id: 'log_001',
      type: 'manual_irrigation', // manual_irrigation, auto_irrigation, fertilization, system_operation
      action: 'start_irrigation',
      description: '手动启动1号大棚灌溉',
      farmId: 'farm_001',
      plotId: 'plot_001',
      deviceId: 'device_001',
      scheduleId: null,
      taskId: null,
      operatorId: 'user_001',
      operatorName: '张三',
      operatorType: 'manual', // manual, auto, scheduled
      status: 'success', // success, failed, pending, cancelled
      startTime: new Date(Date.now() - 1800000).toISOString(), // 30分钟前开始
      endTime: new Date(Date.now() - 900000).toISOString(), // 15分钟前结束
      duration: 900, // 15分钟
      parameters: {
        flowRate: 80,
        zones: ['VALVE_1'],
        targetVolume: 1200, // L
        fertilizer: {
          enabled: false
        },
        conditions: {
          soilMoisture: 45.2,
          temperature: 24.5,
          weather: '晴'
        }
      },
      results: {
        actualDuration: 900,
        actualVolume: 1180,
        energyConsumption: 0.5, // kWh
        waterEfficiency: 98.3, // %
        success: true,
        errorMessage: null,
        qualityScore: 9.2, // 1-10分
        environmentalImpact: 'low',
        costEstimate: 2.35 // 元
      },
      metrics: {
        avgFlowRate: 78.5,
        maxPressure: 3.2,
        minPressure: 2.8,
        temperatureRange: [24.2, 24.8],
        humidityChange: 5.2
      },
      createdAt: new Date(Date.now() - 1800000).toISOString()
    },
    {
      id: 'log_002',
      type: 'auto_irrigation',
      action: 'scheduled_irrigation',
      description: '定时任务：晨间灌溉计划',
      farmId: 'farm_001',
      plotId: 'plot_001',
      deviceId: 'device_001',
      scheduleId: 'schedule_001',
      taskId: 'task_auto_001',
      operatorId: 'system',
      operatorName: '系统',
      operatorType: 'scheduled',
      status: 'success',
      startTime: new Date(Date.now() - 86400000 + 21600000).toISOString(), // 昨天早上6点
      endTime: new Date(Date.now() - 86400000 + 23400000).toISOString(), // 昨天早上6点30分
      duration: 1800, // 30分钟
      parameters: {
        flowRate: 80,
        zones: ['VALVE_1', 'VALVE_2'],
        targetVolume: 2400,
        fertilizer: {
          enabled: true,
          formula: 'tomato_growth',
          concentration: 15,
          volume: 36 // L
        },
        conditions: {
          soilMoisture: 42.8,
          temperature: 18.5,
          weather: '晴'
        }
      },
      results: {
        actualDuration: 1800,
        actualVolume: 2350,
        energyConsumption: 1.2,
        waterEfficiency: 97.9,
        success: true,
        errorMessage: null,
        qualityScore: 9.5,
        environmentalImpact: 'low',
        costEstimate: 5.88
      },
      metrics: {
        avgFlowRate: 78.9,
        maxPressure: 3.5,
        minPressure: 3.0,
        temperatureRange: [18.2, 19.1],
        humidityChange: 8.5
      },
      createdAt: new Date(Date.now() - 86400000 + 21600000).toISOString()
    },
    {
      id: 'log_003',
      type: 'fertilization',
      action: 'manual_fertilization',
      description: '手动施肥操作',
      farmId: 'farm_001',
      plotId: 'plot_002',
      deviceId: 'device_002',
      scheduleId: null,
      taskId: null,
      operatorId: 'user_001',
      operatorName: '张三',
      operatorType: 'manual',
      status: 'failed',
      startTime: new Date(Date.now() - 3600000).toISOString(), // 1小时前
      endTime: new Date(Date.now() - 3300000).toISOString(), // 55分钟前
      duration: 300, // 5分钟（提前终止）
      parameters: {
        flowRate: 40,
        zones: ['VALVE_1'],
        targetVolume: 600,
        fertilizer: {
          enabled: true,
          formula: 'lettuce_boost',
          concentration: 20,
          volume: 12
        },
        conditions: {
          soilMoisture: 52.1,
          temperature: 22.8,
          weather: '多云'
        }
      },
      results: {
        actualDuration: 300,
        actualVolume: 200,
        energyConsumption: 0.2,
        waterEfficiency: 33.3,
        success: false,
        errorMessage: '肥料罐液位过低，操作中止',
        qualityScore: 3.2,
        environmentalImpact: 'medium',
        costEstimate: 0.85
      },
      metrics: {
        avgFlowRate: 38.5,
        maxPressure: 2.8,
        minPressure: 2.2,
        temperatureRange: [22.5, 23.1],
        humidityChange: 1.8
      },
      createdAt: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: 'log_004',
      type: 'system_operation',
      action: 'device_maintenance',
      description: '设备维护检查',
      farmId: 'farm_001',
      plotId: 'plot_003',
      deviceId: 'device_003',
      scheduleId: null,
      taskId: null,
      operatorId: 'user_001',
      operatorName: '张三',
      operatorType: 'manual',
      status: 'success',
      startTime: new Date(Date.now() - 7200000).toISOString(), // 2小时前
      endTime: new Date(Date.now() - 6600000).toISOString(), // 1小时50分钟前
      duration: 600, // 10分钟
      parameters: {
        maintenanceType: 'routine_check',
        items: ['valve_check', 'pump_check', 'sensor_calibration', 'battery_check'],
        checklist: {
          valve_check: 'completed',
          pump_check: 'completed',
          sensor_calibration: 'completed',
          battery_check: 'needs_replacement'
        }
      },
      results: {
        actualDuration: 600,
        success: true,
        findings: [
          '阀门密封轻微磨损，建议更换',
          '水泵运行正常',
          '传感器校准完成',
          '电池电量低，需要更换'
        ],
        nextMaintenanceDate: new Date(Date.now() + 2592000000).toISOString(), // 30天后
        priority: 'medium',
        estimatedCost: 150.0,
        partsNeeded: ['valve_seal', 'battery_pack']
      },
      createdAt: new Date(Date.now() - 7200000).toISOString()
    }
  ],
  
  // 施肥配方
  fertilizerFormulas: [
    {
      id: 'formula_001',
      name: 'tomato_growth',
      displayName: '番茄生长期配方',
      cropType: '番茄',
      stage: '生长期',
      description: '适用于番茄生长期的营养配方，促进植株健康生长',
      category: 'growth',
      composition: {
        N: 20, // 氮 %
        P: 10, // 磷 %
        K: 15, // 钾 %
        Ca: 5, // 钙 %
        Mg: 3, // 镁 %
        S: 2, // 硫 %
        microElements: {
          Fe: 0.5, // 铁 %
          Mn: 0.2, // 锰 %
          Zn: 0.1, // 锌 %
          Cu: 0.05, // 铜 %
          B: 0.02, // 硼 %
          Mo: 0.001 // 钼 %
        }
      },
      concentrationRange: [10, 25], // 推荐浓度范围
      defaultConcentration: 15,
      phRange: [6.0, 7.0],
      ecRange: [1.2, 1.8],
      applicationRate: 2.5, // L/m²
      frequency: 'weekly', // daily, weekly, biweekly, monthly
      timing: ['morning', 'evening'],
      weatherConditions: ['sunny', 'cloudy'],
      soilRequirements: {
        ph: [6.0, 7.5],
        ec: [800, 2000],
        moisture: [60, 80]
      },
      cost: {
        pricePerLiter: 2.5,
        currency: 'CNY',
        supplier: '农业科技有限公司'
      },
      isActive: true,
      createdBy: 'user_001',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    },
    {
      id: 'formula_002',
      name: 'lettuce_boost',
      displayName: '生菜增长配方',
      cropType: '生菜',
      stage: '生长期',
      description: '促进生菜叶片生长的高氮配方，提高产量和品质',
      category: 'boost',
      composition: {
        N: 25,
        P: 8,
        K: 12,
        Ca: 6,
        Mg: 4,
        S: 3,
        microElements: {
          Fe: 0.8,
          Mn: 0.3,
          Zn: 0.15,
          Cu: 0.08,
          B: 0.03,
          Mo: 0.002
        }
      },
      concentrationRange: [15, 30],
      defaultConcentration: 20,
      phRange: [6.2, 7.2],
      ecRange: [1.0, 1.5],
      applicationRate: 3.0,
      frequency: 'twice_weekly',
      timing: ['morning'],
      weatherConditions: ['sunny', 'cloudy'],
      soilRequirements: {
        ph: [6.0, 7.5],
        ec: [600, 1800],
        moisture: [50, 75]
      },
      cost: {
        pricePerLiter: 3.2,
        currency: 'CNY',
        supplier: '农业科技有限公司'
      },
      isActive: true,
      createdBy: 'user_001',
      createdAt: '2024-01-05T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    },
    {
      id: 'formula_003',
      name: 'radish_root',
      displayName: '萝卜根部发育配方',
      cropType: '萝卜',
      stage: '根部发育期',
      description: '促进萝卜根部发育的高钾配方，提高根部质量',
      category: 'root_development',
      composition: {
        N: 15,
        P: 12,
        K: 25,
        Ca: 8,
        Mg: 5,
        S: 4,
        microElements: {
          Fe: 0.6,
          Mn: 0.4,
          Zn: 0.2,
          Cu: 0.06,
          B: 0.04,
          Mo: 0.003
        }
      },
      concentrationRange: [12, 22],
      defaultConcentration: 18,
      phRange: [6.5, 7.5],
      ecRange: [1.3, 1.9],
      applicationRate: 2.0,
      frequency: 'weekly',
      timing: ['morning', 'evening'],
      weatherConditions: ['sunny', 'cloudy'],
      soilRequirements: {
        ph: [6.0, 8.0],
        ec: [1000, 2500],
        moisture: [40, 70]
      },
      cost: {
        pricePerLiter: 2.8,
        currency: 'CNY',
        supplier: '农业科技有限公司'
      },
      isActive: true,
      createdBy: 'user_001',
      createdAt: '2024-01-08T00:00:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z'
    }
  ],
  
  // 通知消息
  notifications: [
    {
      id: 'notification_001',
      type: 'alarm', // alarm, task, system, maintenance
      title: '土壤湿度预警',
      message: '露天菜地土壤湿度偏低，请及时灌溉',
      content: '4号传感器检测到土壤湿度为35.2%，低于设定阈值40%。建议立即进行灌溉操作，确保萝卜根部正常发育。',
      level: 'warning', // info, warning, critical
      farmId: 'farm_001',
      plotId: 'plot_003',
      deviceId: 'sensor_004',
      relatedId: 'alarm_001', // 关联的预警/任务/设备ID
      isRead: false,
      priority: 'high',
      category: 'environmental',
      actions: [
        { type: 'irrigation', label: '立即灌溉', url: '/pages/device/device?action=irrigation&device=device_003' },
        { type: 'view_details', label: '查看详情', url: '/pages/monitor/alarm/alarm?id=alarm_001' }
      ],
      createdAt: new Date(Date.now() - 900000).toISOString(), // 15分钟前
      readAt: null,
      expiresAt: new Date(Date.now() + 86400000).toISOString() // 24小时后过期
    },
    {
      id: 'notification_002',
      type: 'task',
      title: '定时任务完成',
      message: '晨间灌溉计划执行完成，用水量2.35m³',
      content: '1号大棚的晨间灌溉计划已成功执行完成。灌溉时长30分钟，实际用水量2.35m³，施肥量36L，运行效率97.9%。',
      level: 'info',
      farmId: 'farm_001',
      plotId: 'plot_001',
      deviceId: 'device_001',
      relatedId: 'log_002',
      isRead: true,
      priority: 'low',
      category: 'operational',
      actions: [
        { type: 'view_log', label: '查看日志', url: '/pages/device/logs/logs?id=log_002' }
      ],
      createdAt: new Date(Date.now() - 86400000 + 23400000).toISOString(),
      readAt: new Date(Date.now() - 86400000 + 25200000).toISOString(),
      expiresAt: new Date(Date.now() + 86400000 * 7).toISOString() // 7天后过期
    },
    {
      id: 'notification_003',
      type: 'system',
      title: '设备离线',
      message: '3号灌溉设备已离线，请检查设备状态',
      content: '3号灌溉设备自30分钟前开始离线，可能原因包括网络连接中断、电源故障或设备硬件问题。请尽快检查设备状态并联系技术支持。',
      level: 'critical',
      farmId: 'farm_001',
      plotId: 'plot_003',
      deviceId: 'device_003',
      relatedId: 'alarm_002',
      isRead: false,
      priority: 'urgent',
      category: 'equipment',
      actions: [
        { type: 'device_check', label: '检查设备', url: '/pages/device/device?id=device_003' },
        { type: 'maintenance', label: '申请维护', url: '/pages/device/maintenance?device=device_003' }
      ],
      createdAt: new Date(Date.now() - 1800000).toISOString(), // 30分钟前
      readAt: null,
      expiresAt: new Date(Date.now() + 86400000).toISOString()
    },
    {
      id: 'notification_004',
      type: 'maintenance',
      title: '设备维护提醒',
      message: '2号水肥一体机需要进行例行维护检查',
      content: '根据维护计划，2号水肥一体机已运行90天，需要进行例行维护检查。建议检查项目包括：阀门密封、水泵性能、传感器校准、电池电量等。',
      level: 'info',
      farmId: 'farm_001',
      plotId: 'plot_002',
      deviceId: 'device_002',
      relatedId: null,
      isRead: false,
      priority: 'medium',
      category: 'maintenance',
      actions: [
        { type: 'schedule_maintenance', label: '安排维护', url: '/pages/device/maintenance?device=device_002' },
        { type: 'view_manual', label: '查看手册', url: '/pages/device/manual?device=device_002' }
      ],
      createdAt: new Date(Date.now() - 3600000).toISOString(), // 1小时前
      readAt: null,
      expiresAt: new Date(Date.now() + 86400000 * 7).toISOString()
    }
  ],
  
  // 任务列表
  tasks: [
    {
      id: 'task_001',
      type: 'irrigation',
      title: '1号大棚灌溉',
      description: '对1号大棚A、B区域进行灌溉',
      status: 'in_progress', // pending, in_progress, completed, failed, cancelled
      priority: 'high', // low, medium, high, urgent
      farmId: 'farm_001',
      plotId: 'plot_001',
      deviceId: 'device_001',
      scheduleId: null,
      assignedTo: 'user_001',
      estimatedDuration: 1800, // 30分钟
      actualDuration: null,
      startTime: new Date(Date.now() - 900000).toISOString(), // 15分钟前开始
      endTime: null,
      completedAt: null,
      progress: 60, // 进度百分比
      parameters: {
        flowRate: 80,
        zones: ['VALVE_1', 'VALVE_2'],
        targetVolume: 2400,
        fertilizer: {
          enabled: false
        }
      },
      results: null,
      tags: ['urgent', 'manual'],
      notes: '土壤湿度偏低，需要及时灌溉',
      createdBy: 'user_001',
      createdAt: new Date(Date.now() - 1800000).toISOString(),
      updatedAt: new Date(Date.now() - 900000).toISOString()
    },
    {
      id: 'task_002',
      type: 'fertilization',
      title: '2号大棚施肥',
      description: '使用生菜增长配方进行施肥',
      status: 'pending',
      priority: 'medium',
      farmId: 'farm_001',
      plotId: 'plot_002',
      deviceId: 'device_002',
      scheduleId: 'schedule_003',
      assignedTo: 'user_001',
      estimatedDuration: 3600, // 1小时
      actualDuration: null,
      startTime: getNextRunTime('08:00', [6, 7]),
      endTime: null,
      completedAt: null,
      progress: 0,
      parameters: {
        flowRate: 40,
        zones: ['VALVE_1'],
        targetVolume: 2400,
        fertilizer: {
          enabled: true,
          formula: 'lettuce_boost',
          concentration: 20
        }
      },
      results: null,
      tags: ['scheduled', 'fertilization'],
      notes: '周末施肥计划，促进生菜生长',
      createdBy: 'schedule_003',
      createdAt: new Date(Date.now() - 3600000).toISOString(),
      updatedAt: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: 'task_003',
      type: 'maintenance',
      title: '设备维护检查',
      description: '对3号灌溉设备进行维护检查',
      status: 'pending',
      priority: 'urgent',
      farmId: 'farm_001',
      plotId: 'plot_003',
      deviceId: 'device_003',
      scheduleId: null,
      assignedTo: 'user_001',
      estimatedDuration: 1800,
      actualDuration: null,
      startTime: null,
      endTime: null,
      completedAt: null,
      progress: 0,
      parameters: {
        maintenanceType: 'troubleshooting',
        issues: ['offline', 'low_battery'],
        checklist: [
          '检查电源连接',
          '检查网络连接',
          '检查电池电量',
          '检查传感器状态',
          '检查阀门功能'
        ]
      },
      results: null,
      tags: ['maintenance', 'urgent', 'offline'],
      notes: '设备离线，需要紧急维护',
      createdBy: 'system',
      createdAt: new Date(Date.now() - 1800000).toISOString(),
      updatedAt: new Date(Date.now() - 1800000).toISOString()
    }
  ],
  
  // 天气数据
  weatherData: generateWeatherData(),
  
  // 今日数据统计
  todayStats: {
    date: new Date().toISOString().split('T')[0],
    irrigation: {
      totalDuration: 5400, // 秒
      totalVolume: 12500, // 升
      totalTasks: 3,
      completedTasks: 2,
      failedTasks: 0,
      pendingTasks: 1,
      efficiency: 96.5, // %
      cost: 31.25 // 元
    },
    fertilization: {
      totalDuration: 3600,
      totalVolume: 180, // 升
      totalTasks: 2,
      completedTasks: 1,
      failedTasks: 1,
      pendingTasks: 0,
      efficiency: 85.2,
      cost: 57.6
    },
    alarms: {
      total: 4,
      active: 2,
      resolved: 2,
      critical: 1,
      warning: 2,
      info: 1
    },
    devices: {
      total: 3,
      online: 2,
      offline: 1,
      working: 1,
      idle: 1,
      maintenance: 1
    },
    sensors: {
      total: 4,
      online: 3,
      offline: 1,
      normal: 2,
      warning: 1,
      critical: 1
    }
  }
};

// 生成Token（简化版）
function generateToken(userId) {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };
  
  const payload = {
    userId: userId,
    exp: Math.floor(Date.now() / 1000) + (2 * 60 * 60), // 2小时后过期
    iat: Math.floor(Date.now() / 1000)
  };
  
  // 微信小程序环境使用的Base64编码
  const encodedHeader = wx.arrayBufferToBase64(new TextEncoder().encode(JSON.stringify(header)));
  const encodedPayload = wx.arrayBufferToBase64(new TextEncoder().encode(JSON.stringify(payload)));
  const signature = wx.arrayBufferToBase64(new TextEncoder().encode('mock_signature_' + userId + '_' + payload.exp));
  
  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// 验证Token（简化版）
function verifyToken(token) {
  try {
    if (!token) return null;
    
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = JSON.parse(atob(parts[1]));
    
    if (payload.exp < Math.floor(Date.now() / 1000)) {
      return null; // Token过期
    }
    
    return payload;
  } catch (error) {
    return null;
  }
}

// 分页辅助函数
function paginateArray(array, page = 1, pageSize = 10) {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const items = array.slice(startIndex, endIndex);
  
  return {
    items,
    pagination: {
      page,
      pageSize,
      total: array.length,
      totalPages: Math.ceil(array.length / pageSize),
      hasNext: endIndex < array.length,
      hasPrev: page > 1
    }
  };
}

// 过滤辅助函数
function filterByDateRange(array, startDate, endDate, dateField = 'createdAt') {
  if (!startDate && !endDate) return array;
  
  const start = startDate ? new Date(startDate) : new Date('1900-01-01');
  const end = endDate ? new Date(endDate) : new Date('2100-12-31');
  
  return array.filter(item => {
    const itemDate = new Date(item[dateField]);
    return itemDate >= start && itemDate <= end;
  });
}

// 模拟API响应延迟
function simulateDelay(minMs = 200, maxMs = 1000) {
  const delay = Math.random() * (maxMs - minMs) + minMs;
  return new Promise(resolve => setTimeout(resolve, delay));
}

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

// 错误响应格式
function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 导出Mock API模块
module.exports = {
  init() {
    console.log('Mock API 初始化完成');
  },
  
  // 添加更多API方法...
  // 这里只是展示数据模型，具体的API实现方法需要根据现有的mock.js进行扩展
  
  // 获取数据的辅助方法
  getMockData() {
    return mockStorage;
  },
  
  // 更新数据的辅助方法
  updateMockData(key, data) {
    mockStorage[key] = data;
  },
  
  // 辅助函数导出
  generateToken,
  verifyToken,
  paginateArray,
  filterByDateRange,
  simulateDelay,
  createApiResponse,
  createErrorResponse
};