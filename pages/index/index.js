const app = getApp();
const api = require('../../utils/api.js');
const WeatherService = require('../../utils/weather-service.js');
const WeatherAnimations = require('../../utils/weather-animations.js');
import lottie from 'lottie-miniprogram'

Page({
  data: {
    currentLocation: null,
    currentTab: 'all', // 当前选中的设备类型
    weatherData: {
      temperature: 24,
      weather: '中雨',
      icon: '⛅',
      location: '重庆江北',
      humidity: 65,
      windSpeed: 8,
      pressure: 1013,
      lastUpdate: new Date().toLocaleTimeString()
    },
    // 设备列表数据
    deviceList: [
      {
        id: 1,
        name: '水肥一体机',
        status: '灌溉中',
        icon: '/assets/images/水肥一体机.png',
        isOn: false,
        type: 'fertilizer'
      },
      {
        id: 2,
        name: '水肥一体机',
        status: '灌溉中',
        icon: '/assets/images/水肥一体机.png',
        isOn: true,
        type: 'fertilizer'
      }
    ],
    // 球阀开度
    valveOpenness: 30,
    weatherAnimationData: null,
    isAnimationPlaying: false,
    refreshing: false
  },

  onLoad: function (options) {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.getCurrentLocation();
    this.initData();
  },

  onReady: function() {
    // 初始化天气动画
    this.initLottieAnimation();
    this.initWeatherAnimation();
    this._lottieAnimation = null;
  },

  onShow: function () {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    // 更新自定义TabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }
    
    this.loadData();
    this.refreshLocationIfNeeded();
  },

  onPullDownRefresh: function () {
    this.refreshData();
  },

  // 切换设备类型标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  // 切换设备开关
  toggleDevice: function(e) {
    const deviceId = e.currentTarget.dataset.id;
    const deviceList = this.data.deviceList.map(device => {
      if (device.id == deviceId) {
        device.isOn = !device.isOn;
        device.status = device.isOn ? '运行中' : '已停止';
      }
      return device;
    });
    
    this.setData({
      deviceList: deviceList
    });
    
    wx.showToast({
      title: deviceList.find(d => d.id == deviceId).isOn ? '设备已启动' : '设备已停止',
      icon: 'success',
      duration: 2000
    });
  },

  // 球阀滑块变化
  onValveSliderChange: function(e) {
    this.setData({
      valveOpenness: e.detail.value
    });
  },

  // 获取当前位置
  getCurrentLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('位置获取成功:', res);
        
        this.setData({
          currentLocation: {
            latitude: res.latitude,
            longitude: res.longitude
          }
        });
        
        wx.setStorageSync('lastLocationTime', Date.now());
        this.loadWeatherData();
      },
      fail: (error) => {
        console.error('获取位置失败:', error);
        this.useDefaultLocationAndWeather();
      }
    });
  },

  // 使用默认位置和天气
  useDefaultLocationAndWeather: function() {
    this.setData({
      currentLocation: {
        latitude: 29.5647,
        longitude: 106.5507
      },
      'weatherData.location': '重庆江北'
    });
    this.loadWeatherData();
  },

  initData: function () {
    this.loadWeatherData();
  },

  loadWeatherData: function () {
    console.log('开始加载天气数据...');

    api.getWeather().then(res => {
      const weatherData = res.data;
      console.log('天气数据:', weatherData);

      const advice = this.getAgricultureAdvice(weatherData);
      
      this.setData({
        weatherData: {
          temperature: Math.round(weatherData.temperature),
          weather: weatherData.weather,
          icon: this.getWeatherIcon(weatherData.weather),
          humidity: weatherData.humidity,
          windSpeed: weatherData.windSpeed,
          pressure: weatherData.pressure,
          loading: false,
          location: weatherData.location || this.data.weatherData.location || '重庆江北',
          advice: advice,
          lastUpdate: new Date().toLocaleTimeString()
        }
      });
      this.updateWeatherAnimation(weatherData.weather);
    }).catch(err => {
      console.error('获取天气数据失败:', err);
      this.loadWeatherDataFallback();
    });
  },
  
  // 天气数据加载降级方法
  loadWeatherDataFallback: function () {
    if (this.data.currentLocation) {
      this.getWeatherFromAPI(this.data.currentLocation);
    } else {
      this.useSimulatedWeatherData();
    }
  },

  // 从和风天气API获取天气数据
  getWeatherFromAPI: function(location) {
    const weatherService = new WeatherService();
    
    this.setData({
      'weatherData.loading': true
    });
    
    weatherService.getWeatherWithFallback(location)
      .then(data => {
        console.log('天气数据获取结果:', data);
        
        const advice = this.getAgricultureAdvice(data);
        
        this.setData({
          weatherData: {
            temperature: Math.round(data.temperature),
            weather: data.weather,
            icon: this.getWeatherIcon(data.weather),
            humidity: data.humidity,
            windLevel: data.windLevel || '微风',
            windDir: data.windDir || '无风',
            pressure: data.pressure,
            loading: false,
            location: data.location || this.data.weatherData.location,
            source: data.source,
            advice: advice
          }
        });
        
        this.updateWeatherAnimation(data.weather);
        
        if (data.source === 'mock') {
          wx.showToast({
            title: '使用模拟天气数据',
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch(err => {
        console.error('天气数据获取完全失败:', err);
        this.useSimulatedWeatherData();
      });
  },

  // 获取农业建议
  getAgricultureAdvice: function(weatherData) {
    const weatherService = new WeatherService();
    const advices = weatherService.getAgricultureAdvice(weatherData);
    
    if (advices && advices.length > 0) {
      return advices[0].replace(/[🌡️💧🌵⚠️❄️✅☀️🌧️🌱]/g, '').trim();
    }
    
    return '当前天气条件良好';
  },

  // 使用模拟天气数据
  useSimulatedWeatherData: function() {
    console.log('使用模拟天气数据');
    
    const weatherService = new WeatherService();
    const mockData = weatherService.getMockWeatherData();
    
    const advice = this.getAgricultureAdvice(mockData);
    
    this.setData({
      weatherData: {
        temperature: mockData.temperature,
        weather: mockData.weather,
        icon: this.getWeatherIcon(mockData.weather),
        humidity: mockData.humidity,
        windLevel: `${mockData.windLevel}级`,
        windDir: mockData.windDir,
        pressure: mockData.pressure,
        loading: false,
        location: this.data.weatherData.location || '重庆江北',
        advice: advice
      }
    });
    
    this.updateWeatherAnimation(mockData.weather);
    
    wx.showToast({
      title: '使用模拟天气数据',
      icon: 'none',
      duration: 2000
    });
  },

  // 获取天气图标
  getWeatherIcon: function(weather) {
    const iconMap = {
      '晴': '☀️',
      '多云': '⛅',
      '阴': '☁️',
      '小雨': '🌧️',
      '中雨': '🌧️',
      '大雨': '🌧️',
      '暴雨': '⛈️',
      '雷阵雨': '⛈️',
      '雪': '❄️',
      '雾': '🌫️',
      '霾': '😷'
    };
    
    return iconMap[weather] || '🌤️';
  },

  loadData: function () {
    console.log('首页数据加载完成');
  },

  refreshData: function () {
    this.setData({
      refreshing: true
    });
    
    this.loadData();
    
    setTimeout(() => {
      this.setData({
        refreshing: false
      });
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 刷新天气数据
  refreshWeather: function() {
    console.log('手动刷新天气数据');
    
    wx.showToast({
      title: '正在更新天气...',
      icon: 'loading',
      duration: 1500
    });
    
    this.setData({
      'weatherData.loading': true
    });
    
    if (!this.data.currentLocation) {
      this.getCurrentLocation();
    } else {
      this.loadWeatherData();
    }
  },

  // 检查是否需要刷新位置
  refreshLocationIfNeeded() {
    const lastLocationTime = wx.getStorageSync('lastLocationTime') || 0;
    const now = Date.now();
    if (now - lastLocationTime > 10 * 60 * 1000) {
      this.getCurrentLocation();
    }
  },

  // 格式化温度显示
  formatTemperature(temp) {
    if (temp === null || temp === undefined) return '--°C';
    return `${Math.round(temp)}°C`;
  },

  // 初始化Lottie动画
  initLottieAnimation: function() {
    console.log('开始初始化官方Lottie动画');
    wx.createSelectorQuery().select('#weatherCanvas').node(res => {
      const canvas = res.node;
      if (canvas) {
        const context = canvas.getContext('2d');
        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = 60 * dpr;
        canvas.height = 40 * dpr;
        context.scale(dpr, dpr);
        try {
          lottie.setup(canvas);
        
          const weatherType = this.getWeatherAnimationType(this.data.weatherData.weather);
          console.log('Lottie类型',weatherType);
          const animationData = WeatherAnimations.getAnimationData(weatherType);
          this._lottieAnimation = lottie.loadAnimation({
            loop: true,
            autoplay: true,
            animationData: animationData,
            rendererSettings: { context }
          });
          if (this._lottieAnimation) {
            this.setData({
              isAnimationPlaying: true
            });
          }
        } catch (error) {
          console.error('Lottie初始化失败:', error);
        }
      }
    }).exec();
  },

  // 初始化天气动画
  initWeatherAnimation: function() {
    const weatherType = this.getWeatherAnimationType(this.data.weatherData.weather);
    this.loadWeatherAnimation(weatherType);
  },

  // 根据天气类型获取动画类型
  getWeatherAnimationType: function(weather) {
    const animationMap = {
      '晴': 'sunny',
      '晴天': 'sunny',
      '多云': 'cloudy',
      '阴': 'cloudy',
      '阴天': 'cloudy',
      '小雨': 'rain',
      '中雨': 'rain',
      '大雨': 'rain',
      '暴雨': 'rain',
      '雷阵雨': 'rain',
      '雨': 'rain',
      '小雪': 'snow',
      '中雪': 'snow',
      '大雪': 'snow',
      '雪': 'snow'
    };
    
    return animationMap[weather] || 'cloudy';
  },

  // 加载天气动画
  loadWeatherAnimation: function(type) {
    console.log('开始加载天气动画:', type);
    try {
      const animationData = WeatherAnimations.getAnimationData(type);
      const enhancedData = {
        ...animationData,
        type: type
      };
      this.setData({
        weatherAnimationData: enhancedData
      });
    } catch (error) {
      console.error('加载天气动画失败:', error);
    }
  },

  // 更新天气动画
  updateWeatherAnimation: function(weather) {
    const newType = this.getWeatherAnimationType(weather);
    const currentType = this.data.weatherAnimationData ? this.data.weatherAnimationData.type : null;
    
    if (newType !== currentType) {
      console.log('天气类型变化，更新动画:', currentType, '->', newType);
      this.loadWeatherAnimation(newType);
    }
  },

  onUnload: function() {
    if (this._lottieAnimation) {
      this._lottieAnimation.destroy();
      this._lottieAnimation = null;
    }
  }
});