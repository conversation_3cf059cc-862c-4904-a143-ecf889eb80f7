Page({
  data: {
    userInfo: {
      name: '',
      phone: '',
      email: '',
      role: 'farmer',
      farmName: '',
      farmArea: '',
      address: '',
      description: ''
    },
    roleOptions: [
      { value: 'farmer', label: '农场主' },
      { value: 'technician', label: '技术员' },
      { value: 'manager', label: '管理员' },
      { value: 'observer', label: '观察员' }
    ],
    avatarUrl: '',
    hasChanges: false,
    saving: false,
    currentRoleLabel: '请选择角色'
  },

  onLoad() {
    this.loadUserInfo();
  },

  onUnload() {
    if (this.data.hasChanges) {
      wx.showModal({
        title: '提示',
        content: '您有未保存的修改，确定要离开吗？',
        success: (res) => {
          if (!res.confirm) {
            // 阻止返回，但小程序中无法真正阻止
          }
        }
      });
    }
  },

  loadUserInfo() {
    // 加载用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: {
          ...this.data.userInfo,
          ...userInfo
        },
        avatarUrl: userInfo.avatarUrl || ''
      });
    }
    this.updateRoleLabel();
  },

  updateRoleLabel() {
    const { userInfo, roleOptions } = this.data;
    const currentRole = roleOptions.find(item => item.value === userInfo.role);
    this.setData({
      currentRoleLabel: currentRole ? currentRole.label : '请选择角色'
    });
  },

  changeAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        wx.showLoading({
          title: '上传中...'
        });
        
        // 模拟上传头像
        setTimeout(() => {
          this.setData({
            avatarUrl: tempFilePath,
            hasChanges: true
          });
          
          wx.hideLoading();
          wx.showToast({
            title: '头像更新成功',
            icon: 'success'
          });
        }, 1500);
      }
    });
  },

  onNameInput(e) {
    this.setData({
      'userInfo.name': e.detail.value,
      hasChanges: true
    });
  },

  onPhoneInput(e) {
    this.setData({
      'userInfo.phone': e.detail.value,
      hasChanges: true
    });
  },

  onEmailInput(e) {
    this.setData({
      'userInfo.email': e.detail.value,
      hasChanges: true
    });
  },

  onFarmNameInput(e) {
    this.setData({
      'userInfo.farmName': e.detail.value,
      hasChanges: true
    });
  },

  onFarmAreaInput(e) {
    this.setData({
      'userInfo.farmArea': e.detail.value,
      hasChanges: true
    });
  },

  onAddressInput(e) {
    this.setData({
      'userInfo.address': e.detail.value,
      hasChanges: true
    });
  },

  onDescriptionInput(e) {
    this.setData({
      'userInfo.description': e.detail.value,
      hasChanges: true
    });
  },

  selectRole(e) {
    const role = e.detail.value;
    const roleOption = this.data.roleOptions[role];
    
    this.setData({
      'userInfo.role': roleOption.value,
      hasChanges: true
    });
    
    this.updateRoleLabel();
  },

  selectLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          'userInfo.address': res.address,
          hasChanges: true
        });
      },
      fail: () => {
        wx.showToast({
          title: '需要位置权限',
          icon: 'none'
        });
      }
    });
  },

  validateForm() {
    const { userInfo } = this.data;
    
    if (!userInfo.name.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return false;
    }
    
    if (userInfo.phone && !this.isValidPhone(userInfo.phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return false;
    }
    
    if (userInfo.email && !this.isValidEmail(userInfo.email)) {
      wx.showToast({
        title: '邮箱格式不正确',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  isValidPhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone);
  },

  isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  },

  async saveProfile() {
    if (this.data.saving) return;
    
    if (!this.validateForm()) return;
    
    this.setData({ saving: true });
    
    try {
      wx.showLoading({
        title: '保存中...'
      });
      
      const { userInfo, avatarUrl } = this.data;
      
      // 模拟保存用户信息
      await this.uploadUserInfo({
        ...userInfo,
        avatarUrl: avatarUrl,
        updatedAt: new Date().getTime()
      });
      
      // 保存到本地存储
      wx.setStorageSync('userInfo', {
        ...userInfo,
        avatarUrl: avatarUrl
      });
      
      wx.hideLoading();
      
      this.setData({
        hasChanges: false
      });
      
      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 2000
      });
      
      // 2秒后返回
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'error'
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  async uploadUserInfo(data) {
    // 模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('User info updated:', data);
        resolve();
      }, 2000);
    });
  },

  resetForm() {
    wx.showModal({
      title: '重置表单',
      content: '确定要重置所有修改吗？',
      success: (res) => {
        if (res.confirm) {
          this.loadUserInfo();
          this.setData({
            hasChanges: false
          });
          
          wx.showToast({
            title: '已重置',
            icon: 'success'
          });
        }
      }
    });
  }
})