package com.agriculture.iot.service.impl;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.Alarm;
import com.agriculture.iot.mapper.AlarmMapper;
import com.agriculture.iot.service.AlarmService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 报警服务实现类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlarmServiceImpl extends ServiceImpl<AlarmMapper, Alarm> implements AlarmService {

    private final AlarmMapper alarmMapper;

    @Override
    public IPage<Alarm.Detail> getAlarms(Integer current, Integer size, String level, String status, 
                                         String type, Long plotId, Long deviceId, LocalDateTime startTime, LocalDateTime endTime) {
        Page<Alarm.Detail> page = new Page<>(current, size);
        return alarmMapper.selectAlarmDetailsPage(page, level, status, type, plotId, deviceId, startTime, endTime);
    }

    @Override
    public Result<List<Alarm.Detail>> getLatestAlarms(Integer limit) {
        try {
            List<Alarm.Detail> alarms = alarmMapper.selectLatestAlarms(limit);
            return Result.success(alarms);
        } catch (Exception e) {
            log.error("获取最新报警失败", e);
            return Result.error("获取最新报警失败");
        }
    }

    @Override
    public Result<List<Alarm.Detail>> getActiveAlarms(Long plotId, String level) {
        try {
            List<Alarm.Detail> alarms = alarmMapper.selectActiveAlarms(plotId, level);
            return Result.success(alarms);
        } catch (Exception e) {
            log.error("获取活跃报警失败", e);
            return Result.error("获取活跃报警失败");
        }
    }

    @Override
    public Result<Alarm.Detail> getAlarmDetail(Long id) {
        try {
            Alarm.Detail detail = alarmMapper.selectAlarmDetailById(id);
            return detail != null ? Result.success(detail) : Result.error("报警不存在");
        } catch (Exception e) {
            log.error("获取报警详情失败", e);
            return Result.error("获取报警详情失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> acknowledgeAlarm(Long id, String notes) {
        try {
            int result = alarmMapper.acknowledgeAlarm(id, notes, LocalDateTime.now());
            return result > 0 ? Result.success() : Result.error("确认报警失败");
        } catch (Exception e) {
            log.error("确认报警失败", e);
            return Result.error("确认报警失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> resolveAlarm(Long id, Alarm.ProcessRequest request) {
        try {
            int result = alarmMapper.resolveAlarm(id, request.getNotes(), LocalDateTime.now());
            return result > 0 ? Result.success() : Result.error("解决报警失败");
        } catch (Exception e) {
            log.error("解决报警失败", e);
            return Result.error("解决报警失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> ignoreAlarm(Long id, String reason) {
        try {
            Alarm alarm = this.getById(id);
            if (alarm == null) {
                return Result.error("报警不存在");
            }
            
            alarm.setStatus("ignored");
            alarm.setIgnoreReason(reason);
            alarm.setIgnoredAt(LocalDateTime.now());
            alarm.setUpdatedAt(LocalDateTime.now());
            
            boolean updated = this.updateById(alarm);
            return updated ? Result.success() : Result.error("忽略报警失败");
        } catch (Exception e) {
            log.error("忽略报警失败", e);
            return Result.error("忽略报警失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteAlarm(Long id) {
        try {
            boolean deleted = this.removeById(id);
            return deleted ? Result.success() : Result.error("删除报警失败");
        } catch (Exception e) {
            log.error("删除报警失败", e);
            return Result.error("删除报警失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchProcessAlarms(List<Long> alarmIds, String action, String notes) {
        try {
            int result = 0;
            switch (action) {
                case "acknowledge":
                    for (Long alarmId : alarmIds) {
                        result += alarmMapper.acknowledgeAlarm(alarmId, notes, LocalDateTime.now());
                    }
                    break;
                case "resolve":
                    for (Long alarmId : alarmIds) {
                        result += alarmMapper.resolveAlarm(alarmId, notes, LocalDateTime.now());
                    }
                    break;
                case "ignore":
                    result = alarmMapper.batchUpdateAlarmStatus(alarmIds, "ignored");
                    break;
                default:
                    return Result.error("不支持的操作");
            }
            
            return result > 0 ? Result.success() : Result.error("批量处理失败");
        } catch (Exception e) {
            log.error("批量处理报警失败", e);
            return Result.error("批量处理报警失败");
        }
    }

    @Override
    public Result<Alarm.Statistics> getAlarmStatistics(LocalDate startDate, LocalDate endDate, Long plotId, String type) {
        try {
            Alarm.Statistics statistics = alarmMapper.selectAlarmStatistics(startDate, endDate, plotId, type);
            return statistics != null ? Result.success(statistics) : Result.error("统计数据不存在");
        } catch (Exception e) {
            log.error("获取报警统计失败", e);
            return Result.error("获取报警统计失败");
        }
    }

    @Override
    public Result<List<Alarm.TrendData>> getAlarmTrends(LocalDate startDate, LocalDate endDate, String period, Long plotId, String type) {
        try {
            List<Alarm.TrendData> trends = alarmMapper.selectAlarmTrends(startDate, endDate, period, plotId, type);
            return Result.success(trends);
        } catch (Exception e) {
            log.error("获取报警趋势失败", e);
            return Result.error("获取报警趋势失败");
        }
    }

    @Override
    public Result<Alarm.Dashboard> getAlarmDashboard(Long plotId) {
        try {
            Alarm.Dashboard dashboard = alarmMapper.selectAlarmDashboard(plotId);
            return dashboard != null ? Result.success(dashboard) : Result.error("仪表板数据不存在");
        } catch (Exception e) {
            log.error("获取报警仪表板失败", e);
            return Result.error("获取报警仪表板失败");
        }
    }

    @Override
    public Result<Alarm.Report> generateAlarmReport(String reportType, LocalDate startDate, LocalDate endDate, Long plotId) {
        try {
            Alarm.Report report = new Alarm.Report();
            report.setReportType(reportType);
            report.setStartDate(startDate);
            report.setEndDate(endDate);
            report.setPlotId(plotId);
            report.setGenerateTime(LocalDateTime.now());
            
            // 获取报警统计数据
            Alarm.Statistics statistics = alarmMapper.selectAlarmStatistics(startDate, endDate, plotId, null);
            report.setStatistics(statistics);
            
            // 获取趋势数据
            List<Alarm.TrendData> trends = alarmMapper.selectAlarmTrends(startDate, endDate, "daily", plotId, null);
            report.setTrends(trends);
            
            return Result.success(report);
        } catch (Exception e) {
            log.error("生成报警报告失败", e);
            return Result.error("生成报警报告失败");
        }
    }

    @Override
    public Result<IPage<Alarm.Rule>> getAlarmRules(Integer current, Integer size, String type, Boolean enabled, Long plotId, Long deviceId) {
        try {
            Page<Alarm.Rule> page = new Page<>(current, size);
            IPage<Alarm.Rule> result = alarmMapper.selectAlarmRulesPage(page, type, enabled, plotId, deviceId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取报警规则失败", e);
            return Result.error("获取报警规则失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Alarm.Rule> createAlarmRule(Alarm.Rule rule) {
        try {
            rule.setCreatedAt(LocalDateTime.now());
            rule.setUpdatedAt(LocalDateTime.now());
            int result = alarmMapper.insertAlarmRule(rule);
            return result > 0 ? Result.success(rule) : Result.error("创建报警规则失败");
        } catch (Exception e) {
            log.error("创建报警规则失败", e);
            return Result.error("创建报警规则失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Alarm.Rule> updateAlarmRule(Long id, Alarm.Rule rule) {
        try {
            rule.setId(id);
            rule.setUpdatedAt(LocalDateTime.now());
            int result = alarmMapper.updateAlarmRule(rule);
            return result > 0 ? Result.success(rule) : Result.error("更新报警规则失败");
        } catch (Exception e) {
            log.error("更新报警规则失败", e);
            return Result.error("更新报警规则失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteAlarmRule(Long id) {
        try {
            Alarm.Rule rule = alarmMapper.selectAlarmRuleById(id);
            if (rule == null) {
                return Result.error("报警规则不存在");
            }
            
            // 检查是否有关联的报警
            // TODO: 实现关联检查
            
            boolean deleted = this.removeById(id);
            return deleted ? Result.success() : Result.error("删除报警规则失败");
        } catch (Exception e) {
            log.error("删除报警规则失败", e);
            return Result.error("删除报警规则失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> toggleAlarmRule(Long id, Boolean enabled) {
        try {
            int result = alarmMapper.toggleAlarmRule(id, enabled);
            return result > 0 ? Result.success() : Result.error("切换规则状态失败");
        } catch (Exception e) {
            log.error("切换报警规则状态失败", e);
            return Result.error("切换报警规则状态失败");
        }
    }

    @Override
    public Result<Alarm.Response> testAlarmRule(Long ruleId, Long deviceId) {
        try {
            Alarm.Response response = new Alarm.Response();
            response.setRuleId(ruleId);
            response.setDeviceId(deviceId);
            response.setResponseTime(LocalDateTime.now());
            response.setStatus("test");
            response.setMessage("报警规则测试成功");
            
            // TODO: 实际的规则测试逻辑
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("测试报警规则失败", e);
            return Result.error("测试报警规则失败");
        }
    }

    @Override
    public Result<List<Alarm.Config>> getAlarmConfigs(Long plotId, Long deviceId) {
        try {
            List<Alarm.Config> configs = alarmMapper.selectAlarmConfigs(plotId, deviceId);
            return Result.success(configs);
        } catch (Exception e) {
            log.error("获取报警配置失败", e);
            return Result.error("获取报警配置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateAlarmConfig(Long configId, Alarm.Config config) {
        try {
            config.setId(configId);
            config.setUpdatedAt(LocalDateTime.now());
            int result = alarmMapper.updateAlarmConfig(config);
            return result > 0 ? Result.success() : Result.error("更新报警配置失败");
        } catch (Exception e) {
            log.error("更新报警配置失败", e);
            return Result.error("更新报警配置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> resetAlarmConfig(Long plotId, Long deviceId) {
        try {
            // TODO: 实现配置重置逻辑
            // 这里应该将配置恢复到默认值
            return Result.success();
        } catch (Exception e) {
            log.error("重置报警配置失败", e);
            return Result.error("重置报警配置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkAlarmConditions(Long deviceId, String parameter, Object value) {
        try {
            // 获取设备相关的报警规则
            List<Alarm.Rule> rules = alarmMapper.selectRulesByDevice(deviceId);
            
            for (Alarm.Rule rule : rules) {
                if (rule.getParameter().equals(parameter) && rule.getEnabled()) {
                    // 检查是否满足报警条件
                    if (isAlarmConditionMet(rule, value)) {
                        // 触发报警
                        triggerAlarm(deviceId, rule.getPlotId(), rule.getType(), rule.getLevel(),
                                rule.getName(), rule.getDescription(), value, rule.getThresholdValue());
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查报警条件失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Alarm> triggerAlarm(Long deviceId, Long plotId, String type, String level, 
                                      String title, String description, Object currentValue, Object thresholdValue) {
        try {
            Alarm alarm = new Alarm();
            alarm.setDeviceId(deviceId);
            alarm.setPlotId(plotId);
            alarm.setAlarmType(type);
            alarm.setAlarmLevel(level);
            alarm.setTitle(title);
            alarm.setDescription(description);
            alarm.setCurrentValue(currentValue != null ? currentValue.toString() : null);
            alarm.setThresholdValue(thresholdValue != null ? thresholdValue.toString() : null);
            alarm.setStatus("active");
            alarm.setCreatedAt(LocalDateTime.now());
            alarm.setUpdatedAt(LocalDateTime.now());
            
            boolean saved = this.save(alarm);
            if (saved) {
                // 发送报警通知
                sendAlarmNotifications(alarm.getId());
                
                return Result.success(alarm);
            } else {
                return Result.error("触发报警失败");
            }
        } catch (Exception e) {
            log.error("触发报警失败", e);
            return Result.error("触发报警失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<Alarm.Notification>> sendAlarmNotifications(Long alarmId) {
        try {
            List<Alarm.Notification> notifications = new ArrayList<>();
            
            // TODO: 实现通知发送逻辑
            // 这里应该根据报警规则配置发送不同类型的通知（邮件、短信、推送等）
            
            return Result.success(notifications);
        } catch (Exception e) {
            log.error("发送报警通知失败", e);
            return Result.error("发送报警通知失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Alarm.Response> executeAutoResponse(Long alarmId, Long ruleId) {
        try {
            Alarm.Response response = new Alarm.Response();
            response.setAlarmId(alarmId);
            response.setRuleId(ruleId);
            response.setResponseTime(LocalDateTime.now());
            response.setStatus("executed");
            response.setMessage("自动响应执行成功");
            
            // TODO: 实现自动响应逻辑
            // 这里应该根据规则配置执行自动响应动作
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("执行自动响应失败", e);
            return Result.error("执行自动响应失败");
        }
    }

    @Override
    public Result<IPage<Alarm.Notification>> getAlarmNotifications(Integer current, Integer size, Long alarmId, String method, String status) {
        try {
            Page<Alarm.Notification> page = new Page<>(current, size);
            IPage<Alarm.Notification> result = alarmMapper.selectAlarmNotificationsPage(page, alarmId, method, status);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取报警通知失败", e);
            return Result.error("获取报警通知失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> resendAlarmNotification(Long notificationId) {
        try {
            // TODO: 实现重新发送通知逻辑
            int result = alarmMapper.updateNotificationStatus(notificationId, "resending");
            return result > 0 ? Result.success() : Result.error("重新发送通知失败");
        } catch (Exception e) {
            log.error("重新发送报警通知失败", e);
            return Result.error("重新发送报警通知失败");
        }
    }

    @Override
    public Result<IPage<Alarm.Response>> getAlarmResponses(Integer current, Integer size, Long alarmId, String responseType) {
        try {
            Page<Alarm.Response> page = new Page<>(current, size);
            IPage<Alarm.Response> result = alarmMapper.selectAlarmResponsesPage(page, alarmId, responseType);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取报警响应失败", e);
            return Result.error("获取报警响应失败");
        }
    }

    @Override
    public Result<List<Alarm.Detail>> getOverdueAlarms(Integer hours, Long plotId) {
        try {
            List<Alarm.Detail> alarms = alarmMapper.selectOverdueAlarms(hours, plotId);
            return Result.success(alarms);
        } catch (Exception e) {
            log.error("获取逾期报警失败", e);
            return Result.error("获取逾期报警失败");
        }
    }

    @Override
    public Result<List<Alarm.Detail>> getHighPriorityAlarms(Long plotId) {
        try {
            List<Alarm.Detail> alarms = alarmMapper.selectHighPriorityAlarms(plotId);
            return Result.success(alarms);
        } catch (Exception e) {
            log.error("获取高优先级报警失败", e);
            return Result.error("获取高优先级报警失败");
        }
    }

    @Override
    public Result<String> exportAlarmData(LocalDate startDate, LocalDate endDate, String format, Long plotId) {
        try {
            // TODO: 实现数据导出逻辑
            String fileName = "alarm_data_" + startDate + "_to_" + endDate + "." + format;
            return Result.success("/exports/" + fileName);
        } catch (Exception e) {
            log.error("导出报警数据失败", e);
            return Result.error("导出报警数据失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cleanupAlarmHistory(Integer retentionDays) {
        try {
            int result = alarmMapper.cleanupAlarmHistory(retentionDays);
            return Result.success();
        } catch (Exception e) {
            log.error("清理报警历史失败", e);
            return Result.error("清理报警历史失败");
        }
    }

    @Override
    public Result<List<Alarm.Rule>> getCascadeRules(Long triggeredRuleId) {
        try {
            List<Alarm.Rule> rules = alarmMapper.selectCascadeRules(triggeredRuleId);
            return Result.success(rules);
        } catch (Exception e) {
            log.error("获取级联规则失败", e);
            return Result.error("获取级联规则失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<Alarm>> executeCascadeAlarms(Long triggeredAlarmId) {
        try {
            List<Alarm> cascadeAlarms = new ArrayList<>();
            
            // TODO: 实现级联报警逻辑
            // 这里应该根据触发的报警查找级联规则，并触发相应的级联报警
            
            return Result.success(cascadeAlarms);
        } catch (Exception e) {
            log.error("执行级联报警失败", e);
            return Result.error("执行级联报警失败");
        }
    }

    /**
     * 检查报警条件是否满足
     */
    private boolean isAlarmConditionMet(Alarm.Rule rule, Object value) {
        if (value == null || rule.getThresholdValue() == null) {
            return false;
        }
        
        try {
            double currentValue = Double.parseDouble(value.toString());
            double thresholdValue = Double.parseDouble(rule.getThresholdValue().toString());
            
            switch (rule.getCondition()) {
                case "greater_than":
                    return currentValue > thresholdValue;
                case "less_than":
                    return currentValue < thresholdValue;
                case "greater_equal":
                    return currentValue >= thresholdValue;
                case "less_equal":
                    return currentValue <= thresholdValue;
                case "equal":
                    return currentValue == thresholdValue;
                case "not_equal":
                    return currentValue != thresholdValue;
                default:
                    return false;
            }
        } catch (NumberFormatException e) {
            log.warn("无法解析数值进行比较: value={}, threshold={}", value, rule.getThresholdValue());
            return false;
        }
    }
}