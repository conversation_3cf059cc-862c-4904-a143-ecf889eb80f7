/* pages/profile/edit/edit.wxss */

.page-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 140rpx;
}

.form-container {
  padding: 20rpx;
}

/* 头像设置 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #ffffff;
  padding: 40rpx 32rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.avatar-container {
  margin-bottom: 16rpx;
}

.avatar-wrapper {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  overflow: hidden;
  border: 4rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.avatar-wrapper:active {
  transform: scale(0.95);
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #2E7D32, #4CAF50);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64rpx;
  color: #ffffff;
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 8rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-wrapper:active .avatar-overlay {
  opacity: 1;
}

.camera-icon {
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.change-text {
  font-size: 20rpx;
}

.avatar-tip {
  font-size: 24rpx;
  color: #666666;
}

/* 表单区域 */
.form-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

/* 表单组 */
.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.required {
  color: #ff4757;
  margin-left: 4rpx;
  font-size: 24rpx;
}

/* 输入框 */
.form-input {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #2E7D32;
}

/* 选择器 */
.form-picker {
  width: 100%;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  transition: border-color 0.3s ease;
}

.picker-content:active {
  border-color: #2E7D32;
  background: #f8f8f8;
}

.picker-text {
  font-size: 28rpx;
  color: #333333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999999;
}

/* 地址输入 */
.address-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.address-input:active {
  border-color: #2E7D32;
  background: #f8f8f8;
}

.address-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.address-text.placeholder {
  color: #999999;
}

.location-icon {
  font-size: 24rpx;
  margin-left: 16rpx;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #2E7D32;
}

.char-counter {
  text-align: right;
  margin-top: 8rpx;
  font-size: 20rpx;
  color: #999999;
}

/* 操作按钮 */
.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 16rpx;
  padding: 20rpx 32rpx;
  background: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.btn {
  flex: 1;
  padding: 28rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.btn::after {
  border: none;
}

.btn-primary {
  background: #2E7D32;
  color: #ffffff;
}

.btn-primary:active {
  background: #1B5E20;
  transform: scale(0.98);
}

.btn-secondary {
  background: #f5f5f5;
  color: #666666;
}

.btn-secondary:active {
  background: #e0e0e0;
  transform: scale(0.98);
}

.btn[disabled] {
  background: #f5f5f5 !important;
  color: #cccccc !important;
  transform: none !important;
}

/* 保存提示 */
.save-tip {
  position: fixed;
  top: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  animation: slideDown 0.3s ease-out;
}

.tip-content {
  display: flex;
  align-items: center;
  background: #ff9800;
  color: #ffffff;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
  gap: 8rpx;
}

.tip-icon {
  font-size: 24rpx;
}

.tip-text {
  font-size: 26rpx;
  font-weight: 500;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 响应式适配 */
@media screen and (max-width: 400px) {
  .form-section {
    padding: 24rpx 20rpx;
  }
  
  .form-actions {
    padding: 16rpx 20rpx;
  }
}