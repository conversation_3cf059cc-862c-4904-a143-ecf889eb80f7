package com.agriculture.iot.controller;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.Alarm;
import com.agriculture.iot.service.AlarmService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报警管理控制器
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Tag(name = "报警管理", description = "农业IoT系统的报警监控和处理")
@RestController
@RequestMapping("/api/alarms")
@RequiredArgsConstructor
public class AlarmController {

    private final AlarmService alarmService;

    @Operation(summary = "获取报警列表", description = "分页获取报警记录列表")
    @GetMapping
    public Result<IPage<Alarm.Detail>> getAlarms(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "页面大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "报警级别") @RequestParam(required = false) String level,
            @Parameter(description = "处理状态") @RequestParam(required = false) String status,
            @Parameter(description = "报警类型") @RequestParam(required = false) String type,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "开始时间") @RequestParam(required = false) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) LocalDateTime endTime) {
        
        IPage<Alarm.Detail> alarms = alarmService.getAlarms(
                current, size, level, status, type, plotId, deviceId, startTime, endTime);
        return Result.success(alarms);
    }

    @Operation(summary = "获取最新报警", description = "获取最新的报警记录")
    @GetMapping("/latest")
    public Result<List<Alarm.Detail>> getLatestAlarms(
            @Parameter(description = "返回数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        
        return alarmService.getLatestAlarms(limit);
    }

    @Operation(summary = "获取活跃报警", description = "获取当前活跃的报警")
    @GetMapping("/active")
    public Result<List<Alarm.Detail>> getActiveAlarms(
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "报警级别") @RequestParam(required = false) String level) {
        
        return alarmService.getActiveAlarms(plotId, level);
    }

    @Operation(summary = "获取报警详情", description = "获取指定报警的详细信息")
    @GetMapping("/{id}")
    public Result<Alarm.Detail> getAlarmDetail(@Parameter(description = "报警ID") @PathVariable Long id) {
        return alarmService.getAlarmDetail(id);
    }

    @Operation(summary = "确认报警", description = "确认已知晓指定报警")
    @PutMapping("/{id}/acknowledge")
    public Result<Void> acknowledgeAlarm(
            @Parameter(description = "报警ID") @PathVariable Long id,
            @Parameter(description = "确认备注") @RequestParam(required = false) String notes) {
        
        return alarmService.acknowledgeAlarm(id, notes);
    }

    @Operation(summary = "解决报警", description = "标记报警为已解决")
    @PutMapping("/{id}/resolve")
    public Result<Void> resolveAlarm(
            @Parameter(description = "报警ID") @PathVariable Long id,
            @Valid @RequestBody Alarm.ProcessRequest request) {
        
        return alarmService.resolveAlarm(id, request);
    }

    @Operation(summary = "忽略报警", description = "忽略指定的报警")
    @PutMapping("/{id}/ignore")
    public Result<Void> ignoreAlarm(
            @Parameter(description = "报警ID") @PathVariable Long id,
            @Parameter(description = "忽略原因") @RequestParam String reason) {
        
        return alarmService.ignoreAlarm(id, reason);
    }

    @Operation(summary = "删除报警", description = "删除指定的报警记录")
    @DeleteMapping("/{id}")
    public Result<Void> deleteAlarm(@Parameter(description = "报警ID") @PathVariable Long id) {
        return alarmService.deleteAlarm(id);
    }

    @Operation(summary = "批量处理报警", description = "批量确认、解决或忽略报警")
    @PutMapping("/batch-process")
    public Result<Void> batchProcessAlarms(
            @Parameter(description = "报警ID列表") @RequestParam List<Long> alarmIds,
            @Parameter(description = "处理动作") @RequestParam String action,
            @Parameter(description = "处理备注") @RequestParam(required = false) String notes) {
        
        return alarmService.batchProcessAlarms(alarmIds, action, notes);
    }

    @Operation(summary = "获取报警统计", description = "获取指定时间范围的报警统计数据")
    @GetMapping("/statistics")
    public Result<Alarm.Statistics> getAlarmStatistics(
            @Parameter(description = "开始日期") @RequestParam LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam LocalDate endDate,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "报警类型") @RequestParam(required = false) String type) {
        
        return alarmService.getAlarmStatistics(startDate, endDate, plotId, type);
    }

    @Operation(summary = "获取报警趋势", description = "获取报警趋势数据")
    @GetMapping("/trends")
    public Result<List<Alarm.TrendData>> getAlarmTrends(
            @Parameter(description = "开始日期") @RequestParam LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam LocalDate endDate,
            @Parameter(description = "统计周期") @RequestParam(defaultValue = "day") String period,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "报警类型") @RequestParam(required = false) String type) {
        
        return alarmService.getAlarmTrends(startDate, endDate, period, plotId, type);
    }

    @Operation(summary = "获取报警仪表板", description = "获取报警仪表板数据")
    @GetMapping("/dashboard")
    public Result<Alarm.Dashboard> getAlarmDashboard(
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId) {
        
        return alarmService.getAlarmDashboard(plotId);
    }

    @Operation(summary = "生成报警报告", description = "生成指定时间范围的报警报告")
    @GetMapping("/report")
    public Result<Alarm.Report> generateAlarmReport(
            @Parameter(description = "报告类型") @RequestParam String reportType,
            @Parameter(description = "开始日期") @RequestParam LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam LocalDate endDate,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId) {
        
        return alarmService.generateAlarmReport(reportType, startDate, endDate, plotId);
    }

    @Operation(summary = "获取报警规则", description = "分页获取报警规则列表")
    @GetMapping("/rules")
    public Result<IPage<Alarm.Rule>> getAlarmRules(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "页面大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "规则类型") @RequestParam(required = false) String type,
            @Parameter(description = "是否启用") @RequestParam(required = false) Boolean enabled,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId) {
        
        return alarmService.getAlarmRules(current, size, type, enabled, plotId, deviceId);
    }

    @Operation(summary = "创建报警规则", description = "创建新的报警规则")
    @PostMapping("/rules")
    public Result<Alarm.Rule> createAlarmRule(@Valid @RequestBody Alarm.Rule rule) {
        return alarmService.createAlarmRule(rule);
    }

    @Operation(summary = "更新报警规则", description = "更新指定的报警规则")
    @PutMapping("/rules/{id}")
    public Result<Alarm.Rule> updateAlarmRule(
            @Parameter(description = "规则ID") @PathVariable Long id,
            @Valid @RequestBody Alarm.Rule rule) {
        
        return alarmService.updateAlarmRule(id, rule);
    }

    @Operation(summary = "删除报警规则", description = "删除指定的报警规则")
    @DeleteMapping("/rules/{id}")
    public Result<Void> deleteAlarmRule(@Parameter(description = "规则ID") @PathVariable Long id) {
        return alarmService.deleteAlarmRule(id);
    }

    @Operation(summary = "启用/禁用规则", description = "切换报警规则的启用状态")
    @PutMapping("/rules/{id}/toggle")
    public Result<Void> toggleAlarmRule(
            @Parameter(description = "规则ID") @PathVariable Long id,
            @Parameter(description = "是否启用") @RequestParam Boolean enabled) {
        
        return alarmService.toggleAlarmRule(id, enabled);
    }

    @Operation(summary = "测试报警规则", description = "测试报警规则是否正常工作")
    @PostMapping("/rules/{ruleId}/test")
    public Result<Alarm.Response> testAlarmRule(
            @Parameter(description = "规则ID") @PathVariable Long ruleId,
            @Parameter(description = "设备ID") @RequestParam Long deviceId) {
        
        return alarmService.testAlarmRule(ruleId, deviceId);
    }

    @Operation(summary = "获取报警配置", description = "获取设备或地块的报警配置")
    @GetMapping("/configs")
    public Result<List<Alarm.Config>> getAlarmConfigs(
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId) {
        
        return alarmService.getAlarmConfigs(plotId, deviceId);
    }

    @Operation(summary = "更新报警配置", description = "更新报警参数配置")
    @PutMapping("/configs/{configId}")
    public Result<Void> updateAlarmConfig(
            @Parameter(description = "配置ID") @PathVariable Long configId,
            @Valid @RequestBody Alarm.Config config) {
        
        return alarmService.updateAlarmConfig(configId, config);
    }

    @Operation(summary = "重置报警配置", description = "重置为默认报警配置")
    @PostMapping("/configs/reset")
    public Result<Void> resetAlarmConfig(
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId) {
        
        return alarmService.resetAlarmConfig(plotId, deviceId);
    }

    @Operation(summary = "手动触发报警", description = "手动创建报警记录")
    @PostMapping("/trigger")
    public Result<Alarm> triggerAlarm(
            @Parameter(description = "设备ID") @RequestParam Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "报警类型") @RequestParam String type,
            @Parameter(description = "报警级别") @RequestParam String level,
            @Parameter(description = "报警标题") @RequestParam String title,
            @Parameter(description = "报警描述") @RequestParam String description,
            @Parameter(description = "当前值") @RequestParam(required = false) Object currentValue,
            @Parameter(description = "阈值") @RequestParam(required = false) Object thresholdValue) {
        
        return alarmService.triggerAlarm(deviceId, plotId, type, level, title, description, currentValue, thresholdValue);
    }

    @Operation(summary = "发送报警通知", description = "重新发送报警通知")
    @PostMapping("/{alarmId}/notifications")
    public Result<List<Alarm.Notification>> sendAlarmNotifications(
            @Parameter(description = "报警ID") @PathVariable Long alarmId) {
        
        return alarmService.sendAlarmNotifications(alarmId);
    }

    @Operation(summary = "获取通知记录", description = "获取报警通知发送记录")
    @GetMapping("/notifications")
    public Result<IPage<Alarm.Notification>> getAlarmNotifications(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "页面大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "报警ID") @RequestParam(required = false) Long alarmId,
            @Parameter(description = "通知方式") @RequestParam(required = false) String method,
            @Parameter(description = "发送状态") @RequestParam(required = false) String status) {
        
        return alarmService.getAlarmNotifications(current, size, alarmId, method, status);
    }

    @Operation(summary = "重新发送通知", description = "重新发送失败的通知")
    @PostMapping("/notifications/{notificationId}/resend")
    public Result<Void> resendAlarmNotification(
            @Parameter(description = "通知ID") @PathVariable Long notificationId) {
        
        return alarmService.resendAlarmNotification(notificationId);
    }

    @Operation(summary = "获取响应记录", description = "获取报警自动响应记录")
    @GetMapping("/responses")
    public Result<IPage<Alarm.Response>> getAlarmResponses(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "页面大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "报警ID") @RequestParam(required = false) Long alarmId,
            @Parameter(description = "响应类型") @RequestParam(required = false) String responseType) {
        
        return alarmService.getAlarmResponses(current, size, alarmId, responseType);
    }

    @Operation(summary = "获取逾期报警", description = "获取逾期未处理的报警")
    @GetMapping("/overdue")
    public Result<List<Alarm.Detail>> getOverdueAlarms(
            @Parameter(description = "逾期小时数") @RequestParam(defaultValue = "24") Integer hours,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId) {
        
        return alarmService.getOverdueAlarms(hours, plotId);
    }

    @Operation(summary = "获取高优先级报警", description = "获取高优先级未处理报警")
    @GetMapping("/high-priority")
    public Result<List<Alarm.Detail>> getHighPriorityAlarms(
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId) {
        
        return alarmService.getHighPriorityAlarms(plotId);
    }

    @Operation(summary = "导出报警数据", description = "导出指定时间范围的报警数据")
    @GetMapping("/export")
    public Result<String> exportAlarmData(
            @Parameter(description = "开始日期") @RequestParam LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam LocalDate endDate,
            @Parameter(description = "导出格式") @RequestParam(defaultValue = "excel") String format,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId) {
        
        return alarmService.exportAlarmData(startDate, endDate, format, plotId);
    }

    @Operation(summary = "清理历史数据", description = "清理超过保留期的历史报警数据")
    @PostMapping("/cleanup")
    public Result<Void> cleanupAlarmHistory(
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "90") Integer retentionDays) {
        
        return alarmService.cleanupAlarmHistory(retentionDays);
    }

    @Operation(summary = "执行自动响应", description = "执行报警的自动响应动作")
    @PostMapping("/{alarmId}/auto-response")
    public Result<Alarm.Response> executeAutoResponse(
            @Parameter(description = "报警ID") @PathVariable Long alarmId,
            @Parameter(description = "规则ID") @RequestParam Long ruleId) {
        
        return alarmService.executeAutoResponse(alarmId, ruleId);
    }
}