const app = getApp();
const api = require('../../../utils/api.js');

Page({
  data: {
    // 视图模式
    viewMode: 'calendar', // calendar 或 list
    
    // 日历相关
    currentMonth: '',
    currentYear: 2024,
    currentMonthIndex: 0,
    weekDays: ['日', '一', '二', '三', '四', '五', '六'],
    calendarDays: [],
    selectedDate: '',
    selectedDateText: '',
    
    // 当天计划
    daySchedules: [],
    
    // 所有计划
    allSchedules: [],
    
    // 快速模板
    scheduleTemplates: [],
    
    // 区域选项
    zoneOptions: [],
    
    // 加载状态
    loading: false,
    refreshing: false
  },

  onLoad() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.initCalendar();
    this.initData();
  },

  onShow() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return;
    }
    
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  // 初始化数据
  async initData() {
    this.setData({ loading: true });
    
    try {
      await Promise.all([
        this.loadScheduleData(),
        this.loadScheduleTemplates(),
        this.loadZoneOptions()
      ]);
    } catch (error) {
      console.error('初始化数据失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 刷新数据
  async refreshData() {
    this.setData({ refreshing: true });
    
    try {
      await Promise.all([
        this.loadScheduleData(),
        this.loadCalendarData()
      ]);
    } catch (error) {
      console.error('刷新数据失败:', error);
    } finally {
      this.setData({ refreshing: false });
    }
  },

  // 初始化日历
  initCalendar() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();
    
    this.setData({
      currentYear: year,
      currentMonthIndex: month
    });
    
    this.generateCalendar(year, month);
  },

  // 生成日历数据
  generateCalendar(year, month) {
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDay = firstDay.getDay(); // 月初是星期几
    const daysInMonth = lastDay.getDate();
    
    const monthNames = [
      '一月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '十一月', '十二月'
    ];
    
    const calendarDays = [];
    
    // 填充上个月的尾部日期
    const prevMonth = month === 0 ? 11 : month - 1;
    const prevYear = month === 0 ? year - 1 : year;
    const prevLastDay = new Date(prevYear, prevMonth + 1, 0).getDate();
    
    for (let i = startDay - 1; i >= 0; i--) {
      calendarDays.push({
        date: `${prevYear}-${String(prevMonth + 1).padStart(2, '0')}-${String(prevLastDay - i).padStart(2, '0')}`,
        day: prevLastDay - i,
        class: 'prev-month',
        schedules: []
      });
    }
    
    // 填充当前月的日期
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const schedules = this.getSchedulesForDate(dateStr);
      
      calendarDays.push({
        date: dateStr,
        day: day,
        class: this.getDateClass(year, month, day),
        schedules: schedules
      });
    }
    
    // 填充下个月的开始日期
    const nextMonth = month === 11 ? 0 : month + 1;
    const nextYear = month === 11 ? year + 1 : year;
    const remainingCells = 42 - calendarDays.length; // 6周 * 7天 = 42个单元格
    
    for (let day = 1; day <= remainingCells; day++) {
      calendarDays.push({
        date: `${nextYear}-${String(nextMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
        day: day,
        class: 'next-month',
        schedules: []
      });
    }
    
    this.setData({
      currentMonth: `${year}年${monthNames[month]}`,
      calendarDays: calendarDays
    });
  },

  // 获取日期的样式类
  getDateClass(year, month, day) {
    const now = new Date();
    const today = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    
    if (dateStr === today) {
      return 'today';
    }
    if (dateStr === this.data.selectedDate) {
      return 'selected';
    }
    return 'current-month';
  },

  // 获取指定日期的计划
  getSchedulesForDate(dateStr) {
    // 这里应该根据实际的计划规则来计算
    // 简化处理，随机生成一些计划
    const schedules = [];
    
    this.data.allSchedules.forEach(schedule => {
      if (schedule.enabled && this.isScheduleActiveOnDate(schedule, dateStr)) {
        schedule.timeSlots.forEach(slot => {
          if (slot.enabled) {
            schedules.push({
              id: `${schedule.id}-${slot.id}`,
              type: 'irrigation',
              name: schedule.name,
              time: slot.startTime
            });
          }
        });
      }
    });
    
    return schedules;
  },

  // 判断计划是否在指定日期激活
  isScheduleActiveOnDate(schedule, dateStr) {
    const date = new Date(dateStr);
    const dayOfWeek = date.getDay();
    
    // 检查当前星期几是否在选中的自定义星期中
    const selectedWeekday = schedule.customWeekdays.find(weekday => weekday.day === dayOfWeek);
    return selectedWeekday && selectedWeekday.selected;
  },

  // 加载计划数据
  async loadScheduleData() {
    try {
      const response = await api.getSchedules({});
      
      if (response.code === 200) {
        const schedules = response.data.schedules || [];
        
        // 处理调度数据格式
        const processedSchedules = schedules.map(schedule => ({
          ...schedule,
          timeDisplay: schedule.timeSlots ? schedule.timeSlots.map(slot =>
            `${slot.startTime}-${slot.endTime}`
          ).join(', ') : '--:--'
        }));
        
        this.setData({
          allSchedules: processedSchedules
        });
        
        // 重新生成日历数据
        this.generateCalendar(this.data.currentYear, this.data.currentMonthIndex);
      }
    } catch (error) {
      console.error('加载计划数据失败:', error);
      wx.showToast({
        title: '加载计划失败',
        icon: 'none'
      });
    }
  },

  // 加载日历数据
  async loadCalendarData() {
    try {
      const response = await api.getCalendarData(this.data.currentYear, this.data.currentMonthIndex);
      
      if (response.code === 200) {
        const calendarData = response.data.calendarData || {};
        
        // 更新日历显示
        const calendarDays = this.data.calendarDays.map(day => ({
          ...day,
          schedules: calendarData[day.date] || []
        }));
        
        this.setData({ calendarDays });
      }
    } catch (error) {
      console.error('加载日历数据失败:', error);
    }
  },

  // 加载区域选项
  async loadZoneOptions() {
    try {
      // 从地块API获取区域信息
      const response = await api.getPlots('farm_001');
      
      if (response.code === 200) {
        const plots = response.data.plots || [];
        const zoneOptions = [];
        
        plots.forEach((plot, index) => {
          zoneOptions.push({
            id: plot.id,
            name: plot.name,
            plotId: plot.id
          });
        });
        
        this.setData({ zoneOptions });
      }
    } catch (error) {
      console.error('加载区域选项失败:', error);
      // 使用默认区域选项
      this.setData({
        zoneOptions: [
          { id: 'zone_001', name: '1区' },
          { id: 'zone_002', name: '2区' },
          { id: 'zone_003', name: '3区' }
        ]
      });
    }
  },

  // 切换视图模式
  switchViewMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({
      viewMode: mode
    });
  },

  // 上一个月
  previousMonth() {
    let { currentYear, currentMonthIndex } = this.data;
    currentMonthIndex--;
    if (currentMonthIndex < 0) {
      currentMonthIndex = 11;
      currentYear--;
    }
    this.setData({
      currentYear: currentYear,
      currentMonthIndex: currentMonthIndex
    });
    this.generateCalendar(currentYear, currentMonthIndex);
  },

  // 下一个月
  nextMonth() {
    let { currentYear, currentMonthIndex } = this.data;
    currentMonthIndex++;
    if (currentMonthIndex > 11) {
      currentMonthIndex = 0;
      currentYear++;
    }
    this.setData({
      currentYear: currentYear,
      currentMonthIndex: currentMonthIndex
    });
    this.generateCalendar(currentYear, currentMonthIndex);
  },

  // 选择日期
  selectDate(e) {
    const date = e.currentTarget.dataset.date;
    const dateObj = new Date(date);
    const dateText = `${dateObj.getFullYear()}年${dateObj.getMonth() + 1}月${dateObj.getDate()}日`;
    
    this.setData({
      selectedDate: date,
      selectedDateText: dateText
    });
    
    // 重新生成日历以更新选中状态
    this.generateCalendar(this.data.currentYear, this.data.currentMonthIndex);
    
    // 加载当天的计划
    this.loadDaySchedules(date);
  },

  // 加载当天计划
  async loadDaySchedules(date) {
    try {
      const response = await api.getDaySchedules(date);
      
      if (response.code === 200) {
        const scheduleData = response.data;
        const daySchedules = scheduleData.schedules || [];
        
        // 处理当天计划格式
        const processedSchedules = daySchedules.map(schedule => ({
          id: schedule.id,
          startTime: schedule.startTime,
          endTime: schedule.endTime,
          duration: schedule.duration,
          flowRate: schedule.flowRate,
          zones: Array.isArray(schedule.zones) ? schedule.zones.join(', ') : schedule.zones,
          enabled: schedule.enabled,
          repeatText: schedule.weekdaysText || '单次执行',
          scheduleName: schedule.scheduleName,
          type: schedule.type
        }));
        
        this.setData({
          daySchedules: processedSchedules
        });
      }
    } catch (error) {
      console.error('加载当天计划失败:', error);
      wx.showToast({
        title: '加载当天计划失败',
        icon: 'none'
      });
    }
  },

  // 添加当天计划
  addDaySchedule() {
    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'none'
      });
      return;
    }
    
    this.showAddScheduleModal();
  },

  // 跳转到新增计划页面
  showAddScheduleModal() {
    wx.navigateTo({
      url: '/pages/device/schedule/add/add'
    });
  },

  // 关闭弹窗
  closeModal() {
    this.setData({
      showModal: false
    });
  },

  // 阻止弹窗关闭
  preventClose() {
    // 空方法，阻止冒泡
  },

  // 表单输入
  onFormInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`editForm.${field}`]: value
    });
  },

  // 切换工作日
  toggleWeekday(e) {
    const day = e.currentTarget.dataset.day;
    const customWeekdays = this.data.editForm.customWeekdays.map(item => {
      if (item.day === day) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });
    
    // 更新weekdaysText显示
    const weekdaysText = this.generateWeekdaysText(customWeekdays);
    
    this.setData({
      'editForm.customWeekdays': customWeekdays,
      'editForm.weekdaysText': weekdaysText
    });
  },

  // 生成星期显示文本
  generateWeekdaysText(customWeekdays) {
    const selectedDays = customWeekdays.filter(item => item.selected);
    
    if (selectedDays.length === 7) {
      return '每天';
    }
    
    if (selectedDays.length === 5 && 
        selectedDays.every(day => day.day >= 1 && day.day <= 5)) {
      return '工作日';
    }
    
    if (selectedDays.length === 2 && 
        selectedDays.some(day => day.day === 0) && 
        selectedDays.some(day => day.day === 6)) {
      return '周末';
    }
    
    // 其他情况显示具体星期
    return selectedDays.map(day => `周${day.label}`).join('、');
  },

  // 添加时间段
  addTimeSlot() {
    const newSlot = {
      tempId: Date.now(),
      startTime: '07:00',
      endTime: '07:30',
      duration: 30,
      flowRate: 60,
      zoneOptions: this.data.zoneOptions.map(zone => ({ ...zone, selected: false }))
    };
    
    const timeSlots = [...this.data.editForm.timeSlots, newSlot];
    this.setData({
      'editForm.timeSlots': timeSlots
    });
  },

  // 删除时间段
  removeTimeSlot(e) {
    const index = e.currentTarget.dataset.index;
    const timeSlots = this.data.editForm.timeSlots.filter((_, i) => i !== index);
    
    this.setData({
      'editForm.timeSlots': timeSlots
    });
  },

  // 时间段时间改变
  onTimeSlotChange(e) {
    const index = e.currentTarget.dataset.index;
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    const timeSlots = this.data.editForm.timeSlots.map((slot, i) => {
      if (i === index) {
        return { ...slot, [field]: value };
      }
      return slot;
    });
    
    this.setData({
      'editForm.timeSlots': timeSlots
    });
  },

  // 时间段参数输入
  onTimeSlotInput(e) {
    const index = e.currentTarget.dataset.index;
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    const timeSlots = this.data.editForm.timeSlots.map((slot, i) => {
      if (i === index) {
        return { ...slot, [field]: parseInt(value) || 0 };
      }
      return slot;
    });
    
    this.setData({
      'editForm.timeSlots': timeSlots
    });
  },

  // 切换时间段区域
  toggleSlotZone(e) {
    const slotIndex = e.currentTarget.dataset.slot;
    const zoneId = e.currentTarget.dataset.zone;
    
    const timeSlots = this.data.editForm.timeSlots.map((slot, i) => {
      if (i === slotIndex) {
        const zoneOptions = slot.zoneOptions.map(zone => {
          if (zone.id === zoneId) {
            return { ...zone, selected: !zone.selected };
          }
          return zone;
        });
        return { ...slot, zoneOptions };
      }
      return slot;
    });
    
    this.setData({
      'editForm.timeSlots': timeSlots
    });
  },


  // 保存计划
  saveSchedule() {
    const form = this.data.editForm;
    
    if (!form.name) {
      wx.showToast({
        title: '请输入计划名称',
        icon: 'none'
      });
      return;
    }
    
    if (form.timeSlots.length === 0) {
      wx.showToast({
        title: '请添加至少一个时间段',
        icon: 'none'
      });
      return;
    }
    
    // 验证时间段
    for (let slot of form.timeSlots) {
      if (!slot.startTime || !slot.endTime || !slot.duration || !slot.flowRate) {
        wx.showToast({
          title: '请完善时间段信息',
          icon: 'none'
        });
        return;
      }
    }
    
    // 模拟保存到服务器
    console.log('保存计划:', form);
    
    wx.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    this.closeModal();
    this.loadScheduleData();
  },

  // 应用模板
  applyTemplate(e) {
    const templateId = e.currentTarget.dataset.template;
    const template = this.data.scheduleTemplates.find(t => t.id === templateId);
    
    if (template) {
      // 跳转到新增页面并传递模板ID
      wx.navigateTo({
        url: `/pages/device/schedule/add/add?template=${templateId}`
      });
    }
  },

  // 切换计划开关
  toggleSchedule(e) {
    const id = e.currentTarget.dataset.id;
    const checked = e.detail.value;
    
    this.toggleScheduleAPI(id, checked);
  },

  // 编辑计划
  editSchedule(e) {
    const id = e.currentTarget.dataset.id;
    const schedule = this.data.allSchedules.find(s => s.id === id);
    
    if (schedule) {
      // 将编辑的计划数据传递到编辑页面
      wx.navigateTo({
        url: `/pages/device/schedule/add/add?id=${id}&mode=edit`
      });
    }
  },

  // 复制计划
  duplicateSchedule(e) {
    const id = e.currentTarget.dataset.id;
    // 实现复制逻辑
    console.log('复制计划:', id);
  },

  // 删除计划
  deleteSchedule(e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个计划吗？',
      success: (res) => {
        if (res.confirm) {
          this.deleteScheduleAPI(id);
        }
      }
    });
  },

  // 切换时间段开关
  toggleTimeSlot(e) {
    const id = e.currentTarget.dataset.id;
    const checked = e.detail.value;
    
    // 更新时间段状态
    console.log('切换时间段:', id, checked);
  },

  // 编辑时间段
  editTimeSlot(e) {
    const id = e.currentTarget.dataset.id;
    
    // 解析时间段ID，格式通常是 "scheduleId-slotId"
    const parts = id.split('-');
    if (parts.length === 2) {
      const scheduleId = parseInt(parts[0]);
      const slotId = parseInt(parts[1]);
      
      // 跳转到编辑页面，传递计划ID
      wx.navigateTo({
        url: `/pages/device/schedule/add/add?id=${scheduleId}&mode=edit&slotId=${slotId}`
      });
    }
  },

  // 删除时间段
  deleteTimeSlot(e) {
    const id = e.currentTarget.dataset.id;
    // 实现删除时间段逻辑
    console.log('删除时间段:', id);
  },


  // 格式化执行天数显示
  formatExecutionDays(executionDays) {
    const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    
    if (executionDays.length === 7) {
      return '每天';
    }
    
    if (executionDays.length === 5 && executionDays.includes(1) && executionDays.includes(2) && 
        executionDays.includes(3) && executionDays.includes(4) && executionDays.includes(5)) {
      return '工作日';
    }
    
    if (executionDays.length === 2 && executionDays.includes(0) && executionDays.includes(6)) {
      return '周末';
    }
    
    // 其他情况显示具体星期
    return executionDays.map(day => dayNames[day]).join('、');
  },

  // API相关方法
  async loadScheduleTemplates() {
    try {
      const response = await api.getScheduleTemplates();
      
      if (response.code === 200) {
        const templates = response.data.templates || [];
        
        // 处理模板数据格式
        const processedTemplates = templates.map(template => ({
          ...template,
          timeSlots: template.timeSlots || []
        }));
        
        this.setData({
          scheduleTemplates: processedTemplates
        });
      }
    } catch (error) {
      console.error('加载调度模板失败:', error);
      // 使用默认模板
      this.setDefaultTemplates();
    }
  },

  // 设置默认模板
  setDefaultTemplates() {
    const defaultTemplates = [
      {
        id: 'morning',
        name: '晨间灌溉',
        icon: '🌅',
        description: '早晨7:00-7:30，适合大部分作物',
        category: 'irrigation',
        timeSlots: [{
          startTime: '07:00',
          endTime: '07:30',
          duration: 30,
          flowRate: 60
        }]
      },
      {
        id: 'evening',
        name: '傍晚灌溉',
        icon: '🌇',
        description: '傍晚18:00-18:30，避开高温时段',
        category: 'irrigation',
        timeSlots: [{
          startTime: '18:00',
          endTime: '18:30',
          duration: 30,
          flowRate: 50
        }]
      },
      {
        id: 'fertilization',
        name: '施肥计划',
        icon: '🌱',
        description: '每周三次施肥，促进生长',
        category: 'fertilization',
        timeSlots: [{
          startTime: '10:00',
          endTime: '10:25',
          duration: 25,
          flowRate: 45
        }]
      }
    ];
    
    this.setData({
      scheduleTemplates: defaultTemplates
    });
  },

  async deleteScheduleAPI(scheduleId) {
    try {
      wx.showLoading({ title: '删除中...' });
      
      const response = await api.deleteSchedule(scheduleId);
      
      if (response.code === 200) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        // 重新加载数据
        this.loadScheduleData();
      }
    } catch (error) {
      console.error('删除计划失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  async toggleScheduleAPI(scheduleId, enabled) {
    try {
      const response = await api.toggleSchedule(scheduleId, enabled);
      
      if (response.code === 200) {
        wx.showToast({
          title: enabled ? '计划已启用' : '计划已禁用',
          icon: 'success'
        });
        
        // 重新加载数据
        this.loadScheduleData();
      }
    } catch (error) {
      console.error('切换计划状态失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  }

})