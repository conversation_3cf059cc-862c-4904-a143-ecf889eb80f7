package com.agriculture.iot.controller;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.SensorData;
import com.agriculture.iot.entity.SensorType;
import com.agriculture.iot.service.SensorService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 传感器数据控制器
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Tag(name = "传感器数据管理", description = "传感器数据的采集、查询和统计分析")
@RestController
@RequestMapping("/api/sensors")
@RequiredArgsConstructor
public class SensorController {

    private final SensorService sensorService;

    @Operation(summary = "获取传感器数据列表", description = "分页获取传感器数据，支持时间范围查询")
    @GetMapping("/data")
    public Result<IPage<SensorData>> getSensorDataList(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "传感器类型ID") @RequestParam(required = false) Long sensorTypeId,
            @Parameter(description = "开始时间") @RequestParam(required = false) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) LocalDateTime endTime) {
        
        Page<SensorData> page = new Page<>(current, size);
        IPage<SensorData> result = sensorService.getSensorDataList(page, deviceId, plotId, sensorTypeId, startTime, endTime);
        return Result.success(result);
    }

    @Operation(summary = "获取最新传感器数据", description = "获取指定设备或地块的最新传感器数据")
    @GetMapping("/data/latest")
    public Result<List<SensorData>> getLatestSensorData(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId) {
        
        List<SensorData> result = sensorService.getLatestSensorData(deviceId, plotId);
        return Result.success(result);
    }

    @Operation(summary = "获取历史传感器数据", description = "获取指定时间范围内的历史数据")
    @GetMapping("/data/history")
    public Result<List<SensorData>> getHistorySensorData(
            @Parameter(description = "设备ID") @RequestParam Long deviceId,
            @Parameter(description = "传感器类型") @RequestParam(required = false) String sensorType,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime,
            @Parameter(description = "数据间隔（分钟）") @RequestParam(defaultValue = "60") Integer interval) {
        
        List<SensorData> result = sensorService.getHistorySensorData(deviceId, sensorType, startTime, endTime, interval);
        return Result.success(result);
    }

    @Operation(summary = "获取传感器数据统计", description = "获取传感器数据的统计信息")
    @GetMapping("/data/statistics")
    public Result<SensorData.Statistics> getSensorDataStatistics(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "传感器类型") @RequestParam(required = false) String sensorType,
            @Parameter(description = "统计周期") @RequestParam(defaultValue = "7") Integer days) {
        
        SensorData.Statistics statistics = sensorService.getSensorDataStatistics(deviceId, plotId, sensorType, days);
        return Result.success(statistics);
    }

    @Operation(summary = "上传传感器数据", description = "IoT设备推送传感器数据")
    @PostMapping("/data")
    public Result<SensorData> uploadSensorData(@Valid @RequestBody SensorData sensorData) {
        SensorData savedData = sensorService.uploadSensorData(sensorData);
        if (savedData == null) {
            return Result.error("数据上传失败");
        }
        return Result.success("数据上传成功", savedData);
    }

    @Operation(summary = "批量上传传感器数据", description = "批量上传多条传感器数据")
    @PostMapping("/data/batch")
    public Result<Void> batchUploadSensorData(@Valid @RequestBody List<SensorData> sensorDataList) {
        boolean success = sensorService.batchUploadSensorData(sensorDataList);
        if (!success) {
            return Result.error("批量数据上传失败");
        }
        return Result.success("批量数据上传成功", null);
    }

    @Operation(summary = "获取指定设备的传感器数据", description = "获取指定设备的所有类型传感器数据")
    @GetMapping("/data/device/{deviceId}")
    public Result<Map<String, Object>> getDeviceSensorData(
            @Parameter(description = "设备ID") @PathVariable Long deviceId,
            @Parameter(description = "时间范围（小时）") @RequestParam(defaultValue = "24") Integer hours) {
        
        Map<String, Object> result = sensorService.getDeviceSensorData(deviceId, hours);
        return Result.success(result);
    }

    @Operation(summary = "获取指定地块的传感器数据", description = "获取指定地块所有设备的传感器数据")
    @GetMapping("/data/plot/{plotId}")
    public Result<Map<String, Object>> getPlotSensorData(
            @Parameter(description = "地块ID") @PathVariable Long plotId,
            @Parameter(description = "时间范围（小时）") @RequestParam(defaultValue = "24") Integer hours) {
        
        Map<String, Object> result = sensorService.getPlotSensorData(plotId, hours);
        return Result.success(result);
    }

    @Operation(summary = "获取传感器类型列表", description = "获取所有传感器类型")
    @GetMapping("/types")
    public Result<List<SensorType>> getSensorTypes() {
        List<SensorType> sensorTypes = sensorService.getSensorTypes();
        return Result.success(sensorTypes);
    }

    @Operation(summary = "获取传感器数据趋势", description = "获取传感器数据的趋势分析")
    @GetMapping("/data/trend")
    public Result<SensorData.TrendData> getSensorDataTrend(
            @Parameter(description = "设备ID") @RequestParam Long deviceId,
            @Parameter(description = "传感器类型") @RequestParam String sensorType,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime,
            @Parameter(description = "趋势类型") @RequestParam(defaultValue = "hourly") String trendType) {
        
        SensorData.TrendData trendData = sensorService.getSensorDataTrend(deviceId, sensorType, startTime, endTime, trendType);
        return Result.success(trendData);
    }

    @Operation(summary = "获取传感器数据对比", description = "对比不同设备或时间段的传感器数据")
    @GetMapping("/data/compare")
    public Result<SensorData.CompareData> compareSensorData(
            @Parameter(description = "设备ID列表") @RequestParam List<Long> deviceIds,
            @Parameter(description = "传感器类型") @RequestParam String sensorType,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        
        SensorData.CompareData compareData = sensorService.compareSensorData(deviceIds, sensorType, startTime, endTime);
        return Result.success(compareData);
    }

    @Operation(summary = "获取传感器数据报表", description = "生成传感器数据报表")
    @GetMapping("/data/report")
    public Result<SensorData.ReportData> getSensorDataReport(
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "设备ID列表") @RequestParam(required = false) List<Long> deviceIds,
            @Parameter(description = "报表类型") @RequestParam(defaultValue = "daily") String reportType,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        
        SensorData.ReportData reportData = sensorService.getSensorDataReport(plotId, deviceIds, reportType, startTime, endTime);
        return Result.success(reportData);
    }

    @Operation(summary = "导出传感器数据", description = "导出传感器数据为Excel文件")
    @GetMapping("/data/export")
    public Result<String> exportSensorData(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime,
            @Parameter(description = "导出格式") @RequestParam(defaultValue = "excel") String format) {
        
        String exportUrl = sensorService.exportSensorData(deviceId, plotId, startTime, endTime, format);
        return Result.success("数据导出成功", exportUrl);
    }

    @Operation(summary = "获取传感器预警信息", description = "获取传感器数据预警信息")
    @GetMapping("/data/alerts")
    public Result<List<SensorData.AlertInfo>> getSensorAlerts(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "预警级别") @RequestParam(required = false) String alertLevel) {
        
        List<SensorData.AlertInfo> alerts = sensorService.getSensorAlerts(deviceId, plotId, alertLevel);
        return Result.success(alerts);
    }

    @Operation(summary = "设置传感器阈值", description = "设置传感器数据阈值")
    @PostMapping("/thresholds")
    public Result<Void> setSensorThreshold(@Valid @RequestBody SensorData.ThresholdConfig thresholdConfig) {
        boolean success = sensorService.setSensorThreshold(thresholdConfig);
        if (!success) {
            return Result.error("阈值设置失败");
        }
        return Result.success("阈值设置成功", null);
    }

    @Operation(summary = "获取传感器阈值配置", description = "获取传感器阈值配置")
    @GetMapping("/thresholds")
    public Result<List<SensorData.ThresholdConfig>> getSensorThresholds(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "传感器类型") @RequestParam(required = false) String sensorType) {
        
        List<SensorData.ThresholdConfig> thresholds = sensorService.getSensorThresholds(deviceId, sensorType);
        return Result.success(thresholds);
    }
}