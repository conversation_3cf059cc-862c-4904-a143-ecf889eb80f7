/**
 * 认证相关Mock API
 * 包含登录、注册、验证等功能
 */

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 生成Token
function generateToken(userId) {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };
  
  const payload = {
    userId: userId,
    exp: Math.floor(Date.now() / 1000) + (2 * 60 * 60), // 2小时后过期
    iat: Math.floor(Date.now() / 1000)
  };
  
  // 微信小程序环境使用的Base64编码
  const encodedHeader = wx.arrayBufferToBase64(new TextEncoder().encode(JSON.stringify(header)));
  const encodedPayload = wx.arrayBufferToBase64(new TextEncoder().encode(JSON.stringify(payload)));
  const signature = wx.arrayBufferToBase64(new TextEncoder().encode('mock_signature_' + userId + '_' + payload.exp));
  
  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// 生成刷新Token
function generateRefreshToken(userId) {
  const payload = {
    userId: userId,
    type: 'refresh',
    exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60), // 30天后过期
    iat: Math.floor(Date.now() / 1000)
  };
  
  return wx.arrayBufferToBase64(new TextEncoder().encode(JSON.stringify(payload)));
}

// 模拟用户数据
const users = [
  {
    id: 'user_001',
    openid: 'openid_123456',
    unionId: 'unionid_123456',
    phone: '13800138000',
    nickname: '张三',
    avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4uBdhjwwdY1Q3L1JM8ibVN5iarJwzGtDc1Tg2W0s2f/132',
    email: '<EMAIL>',
    realName: '张三',
    farmIds: ['farm_001'],
    role: 'farmer',
    permissions: ['device_control', 'data_view', 'schedule_manage'],
    status: 1,
    lastLoginTime: new Date().toISOString(),
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: new Date().toISOString()
  }
];

// 模拟短信验证码存储
const verificationCodes = new Map();

module.exports = {
  init() {
    console.log('认证模块初始化完成');
  },

  // 微信登录
  async login(data) {
    const { code, userInfo } = data;
    
    if (!code) {
      return createErrorResponse('缺少授权码', 400);
    }

    // 模拟微信授权验证
    if (code === 'mock_code_fail') {
      return createErrorResponse('微信授权失败', 400);
    }

    // 查找或创建用户
    let user = users.find(u => u.openid === 'openid_123456');
    if (!user) {
      user = {
        id: `user_${Date.now()}`,
        openid: 'openid_123456',
        unionId: 'unionid_123456',
        phone: null,
        nickname: userInfo?.nickName || '新用户',
        avatarUrl: userInfo?.avatarUrl || '',
        email: null,
        realName: null,
        farmIds: [],
        role: 'farmer',
        permissions: ['device_control', 'data_view'],
        status: 1,
        lastLoginTime: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      users.push(user);
    } else {
      // 更新用户信息
      user.lastLoginTime = new Date().toISOString();
      user.updatedAt = new Date().toISOString();
      if (userInfo) {
        user.nickname = userInfo.nickName || user.nickname;
        user.avatarUrl = userInfo.avatarUrl || user.avatarUrl;
      }
    }

    const token = generateToken(user.id);
    const refreshToken = generateRefreshToken(user.id);

    return createApiResponse({
      token,
      refreshToken,
      user: {
        id: user.id,
        nickname: user.nickname,
        avatarUrl: user.avatarUrl,
        phone: user.phone,
        email: user.email,
        realName: user.realName,
        role: user.role,
        permissions: user.permissions,
        farmIds: user.farmIds
      },
      isNewUser: user.createdAt === user.updatedAt
    }, '登录成功');
  },

  // 手机号登录
  async phoneLogin(data) {
    const { phone, code } = data;
    
    if (!phone || !code) {
      return createErrorResponse('手机号和验证码不能为空', 400);
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return createErrorResponse('手机号格式不正确', 400);
    }

    // 验证验证码
    const storedCode = verificationCodes.get(phone);
    if (!storedCode || storedCode.code !== code) {
      return createErrorResponse('验证码错误', 400);
    }

    // 检查验证码是否过期
    if (Date.now() > storedCode.expiry) {
      verificationCodes.delete(phone);
      return createErrorResponse('验证码已过期', 400);
    }

    // 清除验证码
    verificationCodes.delete(phone);

    // 查找或创建用户
    let user = users.find(u => u.phone === phone);
    if (!user) {
      user = {
        id: `user_${Date.now()}`,
        openid: null,
        unionId: null,
        phone,
        nickname: `用户${phone.slice(-4)}`,
        avatarUrl: '',
        email: null,
        realName: null,
        farmIds: [],
        role: 'farmer',
        permissions: ['device_control', 'data_view'],
        status: 1,
        lastLoginTime: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      users.push(user);
    } else {
      user.lastLoginTime = new Date().toISOString();
      user.updatedAt = new Date().toISOString();
    }

    const token = generateToken(user.id);
    const refreshToken = generateRefreshToken(user.id);

    return createApiResponse({
      token,
      refreshToken,
      user: {
        id: user.id,
        nickname: user.nickname,
        avatarUrl: user.avatarUrl,
        phone: user.phone,
        email: user.email,
        realName: user.realName,
        role: user.role,
        permissions: user.permissions,
        farmIds: user.farmIds
      },
      isNewUser: user.createdAt === user.updatedAt
    }, '登录成功');
  },

  // 发送验证码
  async sendCode(data) {
    const { phone } = data;
    
    if (!phone) {
      return createErrorResponse('手机号不能为空', 400);
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return createErrorResponse('手机号格式不正确', 400);
    }

    // 检查发送频率限制
    const lastSent = verificationCodes.get(phone);
    if (lastSent && Date.now() - lastSent.sentTime < 60000) {
      return createErrorResponse('请勿频繁发送验证码', 429);
    }

    // 生成6位验证码
    const code = Math.floor(Math.random() * 900000) + 100000;
    const expiry = Date.now() + 5 * 60 * 1000; // 5分钟后过期
    const sentTime = Date.now();

    // 存储验证码
    verificationCodes.set(phone, {
      code: code.toString(),
      expiry,
      sentTime
    });

    // 模拟发送短信
    console.log(`发送验证码到 ${phone}: ${code}`);

    return createApiResponse({
      phone,
      expiry: Math.floor(expiry / 1000),
      cooldown: 60 // 秒
    }, '验证码发送成功');
  },

  // 刷新Token
  async refreshToken(refreshToken) {
    if (!refreshToken) {
      return createErrorResponse('缺少刷新令牌', 400);
    }

    try {
      // 使用微信小程序兼容的base64解码
      const uint8Array = wx.base64ToArrayBuffer(refreshToken);
      const decodedString = new TextDecoder().decode(uint8Array);
      const payload = JSON.parse(decodedString);
      
      if (payload.exp < Math.floor(Date.now() / 1000)) {
        return createErrorResponse('刷新令牌已过期', 401);
      }

      if (payload.type !== 'refresh') {
        return createErrorResponse('无效的刷新令牌', 401);
      }

      const user = users.find(u => u.id === payload.userId);
      if (!user) {
        return createErrorResponse('用户不存在', 404);
      }

      const newToken = generateToken(user.id);
      const newRefreshToken = generateRefreshToken(user.id);

      return createApiResponse({
        token: newToken,
        refreshToken: newRefreshToken,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatarUrl: user.avatarUrl,
          phone: user.phone,
          email: user.email,
          realName: user.realName,
          role: user.role,
          permissions: user.permissions,
          farmIds: user.farmIds
        }
      }, '令牌刷新成功');
    } catch (error) {
      return createErrorResponse('无效的刷新令牌', 401);
    }
  },

  // 登出
  async logout(token) {
    // 在实际应用中，这里应该将token添加到黑名单
    // 在mock环境中，我们只是返回成功
    return createApiResponse(null, '登出成功');
  },

  // 获取用户列表（内部使用）
  getUsers() {
    return users;
  },

  // 根据ID获取用户（内部使用）
  getUserById(userId) {
    return users.find(u => u.id === userId);
  },

  // 根据手机号获取用户（内部使用）
  getUserByPhone(phone) {
    return users.find(u => u.phone === phone);
  },

  // 根据openid获取用户（内部使用）
  getUserByOpenid(openid) {
    return users.find(u => u.openid === openid);
  }
};