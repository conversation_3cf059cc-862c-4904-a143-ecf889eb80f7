<!--意见反馈页面-->
<view class="page-container">
  <view class="form-container">
    
    <!-- 反馈类型选择 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">反馈类型</text>
        <text class="required">*</text>
      </view>
      
      <view class="type-selector">
        <view 
          wx:for="{{feedbackTypes}}" 
          wx:key="value"
          class="type-item {{feedbackType === item.value ? 'selected' : ''}}"
          bindtap="selectType"
          data-type="{{item.value}}"
        >
          <view class="type-icon">{{item.icon}}</view>
          <text class="type-label">{{item.label}}</text>
        </view>
      </view>
    </view>

    <!-- 反馈标题 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">反馈标题</text>
        <text class="required">*</text>
      </view>
      
      <view class="input-group">
        <input 
          class="form-input"
          placeholder="请简要描述问题或建议"
          value="{{title}}"
          bindinput="onTitleInput"
          maxlength="50"
        />
        <view class="input-counter">{{title.length}}/50</view>
      </view>
    </view>

    <!-- 详细内容 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">详细内容</text>
        <text class="required">*</text>
      </view>
      
      <view class="textarea-group">
        <textarea 
          class="form-textarea"
          placeholder="请详细描述遇到的问题或您的建议，包括操作步骤、期望结果等"
          value="{{content}}"
          bindinput="onContentInput"
          maxlength="500"
          auto-height
          show-confirm-bar="{{false}}"
        ></textarea>
        <view class="textarea-counter">{{content.length}}/500</view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">联系方式</text>
        <text class="optional">（选填）</text>
      </view>
      
      <view class="input-group">
        <input 
          class="form-input"
          placeholder="手机号或邮箱，便于我们联系您"
          value="{{contact}}"
          bindinput="onContactInput"
          maxlength="50"
        />
      </view>
      
      <view class="form-tip">
        <text class="tip-text">💡 留下联系方式，我们可以及时回复处理进展</text>
      </view>
    </view>

    <!-- 图片上传 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">相关截图</text>
        <text class="optional">（选填）</text>
      </view>
      
      <view class="image-upload">
        <view class="image-list">
          <view 
            wx:for="{{images}}" 
            wx:key="*this"
            class="image-item"
          >
            <image 
              class="upload-image" 
              src="{{item}}" 
              mode="aspectFill"
              bindtap="previewImage"
              data-current="{{item}}"
            ></image>
            <view 
              class="image-remove" 
              bindtap="removeImage"
              data-index="{{index}}"
            >
              <text class="remove-icon">×</text>
            </view>
          </view>
          
          <view 
            wx:if="{{images.length < maxImages}}"
            class="image-add" 
            bindtap="chooseImage"
          >
            <view class="add-icon">📷</view>
            <text class="add-text">添加图片</text>
          </view>
        </view>
        
        <view class="upload-tip">
          <text class="tip-text">最多上传{{maxImages}}张图片，支持截图和拍照</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <view class="action-buttons">
        <button class="btn btn-secondary" bindtap="saveDraft">
          💾 保存草稿
        </button>
        <button class="btn btn-secondary" bindtap="loadDraft">
          📄 加载草稿
        </button>
      </view>
      
      <button 
        class="btn btn-primary btn-large" 
        bindtap="submitFeedback"
        disabled="{{submitting}}"
      >
        {{submitting ? '提交中...' : '📤 提交反馈'}}
      </button>
    </view>

  </view>

  <!-- 反馈提示 -->
  <view class="feedback-tips">
    <view class="tips-card">
      <view class="tips-header">
        <view class="tips-icon">💡</view>
        <text class="tips-title">反馈小贴士</text>
      </view>
      
      <view class="tips-content">
        <view class="tip-item">
          <text class="tip-bullet">•</text>
          <text class="tip-text">详细描述问题有助于我们快速定位和解决</text>
        </view>
        <view class="tip-item">
          <text class="tip-bullet">•</text>
          <text class="tip-text">提供截图可以更直观地展示问题</text>
        </view>
        <view class="tip-item">
          <text class="tip-bullet">•</text>
          <text class="tip-text">我们会在1-3个工作日内回复您的反馈</text>
        </view>
      </view>
    </view>
  </view>
</view>