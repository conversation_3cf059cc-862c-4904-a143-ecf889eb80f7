// 导入腾讯地图SDK和配置
const QQMapWX = require('../../../libs/qqmap-wx-jssdk.js');
const MAP_CONFIG = require('../../../utils/tencent-map-config.js');

Page({
  data: {
    mode: 'view', // view, add, edit
    plotId: null,
    
    plotData: {
      name: '',
      area: '',
      location: '',
      coordinates: '',
      gpsLongitude: '',
      gpsLatitude: '',
      plotType: '',
      irrigationType: '',
      slope: '',
      description: '',
      polygonPath: [], // 地块多边形坐标点
      currentCrop: {
        name: '',
        plantDate: '',
        harvestDate: '',
        progress: 0
      }
    },
    
    // 地块类型选项
    plotTypes: [
      { value: 'greenhouse', label: '大棚', icon: '🏠' },
      { value: 'open_field', label: '露天', icon: '🌾' },
      { value: 'orchard', label: '果园', icon: '🍎' },
      { value: 'vegetable_field', label: '菜地', icon: '🥬' },
      { value: 'nursery', label: '育苗', icon: '🌱' }
    ],
    
    // 地图相关
    qqmapsdk: null,
    
    // 预设选项
    
    irrigationTypes: [
      { value: 'drip', label: '滴灌', icon: '💧' },
      { value: 'spray', label: '喷灌', icon: '🌧️' },
      { value: 'flood', label: '漫灌', icon: '🌊' },
      { value: 'micro', label: '微喷', icon: '💨' },
      { value: 'manual', label: '人工', icon: '🚰' }
    ],
    
    slopeOptions: [
      { value: 'flat', label: '平地' },
      { value: 'gentle', label: '缓坡' },
      { value: 'moderate', label: '中坡' },
      { value: 'steep', label: '陡坡' }
    ],
    
    cropOptions: [
      '未种植', '番茄', '黄瓜', '茄子', '辣椒', '草莓', '白菜', '萝卜', '菠菜', '生菜', '玉米', '大豆', '其他'
    ],
    
    growthStages: [
      { value: 'seeding', label: '播种期' },
      { value: 'germination', label: '发芽期' },
      { value: 'seedling', label: '幼苗期' },
      { value: 'growing', label: '生长期' },
      { value: 'flowering', label: '开花期' },
      { value: 'fruiting', label: '结果期' },
      { value: 'mature', label: '成熟期' },
      { value: 'harvest', label: '收获期' }
    ],
    
    // 关联的设备和区域
    relatedDevices: [],
  },

  onLoad(options) {
    const { mode, id } = options;
    
    // 初始化腾讯地图SDK
    this.data.qqmapsdk = new QQMapWX({
      key: MAP_CONFIG.key
    });
    
    this.setData({
      mode: mode || 'view',
      plotId: id ? parseInt(id) : null
    });
    
    if (mode === 'edit' && id) {
      this.loadPlotData(parseInt(id));
    }
  },

  // 加载地块数据
  loadPlotData(plotId) {
    // 从本地存储或服务器加载数据
    const farmData = wx.getStorageSync('farmData');
    if (farmData && farmData.plots) {
      const plot = farmData.plots.find(p => p.id === plotId);
      if (plot) {
        this.setData({
          plotData: { ...plot }
        });
        
      }
    }
    
    // 加载关联的设备和区域
    this.loadRelatedData(plotId);
  },

  // 加载关联设备
  loadRelatedData(plotId) {
    const farmData = wx.getStorageSync('farmData');
    
    // 从地块数据中加载设备
    let relatedDevices = [];
    if (farmData && farmData.plots) {
      const currentPlot = farmData.plots.find(plot => plot.id === plotId);
      if (currentPlot && currentPlot.devices) {
        relatedDevices = currentPlot.devices;
      }
    }
    
    this.setData({
      relatedDevices
    });
  },


  // 切换模式
  switchMode(e) {
    const { mode } = e.currentTarget.dataset;
    this.setData({ mode });
  },

  // 表单输入处理
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`plotData.${field}`]: value
    });
  },

  // 作物信息输入
  onCropInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`plotData.currentCrop.${field}`]: value
    });
  },

  // 选择器处理
  onPickerChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    let selectedValue;
    
    switch(field) {
      case 'soilType':
        selectedValue = this.data.soilTypes[value].value;
        break;
      case 'drainage':
        selectedValue = this.data.drainageOptions[value].value;
        break;
      case 'irrigationType':
        selectedValue = this.data.irrigationTypes[value].value;
        break;
      case 'slope':
        selectedValue = this.data.slopeOptions[value].value;
        break;
      case 'plotType':
        selectedValue = this.data.plotTypes[value].value;
        break;
      case 'cropName':
        selectedValue = this.data.cropOptions[value];
        this.setData({
          'plotData.currentCrop.name': selectedValue
        });
        return;
    }
    
    this.setData({
      [`plotData.${field}`]: selectedValue
    });
  },

  // 获取当前选中的选项索引
  getSelectedIndex(field) {
    const value = this.data.plotData[field];
    
    switch(field) {
      case 'soilType':
        return this.data.soilTypes.findIndex(item => item.value === value);
      case 'drainage':
        return this.data.drainageOptions.findIndex(item => item.value === value);
      case 'irrigationType':
        return this.data.irrigationTypes.findIndex(item => item.value === value);
      case 'slope':
        return this.data.slopeOptions.findIndex(item => item.value === value);
      default:
        return 0;
    }
  },

  // 获取选项标签
  getOptionLabel(field, value) {
    switch(field) {
      case 'soilType':
        const soilType = this.data.soilTypes.find(item => item.value === value);
        return soilType ? soilType.label : '壤土';
      case 'drainage':
        const drainage = this.data.drainageOptions.find(item => item.value === value);
        return drainage ? drainage.label : '良好';
      case 'irrigationType':
        const irrigation = this.data.irrigationTypes.find(item => item.value === value);
        return irrigation ? irrigation.label : '滴灌';
      case 'slope':
        const slope = this.data.slopeOptions.find(item => item.value === value);
        return slope ? slope.label : '平地';
      default:
        return '';
    }
  },


  // 保存地块信息
  savePlot() {
    const { plotData, mode, plotId } = this.data;
    
    // 表单验证
    if (!plotData.name || !plotData.area) {
      wx.showToast({
        title: '请填写地块名称和面积',
        icon: 'none'
      });
      return;
    }
    
    const area = parseFloat(plotData.area);
    if (isNaN(area) || area <= 0) {
      wx.showToast({
        title: '请输入有效的面积数值',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: mode === 'add' ? '创建中...' : '保存中...'
    });
    
    // 获取农场数据
    const farmData = wx.getStorageSync('farmData') || {};
    if (!farmData.plots) {
      farmData.plots = [];
    }
    
    if (mode === 'add') {
      // 新增地块
      const newPlot = {
        ...plotData,
        id: Date.now(),
        status: 'ready',
        statusText: '待配置',
        devices: [],
        createTime: new Date().toISOString()
      };
      
      farmData.plots.push(newPlot);
      
    } else if (mode === 'edit') {
      // 编辑地块
      const plotIndex = farmData.plots.findIndex(p => p.id === plotId);
      if (plotIndex > -1) {
        farmData.plots[plotIndex] = {
          ...farmData.plots[plotIndex],
          ...plotData,
          updateTime: new Date().toISOString()
        };
      }
    }
    
    // 保存到本地存储
    wx.setStorageSync('farmData', farmData);
    
    setTimeout(() => {
      wx.hideLoading();
      
      wx.showToast({
        title: mode === 'add' ? '创建成功' : '保存成功',
        icon: 'success'
      });
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  },

  // 删除地块
  deletePlot() {
    wx.showModal({
      title: '确认删除',
      content: '删除地块将同时删除相关的设备和区域配置，此操作不可恢复。确定要删除吗？',
      confirmText: '确定删除',
      confirmColor: '#F44336',
      success: (res) => {
        if (res.confirm) {
          this.executeDelete();
        }
      }
    });
  },

  // 执行删除操作
  executeDelete() {
    wx.showLoading({
      title: '删除中...'
    });
    
    const farmData = wx.getStorageSync('farmData') || {};
    
    // 删除地块
    if (farmData.plots) {
      farmData.plots = farmData.plots.filter(p => p.id !== this.data.plotId);
    }
    
    // 删除相关设备
    if (farmData.devices) {
      farmData.devices = farmData.devices.filter(d => d.plotId !== this.data.plotId);
    }
    
    // 删除相关区域
    if (farmData.zones) {
      farmData.zones = farmData.zones.filter(z => z.plotId !== this.data.plotId);
    }
    
    wx.setStorageSync('farmData', farmData);
    
    setTimeout(() => {
      wx.hideLoading();
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  },

  // 管理地块设备
  manageDevices() {
    wx.navigateTo({
      url: `/pages/farm/farm?tab=devices&plotId=${this.data.plotId}`
    });
  },

  // 查看设备详情
  viewDevice(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/device/detail/detail?id=${id}`
    });
  },


  // 添加设备到地块
  addDevice() {
    const that = this;
    wx.showActionSheet({
      itemList: ['扫码添加设备', '手动输入设备'],
      success: function(res) {
        if (res.tapIndex === 0) {
          that.scanDeviceForPlot();
        } else {
          that.manualAddDeviceToPlot();
        }
      }
    });
  },

  // 扫码添加设备到地块
  scanDeviceForPlot() {
    const that = this;
    wx.scanCode({
      success: function(res) {
        try {
          const deviceInfo = JSON.parse(res.result);
          that.addDeviceToCurrentPlot(deviceInfo);
        } catch (error) {
          that.addDeviceToCurrentPlot({
            serialNumber: res.result,
            name: '设备-' + res.result.slice(-4),
            type: 'sensor'
          });
        }
      },
      fail: function() {
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        });
      }
    });
  },

  // 手动添加设备到地块
  manualAddDeviceToPlot() {
    const that = this;
    wx.showModal({
      title: '手动添加设备',
      editable: true,
      placeholderText: '请输入设备序列号',
      success: function(res) {
        if (res.confirm && res.content) {
          const deviceInfo = {
            serialNumber: res.content,
            name: '设备-' + res.content.slice(-4),
            type: that.inferDeviceType(res.content)
          };
          that.addDeviceToCurrentPlot(deviceInfo);
        }
      }
    });
  },

  // 设备类型推断
  inferDeviceType(serialNumber) {
    const serial = serialNumber.toUpperCase();
    if (serial.includes('WF') || serial.includes('IRR')) {
      return 'irrigation';
    } else if (serial.includes('SNS') || serial.includes('SOIL')) {
      return 'sensor';
    } else if (serial.includes('WTH') || serial.includes('ENV')) {
      return 'weather';
    }
    return 'sensor';
  },

  // 添加设备到当前地块
  addDeviceToCurrentPlot(deviceInfo) {
    const farmData = wx.getStorageSync('farmData') || {};
    
    if (farmData.plots) {
      const plotIndex = farmData.plots.findIndex(plot => plot.id === this.data.plotId);
      if (plotIndex > -1) {
        if (!farmData.plots[plotIndex].devices) {
          farmData.plots[plotIndex].devices = [];
        }
        
        const newDevice = {
          id: Date.now(),
          name: deviceInfo.name,
          type: deviceInfo.type,
          serialNumber: deviceInfo.serialNumber,
          status: 'online'
        };
        
        farmData.plots[plotIndex].devices.push(newDevice);
        farmData.plots[plotIndex].deviceCount = farmData.plots[plotIndex].devices.length;
        
        // 保存更新的数据
        wx.setStorageSync('farmData', farmData);
        
        // 重新加载数据
        this.loadRelatedData(this.data.plotId);
        
        wx.showToast({
          title: '设备添加成功',
          icon: 'success'
        });
      }
    }
  },

  // 设置作物信息
  setCropInfo() {
    if (this.data.mode !== 'edit') {
      this.setData({ mode: 'edit' });
    }
  },


  // 输入处理函数
  onPlotNameInput(e) {
    this.setData({
      'plotData.name': e.detail.value
    });
  },

  onAreaInput(e) {
    this.setData({
      'plotData.area': e.detail.value
    });
  },

  onLocationInput(e) {
    this.setData({
      'plotData.location': e.detail.value
    });
  },

  onDescriptionInput(e) {
    this.setData({
      'plotData.description': e.detail.value
    });
  },

  // 选择器处理函数

  onIrrigationChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      'plotData.irrigationType': this.data.irrigationTypes[index].label,
      'plotData.irrigationIndex': index
    });
  },

  onSlopeChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      'plotData.slope': this.data.slopeOptions[index].label,
      'plotData.slopeIndex': index
    });
  },

  // 编辑地块
  editPlot() {
    this.setData({ mode: 'edit' });
  },

  // 作物相关处理函数
  onCropChange(e) {
    const index = parseInt(e.detail.value);
    const cropName = this.data.cropOptions[index];
    
    if (cropName === '未种植') {
      this.setData({
        'plotData.currentCrop': {
          name: '未种植',
          plantDate: '--',
          harvestDate: '--',
          stage: '',
          progress: 0,
          cropIndex: index
        }
      });
    } else {
      // 设置默认值
      const today = new Date().toISOString().split('T')[0];
      this.setData({
        'plotData.currentCrop': {
          name: cropName,
          plantDate: today,
          harvestDate: '',
          stage: '播种期',
          progress: 5,
          cropIndex: index,
          stageIndex: 0
        }
      });
      
      // 自动计算预计收获日期
      this.calculateHarvestDate(cropName, today);
    }
  },

  onPlantDateChange(e) {
    const plantDate = e.detail.value;
    this.setData({
      'plotData.currentCrop.plantDate': plantDate
    });
    
    // 重新计算收获日期和进度
    if (this.data.plotData.currentCrop.name && this.data.plotData.currentCrop.name !== '未种植') {
      this.calculateHarvestDate(this.data.plotData.currentCrop.name, plantDate);
      this.updateGrowthProgress();
    }
  },

  onHarvestDateChange(e) {
    this.setData({
      'plotData.currentCrop.harvestDate': e.detail.value
    });
    this.updateGrowthProgress();
  },

  onGrowthStageChange(e) {
    const index = parseInt(e.detail.value);
    const stage = this.data.growthStages[index];
    
    this.setData({
      'plotData.currentCrop.stage': stage.label,
      'plotData.currentCrop.stageIndex': index
    });
    
    // 根据生长阶段更新进度
    this.updateProgressByStage(index);
  },

  // 计算预计收获日期
  calculateHarvestDate(cropName, plantDate) {
    const cropGrowthDays = {
      '番茄': 90,
      '黄瓜': 65,
      '茄子': 80,
      '辣椒': 75,
      '草莓': 120,
      '白菜': 45,
      '萝卜': 60,
      '菠菜': 35,
      '生菜': 40,
      '玉米': 100,
      '大豆': 110,
      '其他': 70
    };
    
    const growthDays = cropGrowthDays[cropName] || 70;
    const plantDateObj = new Date(plantDate);
    const harvestDateObj = new Date(plantDateObj.getTime() + growthDays * 24 * 60 * 60 * 1000);
    const harvestDate = harvestDateObj.toISOString().split('T')[0];
    
    this.setData({
      'plotData.currentCrop.harvestDate': harvestDate
    });
  },

  // 更新生长进度
  updateGrowthProgress() {
    const crop = this.data.plotData.currentCrop;
    if (!crop.plantDate || !crop.harvestDate) return;
    
    const plantDate = new Date(crop.plantDate);
    const harvestDate = new Date(crop.harvestDate);
    const currentDate = new Date();
    
    const totalDays = (harvestDate - plantDate) / (24 * 60 * 60 * 1000);
    const elapsedDays = (currentDate - plantDate) / (24 * 60 * 60 * 1000);
    
    let progress = Math.max(0, Math.min(100, (elapsedDays / totalDays) * 100));
    
    this.setData({
      'plotData.currentCrop.progress': Math.round(progress)
    });
  },

  // 根据生长阶段更新进度
  updateProgressByStage(stageIndex) {
    const stageProgress = [5, 15, 25, 45, 65, 75, 90, 100];
    const progress = stageProgress[stageIndex] || 0;
    
    this.setData({
      'plotData.currentCrop.progress': progress
    });
  },

  // 保存地块信息
  savePlot() {
    const plotData = this.data.plotData;
    
    // 验证必填字段
    if (!plotData.name.trim()) {
      wx.showToast({
        title: '请输入地块名称',
        icon: 'none'
      });
      return;
    }
    
    if (!plotData.area || plotData.area <= 0) {
      wx.showToast({
        title: '请输入有效面积',
        icon: 'none'
      });
      return;
    }

    if (!plotData.location.trim()) {
      wx.showToast({
        title: '请输入地块位置',
        icon: 'none'
      });
      return;
    }

    const farmData = wx.getStorageSync('farmData') || { plots: [] };
    
    if (this.data.mode === 'add') {
      // 新增地块
      const newPlot = {
        id: Date.now(),
        name: plotData.name,
        area: parseFloat(plotData.area),
        location: plotData.location,
        coordinates: plotData.coordinates,
        gpsLongitude: plotData.gpsLongitude,
        gpsLatitude: plotData.gpsLatitude,
        plotType: plotData.plotType || 'greenhouse',
        polygonPath: plotData.polygonPath || [],
        soilType: plotData.soilType || '待检测',
        drainage: plotData.drainage || '待评估',
        irrigationType: plotData.irrigationType || '待配置',
        slope: plotData.slope || '平地',
        description: plotData.description,
        status: 'ready',
        statusText: '待规划',
        currentCrop: plotData.currentCrop || {
          name: '未种植',
          plantDate: '--',
          harvestDate: '--',
          progress: 0
        },
        devices: [],
        deviceCount: 0,
        createTime: new Date().toISOString()
      };
      
      farmData.plots.push(newPlot);
      
    } else if (this.data.mode === 'edit') {
      // 编辑地块
      const plotIndex = farmData.plots.findIndex(p => p.id === this.data.plotId);
      if (plotIndex > -1) {
        farmData.plots[plotIndex] = {
          ...farmData.plots[plotIndex],
          name: plotData.name,
          area: parseFloat(plotData.area),
          location: plotData.location,
          coordinates: plotData.coordinates,
          gpsLongitude: plotData.gpsLongitude,
          gpsLatitude: plotData.gpsLatitude,
          plotType: plotData.plotType,
          polygonPath: plotData.polygonPath,
          soilType: plotData.soilType,
          drainage: plotData.drainage,
          irrigationType: plotData.irrigationType,
          slope: plotData.slope,
          description: plotData.description,
          currentCrop: plotData.currentCrop,
          updateTime: new Date().toISOString()
        };
      }
    }
    
    // 保存到本地存储
    wx.setStorageSync('farmData', farmData);
    
    wx.showToast({
      title: this.data.mode === 'add' ? '地块创建成功' : '地块更新成功',
      icon: 'success'
    });
    
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  },

  // 取消操作
  cancelEdit() {
    wx.showModal({
      title: '确认取消',
      content: '是否放弃当前编辑的内容？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 打开地块绘制页面
  openDrawingPage() {
    // 通过本地存储传递数据，避免URL参数过长
    wx.setStorageSync('plotDrawingData', {
      plotData: this.data.plotData,
      mode: this.data.mode
    });
    
    wx.navigateTo({
      url: `/pages/farm/plot-drawing/plot-drawing?mode=${this.data.mode}`
    });
  },

  // 查看地块位置
  viewOnMap() {
    // 通过本地存储传递数据
    wx.setStorageSync('plotDrawingData', {
      plotData: this.data.plotData,
      mode: 'view'
    });
    
    wx.navigateTo({
      url: `/pages/farm/plot-drawing/plot-drawing?mode=view`
    });
  },

  // 地块类型选择处理
  onPlotTypeChange(e) {
    const index = parseInt(e.detail.value);
    const plotType = this.data.plotTypes[index];
    
    this.setData({
      'plotData.plotType': plotType.value,
      'plotData.plotTypeIndex': index
    });
  },


  onShow() {
    // 处理从绘制页面返回的数据
    const plotDrawingData = wx.getStorageSync('plotDrawingResult');
    if (plotDrawingData) {
      this.setData({
        'plotData.polygonPath': plotDrawingData.polygonPath || [],
        'plotData.area': plotDrawingData.area || ''
      });
      
      // 清除临时数据
      wx.removeStorageSync('plotDrawingResult');
      
      if (plotDrawingData.polygonPath && plotDrawingData.polygonPath.length > 0) {
        wx.showToast({
          title: '地块绘制已更新',
          icon: 'success'
        });
      }
    }
  },

});