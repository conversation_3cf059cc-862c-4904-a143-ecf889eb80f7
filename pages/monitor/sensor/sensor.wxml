<!--传感器管理页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 传感器网络拓扑 -->
    <view class="card">
      <view class="card-title">传感器网络</view>
      <view class="network-topology">
        <view class="hub-device">
          <view class="device-node hub">
            <text class="device-icon">🖥️</text>
            <text class="device-name">数据集线器</text>
            <text class="device-status online">在线</text>
          </view>
        </view>
        
        <view class="sensor-network">
          <view class="sensor-node {{item.status}}" wx:for="{{sensorList}}" wx:key="id"
                bindtap="viewSensorDetail" data-id="{{item.id}}">
            <view class="connection-line"></view>
            <view class="sensor-device">
              <text class="sensor-icon">{{item.icon}}</text>
              <text class="sensor-name">{{item.name}}</text>
              <text class="sensor-location">{{item.location}}</text>
              <view class="signal-strength {{item.signalLevel}}">
                <view class="signal-bar"></view>
                <view class="signal-bar"></view>
                <view class="signal-bar"></view>
                <view class="signal-bar"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 传感器列表 -->
    <view class="card">
      <view class="card-title">
        <text>传感器管理</text>
        <button class="btn btn-small btn-primary" bindtap="addSensor">
          ➕ 添加传感器
        </button>
      </view>
      
      <view class="sensor-list">
        <view class="sensor-item" wx:for="{{sensorList}}" wx:key="id">
          <view class="sensor-header">
            <view class="sensor-info">
              <text class="sensor-name">{{item.name}}</text>
              <text class="sensor-type">{{item.type}}</text>
              <text class="sensor-model">{{item.model}}</text>
            </view>
            <view class="sensor-status {{item.status}}">
              <text class="status-dot"></text>
              <text class="status-text">{{item.statusText}}</text>
            </view>
          </view>
          
          <view class="sensor-details">
            <view class="detail-row">
              <text class="detail-label">安装位置:</text>
              <text class="detail-value">{{item.location}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">信号强度:</text>
              <text class="detail-value">{{item.signalStrength}}dBm</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">电池电量:</text>
              <text class="detail-value">{{item.battery}}%</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">最后更新:</text>
              <text class="detail-value">{{item.lastUpdate}}</text>
            </view>
          </view>
          
          <view class="sensor-data">
            <view class="data-item" wx:for="{{item.currentData}}" wx:key="type" wx:for-item="data">
              <text class="data-icon">{{data.icon}}</text>
              <text class="data-name">{{data.name}}</text>
              <text class="data-value {{data.status}}">{{data.value}}{{data.unit}}</text>
            </view>
          </view>
          
          <view class="sensor-actions">
            <button class="btn btn-mini btn-outline" bindtap="viewSensorDetail" data-id="{{item.id}}">
              详情
            </button>
            <button class="btn btn-mini btn-outline" bindtap="calibrateSensor" data-id="{{item.id}}">
              校准
            </button>
            <button class="btn btn-mini btn-info" bindtap="testSensor" data-id="{{item.id}}">
              测试
            </button>
            <button class="btn btn-mini btn-warning" bindtap="configureSensor" data-id="{{item.id}}">
              配置
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 维护计划 -->
    <view class="card">
      <view class="card-title">
        <text>维护计划</text>
        <button class="btn btn-small btn-outline" bindtap="addMaintenancePlan">
          📅 新增计划
        </button>
      </view>
      
      <view class="maintenance-list">
        <view class="maintenance-item {{item.priority}}" wx:for="{{maintenancePlan}}" wx:key="id">
          <view class="maintenance-header">
            <text class="maintenance-title">{{item.title}}</text>
            <text class="maintenance-date">{{item.date}}</text>
          </view>
          <view class="maintenance-content">
            <text class="maintenance-desc">{{item.description}}</text>
            <text class="maintenance-sensors">涉及传感器: {{item.sensors}}</text>
          </view>
          <view class="maintenance-actions">
            <button class="btn btn-mini btn-outline" bindtap="completeMaintenance" data-id="{{item.id}}">
              {{item.status === 'pending' ? '完成' : '已完成'}}
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="card">
      <view class="card-title">传感器统计</view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-icon">📊</text>
          <view class="stat-content">
            <text class="stat-value">{{statsData.totalCount}}</text>
            <text class="stat-label">总数量</text>
          </view>
        </view>
        <view class="stat-item">
          <text class="stat-icon">✅</text>
          <view class="stat-content">
            <text class="stat-value">{{statsData.onlineCount}}</text>
            <text class="stat-label">在线数</text>
          </view>
        </view>
        <view class="stat-item">
          <text class="stat-icon">⚠️</text>
          <view class="stat-content">
            <text class="stat-value">{{statsData.warningCount}}</text>
            <text class="stat-label">异常数</text>
          </view>
        </view>
        <view class="stat-item">
          <text class="stat-icon">🔋</text>
          <view class="stat-content">
            <text class="stat-value">{{statsData.lowBatteryCount}}</text>
            <text class="stat-label">低电量</text>
          </view>
        </view>
      </view>
    </view>

  </view>
</view>

<!-- 传感器添加弹窗 -->
<view class="modal-overlay" wx:if="{{showAddModal}}" bindtap="closeAddModal">
  <view class="modal-content" catchtap="preventClose">
    <view class="modal-header">
      <text class="modal-title">添加传感器</text>
      <button class="modal-close" bindtap="closeAddModal">×</button>
    </view>
    
    <view class="modal-body">
      <view class="form-item">
        <text class="form-label">传感器名称</text>
        <input class="form-input" value="{{newSensor.name}}" bindinput="onNameInput" placeholder="请输入传感器名称"/>
      </view>
      
      <view class="form-item">
        <text class="form-label">传感器类型</text>
        <picker range="{{sensorTypeOptions}}" value="{{selectedType}}" bindchange="onTypeChange">
          <view class="picker-view">{{sensorTypeOptions[selectedType] || '选择类型'}}</view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="form-label">安装位置</text>
        <input class="form-input" value="{{newSensor.location}}" bindinput="onLocationInput" placeholder="请输入安装位置"/>
      </view>
      
      <view class="form-item">
        <text class="form-label">设备ID</text>
        <input class="form-input" value="{{newSensor.deviceId}}" bindinput="onDeviceIdInput" placeholder="请输入设备ID"/>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="closeAddModal">取消</button>
      <button class="btn btn-primary" bindtap="saveNewSensor">保存</button>
    </view>
  </view>
</view>