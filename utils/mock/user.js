/**
 * 用户相关Mock API
 */

const dataModule = require('./data');

// 统一的API响应格式
function createApiResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

function createErrorResponse(message, code = 500, details = null) {
  return {
    code,
    message,
    error: details,
    timestamp: new Date().toISOString()
  };
}

// 验证Token
function verifyToken(token) {
  // 开发环境始终返回 mock 用户信息，跳过 token 校验
  return {
    userId: 'mock_user',
    username: 'testuser',
    exp: Math.floor(Date.now() / 1000) + 3600
  };
}

module.exports = {
  init() {
    console.log('用户模块初始化完成');
  },

  // 获取用户信息
  async getProfile(token) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    // 从认证模块获取用户信息
    const authModule = require('./auth');
    const user = authModule.getUserById(payload.userId);
    
    if (!user) {
      return createErrorResponse('用户不存在', 404);
    }

    // 获取用户的农场信息
    const farms = dataModule.getFarms().filter(farm => 
      user.farmIds.includes(farm.id)
    );

    // 计算用户统计数据
    const userStats = {
      totalFarms: farms.length,
      totalPlots: farms.reduce((sum, farm) => sum + farm.plotCount, 0),
      totalDevices: farms.reduce((sum, farm) => sum + farm.deviceCount, 0),
      totalSensors: farms.reduce((sum, farm) => sum + farm.sensorCount, 0),
      activeFarms: farms.filter(farm => farm.status === 'active').length,
      cropTypes: [...new Set(farms.flatMap(farm => farm.cropTypes))],
      totalArea: farms.reduce((sum, farm) => sum + farm.totalArea, 0)
    };

    return createApiResponse({
      user: {
        id: user.id,
        nickname: user.nickname,
        avatarUrl: user.avatarUrl,
        phone: user.phone,
        email: user.email,
        realName: user.realName,
        role: user.role,
        permissions: user.permissions,
        status: user.status,
        lastLoginTime: user.lastLoginTime,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      },
      farms: farms.map(farm => ({
        id: farm.id,
        name: farm.name,
        location: farm.location,
        totalArea: farm.totalArea,
        status: farm.status,
        plotCount: farm.plotCount,
        deviceCount: farm.deviceCount,
        sensorCount: farm.sensorCount,
        cropTypes: farm.cropTypes
      })),
      stats: userStats
    });
  },

  // 更新用户信息
  async updateProfile(token, data) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const authModule = require('./auth');
    const users = authModule.getUsers();
    const userIndex = users.findIndex(u => u.id === payload.userId);
    
    if (userIndex === -1) {
      return createErrorResponse('用户不存在', 404);
    }

    const user = users[userIndex];
    
    // 验证和更新允许的字段
    const allowedFields = ['nickname', 'avatarUrl', 'email', 'realName'];
    const updatedFields = {};
    
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        updatedFields[field] = data[field];
        user[field] = data[field];
      }
    }

    // 特殊处理手机号更新（需要验证）
    if (data.phone && data.phone !== user.phone) {
      // 这里应该有验证码验证逻辑
      if (!data.verificationCode) {
        return createErrorResponse('手机号更新需要验证码', 400);
      }
      // 简化处理，直接更新
      user.phone = data.phone;
      updatedFields.phone = data.phone;
    }

    user.updatedAt = new Date().toISOString();

    return createApiResponse({
      user: {
        id: user.id,
        nickname: user.nickname,
        avatarUrl: user.avatarUrl,
        phone: user.phone,
        email: user.email,
        realName: user.realName,
        role: user.role,
        permissions: user.permissions,
        status: user.status,
        lastLoginTime: user.lastLoginTime,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      },
      updatedFields
    }, '用户信息更新成功');
  },

  // 获取用户设置
  async getSettings(token) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    // 模拟用户设置
    const settings = {
      notifications: {
        alarmEnabled: true,
        taskEnabled: true,
        systemEnabled: true,
        maintenanceEnabled: true,
        pushEnabled: true,
        smsEnabled: false,
        emailEnabled: false,
        quietHours: {
          enabled: true,
          start: '22:00',
          end: '07:00'
        }
      },
      display: {
        theme: 'light', // light, dark, auto
        language: 'zh-CN',
        dateFormat: 'YYYY-MM-DD',
        timeFormat: '24h',
        temperatureUnit: 'celsius',
        measurementUnit: 'metric'
      },
      privacy: {
        dataSharing: false,
        analytics: true,
        locationTracking: false,
        crashReporting: true
      },
      automation: {
        autoIrrigation: true,
        autoFertilization: false,
        weatherIntegration: true,
        smartScheduling: true
      }
    };

    return createApiResponse(settings);
  },

  // 更新用户设置
  async updateSettings(token, settings) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    // 在实际应用中，这里应该保存到数据库
    // 这里只是模拟返回成功
    return createApiResponse(settings, '设置更新成功');
  },

  // 获取用户活动日志
  async getActivityLogs(token, params = {}) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { page = 1, pageSize = 20, type = 'all', startDate, endDate } = params;

    // 模拟用户活动日志
    const activities = [
      {
        id: 'activity_001',
        type: 'login',
        description: '用户登录',
        details: { method: 'wechat', device: 'mobile' },
        timestamp: new Date(Date.now() - 1800000).toISOString(),
        ip: '*************',
        location: '重庆市江北区'
      },
      {
        id: 'activity_002',
        type: 'device_operation',
        description: '手动启动1号大棚灌溉',
        details: { deviceId: 'device_001', action: 'start_irrigation' },
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        ip: '*************',
        location: '重庆市江北区'
      },
      {
        id: 'activity_003',
        type: 'profile_update',
        description: '更新用户信息',
        details: { fields: ['nickname', 'email'] },
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        ip: '*************',
        location: '重庆市江北区'
      },
      {
        id: 'activity_004',
        type: 'schedule_create',
        description: '创建定时灌溉计划',
        details: { scheduleId: 'schedule_001', name: '晨间灌溉计划' },
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        ip: '*************',
        location: '重庆市江北区'
      }
    ];

    // 过滤活动类型
    let filteredActivities = activities;
    if (type !== 'all') {
      filteredActivities = activities.filter(activity => activity.type === type);
    }

    // 日期范围过滤
    if (startDate || endDate) {
      const start = startDate ? new Date(startDate) : new Date('1900-01-01');
      const end = endDate ? new Date(endDate) : new Date('2100-12-31');
      
      filteredActivities = filteredActivities.filter(activity => {
        const activityDate = new Date(activity.timestamp);
        return activityDate >= start && activityDate <= end;
      });
    }

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedActivities = filteredActivities.slice(startIndex, endIndex);

    return createApiResponse({
      activities: paginatedActivities,
      pagination: {
        page,
        pageSize,
        total: filteredActivities.length,
        totalPages: Math.ceil(filteredActivities.length / pageSize),
        hasNext: endIndex < filteredActivities.length,
        hasPrev: page > 1
      }
    });
  },

  // 删除用户账号
  async deleteAccount(token, data) {
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('无效的访问令牌', 401);
    }

    const { password, reason } = data;

    if (!password) {
      return createErrorResponse('需要确认密码', 400);
    }

    // 在实际应用中，这里应该验证密码并执行删除操作
    // 这里只是模拟返回成功
    return createApiResponse({
      message: '账号删除申请已提交，将在7个工作日内处理',
      deletionId: `deletion_${Date.now()}`,
      processTime: '7个工作日',
      reason: reason || '用户主动删除'
    }, '账号删除申请提交成功');
  }
};