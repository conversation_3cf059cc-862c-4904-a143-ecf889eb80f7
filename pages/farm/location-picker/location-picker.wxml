<!--地图选点页面-->
<view class="page-container">
  
  <!-- 顶部搜索栏 -->
  <view class="search-header">
    <view class="search-box">
      <input class="search-input" 
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             placeholder="搜索地点、地址或POI"
             confirm-type="search"
             bindconfirm="performSearch" />
      <button class="search-btn" 
              bindtap="performSearch"
              disabled="{{isSearching}}">
        {{isSearching ? '搜索中...' : '🔍'}}
      </button>
    </view>
    
    <button class="location-btn" bindtap="useCurrentLocation">
      📍 定位
    </button>
  </view>

  <!-- 地图容器 -->
  <view class="map-container">
    <map id="locationMap" 
         class="location-map"
         longitude="{{selectedLocation.longitude}}"
         latitude="{{selectedLocation.latitude}}"
         scale="15"
         markers="{{markers}}"
         show-location="true"
         bindtap="onMapTap">
    </map>
    
    <!-- 地图中心十字标记 -->
    <view class="map-center-mark">
      <view class="crosshair"></view>
    </view>
  </view>

  <!-- 搜索结果面板 -->
  <view wx:if="{{showSearchResults}}" class="search-results-panel">
    <view class="results-header">
      <text class="results-title">搜索结果</text>
      <button class="close-btn" bindtap="hideSearchResults">✕</button>
    </view>
    
    <scroll-view class="results-list" scroll-y="true">
      <view wx:for="{{searchResults}}" 
            wx:key="id" 
            class="result-item"
            data-index="{{index}}"
            bindtap="selectSearchResult">
        <view class="result-info">
          <text class="result-name">{{item.title}}</text>
          <text class="result-address">{{item.address}}</text>
        </view>
        <view class="result-distance" wx:if="{{item.distance}}">
          {{item.distance}}m
        </view>
      </view>
      
      <view wx:if="{{searchResults.length === 0}}" class="no-results">
        <text class="no-results-text">未找到相关结果</text>
      </view>
    </scroll-view>
  </view>

  <!-- 底部信息面板 -->
  <view class="bottom-panel">
    <!-- 选中位置信息 -->
    <view class="selected-location">
      <view class="location-header">
        <text class="location-icon">📍</text>
        <text class="location-title">选中位置</text>
      </view>
      
      <view class="location-details">
        <text class="location-name">{{selectedLocation.name || '未知位置'}}</text>
        <text class="location-address">{{selectedLocation.address || '正在获取地址...'}}</text>
        <text class="location-coords">
          {{selectedLocation.latitude ? selectedLocation.latitude.toFixed(6) : ''}}°N, 
          {{selectedLocation.longitude ? selectedLocation.longitude.toFixed(6) : ''}}°E
        </text>
      </view>
    </view>

    <!-- 常用地点快捷选择 -->
    <view class="common-places">
      <text class="section-title">常用地点</text>
      <scroll-view class="places-list" scroll-x="true">
        <view wx:for="{{commonPlaces}}" 
              wx:key="name" 
              class="place-item"
              data-index="{{index}}"
              bindtap="selectCommonPlace">
          <text class="place-name">{{item.name}}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn btn-secondary" bindtap="cancelSelection">
        取消
      </button>
      <button class="btn btn-primary" bindtap="confirmLocation">
        确认选择
      </button>
    </view>
  </view>
  
</view>