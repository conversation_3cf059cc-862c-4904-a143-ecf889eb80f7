package com.agriculture.iot.controller;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.Plot;
import com.agriculture.iot.service.PlotService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 地块管理控制器
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Tag(name = "地块管理", description = "地块信息的增删改查操作")
@RestController
@RequestMapping("/api/plots")
@RequiredArgsConstructor
public class PlotController {

    private final PlotService plotService;

    @Operation(summary = "获取地块列表", description = "分页获取地块列表，支持按农场筛选")
    @GetMapping
    public Result<IPage<Plot>> getPlotList(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "农场ID") @RequestParam(required = false) Long farmId,
            @Parameter(description = "地块名称") @RequestParam(required = false) String name,
            @Parameter(description = "作物类型") @RequestParam(required = false) String cropType,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        Page<Plot> page = new Page<>(current, size);
        IPage<Plot> result = plotService.getPlotList(page, farmId, name, cropType, status);
        return Result.success(result);
    }

    @Operation(summary = "获取地块详情", description = "根据ID获取地块详细信息")
    @GetMapping("/{id}")
    public Result<Plot> getPlotById(
            @Parameter(description = "地块ID") @PathVariable Long id) {
        
        Plot plot = plotService.getPlotById(id);
        if (plot == null) {
            return Result.notFound("地块不存在");
        }
        return Result.success(plot);
    }

    @Operation(summary = "创建地块", description = "创建新的地块")
    @PostMapping
    public Result<Plot> createPlot(@Valid @RequestBody Plot plot) {
        Plot createdPlot = plotService.createPlot(plot);
        return Result.success("地块创建成功", createdPlot);
    }

    @Operation(summary = "更新地块", description = "更新地块信息")
    @PutMapping("/{id}")
    public Result<Plot> updatePlot(
            @Parameter(description = "地块ID") @PathVariable Long id,
            @Valid @RequestBody Plot plot) {
        
        plot.setId(id);
        Plot updatedPlot = plotService.updatePlot(plot);
        if (updatedPlot == null) {
            return Result.notFound("地块不存在");
        }
        return Result.success("地块更新成功", updatedPlot);
    }

    @Operation(summary = "删除地块", description = "根据ID删除地块")
    @DeleteMapping("/{id}")
    public Result<Void> deletePlot(
            @Parameter(description = "地块ID") @PathVariable Long id) {
        
        boolean deleted = plotService.deletePlot(id);
        if (!deleted) {
            return Result.notFound("地块不存在");
        }
        return Result.success("地块删除成功");
    }

    @Operation(summary = "获取指定农场的地块列表", description = "获取指定农场下的所有地块")
    @GetMapping("/farm/{farmId}")
    public Result<List<Plot>> getPlotsByFarm(
            @Parameter(description = "农场ID") @PathVariable Long farmId) {
        
        List<Plot> plots = plotService.getPlotsByFarmId(farmId);
        return Result.success(plots);
    }

    @Operation(summary = "获取地块绑定的设备列表", description = "获取地块绑定的所有设备")
    @GetMapping("/{id}/devices")
    public Result<List<Object>> getPlotDevices(
            @Parameter(description = "地块ID") @PathVariable Long id) {
        
        List<Object> devices = plotService.getPlotDevices(id);
        return Result.success(devices);
    }

    @Operation(summary = "为地块绑定设备", description = "将设备绑定到指定地块")
    @PostMapping("/{id}/devices")
    public Result<Void> bindDeviceToPlot(
            @Parameter(description = "地块ID") @PathVariable Long id,
            @Parameter(description = "设备ID") @RequestParam Long deviceId) {
        
        boolean success = plotService.bindDeviceToPlot(id, deviceId);
        if (!success) {
            return Result.error("设备绑定失败");
        }
        return Result.success("设备绑定成功");
    }

    @Operation(summary = "解绑设备", description = "将设备从地块解绑")
    @DeleteMapping("/{plotId}/devices/{deviceId}")
    public Result<Void> unbindDeviceFromPlot(
            @Parameter(description = "地块ID") @PathVariable Long plotId,
            @Parameter(description = "设备ID") @PathVariable Long deviceId) {
        
        boolean success = plotService.unbindDeviceFromPlot(plotId, deviceId);
        if (!success) {
            return Result.error("设备解绑失败");
        }
        return Result.success("设备解绑成功");
    }

    @Operation(summary = "获取地块统计信息", description = "获取地块的统计数据")
    @GetMapping("/{id}/statistics")
    public Result<Plot.Statistics> getPlotStatistics(
            @Parameter(description = "地块ID") @PathVariable Long id) {
        
        Plot.Statistics statistics = plotService.getPlotStatistics(id);
        if (statistics == null) {
            return Result.notFound("地块不存在");
        }
        return Result.success(statistics);
    }

    @Operation(summary = "更新地块状态", description = "更新地块的种植状态")
    @PatchMapping("/{id}/status")
    public Result<Void> updatePlotStatus(
            @Parameter(description = "地块ID") @PathVariable Long id,
            @Parameter(description = "状态：0-休耕，1-种植中，2-收获") @RequestParam Integer status) {
        
        boolean updated = plotService.updatePlotStatus(id, status);
        if (!updated) {
            return Result.notFound("地块不存在");
        }
        return Result.success("地块状态更新成功");
    }

    @Operation(summary = "获取地块作物信息", description = "获取地块当前的作物信息")
    @GetMapping("/{id}/crop")
    public Result<Plot.CropInfo> getPlotCropInfo(
            @Parameter(description = "地块ID") @PathVariable Long id) {
        
        Plot.CropInfo cropInfo = plotService.getPlotCropInfo(id);
        if (cropInfo == null) {
            return Result.notFound("地块不存在或无作物信息");
        }
        return Result.success(cropInfo);
    }

    @Operation(summary = "更新地块作物信息", description = "更新地块的作物信息")
    @PutMapping("/{id}/crop")
    public Result<Void> updatePlotCropInfo(
            @Parameter(description = "地块ID") @PathVariable Long id,
            @Valid @RequestBody Plot.CropInfo cropInfo) {
        
        boolean updated = plotService.updatePlotCropInfo(id, cropInfo);
        if (!updated) {
            return Result.notFound("地块不存在");
        }
        return Result.success("作物信息更新成功", null);
    }
}