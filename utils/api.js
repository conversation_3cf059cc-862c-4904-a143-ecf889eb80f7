// 简化版API - 移除了所有mock数据依赖
const API_BASE_URL = 'https://api.your-domain.com';

const request = (options) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token');
    
    wx.request({
      url: API_BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === 200) {
            resolve(res.data);
          } else {
            reject(res.data);
          }
        } else if (res.statusCode === 401) {
          // Token过期处理
          handleTokenExpired();
          reject({ message: '登录已过期，请重新登录' });
        } else {
          reject({ message: '网络错误' });
        }
      },
      fail: (err) => {
        reject({ message: '网络连接失败' });
      }
    });
  });
};

// Token过期处理
function handleTokenExpired() {
  wx.removeStorageSync('token');
  wx.removeStorageSync('userInfo');
  wx.reLaunch({
    url: '/pages/login/login'
  });
}

// 导出基础API方法
module.exports = {
  // 用户认证
  login: (data) => request({
    url: '/auth/login',
    method: 'POST',
    data: data
  }),

  // 用户信息
  getUserProfile: () => request({
    url: '/user/profile',
    method: 'GET'
  }),

  updateUserProfile: (data) => request({
    url: '/user/profile',
    method: 'PUT',
    data: data
  }),

  // 天气信息
  getWeather: () => request({
    url: '/weather/current',
    method: 'GET'
  }),

  // 通用请求方法
  request: request
};