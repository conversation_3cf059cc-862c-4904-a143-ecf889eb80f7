package com.agriculture.iot.controller;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.Farm;
import com.agriculture.iot.service.FarmService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 农场管理控制器
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Tag(name = "农场管理", description = "农场信息的增删改查操作")
@RestController
@RequestMapping("/api/farms")
@RequiredArgsConstructor
public class FarmController {

    private final FarmService farmService;

    @Operation(summary = "获取农场列表", description = "分页获取农场列表")
    @GetMapping
    public Result<IPage<Farm>> getFarmList(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "农场名称") @RequestParam(required = false) String name,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        Page<Farm> page = new Page<>(current, size);
        IPage<Farm> result = farmService.getFarmList(page, name, status);
        return Result.success(result);
    }

    @Operation(summary = "获取农场详情", description = "根据ID获取农场详细信息")
    @GetMapping("/{id}")
    public Result<Farm> getFarmById(
            @Parameter(description = "农场ID") @PathVariable Long id) {
        
        Farm farm = farmService.getFarmById(id);
        if (farm == null) {
            return Result.notFound("农场不存在");
        }
        return Result.success(farm);
    }

    @Operation(summary = "创建农场", description = "创建新的农场")
    @PostMapping
    public Result<Farm> createFarm(@RequestBody Farm farm) {
        Farm createdFarm = farmService.createFarm(farm);
        return Result.success("农场创建成功", createdFarm);
    }

    @Operation(summary = "更新农场", description = "更新农场信息")
    @PutMapping("/{id}")
    public Result<Farm> updateFarm(
            @Parameter(description = "农场ID") @PathVariable Long id,
            @RequestBody Farm farm) {
        
        farm.setId(id);
        Farm updatedFarm = farmService.updateFarm(farm);
        if (updatedFarm == null) {
            return Result.notFound("农场不存在");
        }
        return Result.success("农场更新成功", updatedFarm);
    }

    @Operation(summary = "删除农场", description = "根据ID删除农场")
    @DeleteMapping("/{id}")
    public Result<Void> deleteFarm(
            @Parameter(description = "农场ID") @PathVariable Long id) {
        
        boolean deleted = farmService.deleteFarm(id);
        if (!deleted) {
            return Result.notFound("农场不存在");
        }
        return Result.success("农场删除成功");
    }

    @Operation(summary = "获取所有农场", description = "获取所有启用状态的农场列表")
    @GetMapping("/all")
    public Result<List<Farm>> getAllFarms() {
        List<Farm> farms = farmService.getAllActiveFarms();
        return Result.success(farms);
    }

    @Operation(summary = "获取农场统计信息", description = "获取农场的统计数据")
    @GetMapping("/{id}/statistics")
    public Result<Farm.Statistics> getFarmStatistics(
            @Parameter(description = "农场ID") @PathVariable Long id) {
        
        Farm.Statistics statistics = farmService.getFarmStatistics(id);
        if (statistics == null) {
            return Result.notFound("农场不存在");
        }
        return Result.success(statistics);
    }

    @Operation(summary = "启用/禁用农场", description = "切换农场状态")
    @PatchMapping("/{id}/status")
    public Result<Void> toggleFarmStatus(
            @Parameter(description = "农场ID") @PathVariable Long id,
            @Parameter(description = "状态") @RequestParam Integer status) {
        
        boolean updated = farmService.updateFarmStatus(id, status);
        if (!updated) {
            return Result.notFound("农场不存在");
        }
        return Result.success("农场状态更新成功");
    }
}