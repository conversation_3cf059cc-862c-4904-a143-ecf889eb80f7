Page({
  data: {
    tutorials: [
      {
        id: 1,
        title: '快速入门',
        description: '了解小程序基本功能和操作',
        icon: '🚀',
        steps: [
          {
            title: '登录注册',
            content: '使用微信授权登录，完善个人信息',
            image: ''
          },
          {
            title: '添加设备',
            content: '扫描设备二维码或手动添加设备',
            image: ''
          },
          {
            title: '查看数据',
            content: '在监测页面查看实时数据和历史趋势',
            image: ''
          },
          {
            title: '远程控制',
            content: '通过设备页面远程控制水肥一体机',
            image: ''
          }
        ]
      },
      {
        id: 2,
        title: '设备管理',
        description: '学习如何管理和控制设备',
        icon: '📱',
        steps: [
          {
            title: '设备绑定',
            content: '扫描设备二维码完成绑定',
            image: ''
          },
          {
            title: '手动控制',
            content: '设置流量、时长等参数，手动启停设备',
            image: ''
          },
          {
            title: '定时任务',
            content: '设置自动灌溉计划，实现无人管理',
            image: ''
          },
          {
            title: '配方管理',
            content: '创建和管理施肥配方',
            image: ''
          }
        ]
      },
      {
        id: 3,
        title: '数据监测',
        description: '掌握土壤监测和数据分析',
        icon: '📊',
        steps: [
          {
            title: '实时监测',
            content: '查看土壤湿度、温度、pH值等实时数据',
            image: ''
          },
          {
            title: '历史分析',
            content: '查看历史数据趋势和统计信息',
            image: ''
          },
          {
            title: '预警设置',
            content: '设置预警阈值，及时发现异常',
            image: ''
          },
          {
            title: '数据导出',
            content: '导出数据用于进一步分析',
            image: ''
          }
        ]
      },
      {
        id: 4,
        title: '地块管理',
        description: '管理地块信息和作物档案',
        icon: '🌱',
        steps: [
          {
            title: '地块信息',
            content: '录入地块基本信息和位置',
            image: ''
          },
          {
            title: '作物档案',
            content: '记录作物品种和生长阶段',
            image: ''
          },
          {
            title: '种植计划',
            content: '制定种植、施肥、收获计划',
            image: ''
          },
          {
            title: '设备绑定',
            content: '将设备绑定到对应地块',
            image: ''
          }
        ]
      }
    ],
    currentTutorial: null,
    currentStep: 0
  },

  onLoad(options) {
    if (options.id) {
      this.viewTutorial(parseInt(options.id));
    }
  },

  viewTutorial(tutorialId) {
    const tutorial = this.data.tutorials.find(t => t.id === tutorialId);
    if (tutorial) {
      this.setData({
        currentTutorial: tutorial,
        currentStep: 0
      });
    }
  },

  selectTutorial(e) {
    const tutorialId = e.currentTarget.dataset.id;
    this.viewTutorial(tutorialId);
  },

  backToList() {
    this.setData({
      currentTutorial: null,
      currentStep: 0
    });
  },

  nextStep() {
    const { currentTutorial, currentStep } = this.data;
    if (currentStep < currentTutorial.steps.length - 1) {
      this.setData({
        currentStep: currentStep + 1
      });
    }
  },

  prevStep() {
    const { currentStep } = this.data;
    if (currentStep > 0) {
      this.setData({
        currentStep: currentStep - 1
      });
    }
  },

  goToStep(e) {
    const step = e.currentTarget.dataset.step;
    this.setData({
      currentStep: step
    });
  }
})