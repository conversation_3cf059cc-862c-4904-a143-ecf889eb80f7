/* 设备管理页面样式 */

.device-container {
  background: #F5F5F5;
  min-height: 100vh;
  padding: 24rpx;
  padding-bottom: 120rpx;
}

/* 卡片通用样式 */
.device-overview-card,
.device-category-card,
.operation-history-card,
.quick-actions-card {
  background: white;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  padding: 32rpx 32rpx 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #F0F0F0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.view-all-link {
  font-size: 28rpx;
  color: #2196F3;
}

/* 设备状态总览 */
.device-list {
  padding: 32rpx;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
  position: relative;
}

.device-item:last-child {
  border-bottom: none;
}

.left-line {
  width: 8rpx;
  height: 80rpx;
  border-radius: 4rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.green-line {
  background: #08C160;
}

.red-line {
  background: #F44336;
}

.device-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.device-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.device-type {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.device-location {
  font-size: 22rpx;
  color: #999;
}

.device-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.status-text {
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.status-text.online {
  color: #08C160;
}

.status-text.offline {
  color: #F44336;
}

.control-btn,
.detail-btn {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  text-align: center;
  min-width: 60rpx;
}

.green-btn {
  background: #08C160;
  color: white;
}

.outline-btn {
  border: 1rpx solid #E0E0E0;
  color: #666;
  background: white;
}

.disabled-btn {
  background: #F0F0F0;
  color: #999;
}

.detail-btn {
  border: 1rpx solid #E0E0E0;
  color: #666;
  background: white;
}

/* 设备分类 */
.category-grid {
  padding: 32rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.category-item:active {
  transform: scale(0.98);
}

.category-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.water-icon {
  background: #64B5F6;
}

.sensor-icon {
  background: #FFB74D;
}

.control-icon {
  background: #8EEAB2;
}

.all-icon {
  background: #BCAAA4;
}

.category-info {
  display: flex;
  flex-direction: column;
}

.category-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.category-count {
  font-size: 24rpx;
  color: #666;
}

/* 操作历史 */
.operation-list {
  padding: 32rpx;
}

.operation-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.operation-item:last-child {
  border-bottom: none;
}

.operation-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.water-icon {
  background: #64B5F6;
}

.fertilizer-icon {
  background: #6DE59A;
}

.operation-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.operation-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.operation-time {
  font-size: 24rpx;
  color: #666;
}

.operation-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.operation-status.success {
  background: #E8F8EC;
  color: #08C160;
}

/* 快捷操作 */
.quick-actions-card {
  padding: 32rpx;
}

.quick-action-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.quick-action-item:last-child {
  margin-bottom: 0;
}

.quick-action-item:active {
  transform: scale(0.98);
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.add-icon {
  background: #08C160;
}

.refresh-icon {
  background: #2196F3;
}

.action-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}