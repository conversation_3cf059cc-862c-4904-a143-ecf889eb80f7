package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 传感器数据实体类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sensor_data")
public class SensorData {

    /**
     * 数据ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private Long deviceId;

    /**
     * 传感器类型ID
     */
    @TableField("sensor_type_id")
    private Long sensorTypeId;

    /**
     * 传感器数值
     */
    @TableField("value")
    private BigDecimal value;

    /**
     * 数据单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 数据质量: good-良好, warning-警告, error-错误
     */
    @TableField("quality")
    private String quality;

    /**
     * 采集时间
     */
    @TableField("collected_at")
    private LocalDateTime collectedAt;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    // 关联对象
    /**
     * 关联设备信息
     */
    @TableField(exist = false)
    private Device device;

    /**
     * 关联传感器类型信息
     */
    @TableField(exist = false)
    private SensorType sensorType;

    /**
     * 传感器数据统计信息内部类
     */
    @Data
    public static class Statistics {
        /**
         * 最大值
         */
        private BigDecimal maxValue;

        /**
         * 最小值
         */
        private BigDecimal minValue;

        /**
         * 平均值
         */
        private BigDecimal avgValue;

        /**
         * 当前值
         */
        private BigDecimal currentValue;

        /**
         * 数据点数量
         */
        private Integer dataCount;

        /**
         * 标准差
         */
        private BigDecimal standardDeviation;

        /**
         * 变化趋势
         */
        private String trend;

        /**
         * 统计时间范围
         */
        private String timeRange;

        /**
         * 数据单位
         */
        private String unit;
    }

    /**
     * 传感器数据趋势分析内部类
     */
    @Data
    public static class TrendData {
        /**
         * 时间序列数据
         */
        private java.util.List<DataPoint> dataPoints;

        /**
         * 趋势方向
         */
        private String trendDirection;

        /**
         * 趋势强度
         */
        private BigDecimal trendStrength;

        /**
         * 预测值
         */
        private BigDecimal predictedValue;

        @Data
        public static class DataPoint {
            private LocalDateTime timestamp;
            private BigDecimal value;
            private String quality;
        }
    }

    /**
     * 传感器数据对比分析内部类
     */
    @Data
    public static class CompareData {
        /**
         * 对比项目列表
         */
        private java.util.List<CompareItem> compareItems;

        /**
         * 对比结果总结
         */
        private String summary;

        @Data
        public static class CompareItem {
            private Long deviceId;
            private String deviceName;
            private BigDecimal avgValue;
            private BigDecimal maxValue;
            private BigDecimal minValue;
            private String trend;
        }
    }

    /**
     * 传感器数据报表内部类
     */
    @Data
    public static class ReportData {
        /**
         * 报表标题
         */
        private String title;

        /**
         * 报表数据
         */
        private java.util.List<ReportItem> reportItems;

        /**
         * 报表总结
         */
        private String summary;

        /**
         * 生成时间
         */
        private LocalDateTime generatedAt;

        @Data
        public static class ReportItem {
            private String category;
            private BigDecimal value;
            private String unit;
            private String description;
            private LocalDateTime timestamp;
        }
    }

    /**
     * 传感器预警信息内部类
     */
    @Data
    public static class AlertInfo {
        /**
         * 预警ID
         */
        private Long alertId;

        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 设备名称
         */
        private String deviceName;

        /**
         * 传感器类型
         */
        private String sensorType;

        /**
         * 当前值
         */
        private BigDecimal currentValue;

        /**
         * 阈值
         */
        private BigDecimal thresholdValue;

        /**
         * 预警级别
         */
        private String alertLevel;

        /**
         * 预警消息
         */
        private String alertMessage;

        /**
         * 预警时间
         */
        private LocalDateTime alertTime;

        /**
         * 处理状态
         */
        private String status;
    }

    /**
     * 传感器阈值配置内部类
     */
    @Data
    public static class ThresholdConfig {
        /**
         * 配置ID
         */
        private Long configId;

        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 传感器类型
         */
        private String sensorType;

        /**
         * 最小阈值
         */
        private BigDecimal minThreshold;

        /**
         * 最大阈值
         */
        private BigDecimal maxThreshold;

        /**
         * 警告阈值
         */
        private BigDecimal warningThreshold;

        /**
         * 是否启用
         */
        private Boolean enabled;

        /**
         * 预警方式
         */
        private String alertMethod;

        /**
         * 创建时间
         */
        private LocalDateTime createdAt;

        /**
         * 更新时间
         */
        private LocalDateTime updatedAt;
    }
}