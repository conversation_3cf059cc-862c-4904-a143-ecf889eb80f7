// 引入Mock API
const MockAPI = require('./mock/index.js');

// 判断是否使用Mock数据
const USE_MOCK = true; // 开发环境使用Mock数据，生产环境改为false
const API_BASE_URL = 'https://api.your-domain.com';

// 初始化Mock API
if (USE_MOCK) {
  MockAPI.init();
}

const request = (options) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token');
    
    // 如果使用Mock数据，直接调用Mock API
    if (USE_MOCK) {
      handleMockRequest(options, token, resolve, reject);
      return;
    }
    
    // 真实API请求
    wx.request({
      url: API_BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === 200) {
            resolve(res.data);
          } else {
            reject(res.data);
          }
        } else if (res.statusCode === 401) {
          // Token过期，尝试刷新
          const refreshToken = wx.getStorageSync('refreshToken');
          if (refreshToken) {
            refreshTokenAndRetry(refreshToken, options, resolve, reject);
          } else {
            handleTokenExpired();
            reject({ message: '登录已过期，请重新登录' });
          }
        } else {
          reject({ message: '网络错误' });
        }
      },
      fail: (err) => {
        reject({ message: '网络连接失败' });
      }
    });
  });
};

// 处理Mock请求
function handleMockRequest(options, token, resolve, reject) {
  const mockApiCall = getMockApiCall(options, token);
  
  if (mockApiCall) {
    mockApiCall
      .then(result => resolve(result))
      .catch(error => reject(error));
  } else {
    reject({ code: 404, message: 'Mock API not found: ' + options.url });
  }
}

// 获取对应的Mock API调用
function getMockApiCall(options, token) {
  const { url, method = 'GET', data = {} } = options;
  
  // 根据URL和方法匹配对应的Mock API
  switch (url) {
    // 认证相关
    case '/auth/login':
      return MockAPI.auth.login(data);
    case '/auth/refresh':
      return MockAPI.auth.refreshToken(data.refreshToken);
    
    // 用户相关
    case '/user/profile':
      if (method === 'GET') {
        return MockAPI.user.getProfile(token);
      } else if (method === 'PUT') {
        return MockAPI.user.updateProfile(token, data);
      }
      break;
    
    // 农场相关
    case '/farms':
      if (method === 'GET') {
        return MockAPI.farms.list(token);
      } else if (method === 'POST') {
        return MockAPI.farms.create(token, data);
      }
      break;
    
    // 设备相关
    case '/device/status':
      return MockAPI.devices.getStatusSummary(token);
    case '/devices':
      return MockAPI.devices.list(token, data);
    case '/device/irrigation/control':
      return MockAPI.devices.control(token, data.deviceId || 'device_001', 'start_irrigation', data);
    
    // 传感器相关
    case '/sensor/latest':
      return MockAPI.sensors.getLatestAll(token);
    case '/sensor/data':
      return MockAPI.sensors.getRealtimeData(token, data.sensorId);
    case '/sensor/history':
      return MockAPI.sensors.getHistory(token, data.sensorId, data);
    
    // 预警相关
    case '/alarms':
      return MockAPI.alarms.list(token, data);
    case '/alarms/latest':
      return MockAPI.alarms.list(token, { status: 'active' });
    
    // 任务相关
    case '/tasks':
      if (method === 'GET') {
        return MockAPI.getTasks(token, data);
      } else if (method === 'POST') {
        return MockAPI.createTask(token, data);
      }
      break;
    case '/tasks/today':
      return MockAPI.getTasks(token, { today: true });
    
    // 调度相关 - 增强版
    case '/schedules':
      if (method === 'GET') {
        return MockAPI.schedules.list(token, data);
      } else if (method === 'POST') {
        return MockAPI.schedules.create(token, data);
      }
      break;
    case '/schedules/calendar':
      return MockAPI.schedules.getCalendarData(token, data.year, data.month);
    case '/schedules/day':
      return MockAPI.schedules.getDaySchedules(token, data.date);
    case '/schedules/templates':
      return MockAPI.schedules.getTemplates(token);
    
    // 操作日志相关
    case '/logs/operations':
      return MockAPI.logs.getOperationLogs(token, data);
    case '/logs/reports':
      return MockAPI.logs.getReports(token, data);
    case '/logs/alerts':
      return MockAPI.logs.getAlerts(token, data);
    case '/logs/export':
      return MockAPI.logs.exportLogs(token, data);
    
    // 仪表板数据
    case '/dashboard/today':
      return MockAPI.getTodayData(token);
    
    // 通知相关
    case '/notifications/recent':
      return MockAPI.notifications.list(token, data);
    case '/notifications/read':
      return MockAPI.notifications.markAsRead(token, data.notificationId);
    
    // 天气相关
    case '/weather':
      return MockAPI.weather.getCurrent(token);
    
    // 农场相关
    case '/farms':
      if (method === 'GET') {
        return MockAPI.farms.list(token);
      } else if (method === 'POST') {
        return MockAPI.farms.create(token, data);
      }
      break;
    case '/farms/detail':
      return MockAPI.farms.getDetail(token, data.farmId);
    
    // 地块相关
    case '/plots':
      if (method === 'GET') {
        return MockAPI.plots.list(token, data.farmId);
      } else if (method === 'POST') {
        return MockAPI.plots.create(token, data);
      }
      break;
    case '/plots/detail':
      return MockAPI.plots.getDetail(token, data.plotId);
    
    // 用户相关
    case '/user/profile':
      if (method === 'GET') {
        return MockAPI.user.getProfile(token);
      } else if (method === 'PUT') {
        return MockAPI.user.updateProfile(token, data);
      }
      break;
    
    // 手机号登录相关
    case '/auth/phone-login':
      return MockAPI.auth.phoneLogin(data);
    case '/auth/send-code':
      return MockAPI.auth.sendCode(data);
    
    // 文件上传相关
    case '/upload/image':
      return MockAPI.upload.image(token, data);
    
    // 施肥配方相关
    case '/fertilizer/formulas':
      if (method === 'GET') {
        return MockAPI.fertilizer.getFormulas(token, data);
      } else if (method === 'POST') {
        return MockAPI.fertilizer.createFormula(token, data);
      }
      break;
    case '/fertilizer/records':
      return MockAPI.fertilizer.getRecords(token, data);
    case '/fertilizer/start':
      return MockAPI.fertilizer.startFertilization(token, data);
    case '/fertilizer/stop':
      return MockAPI.fertilizer.stopFertilization(token, data.recordId);
    case '/fertilizer/recommendations':
      return MockAPI.fertilizer.getRecommendations(token, data);
    
    // 设备管理相关
    default:
      // 处理动态URL（包含ID的路径）
      if (url.match(/^\/farms\/\w+$/)) {
        const farmId = url.split('/')[2];
        return MockAPI.getFarmDetail(token, farmId);
      }
      
      if (url.match(/^\/devices\/\w+\/settings$/)) {
        const deviceId = url.split('/')[2];
        return MockAPI.getDeviceStatus(token, deviceId);
      }
      
      if (url.match(/^\/devices\/\w+\/status$/)) {
        const deviceId = url.split('/')[2];
        return MockAPI.getDeviceStatus(token, deviceId);
      }
      
      // 调度相关动态路由
      if (url.match(/^\/schedules\/\d+$/)) {
        const scheduleId = url.split('/')[2];
        if (method === 'PUT') {
          return MockAPI.schedules.update(token, scheduleId, data);
        } else if (method === 'DELETE') {
          return MockAPI.schedules.delete(token, scheduleId);
        }
      }
      
      if (url.match(/^\/schedules\/\d+\/duplicate$/)) {
        const scheduleId = url.split('/')[2];
        return MockAPI.schedules.duplicate(token, scheduleId);
      }
      
      if (url.match(/^\/schedules\/\d+\/toggle$/)) {
        const scheduleId = url.split('/')[2];
        return MockAPI.schedules.toggle(token, scheduleId, data.enabled);
      }
      
      // 日志相关动态路由
      if (url.match(/^\/logs\/alerts\/\d+\/resolve$/)) {
        const alertId = url.split('/')[3];
        return MockAPI.logs.resolveAlert(token, alertId, data.solution);
      }
      
      if (url.match(/^\/logs\/operations\/\d+\/retry$/)) {
        const logId = url.split('/')[3];
        return MockAPI.logs.retryOperation(token, logId);
      }
      
      if (url.match(/^\/devices\/\w+\/control$/)) {
        const deviceId = url.split('/')[2];
        return MockAPI.devices.control(token, deviceId, data.action, data.parameters);
      }
      
      // 施肥配方动态路由
      if (url.match(/^\/fertilizer\/formulas\/\w+$/)) {
        const formulaId = url.split('/')[3];
        if (method === 'PUT') {
          return MockAPI.fertilizer.updateFormula(token, formulaId, data);
        } else if (method === 'DELETE') {
          return MockAPI.fertilizer.deleteFormula(token, formulaId);
        }
      }
      
      // 调度执行历史
      if (url.match(/^\/schedules\/history$/)) {
        return MockAPI.schedules.getExecutionHistory(token, data);
      }
      
      // 地块相关动态路由
      if (url.match(/^\/plots$/)) {
        return MockAPI.plots.list(token, data);
      }
      
      if (url.match(/^\/plots\/detail$/)) {
        return MockAPI.plots.getDetail(token, data.plotId);
      }
      
      // 操作日志路由
      if (url.match(/^\/logs\/operations$/)) {
        return MockAPI.log.getOperationLogs(token, data);
      }
      
      return null;
  }
  
  return null;
}

// Token刷新和重试机制
function refreshTokenAndRetry(refreshToken, originalOptions, resolve, reject) {
  if (USE_MOCK) {
    MockAPI.auth.refreshToken(refreshToken)
      .then(result => {
        if (result.code === 200) {
          // 保存新Token
          wx.setStorageSync('token', result.data.token);
          wx.setStorageSync('refreshToken', result.data.refreshToken);
          
          // 重试原请求
          request(originalOptions).then(resolve).catch(reject);
        } else {
          handleTokenExpired();
          reject({ message: '登录已过期，请重新登录' });
        }
      })
      .catch(() => {
        handleTokenExpired();
        reject({ message: '登录已过期，请重新登录' });
      });
  } else {
    // 真实API的Token刷新逻辑
    wx.request({
      url: API_BASE_URL + '/auth/refresh',
      method: 'POST',
      data: { refreshToken },
      success: (res) => {
        if (res.statusCode === 200 && res.data.code === 200) {
          wx.setStorageSync('token', res.data.data.token);
          wx.setStorageSync('refreshToken', res.data.data.refreshToken);
          
          // 重试原请求
          request(originalOptions).then(resolve).catch(reject);
        } else {
          handleTokenExpired();
          reject({ message: '登录已过期，请重新登录' });
        }
      },
      fail: () => {
        handleTokenExpired();
        reject({ message: '登录已过期，请重新登录' });
      }
    });
  }
}

// 处理Token过期
function handleTokenExpired() {
  wx.removeStorageSync('token');
  wx.removeStorageSync('refreshToken');
  wx.removeStorageSync('userInfo');
  
  // 跳转到登录页面
  wx.reLaunch({
    url: '/pages/login/login'
  });
}

const api = {
  login: (data) => request({
    url: '/auth/login',
    method: 'POST',
    data: data
  }),
  
  getNotifications: (params) => request({
    url: '/notifications/recent',
    data: params
  }),
  
  markNotificationAsRead: (data) => request({
    url: '/notifications/read',
    method: 'PUT',
    data: data
  }),

  getWeather: () => request({
    url: '/weather'
  }),

  getUserInfo: () => request({
    url: '/user/profile'
  }),
  
  getDeviceStatus: () => request({
    url: '/device/status'
  }),
  
  controlIrrigation: (data) => request({
    url: '/device/irrigation/control',
    method: 'POST',
    data: data
  }),
  
  getSensorData: (params) => request({
    url: '/sensor/data',
    data: params
  }),
  
  getLatestSensorData: () => request({
    url: '/sensor/latest'
  }),
  
  getHistoryData: (params) => request({
    url: '/sensor/history',
    data: params
  }),
  
  getAlarms: (params) => request({
    url: '/alarms',
    data: params
  }),
  
  getLatestAlarms: () => request({
    url: '/alarms/latest'
  }),
  
  getTodayTasks: () => request({
    url: '/tasks/today'
  }),
  
  createTask: (data) => request({
    url: '/tasks',
    method: 'POST',
    data: data
  }),
  
  // 增强的调度API
  getSchedules: (params) => request({
    url: '/schedules',
    data: params
  }),
  
  getCalendarData: (year, month) => request({
    url: '/schedules/calendar',
    data: { year, month }
  }),
  
  getDaySchedules: (date) => request({
    url: '/schedules/day',
    data: { date }
  }),
  
  createSchedule: (data) => request({
    url: '/schedules',
    method: 'POST',
    data: data
  }),
  
  updateSchedule: (id, data) => request({
    url: `/schedules/${id}`,
    method: 'PUT',
    data: data
  }),
  
  deleteSchedule: (id) => request({
    url: `/schedules/${id}`,
    method: 'DELETE'
  }),
  
  duplicateSchedule: (id) => request({
    url: `/schedules/${id}/duplicate`,
    method: 'POST'
  }),
  
  toggleSchedule: (id, enabled) => request({
    url: `/schedules/${id}/toggle`,
    method: 'PUT',
    data: { enabled }
  }),
  
  getScheduleTemplates: () => request({
    url: '/schedules/templates'
  }),
  
  // 操作日志API
  getOperationLogs: (params) => request({
    url: '/logs/operations',
    data: params
  }),
  
  getReports: (params) => request({
    url: '/logs/reports',
    data: params
  }),
  
  getAlerts: (params) => request({
    url: '/logs/alerts',
    data: params
  }),
  
  resolveAlert: (id, solution) => request({
    url: `/logs/alerts/${id}/resolve`,
    method: 'PUT',
    data: { solution }
  }),
  
  exportLogs: (params) => request({
    url: '/logs/export',
    method: 'POST',
    data: params
  }),
  
  retryOperation: (logId) => request({
    url: `/logs/operations/${logId}/retry`,
    method: 'POST'
  }),
  
  // 首页数据API
  getTodayData: () => request({
    url: '/dashboard/today'
  }),
  
  
  getFarms: () => request({
    url: '/farms'
  }),
  
  getFarmDetail: (farmId) => request({
    url: '/farms/detail',
    data: { farmId }
  }),
  
  createFarm: (data) => request({
    url: '/farms',
    method: 'POST',
    data: data
  }),
  
  // 地块相关API
  getPlots: (farmId) => request({
    url: '/plots',
    data: { farmId }
  }),
  
  getPlotDetail: (plotId) => request({
    url: '/plots/detail',
    data: { plotId }
  }),
  
  createPlot: (data) => request({
    url: '/plots',
    method: 'POST',
    data: data
  }),
  
  // 用户相关API
  getUserProfile: () => request({
    url: '/user/profile'
  }),
  
  updateUserProfile: (data) => request({
    url: '/user/profile',
    method: 'PUT',
    data: data
  }),
  
  // 手机号登录相关API
  phoneLogin: (data) => request({
    url: '/auth/phone-login',
    method: 'POST',
    data: data
  }),
  
  sendVerificationCode: (data) => request({
    url: '/auth/send-code',
    method: 'POST',
    data: data
  }),
  
  // 施肥配方API
  getFertilizerFormulas: () => request({
    url: '/fertilizer/formulas'
  }),
  
  createFertilizerFormula: (data) => request({
    url: '/fertilizer/formulas',
    method: 'POST',
    data: data
  }),
  
  // 文件上传API
  uploadImage: (filePath) => {
    return new Promise((resolve, reject) => {
      if (USE_MOCK) {
        // 模拟上传成功
        setTimeout(() => {
          resolve({
            code: 200,
            message: '上传成功',
            data: {
              url: '/mock/images/uploaded_' + Date.now() + '.jpg',
              size: 1024,
              type: 'image/jpeg'
            }
          });
        }, 1000);
      } else {
        wx.uploadFile({
          url: API_BASE_URL + '/upload/image',
          filePath: filePath,
          name: 'file',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('token')}`
          },
          success: (res) => {
            const data = JSON.parse(res.data);
            if (data.code === 200) {
              resolve(data);
            } else {
              reject(data);
            }
          },
          fail: reject
        });
      }
    });
  },

  getDeviceList: () => request({
    url: '/devices'
  }),
  
  
  updateFertilizerFormula: (formulaId, data) => request({
    url: `/fertilizer/formulas/${formulaId}`,
    method: 'PUT',
    data: data
  }),
  
  deleteFertilizerFormula: (formulaId) => request({
    url: `/fertilizer/formulas/${formulaId}`,
    method: 'DELETE'
  }),
  
  getFertilizerRecords: (params) => request({
    url: '/fertilizer/records',
    data: params
  }),
  
  startFertilization: (data) => request({
    url: '/fertilizer/start',
    method: 'POST',
    data: data
  }),
  
  stopFertilization: (recordId) => request({
    url: '/fertilizer/stop',
    method: 'POST',
    data: { recordId }
  }),
  
  getFertilizerRecommendations: (params) => request({
    url: '/fertilizer/recommendations',
    data: params
  }),
  
  // 调度相关API增强
  getScheduleExecutionHistory: (params) => request({
    url: '/schedules/history',
    data: params
  }),

  
};

module.exports = api;