<!--农场初始化页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 进度指示器 -->
    <view class="progress-header">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{(currentStep / totalSteps) * 100}}%"></view>
      </view>
      <text class="progress-text">第 {{currentStep}} 步，共 {{totalSteps}} 步</text>
    </view>

    <!-- 步骤1：农场基本信息 -->
    <view wx:if="{{currentStep === 1}}" class="step-content">
      <view class="card">
        <view class="card-title">
          <text class="title-icon">🏡</text>
          <text>农场基本信息</text>
        </view>
        
        <view class="form-section">
          <view class="form-item">
            <text class="form-label">农场名称 *</text>
            <input class="form-input" placeholder="请输入农场名称" 
                   value="{{farmInfo.name}}" 
                   data-field="name" 
                   bindinput="onFarmInfoInput" />
          </view>
          
          <view class="form-item">
            <text class="form-label">农场位置 *</text>
            <view class="location-input-group">
              <input class="form-input location-input" 
                     placeholder="请输入农场地址或点击地图选点" 
                     value="{{farmInfo.location}}" 
                     data-field="location" 
                     bindinput="onFarmInfoInput" />
              <button class="location-btn" bindtap="openLocationPicker">
                📍 地图选点
              </button>
            </view>
            <view wx:if="{{farmInfo.coordinates}}" class="location-coords">
              <text class="coords-text">📍 {{farmInfo.coordinates}}</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">总面积(亩) *</text>
            <input class="form-input" placeholder="请输入总面积" type="number"
                   value="{{farmInfo.totalArea}}" 
                   data-field="totalArea" 
                   bindinput="onFarmInfoInput" />
          </view>
          
          <view class="form-item">
            <text class="form-label">农场描述</text>
            <textarea class="form-textarea" placeholder="请简单描述您的农场..." 
                      value="{{farmInfo.description}}" 
                      data-field="description" 
                      bindinput="onFarmInfoInput"></textarea>
          </view>
        </view>
      </view>
    </view>

    <!-- 步骤2：设备配置 -->
    <view wx:if="{{currentStep === 2}}" class="step-content">
      <view class="card">
        <view class="card-title">
          <text class="title-icon">📱</text>
          <text>设备配置</text>
          <button class="btn btn-small btn-primary" bindtap="addDevice">
            ➕ 添加设备
          </button>
        </view>
        
        <view class="device-list">
          <view class="device-item" wx:for="{{devices}}" wx:key="id">
            <view class="device-header">
              <text class="device-icon">{{item.typeIcon}}</text>
              <view class="device-info">
                <text class="device-name">{{item.name}}</text>
                <text class="device-serial">序列号: {{item.serialNumber}}</text>
              </view>
              <button class="btn btn-mini btn-danger" 
                      bindtap="removeDevice" 
                      data-index="{{index}}">
                删除
              </button>
            </view>
          </view>
          
          <view wx:if="{{devices.length === 0}}" class="empty-state">
            <text class="empty-icon">🔧</text>
            <text class="empty-text">还没有添加设备</text>
            <text class="empty-desc">点击"添加设备"开始配置</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 步骤3：配置预览 -->
    <view wx:if="{{currentStep === 3}}" class="step-content">
      <view class="card">
        <view class="card-title">
          <text class="title-icon">📋</text>
          <text>配置预览</text>
        </view>
        
        <view class="config-preview">
          <view class="summary-grid">
            <view class="summary-item">
              <text class="summary-value">{{farmInfo.name}}</text>
              <text class="summary-label">农场名称</text>
            </view>
            <view class="summary-item">
              <text class="summary-value">{{farmInfo.location}}</text>
              <text class="summary-label">农场位置</text>
            </view>
            <view class="summary-item">
              <text class="summary-value">{{farmInfo.totalArea}}亩</text>
              <text class="summary-label">总面积</text>
            </view>
            <view class="summary-item">
              <text class="summary-value">{{plots.length}}</text>
              <text class="summary-label">地块数量</text>
            </view>
            <view class="summary-item">
              <text class="summary-value">{{devices.length}}</text>
              <text class="summary-label">设备数量</text>
            </view>
          </view>
          
          <view class="setup-complete">
            <text class="complete-icon">✅</text>
            <text class="complete-title">初始化配置完成</text>
            <text class="complete-desc">点击"完成初始化"开始使用智慧农业系统</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button wx:if="{{currentStep > 1}}" 
              class="btn btn-outline" 
              bindtap="prevStep">
        上一步
      </button>
      
      <button wx:if="{{currentStep < totalSteps}}" 
              class="btn btn-primary" 
              bindtap="nextStep">
        下一步
      </button>
      
      <button wx:if="{{currentStep === totalSteps}}" 
              class="btn btn-primary" 
              bindtap="completeInit">
        完成初始化
      </button>
      
      <button class="btn btn-text" bindtap="skipInit">
        跳过初始化
      </button>
    </view>

  </view>
</view>