package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 灌溉计划实体
 *
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@TableName("irrigation_schedule")
public class IrrigationSchedule {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("device_id")
    private Long deviceId;
    
    @TableField("plot_id")
    private Long plotId;
    
    @TableField("schedule_name")
    private String scheduleName;
    
    @TableField("start_time")
    private LocalDateTime startTime;
    
    @TableField("end_time")
    private LocalDateTime endTime;
    
    @TableField("min_moisture")
    private BigDecimal minMoisture;
    
    @TableField("water_amount")
    private BigDecimal waterAmount;
    
    @TableField("duration")
    private Integer duration;
    
    @TableField("status")
    private String status;
    
    @TableField("created_at")
    private LocalDateTime createdAt;
    
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 灌溉模板内部类
     */
    @Data
    public static class Template {
        /**
         * 模板ID
         */
        private Long templateId;

        /**
         * 模板名称
         */
        private String templateName;

        /**
         * 模板描述
         */
        private String description;

        /**
         * 模板类型
         */
        private String templateType;

        /**
         * 默认参数
         */
        private TemplateParams defaultParams;

        /**
         * 适用作物类型
         */
        private java.util.List<String> applicableCrops;

        /**
         * 创建时间
         */
        private LocalDateTime createdAt;
    }

    /**
     * 模板参数内部类
     */
    @Data
    public static class TemplateParams {
        /**
         * 灌溉持续时间（分钟）
         */
        private Integer duration;

        /**
         * 流量设置
         */
        private BigDecimal flowRate;

        /**
         * 土壤湿度阈值
         */
        private BigDecimal moistureThreshold;

        /**
         * 重复模式
         */
        private String repeatPattern;

        /**
         * 重复间隔（小时）
         */
        private Integer repeatInterval;

        /**
         * 执行时间列表
         */
        private java.util.List<String> executionTimes;

        /**
         * 作物类型
         */
        private String cropType;

        /**
         * 季节适用性
         */
        private String seasonApplicability;
    }
}