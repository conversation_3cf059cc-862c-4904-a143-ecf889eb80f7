Page({
  data: {
    currentStep: 1,
    totalSteps: 3,
    
    // 农场基本信息
    farmInfo: {
      name: '',
      location: '',
      totalArea: '',
      description: '',
      latitude: '',
      longitude: '',
      coordinates: ''
    },
    
    // 地块信息
    plots: [],
    
    // 设备信息
    devices: [],
    
    
    // 预设的地块类型
    plotTypes: [
      { id: 'greenhouse', name: '温室大棚', icon: '🏠' },
      { id: 'openfield', name: '露天农田', icon: '🌾' },
      { id: 'orchard', name: '果园', icon: '🍎' },
      { id: 'vegetable', name: '蔬菜园', icon: '🥕' }
    ],
    
    // 预设的设备类型
    deviceTypes: [
      { id: 'irrigation', name: '水肥一体机', icon: '💧' },
      { id: 'sensor', name: '土壤传感器', icon: '📡' },
      { id: 'weather', name: '环境监测站', icon: '🌡️' }
    ]
  },

  onLoad(options) {
    // 检查是否已经初始化过
    const farmData = wx.getStorageSync('farmData');
    if (farmData) {
      wx.showModal({
        title: '农场已初始化',
        content: '检测到农场信息已存在，是否重新初始化？',
        success: (res) => {
          if (!res.confirm) {
            wx.navigateBack();
          }
        }
      });
    }
  },

  // 步骤导航
  nextStep() {
    if (this.validateCurrentStep()) {
      if (this.data.currentStep < this.data.totalSteps) {
        this.setData({
          currentStep: this.data.currentStep + 1
        });
      } else {
        this.completeInit();
      }
    }
  },

  prevStep() {
    if (this.data.currentStep > 1) {
      this.setData({
        currentStep: this.data.currentStep - 1
      });
    }
  },

  // 验证当前步骤
  validateCurrentStep() {
    switch (this.data.currentStep) {
      case 1:
        return this.validateFarmInfo();
      case 2:
        return this.validatePlots();
      case 3:
        return true; // 最后一步总是有效
      default:
        return false;
    }
  },

  validateFarmInfo() {
    const { name, location, totalArea } = this.data.farmInfo;
    if (!name || !location || !totalArea) {
      wx.showToast({
        title: '请填写完整的农场信息',
        icon: 'none'
      });
      return false;
    }
    return true;
  },

  validatePlots() {
    if (this.data.plots.length === 0) {
      wx.showToast({
        title: '至少需要添加一个地块',
        icon: 'none'
      });
      return false;
    }
    return true;
  },


  // 表单输入处理
  onFarmInfoInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`farmInfo.${field}`]: value
    });
  },

  // 添加地块
  addPlot() {
    const that = this;
    const plotTypeNames = this.data.plotTypes.map(type => type.name);
    
    wx.showActionSheet({
      itemList: plotTypeNames,
      success: (res) => {
        const selectedType = that.data.plotTypes[res.tapIndex];
        that.showPlotForm(selectedType);
      }
    });
  },

  showPlotForm(plotType) {
    const that = this;
    wx.showModal({
      title: '添加' + plotType.name,
      editable: true,
      placeholderText: '请输入地块面积(亩)',
      success: (res) => {
        if (res.confirm && res.content) {
          const area = parseFloat(res.content);
          if (!isNaN(area) && area > 0) {
            const newPlot = {
              id: Date.now(),
              name: plotType.name + (that.data.plots.length + 1),
              type: plotType.id,
              typeIcon: plotType.icon,
              area: area,
              status: 'ready'
            };
            
            that.setData({
              plots: [...that.data.plots, newPlot]
            });
          } else {
            wx.showToast({
              title: '请输入有效的面积数值',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 删除地块
  removePlot(e) {
    const { index } = e.currentTarget.dataset;
    const plots = this.data.plots.filter((_, i) => i !== index);
    this.setData({ plots });
  },

  // 添加设备
  addDevice() {
    const that = this;
    
    wx.showActionSheet({
      itemList: ['扫描设备二维码', '手动输入设备信息'],
      success: (res) => {
        if (res.tapIndex === 0) {
          that.scanDeviceQR();
        } else {
          that.showDeviceTypeSelection();
        }
      }
    });
  },

  // 扫描设备二维码
  scanDeviceQR() {
    const that = this;
    wx.scanCode({
      success: (res) => {
        // 解析二维码中的设备信息
        try {
          const deviceInfo = JSON.parse(res.result);
          const newDevice = {
            id: Date.now(),
            name: deviceInfo.name || '未知设备',
            type: deviceInfo.type || 'sensor',
            typeIcon: that.getDeviceTypeIcon(deviceInfo.type),
            serialNumber: deviceInfo.serialNumber || res.result,
            model: deviceInfo.model || '',
            status: 'online'
          };
          
          that.setData({
            devices: [...that.data.devices, newDevice]
          });
          
          wx.showToast({
            title: '设备添加成功',
            icon: 'success'
          });
        } catch (error) {
          // 如果不是JSON格式，直接作为序列号处理
          that.createDeviceFromSerial(res.result);
        }
      },
      fail: () => {
        wx.showToast({
          title: '扫描失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 显示设备类型选择
  showDeviceTypeSelection() {
    const that = this;
    const deviceTypeNames = this.data.deviceTypes.map(type => type.name);
    
    wx.showActionSheet({
      itemList: deviceTypeNames,
      success: (res) => {
        const selectedType = that.data.deviceTypes[res.tapIndex];
        that.showDeviceForm(selectedType);
      }
    });
  },

  showDeviceForm(deviceType) {
    const that = this;
    wx.showModal({
      title: '添加' + deviceType.name,
      editable: true,
      placeholderText: '请输入设备序列号或ID',
      success: (res) => {
        if (res.confirm && res.content) {
          that.createDeviceFromSerial(res.content, deviceType);
        }
      }
    });
  },

  // 根据序列号创建设备
  createDeviceFromSerial(serialNumber, deviceType = null) {
    const that = this;
    
    // 如果没有指定设备类型，尝试从序列号推断
    if (!deviceType) {
      deviceType = this.inferDeviceType(serialNumber);
    }
    
    const newDevice = {
      id: Date.now(),
      name: deviceType.name + '-' + serialNumber.slice(-4),
      type: deviceType.id,
      typeIcon: deviceType.icon,
      serialNumber: serialNumber,
      status: 'online'
    };
    
    that.setData({
      devices: [...that.data.devices, newDevice]
    });
    
    wx.showToast({
      title: '设备添加成功',
      icon: 'success'
    });
  },

  // 推断设备类型
  inferDeviceType(serialNumber) {
    const serial = serialNumber.toUpperCase();
    if (serial.includes('WF') || serial.includes('IRR')) {
      return this.data.deviceTypes.find(t => t.id === 'irrigation');
    } else if (serial.includes('SNS') || serial.includes('SOIL')) {
      return this.data.deviceTypes.find(t => t.id === 'sensor');
    } else if (serial.includes('WTH') || serial.includes('ENV')) {
      return this.data.deviceTypes.find(t => t.id === 'weather');
    }
    // 默认为传感器
    return this.data.deviceTypes.find(t => t.id === 'sensor');
  },

  // 获取设备类型图标
  getDeviceTypeIcon(typeId) {
    const deviceType = this.data.deviceTypes.find(t => t.id === typeId);
    return deviceType ? deviceType.icon : '📡';
  },

  // 删除设备
  removeDevice(e) {
    const { index } = e.currentTarget.dataset;
    const devices = this.data.devices.filter((_, i) => i !== index);
    this.setData({ devices });
  },

  // 地块设备绑定
  bindDevicesToPlots() {
    const plots = this.data.plots.map(plot => {
      // 为每个地块初始化设备数组和设备计数
      if (!plot.devices) {
        plot.devices = [];
      }
      return plot;
    });
    
    // 将设备分配给地块 - 智能分配策略
    this.data.devices.forEach((device, index) => {
      if (plots.length > 0) {
        let targetPlotIndex;
        
        // 根据设备类型智能分配
        if (device.type === 'irrigation') {
          // 灌溉设备优先分配给面积大的地块
          targetPlotIndex = plots.reduce((maxIndex, plot, currentIndex) => 
            plot.area > plots[maxIndex].area ? currentIndex : maxIndex, 0);
        } else {
          // 其他设备均匀分配
          targetPlotIndex = index % plots.length;
        }
        
        // 将设备对象添加到地块
        plots[targetPlotIndex].devices.push({
          id: device.id,
          name: device.name,
          type: device.type,
          serialNumber: device.serialNumber,
          status: 'online'
        });
      }
    });
    
    // 更新地块的设备计数
    plots.forEach(plot => {
      plot.deviceCount = plot.devices.length;
    });
    
    this.setData({ plots });
    
    wx.showToast({
      title: `${this.data.devices.length}台设备已智能绑定到${plots.length}个地块`,
      icon: 'success'
    });
  },

  // 完成初始化
  completeInit() {
    // 确保设备已绑定到地块
    this.bindDevicesToPlots();
    
    const initData = {
      farmInfo: {
        ...this.data.farmInfo,
        // 确保有位置信息
        latitude: this.data.farmInfo.latitude || '',
        longitude: this.data.farmInfo.longitude || '',
        coordinates: this.data.farmInfo.coordinates || ''
      },
      plots: this.data.plots,
      devices: this.data.devices,
      initTime: new Date().toISOString(),
      version: '2.0'
    };
    
    // 保存到本地存储
    wx.setStorageSync('farmData', initData);
    wx.setStorageSync('farmInitialized', true);
    
    wx.showModal({
      title: '初始化完成',
      content: '农场信息已设置完成！\n\n您现在可以开始使用智慧农业系统了。',
      showCancel: false,
      success: () => {
        wx.switchTab({
          url: '/pages/farm/farm'
        });
      }
    });
  },

  // 使用腾讯地图位置选择插件
  openLocationPicker() {
    const key = 'MTDBZ-RVJLB-AYLUH-NNYOY-5SJG3-UNF3M'; // 腾讯地图API Key
    const referer = '智慧农业小程序'; // 小程序名称
    
    wx.navigateTo({
      url: `plugin://chooseLocation/index?key=${key}&referer=${referer}&hotCity=北京,上海,南京,苏州`
    });
  },

  onShow() {
    // 获取插件返回的位置信息
    try {
      const chooseLocation = requirePlugin("chooseLocation");
      const location = chooseLocation.getLocation();
      
      if (location) {
        // 处理选择的位置信息
        this.setData({
          'farmInfo.location': location.address || location.name,
          'farmInfo.latitude': location.latitude,
          'farmInfo.longitude': location.longitude,
          'farmInfo.coordinates': `${location.latitude.toFixed(6)}°N, ${location.longitude.toFixed(6)}°E`
        });
        
        wx.showToast({
          title: '位置选择成功',
          icon: 'success'
        });
        
        // 清除插件中的位置信息，避免重复获取
        chooseLocation.setLocation(null);
      }
    } catch (error) {
      console.log('插件调用失败:', error);
    }
  },

  // 跳过初始化
  skipInit() {
    wx.showModal({
      title: '跳过初始化',
      content: '跳过初始化将使用默认配置，您可以后续在设置中修改。确定要跳过吗？',
      success: (res) => {
        if (res.confirm) {
          // 使用默认配置
          const defaultData = {
            farmInfo: {
              name: '示例农场',
              location: '默认位置',
              totalArea: 50,
              description: '默认农场配置'
            },
            plots: [
              {
                id: 1,
                name: '示例地块',
                type: 'greenhouse',
                area: 25,
                status: 'ready'
              }
            ],
            devices: [],
            initTime: new Date().toISOString(),
            version: '2.0'
          };
          
          wx.setStorageSync('farmData', defaultData);
          wx.setStorageSync('farmInitialized', true);
          
          wx.switchTab({
            url: '/pages/farm/farm'
          });
        }
      }
    });
  }
});