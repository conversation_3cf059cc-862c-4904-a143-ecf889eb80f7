package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备实体类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("devices")
public class Device {

    /**
     * 设备ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备编码
     */
    @TableField("device_code")
    private String deviceCode;

    /**
     * 设备名称
     */
    @TableField("name")
    private String name;

    /**
     * 设备类型ID
     */
    @TableField("device_type_id")
    private Long deviceTypeId;

    /**
     * 所属地块ID
     */
    @TableField("plot_id")
    private Long plotId;

    /**
     * 设备位置描述
     */
    @TableField("location")
    private String location;

    /**
     * 纬度
     */
    @TableField("latitude")
    private BigDecimal latitude;

    /**
     * 经度
     */
    @TableField("longitude")
    private BigDecimal longitude;

    /**
     * 安装日期
     */
    @TableField("install_date")
    private LocalDate installDate;

    /**
     * 最后维护日期
     */
    @TableField("last_maintenance_date")
    private LocalDate lastMaintenanceDate;

    /**
     * 下次维护日期
     */
    @TableField("next_maintenance_date")
    private LocalDate nextMaintenanceDate;

    /**
     * 在线状态: 0-离线, 1-在线
     */
    @TableField("online_status")
    private Integer onlineStatus;

    /**
     * 工作状态: 0-停止, 1-运行, 2-故障
     */
    @TableField("work_status")
    private Integer workStatus;

    /**
     * 电池电量百分比
     */
    @TableField("battery_level")
    private Integer batteryLevel;

    /**
     * 信号强度
     */
    @TableField("signal_strength")
    private Integer signalStrength;

    /**
     * 固件版本
     */
    @TableField("firmware_version")
    private String firmwareVersion;

    /**
     * 设备配置参数
     */
    @TableField("config_params")
    private String configParams;

    /**
     * 状态: 0-禁用, 1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 最后心跳时间
     */
    @TableField("last_heartbeat")
    private LocalDateTime lastHeartbeat;

    /**
     * 关联的设备类型信息 (多对一)
     */
    @TableField(exist = false)
    private DeviceType deviceType;

    /**
     * 关联的地块信息 (多对一)
     */
    @TableField(exist = false)
    private Plot plot;

    /**
     * 关联的传感器数据列表 (一对多)
     */
    @TableField(exist = false)
    private List<SensorData> sensorDataList;

    /**
     * 最新传感器数据
     */
    @TableField(exist = false)
    private List<SensorData> latestSensorData;

    /**
     * 设备控制请求内部类
     */
    @Data
    public static class ControlRequest {
        /**
         * 控制动作
         */
        private String action;

        /**
         * 控制参数
         */
        private String parameters;

        /**
         * 持续时间（秒）
         */
        private Integer duration;

        /**
         * 流量控制
         */
        private Integer flowRate;

        /**
         * 施肥配置
         */
        private FertilizerConfig fertilizer;

        @Data
        public static class FertilizerConfig {
            /**
             * 是否启用施肥
             */
            private Boolean enabled;

            /**
             * 施肥配方
             */
            private String formula;

            /**
             * 浓度
             */
            private BigDecimal concentration;
        }
    }

    /**
     * 设备状态信息内部类
     */
    @Data
    public static class StatusInfo {
        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 在线状态
         */
        private Integer onlineStatus;

        /**
         * 工作状态
         */
        private Integer workStatus;

        /**
         * 电池电量
         */
        private Integer batteryLevel;

        /**
         * 信号强度
         */
        private Integer signalStrength;

        /**
         * 最后心跳时间
         */
        private LocalDateTime lastHeartbeat;

        /**
         * 当前工作参数
         */
        private String currentParams;

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 运行时长（分钟）
         */
        private Integer runningTime;
    }

    /**
     * 设备统计信息内部类
     */
    @Data
    public static class Statistics {
        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 总运行时长（小时）
         */
        private Integer totalRunningHours;

        /**
         * 本月运行时长（小时）
         */
        private Integer monthlyRunningHours;

        /**
         * 总操作次数
         */
        private Integer totalOperations;

        /**
         * 本月操作次数
         */
        private Integer monthlyOperations;

        /**
         * 故障次数
         */
        private Integer faultCount;

        /**
         * 维护次数
         */
        private Integer maintenanceCount;

        /**
         * 在线率（%）
         */
        private BigDecimal onlineRate;

        /**
         * 平均响应时间（毫秒）
         */
        private Integer avgResponseTime;

        /**
         * 最后维护时间
         */
        private LocalDateTime lastMaintenanceTime;

        /**
         * 下次维护时间
         */
        private LocalDateTime nextMaintenanceTime;
    }

    /**
     * 设备健康信息内部类
     */
    @Data
    public static class HealthInfo {
        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 健康状态
         */
        private String healthStatus;

        /**
         * 健康评分（0-100）
         */
        private Integer healthScore;

        /**
         * 检查时间
         */
        private LocalDateTime checkTime;

        /**
         * 健康问题列表
         */
        private List<String> healthIssues;

        /**
         * 建议维护项目
         */
        private List<String> maintenanceRecommendations;

        /**
         * 预计寿命（天）
         */
        private Integer estimatedLifeDays;
    }

    /**
     * 设备配置信息内部类
     */
    @Data
    public static class ConfigInfo {
        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 采样间隔（秒）
         */
        private Integer samplingInterval;

        /**
         * 上报间隔（秒）
         */
        private Integer reportingInterval;

        /**
         * 工作模式
         */
        private String workMode;

        /**
         * 阈值配置
         */
        private String thresholdConfig;

        /**
         * 自动控制参数
         */
        private String autoControlParams;

        /**
         * 节能模式
         */
        private Boolean energySavingMode;

        /**
         * 故障自检
         */
        private Boolean selfDiagnostics;

        /**
         * 其他配置参数
         */
        private String otherParams;
    }
}