/* 设备配置页面样式 - 原生版本 */

.config-container {
  background: #F5F5F5;
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* Tab导航样式 */
.config-tabs {
  background: white;
  border-bottom: 1rpx solid #E0E0E0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-nav {
  display: flex;
  align-items: center;
}

.tab-item {
  flex: 1;
  padding: 32rpx 16rpx;
  text-align: center;
  position: relative;
  background: white;
  border: none;
}

.tab-item text {
  font-size: 28rpx;
  color: #666666;
  transition: color 0.3s ease;
}

.tab-item.active text {
  color: #08C160;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #08C160;
  border-radius: 2rpx;
}

/* Tab内容区域 */
.tab-content {
  padding: 0;
}

/* 配置卡片样式 */
.config-card {
  margin: 24rpx 32rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(46, 125, 50, 0.1);
  overflow: hidden;
}

.config-card:first-child {
  margin-top: 32rpx;
}

/* 设备状态概览 */
.status-overview {
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  color: white;
  border: none;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.device-name {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.connection-status.online .status-dot {
  background: #4CAF50;
  box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.3);
}

.connection-status.offline .status-dot {
  background: #F44336;
  box-shadow: 0 0 0 4rpx rgba(244, 67, 54, 0.3);
}

.connection-status .status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.connection-status .status-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  background: rgba(255, 255, 255, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.25);
  border-radius: 20rpx;
  padding: 10rpx 16rpx;
  color: white;
  font-size: 22rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.refresh-btn:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.98);
}

.refresh-icon {
  font-size: 24rpx;
  animation: rotate 0.3s ease;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 状态指标网格 */
.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  padding: 32rpx;
}

.status-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
}

.status-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon.temperature {
  background: rgba(255, 152, 0, 0.3);
}

.status-icon.humidity {
  background: rgba(33, 150, 243, 0.3);
}

.status-icon.water {
  background: rgba(0, 188, 212, 0.3);
}

.status-icon.fertilizer {
  background: rgba(156, 39, 176, 0.3);
}

.status-icon.pressure {
  background: rgba(255, 87, 34, 0.3);
}

.status-icon.flow {
  background: rgba(76, 175, 80, 0.3);
}

.status-icon .icon {
  font-size: 24rpx;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  flex: 1;
}

.status-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.2;
}

.status-value {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  line-height: 1.2;
}

/* 进度条 */
.progress-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
  margin: 4rpx 0;
}

.progress-fill {
  height: 100%;
  background: #4CAF50;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-fill.fertilizer {
  background: #9C27B0;
}

/* 卡片头部 */
.card-header {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.card-header-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

/* 统计网格 */
.stats-grid {
  padding: 0;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.stats-item:last-child {
  border-bottom: none;
}

.stats-label {
  font-size: 28rpx;
  color: #333333;
}

.stats-value {
  font-size: 28rpx;
  color: #666666;
}

/* 表单样式 */
.config-form {
  padding: 0;
}

.form-item {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.form-desc {
  font-size: 22rpx;
  color: #999999;
  margin-bottom: 16rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 16rpx 20rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  background: #FAFAFA;
  font-size: 28rpx;
  color: #333333;
}

.form-input:focus {
  border-color: #2E7D32;
  background: white;
}

/* 输入框带单位 */
.input-with-unit {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.input-with-unit .form-input {
  flex: 1;
  min-width: 0;
}

.unit-label {
  font-size: 24rpx;
  color: #666666;
  white-space: nowrap;
}

/* 选择器字段 */
.picker-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  background: #FAFAFA;
}

.picker-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999999;
}

/* 开关项 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-info {
  flex: 1;
}

/* 配置操作按钮 */
.config-actions {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #F0F0F0;
  display: flex;
  justify-content: center;
  gap: 24rpx;
}


.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  padding: 14rpx 28rpx;
  background: #FAFAFA;
  border: 1rpx solid #E0E0E0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #333333;
  min-width: 140rpx;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.98);
  background: #F0F0F0;
}

.action-btn.primary {
  background: #2E7D32;
  border-color: #2E7D32;
  color: white;
}

.action-btn.primary:active {
  background: #1B5E20;
  transform: scale(0.98);
}

.action-btn.secondary {
  background: white;
  border-color: #E0E0E0;
  color: #666666;
}

.action-btn.secondary:active {
  background: #F5F5F5;
  transform: scale(0.98);
}

.btn-icon {
  font-size: 24rpx;
}

/* 添加按钮 */
.add-btn, .scan-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: white;
  border: 1rpx solid #2E7D32;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  color: #2E7D32;
  font-size: 24rpx;
}

/* WiFi连接状态 */
.wifi-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.wifi-status .status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #CCCCCC;
}

.wifi-status.connected .status-dot {
  background: #4CAF50;
}

.wifi-status.connecting .status-dot {
  background: #FF9800;
  animation: pulse 1.5s infinite;
}

.wifi-status.disconnected .status-dot {
  background: #F44336;
}

.wifi-status .status-text {
  font-size: 24rpx;
  color: #666666;
}


/* 传感器列表 */
.sensor-list {
  padding: 0;
}

.sensor-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.sensor-item:last-child {
  border-bottom: none;
}

.sensor-left {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  flex: 1;
}

.sensor-status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #CCCCCC;
  margin-top: 8rpx;
}

.sensor-status-dot.online {
  background: #4CAF50;
  animation: pulse 2s infinite;
}

.sensor-status-dot.offline {
  background: #F44336;
}

.sensor-status-dot.warning {
  background: #FF9800;
  animation: pulse 1s infinite;
}

.sensor-info {
  flex: 1;
}

.sensor-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.sensor-desc {
  font-size: 24rpx;
  color: #666666;
}

.sensor-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
  max-width: 300rpx;
}

.sensor-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.current-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

.value-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.value-range {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  line-height: 1.2;
}

.value-range.normal {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.value-range.abnormal {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.calibration-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2rpx;
}

.calibration-status {
  font-size: 22rpx;
  color: #666666;
  font-weight: 500;
}

.last-calibration {
  font-size: 18rpx;
  color: #999999;
}

.sensor-controls {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.sensor-btn {
  padding: 8rpx 16rpx;
  background: #FAFAFA;
  border: 1rpx solid #E0E0E0;
  border-radius: 6rpx;
  font-size: 22rpx;
  color: #333333;
}

.sensor-btn.warning {
  color: #FF9800;
  border-color: #FF9800;
}

/* 空状态样式 */
.empty-sensor {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 24rpx;
}

.empty-btn {
  padding: 16rpx 32rpx;
  background: #2E7D32;
  border: none;
  border-radius: 8rpx;
  color: white;
  font-size: 24rpx;
}

/* 底部操作区域 */
.config-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 32rpx;
  border-top: 1rpx solid #E0E0E0;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.footer-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  width: 100%;
  padding: 24rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.footer-btn.primary {
  background: #2E7D32;
  border: 1rpx solid #2E7D32;
  color: white;
}

.footer-btn.secondary {
  background: white;
  border: 1rpx solid #E0E0E0;
  color: #666666;
}

/* 选择器弹窗 */
.picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.picker-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.picker-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 16rpx 16rpx 0 0;
  max-height: 60vh;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.picker-cancel,
.picker-confirm {
  font-size: 32rpx;
  color: #2E7D32;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.picker-view {
  height: 400rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #333333;
}

/* 模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  font-size: 40rpx;
  color: #999999;
  cursor: pointer;
}


.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F0F0F0;
}

.modal-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
}

.modal-btn.cancel {
  background: white;
  border: 1rpx solid #E0E0E0;
  color: #666666;
}

.modal-btn.confirm {
  background: #2E7D32;
  border: 1rpx solid #2E7D32;
  color: white;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .config-card {
    margin: 16rpx 24rpx;
  }
  
  .card-header,
  .card-header-with-action {
    padding: 20rpx 24rpx;
  }
  
  .form-item {
    padding: 20rpx 24rpx;
  }
  
  .config-actions {
    padding: 20rpx 24rpx;
  }
  
  .config-footer {
    padding: 24rpx;
  }
}