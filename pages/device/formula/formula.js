Page({
  data: {
    formulaData: {
      name: '',
      cropIndex: 0,
      stageIndex: 0,
      description: '',
      nitrogen: 20,
      phosphorus: 10,
      potassium: 30,
      targetEC: '1.5',
      targetPH: '6.5',
      concentration: '80',
      frequencyIndex: 0,
      duration: '15',
      seasons: ['春季', '夏季'],
      notes: ''
    },
    
    cropOptions: ['番茄', '黄瓜', '草莓', '生菜', '菠菜', '青椒', '茄子', '豆角', '萝卜', '白菜'],
    stageOptions: ['苗期', '生长期', '开花期', '结果期', '成熟期'],
    frequencyOptions: ['每天', '每2天', '每3天', '每周', '每2周'],
    seasonOptions: ['春季', '夏季', '秋季', '冬季'],
    
    microElements: [
      { name: '铁 (Fe)', value: '', placeholder: '2.0' },
      { name: '锰 (Mn)', value: '', placeholder: '0.5' },
      { name: '锌 (Zn)', value: '', placeholder: '0.3' },
      { name: '铜 (Cu)', value: '', placeholder: '0.2' },
      { name: '硼 (B)', value: '', placeholder: '0.5' },
      { name: '钼 (Mo)', value: '', placeholder: '0.1' }
    ],
    
    isEditing: false,
    formulaId: null
  },

  onLoad(options) {
    const action = options.action;
    const id = options.id;
    
    if (action === 'edit' && id) {
      this.setData({
        isEditing: true,
        formulaId: id
      });
      this.loadFormulaData(id);
    } else {
      // 新增配方，设置默认值
      this.setDefaultFormula();
    }
    
    // 设置导航标题
    wx.setNavigationBarTitle({
      title: action === 'edit' ? '编辑配方' : '新增配方'
    });
  },

  loadFormulaData(id) {
    wx.showLoading({
      title: '加载配方数据...'
    });
    
    // 模拟从数据库加载配方数据
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟数据
      const mockFormula = {
        name: '番茄生长期专用配方',
        cropIndex: 0,
        stageIndex: 1,
        description: '适用于番茄生长期的营养配方，促进植株健康生长',
        nitrogen: 22,
        phosphorus: 12,
        potassium: 35,
        targetEC: '1.8',
        targetPH: '6.2',
        concentration: '85',
        frequencyIndex: 1,
        duration: '18',
        seasons: ['春季', '夏季', '秋季'],
        notes: '注意观察叶片颜色变化，如出现黄化需要调整氮肥用量'
      };
      
      // 加载微量元素数据
      const microElements = this.data.microElements.map(item => ({
        ...item,
        value: item.name.includes('铁') ? '2.5' : 
               item.name.includes('锰') ? '0.8' :
               item.name.includes('锌') ? '0.4' :
               item.name.includes('铜') ? '0.15' :
               item.name.includes('硼') ? '0.6' : '0.08'
      }));
      
      this.setData({
        formulaData: mockFormula,
        microElements: microElements
      });
    }, 1000);
  },

  setDefaultFormula() {
    // 根据作物类型设置推荐默认值
    const cropIndex = this.data.formulaData.cropIndex;
    const defaults = {
      0: { nitrogen: 20, phosphorus: 10, potassium: 30, ec: '1.5', ph: '6.2' }, // 番茄
      1: { nitrogen: 18, phosphorus: 12, potassium: 28, ec: '1.6', ph: '6.0' }, // 黄瓜
      2: { nitrogen: 15, phosphorus: 15, potassium: 35, ec: '1.4', ph: '5.8' }, // 草莓
    };
    
    const defaultValues = defaults[cropIndex] || defaults[0];
    
    this.setData({
      'formulaData.nitrogen': defaultValues.nitrogen,
      'formulaData.phosphorus': defaultValues.phosphorus,
      'formulaData.potassium': defaultValues.potassium,
      'formulaData.targetEC': defaultValues.ec,
      'formulaData.targetPH': defaultValues.ph
    });
  },

  // 基本信息输入方法
  onNameInput(e) {
    this.setData({
      'formulaData.name': e.detail.value
    });
  },

  onCropChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      'formulaData.cropIndex': index
    });
    
    // 根据作物类型调整推荐配方
    this.adjustFormulaForCrop(index);
  },

  onStageChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      'formulaData.stageIndex': index
    });
    
    // 根据生长阶段调整配方
    this.adjustFormulaForStage(index);
  },

  onDescriptionInput(e) {
    this.setData({
      'formulaData.description': e.detail.value
    });
  },

  adjustFormulaForCrop(cropIndex) {
    const adjustments = {
      0: { nitrogen: 20, phosphorus: 10, potassium: 30 }, // 番茄
      1: { nitrogen: 18, phosphorus: 12, potassium: 28 }, // 黄瓜
      2: { nitrogen: 15, phosphorus: 15, potassium: 35 }, // 草莓
      3: { nitrogen: 25, phosphorus: 8, potassium: 20 },  // 生菜
      4: { nitrogen: 22, phosphorus: 10, potassium: 25 }  // 菠菜
    };
    
    const adjustment = adjustments[cropIndex];
    if (adjustment) {
      this.setData({
        'formulaData.nitrogen': adjustment.nitrogen,
        'formulaData.phosphorus': adjustment.phosphorus,
        'formulaData.potassium': adjustment.potassium
      });
    }
  },

  adjustFormulaForStage(stageIndex) {
    const currentFormula = this.data.formulaData;
    let nitrogen = currentFormula.nitrogen;
    let phosphorus = currentFormula.phosphorus;
    let potassium = currentFormula.potassium;
    
    switch (stageIndex) {
      case 0: // 苗期
        nitrogen = Math.max(nitrogen - 5, 10);
        phosphorus = Math.min(phosphorus + 3, 20);
        break;
      case 1: // 生长期
        nitrogen = Math.min(nitrogen + 3, 30);
        break;
      case 2: // 开花期
        phosphorus = Math.min(phosphorus + 5, 25);
        break;
      case 3: // 结果期
        potassium = Math.min(potassium + 8, 50);
        break;
    }
    
    this.setData({
      'formulaData.nitrogen': nitrogen,
      'formulaData.phosphorus': phosphorus,
      'formulaData.potassium': potassium
    });
  },

  // NPK滑动条方法 - 原生滑块（已弃用）
  onNitrogenChange(e) {
    this.setData({
      'formulaData.nitrogen': e.detail.value
    });
  },

  onPhosphorusChange(e) {
    this.setData({
      'formulaData.phosphorus': e.detail.value
    });
  },

  onPotassiumChange(e) {
    this.setData({
      'formulaData.potassium': e.detail.value
    });
  },

  // 自定义营养元素滑块触摸事件
  onNutrientTouchStart(e) {
    const type = e.currentTarget.dataset.type;
    const maxValue = parseFloat(e.currentTarget.dataset.max);
    
    this.setData({
      [`${type}Dragging`]: true,
      [`${type}StartX`]: e.touches[0].clientX,
      [`${type}MaxValue`]: maxValue
    });
    
    // 获取滑块轨道的宽度
    const query = wx.createSelectorQuery();
    query.select(`.custom-nutrient-slider.${type} .slider-track`).boundingClientRect((rect) => {
      this.setData({
        [`${type}Width`]: rect.width
      });
    }).exec();
  },

  onNutrientTouchMove(e) {
    const type = e.currentTarget.dataset.type;
    const draggingKey = `${type}Dragging`;
    const startXKey = `${type}StartX`;
    const widthKey = `${type}Width`;
    const maxValueKey = `${type}MaxValue`;
    
    if (!this.data[draggingKey] || !this.data[widthKey]) return;
    
    const deltaX = e.touches[0].clientX - this.data[startXKey];
    const percentage = Math.max(0, Math.min(100, (deltaX / this.data[widthKey]) * 100));
    const value = Math.round((percentage / 100) * this.data[maxValueKey]);
    
    this.setData({
      [`formulaData.${type}`]: Math.max(0, Math.min(this.data[maxValueKey], value))
    });
  },

  onNutrientTouchEnd(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      [`${type}Dragging`]: false
    });
  },

  // 微量元素输入
  onMicroElementInput(e) {
    const name = e.currentTarget.dataset.name;
    const value = e.detail.value;
    
    const microElements = this.data.microElements.map(item => {
      if (item.name === name) {
        item.value = value;
      }
      return item;
    });
    
    this.setData({
      microElements: microElements
    });
  },

  // 目标参数输入
  onTargetECInput(e) {
    this.setData({
      'formulaData.targetEC': e.detail.value
    });
  },

  onTargetPHInput(e) {
    this.setData({
      'formulaData.targetPH': e.detail.value
    });
  },

  onConcentrationInput(e) {
    this.setData({
      'formulaData.concentration': e.detail.value
    });
  },

  // 使用说明相关方法
  onFrequencyChange(e) {
    this.setData({
      'formulaData.frequencyIndex': e.detail.value
    });
  },

  onDurationInput(e) {
    this.setData({
      'formulaData.duration': e.detail.value
    });
  },

  onSeasonChange(e) {
    this.setData({
      'formulaData.seasons': e.detail.value
    });
  },

  onNotesInput(e) {
    this.setData({
      'formulaData.notes': e.detail.value
    });
  },

  // 操作方法
  previewFormula() {
    const formula = this.data.formulaData;
    
    if (!formula.name.trim()) {
      wx.showToast({
        title: '请输入配方名称',
        icon: 'none'
      });
      return;
    }
    
    const preview = `配方预览：\n\n` +
      `名称：${formula.name}\n` +
      `作物：${this.data.cropOptions[formula.cropIndex]}\n` +
      `阶段：${this.data.stageOptions[formula.stageIndex]}\n` +
      `NPK配比：${formula.nitrogen}%-${formula.phosphorus}%-${formula.potassium}%\n` +
      `目标EC：${formula.targetEC} mS/cm\n` +
      `目标pH：${formula.targetPH}\n` +
      `施肥浓度：${formula.concentration}%\n` +
      `施肥频率：${this.data.frequencyOptions[formula.frequencyIndex]}`;
    
    wx.showModal({
      title: '配方预览',
      content: preview,
      showCancel: false,
      confirmText: '确定'
    });
  },

  testFormula() {
    wx.showModal({
      title: '测试配方',
      content: '是否要使用此配方进行小规模测试？测试将在指定区域进行5分钟的试运行。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '开始测试...'
          });
          
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '测试完成，配方运行正常',
              icon: 'success',
              duration: 2000
            });
          }, 3000);
        }
      }
    });
  },

  saveFormula() {
    const formula = this.data.formulaData;
    
    // 表单验证
    if (!formula.name.trim()) {
      wx.showToast({
        title: '请输入配方名称',
        icon: 'none'
      });
      return;
    }
    
    if (!formula.targetEC || parseFloat(formula.targetEC) <= 0) {
      wx.showToast({
        title: '请输入有效的EC值',
        icon: 'none'
      });
      return;
    }
    
    if (!formula.targetPH || parseFloat(formula.targetPH) < 5.0 || parseFloat(formula.targetPH) > 8.0) {
      wx.showToast({
        title: '请输入有效的pH值 (5.0-8.0)',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: this.data.isEditing ? '更新配方中...' : '保存配方中...'
    });
    
    // 模拟保存API调用
    setTimeout(() => {
      wx.hideLoading();
      
      const successMessage = this.data.isEditing ? '配方更新成功' : '配方保存成功';
      
      wx.showToast({
        title: successMessage,
        icon: 'success',
        duration: 1500,
        success: () => {
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      });
    }, 2000);
  },

  // 快捷配方推荐
  recommendFormula() {
    const cropIndex = this.data.formulaData.cropIndex;
    const stageIndex = this.data.formulaData.stageIndex;
    
    wx.showActionSheet({
      itemList: ['通用配方', '高产配方', '有机配方', '节能配方'],
      success: (res) => {
        const templates = {
          0: { nitrogen: 20, phosphorus: 10, potassium: 30, ec: '1.5', ph: '6.5' },
          1: { nitrogen: 25, phosphorus: 15, potassium: 35, ec: '1.8', ph: '6.2' },
          2: { nitrogen: 18, phosphorus: 12, potassium: 28, ec: '1.4', ph: '6.8' },
          3: { nitrogen: 15, phosphorus: 8, potassium: 25, ec: '1.2', ph: '6.5' }
        };
        
        const template = templates[res.tapIndex];
        if (template) {
          this.setData({
            'formulaData.nitrogen': template.nitrogen,
            'formulaData.phosphorus': template.phosphorus,
            'formulaData.potassium': template.potassium,
            'formulaData.targetEC': template.ec,
            'formulaData.targetPH': template.ph
          });
          
          wx.showToast({
            title: '已应用推荐配方',
            icon: 'success'
          });
        }
      }
    });
  }
})