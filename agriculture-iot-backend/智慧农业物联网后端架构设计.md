# 智慧农业物联网后端架构设计

## 项目概述

基于SpringBoot3 + MyBatis-Plus + JDK17的智慧农业物联网后端系统，为微信小程序提供完整的业务API支持。

## 技术栈

- **框架**: Spring Boot 3.2.x
- **数据库**: MySQL 8.0+
- **缓存**: Redis 7.0+
- **ORM**: MyBatis-Plus 3.5.x
- **JDK**: OpenJDK 17
- **构建工具**: Maven 3.9+
- **API文档**: SpringDoc OpenAPI 3
- **日志**: Logback + SLF4J
- **监控**: Spring Boot Actuator + Micrometer

## 项目结构

```
agriculture-iot-backend/
├── src/main/java/com/agriculture/iot/
│   ├── AgricultureIotApplication.java          # 启动类
│   ├── config/                                 # 配置类
│   │   ├── DatabaseConfig.java                # 数据库配置
│   │   ├── RedisConfig.java                   # Redis配置
│   │   ├── ScheduleConfig.java                # 定时任务配置
│   │   └── WebConfig.java                     # Web配置
│   ├── controller/                            # 控制器层
│   │   ├── FarmController.java                # 农场管理
│   │   ├── PlotController.java                # 地块管理
│   │   ├── DeviceController.java              # 设备管理
│   │   ├── SensorController.java              # 传感器数据
│   │   ├── IrrigationController.java          # 灌溉控制
│   │   ├── FertilizerController.java          # 施肥控制
│   │   ├── ScheduleController.java            # 调度管理
│   │   ├── AlarmController.java               # 报警管理
│   │   └── WeatherController.java             # 天气服务
│   ├── service/                               # 业务逻辑层
│   │   ├── FarmService.java
│   │   ├── PlotService.java
│   │   ├── DeviceService.java
│   │   ├── SensorService.java
│   │   ├── IrrigationService.java
│   │   ├── FertilizerService.java
│   │   ├── ScheduleService.java
│   │   ├── AlarmService.java
│   │   └── WeatherService.java
│   ├── mapper/                                # 数据访问层
│   │   ├── FarmMapper.java
│   │   ├── PlotMapper.java
│   │   ├── DeviceMapper.java
│   │   ├── SensorDataMapper.java
│   │   ├── IrrigationLogMapper.java
│   │   ├── FertilizerLogMapper.java
│   │   ├── ScheduleTaskMapper.java
│   │   └── AlarmRecordMapper.java
│   ├── entity/                                # 实体类
│   │   ├── Farm.java                          # 农场实体
│   │   ├── Plot.java                          # 地块实体
│   │   ├── Device.java                        # 设备实体
│   │   ├── SensorData.java                    # 传感器数据
│   │   ├── IrrigationLog.java                 # 灌溉记录
│   │   ├── FertilizerLog.java                 # 施肥记录
│   │   ├── ScheduleTask.java                  # 调度任务
│   │   └── AlarmRecord.java                   # 报警记录
│   ├── dto/                                   # 数据传输对象
│   │   ├── request/                           # 请求DTO
│   │   └── response/                          # 响应DTO
│   ├── common/                                # 公共组件
│   │   ├── result/                            # 统一响应结果
│   │   ├── exception/                         # 异常处理
│   │   ├── constant/                          # 常量定义
│   │   └── util/                              # 工具类
│   └── task/                                  # 定时任务
│       ├── DataCollectionTask.java            # 数据采集任务
│       ├── IrrigationTask.java                # 灌溉任务
│       └── AlarmCheckTask.java                # 报警检查任务
├── src/main/resources/
│   ├── application.yml                        # 主配置文件
│   ├── application-dev.yml                    # 开发环境配置
│   ├── application-prod.yml                   # 生产环境配置
│   ├── mapper/                                # MyBatis映射文件
│   └── db/migration/                          # 数据库迁移脚本
└── pom.xml                                    # Maven配置文件
```

## 核心业务模块

### 1. 农场管理模块 (Farm Management)
- 农场基本信息管理
- 农场统计数据
- 农场设备概览

### 2. 地块管理模块 (Plot Management)
- 地块信息管理
- 地块设备绑定
- 地块作物管理

### 3. 设备管理模块 (Device Management)
- 设备注册与配置
- 设备状态监控
- 设备控制指令

### 4. 传感器数据模块 (Sensor Data)
- 实时数据采集
- 历史数据查询
- 数据统计分析

### 5. 灌溉控制模块 (Irrigation Control)
- 手动灌溉控制
- 自动灌溉调度
- 灌溉记录管理

### 6. 施肥控制模块 (Fertilizer Control)
- 施肥配方管理
- 施肥执行控制
- 施肥记录跟踪

### 7. 调度管理模块 (Schedule Management)
- 定时任务配置
- 任务执行监控
- 任务历史记录

### 8. 报警管理模块 (Alarm Management)
- 阈值配置管理
- 实时报警检测
- 报警记录查询

## 数据库设计

### 核心实体关系
```
Farm (农场)
├── Plot (地块) [1:N]
    ├── Device (设备) [1:N]
    │   ├── SensorData (传感器数据) [1:N]
    │   ├── IrrigationLog (灌溉记录) [1:N]
    │   └── FertilizerLog (施肥记录) [1:N]
    ├── ScheduleTask (调度任务) [1:N]
    └── AlarmRecord (报警记录) [1:N]
```

### 主要数据表

1. **farms** - 农场表
2. **plots** - 地块表
3. **devices** - 设备表
4. **sensor_data** - 传感器数据表
5. **irrigation_logs** - 灌溉记录表
6. **fertilizer_logs** - 施肥记录表
7. **schedule_tasks** - 调度任务表
8. **alarm_records** - 报警记录表
9. **device_types** - 设备类型表
10. **sensor_types** - 传感器类型表

## API设计规范

### 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### RESTful API规范
- GET: 查询操作
- POST: 创建操作
- PUT: 更新操作
- DELETE: 删除操作

### 主要API端点

#### 农场管理 (已实现)
- `GET /api/farms` - 获取农场列表（支持分页和条件查询）
- `GET /api/farms/{id}` - 获取农场详情
- `POST /api/farms` - 创建农场
- `PUT /api/farms/{id}` - 更新农场
- `DELETE /api/farms/{id}` - 删除农场
- `GET /api/farms/all` - 获取所有启用状态的农场列表
- `GET /api/farms/{id}/statistics` - 获取农场统计信息
- `PATCH /api/farms/{id}/status` - 启用/禁用农场

#### 地块管理 (待实现)
- `GET /api/plots` - 获取地块列表（支持按农场筛选）
- `GET /api/plots/{id}` - 获取地块详情
- `POST /api/plots` - 创建地块
- `PUT /api/plots/{id}` - 更新地块
- `DELETE /api/plots/{id}` - 删除地块
- `GET /api/plots/farm/{farmId}` - 获取指定农场的地块列表
- `GET /api/plots/{id}/devices` - 获取地块绑定的设备列表
- `POST /api/plots/{id}/devices` - 为地块绑定设备
- `DELETE /api/plots/{plotId}/devices/{deviceId}` - 解绑设备

#### 设备管理 (待实现)
- `GET /api/devices` - 获取设备列表（支持按类型、状态筛选）
- `GET /api/devices/{id}` - 获取设备详情
- `POST /api/devices` - 添加设备
- `PUT /api/devices/{id}` - 更新设备
- `DELETE /api/devices/{id}` - 删除设备
- `POST /api/devices/{id}/control` - 设备控制（启停、参数调节）
- `GET /api/devices/{id}/status` - 获取设备实时状态
- `GET /api/devices/types` - 获取设备类型列表
- `POST /api/devices/{id}/bind` - 绑定设备到地块

#### 传感器数据 (待实现)
- `GET /api/sensors/data` - 获取传感器数据（支持时间范围查询）
- `GET /api/sensors/data/latest` - 获取最新数据
- `GET /api/sensors/data/history` - 获取历史数据
- `GET /api/sensors/data/statistics` - 获取统计数据
- `POST /api/sensors/data` - 上传传感器数据（IoT设备推送）
- `GET /api/sensors/data/device/{deviceId}` - 获取指定设备的传感器数据
- `GET /api/sensors/data/plot/{plotId}` - 获取指定地块的传感器数据
- `GET /api/sensors/types` - 获取传感器类型列表

#### 灌溉控制 (待实现)
- `POST /api/irrigation/start` - 开始灌溉
- `POST /api/irrigation/stop` - 停止灌溉
- `GET /api/irrigation/records` - 获取灌溉记录
- `GET /api/irrigation/status` - 获取灌溉状态
- `GET /api/irrigation/schedules` - 获取灌溉计划
- `POST /api/irrigation/schedules` - 创建灌溉计划
- `PUT /api/irrigation/schedules/{id}` - 更新灌溉计划
- `DELETE /api/irrigation/schedules/{id}` - 删除灌溉计划
- `POST /api/irrigation/schedules/{id}/execute` - 立即执行灌溉计划

#### 施肥控制 (待实现)
- `POST /api/fertilizer/apply` - 执行施肥
- `GET /api/fertilizer/formulas` - 获取施肥配方
- `GET /api/fertilizer/logs` - 获取施肥记录
- `GET /api/fertilizer/records` - 获取施肥记录
- `POST /api/fertilizer/records` - 创建施肥记录
- `GET /api/fertilizer/schedules` - 获取施肥计划
- `POST /api/fertilizer/schedules` - 创建施肥计划
- `PUT /api/fertilizer/schedules/{id}` - 更新施肥计划
- `DELETE /api/fertilizer/schedules/{id}` - 删除施肥计划
- `GET /api/fertilizer/types` - 获取肥料类型列表

#### 调度管理 (待实现)
- `GET /api/schedules` - 获取调度任务
- `POST /api/schedules` - 创建调度任务
- `PUT /api/schedules/{id}` - 更新调度任务
- `DELETE /api/schedules/{id}` - 删除调度任务
- `POST /api/schedules/{id}/execute` - 立即执行调度任务
- `GET /api/schedules/history` - 获取调度执行历史
- `PATCH /api/schedules/{id}/status` - 启用/禁用调度任务

#### 报警管理 (待实现)
- `GET /api/alarms` - 获取报警记录
- `GET /api/alarms/active` - 获取活跃报警
- `PUT /api/alarms/{id}/handle` - 处理报警
- `POST /api/alarms/rules` - 创建报警规则
- `PUT /api/alarms/rules/{id}` - 更新报警规则
- `DELETE /api/alarms/rules/{id}` - 删除报警规则
- `GET /api/alarms/rules` - 获取报警规则列表
- `GET /api/alarms/statistics` - 获取报警统计信息

### 详细API规范示例

#### 农场管理API详细规范

**获取农场列表**
```http
GET /api/farms?current=1&size=10&name=张三&status=1
```
响应：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "张三的有机农场",
        "description": "专业有机蔬菜种植",
        "location": "重庆市江北区",
        "latitude": 29.5647,
        "longitude": 106.5507,
        "area": 120.5,
        "ownerId": 1,
        "status": 1,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "current": 1,
    "size": 10,
    "pages": 1
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**创建农场**
```http
POST /api/farms
Content-Type: application/json

{
  "name": "张三的有机农场",
  "description": "专业有机蔬菜种植",
  "location": "重庆市江北区",
  "latitude": 29.5647,
  "longitude": 106.5507,
  "area": 120.5,
  "ownerId": 1
}
```

**设备控制API规范**
```http
POST /api/devices/1/control
Content-Type: application/json

{
  "action": "start_irrigation",
  "parameters": {
    "duration": 1800,
    "flowRate": 80,
    "fertilizer": {
      "enabled": true,
      "formula": "tomato_bloom",
      "concentration": 0.5
    }
  }
}
```

**传感器数据推送API规范**
```http
POST /api/sensors/data
Content-Type: application/json

{
  "deviceId": 1,
  "sensorType": "soil_humidity",
  "value": 65.5,
  "unit": "%",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 缓存策略

### Redis缓存设计
- **实时数据缓存**: 传感器最新数据 (TTL: 5分钟)
- **设备状态缓存**: 设备在线状态 (TTL: 1分钟)
- **统计数据缓存**: 农场统计信息 (TTL: 30分钟)
- **配置缓存**: 系统配置信息 (TTL: 1小时)

### 缓存Key规范
```
sensor:data:{deviceId}:latest
device:status:{deviceId}
farm:stats:{farmId}
system:config:{configKey}
```

## 定时任务设计

### 数据采集任务
- **频率**: 每30秒执行一次
- **功能**: 从IoT设备采集传感器数据

### 报警检查任务
- **频率**: 每1分钟执行一次
- **功能**: 检查传感器数据是否超过阈值

### 自动灌溉任务
- **频率**: 每5分钟执行一次
- **功能**: 根据调度规则执行自动灌溉

### 数据清理任务
- **频率**: 每天凌晨2点执行
- **功能**: 清理过期的历史数据

## 异常处理

### 全局异常处理器
- 业务异常处理
- 参数校验异常
- 系统异常处理
- 数据库异常处理

### 异常码定义
```
1000-1999: 系统异常
2000-2999: 业务异常
3000-3999: 参数异常
4000-4999: 权限异常
```

## 日志管理

### 日志级别
- ERROR: 系统错误
- WARN: 警告信息
- INFO: 关键业务信息
- DEBUG: 调试信息

### 日志分类
- **业务日志**: 记录关键业务操作
- **系统日志**: 记录系统运行状态
- **错误日志**: 记录异常和错误信息
- **访问日志**: 记录API访问情况

## 性能优化

### 数据库优化
- 合理设计索引
- 分页查询优化
- 连接池配置优化
- 慢查询监控

### 缓存优化
- 热点数据缓存
- 查询结果缓存
- 缓存预热策略
- 缓存穿透防护

### 接口优化
- 异步处理
- 批量操作
- 数据压缩
- 响应时间监控

## 安全考虑

### 数据安全
- 敏感数据加密
- SQL注入防护
- XSS攻击防护
- 数据脱敏处理

### 接口安全
- 请求频率限制
- 参数校验
- 异常信息脱敏
- 安全审计日志

## 部署架构

### 开发环境
- 单机部署
- 内存数据库
- 本地缓存

### 生产环境
- 集群部署
- 读写分离
- Redis集群
- 负载均衡

## 监控告警

### 系统监控
- JVM监控
- 数据库监控
- Redis监控
- 接口性能监控

### 业务监控
- 设备在线率
- 数据采集成功率
- 报警响应时间
- 任务执行状态

## 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 统一代码格式化
- 必要的注释说明
- 单元测试覆盖

### Git规范
- 分支管理策略
- 提交信息规范
- 代码审查流程
- 版本发布流程

## 扩展性设计

### 微服务拆分预留
- 按业务模块拆分
- 服务间通信设计
- 数据一致性保证
- 分布式事务处理

### 插件化设计
- 设备驱动插件
- 数据处理插件
- 报警规则插件
- 第三方集成插件

## 实际实现状态

### ✅ 已完成实现
- [x] 完整的项目结构和配置文件
- [x] 15个数据库表的DDL脚本
- [x] 所有实体类（Farm, Plot, Device, SensorData等）
- [x] 统一响应结果类（Result）
- [x] 全局异常处理机制
- [x] MyBatis-Plus配置
- [x] Swagger/OpenAPI配置
- [x] 完整的部署文档（README.md）
- [x] 项目启动类
- [x] 农场管理Controller（FarmController）完整实现

### 📋 待实现清单
- [ ] PlotController - 地块管理控制器
- [ ] DeviceController - 设备管理控制器
- [ ] SensorController - 传感器数据控制器
- [ ] IrrigationController - 灌溉控制控制器
- [ ] FertilizerController - 施肥控制控制器
- [ ] ScheduleController - 调度管理控制器
- [ ] AlarmController - 报警管理控制器
- [ ] 所有Service业务逻辑层实现
- [ ] 所有Mapper接口层实现
- [ ] MyBatis XML映射文件
- [ ] DTO数据传输对象（请求/响应类）
- [ ] 定时任务实现（数据采集、报警检查、自动灌溉）
- [ ] 单元测试和集成测试

### 🔄 正在实现
- [x] 农场管理完整流程（Controller + Service + Mapper）
- [ ] 地块管理完整流程
- [ ] 设备管理完整流程
- [ ] 传感器数据管理完整流程
- [ ] 灌溉控制完整流程
- [ ] 施肥控制完整流程
- [ ] 调度管理完整流程
- [ ] 报警管理完整流程

## 快速开始

### 环境要求
- Java 17+
- MySQL 8.0+
- Redis 5.0+
- Maven 3.6+

### 启动步骤
1. 创建数据库并执行`database-schema.sql`
2. 修改`application.yml`中的数据库配置
3. 运行 `mvn spring-boot:run`
4. 访问 http://localhost:8080/api/swagger-ui.html

### 数据库连接配置
```yaml
spring:
  datasource:
    url: **********************************************************************************************************************
    username: root
    password: your_password
  redis:
    host: localhost
    port: 6379
    password: your_redis_password