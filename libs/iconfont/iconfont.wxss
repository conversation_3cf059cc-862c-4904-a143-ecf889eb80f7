/* 智慧农业物联网项目图标库 */

@font-face {
  font-family: 'iconfont';
  src: url('//at.alicdn.com/t/font_agriculture_iot.woff2?t=1') format('woff2'),
       url('//at.alicdn.com/t/font_agriculture_iot.woff?t=1') format('woff'),
       url('//at.alicdn.com/t/font_agriculture_iot.ttf?t=1') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 传感器图标 */
.icon-sensor:before {
  content: "\e601";
}

.icon-temperature:before {
  content: "\e602";
}

.icon-humidity:before {
  content: "\e603";
}

.icon-ph:before {
  content: "\e604";
}

.icon-ec:before {
  content: "\e605";
}

.icon-light:before {
  content: "\e606";
}

.icon-soil:before {
  content: "\e607";
}

/* 设备图标 */
.icon-device:before {
  content: "\e608";
}

.icon-gateway:before {
  content: "\e609";
}

.icon-water:before {
  content: "\e60a";
}

.icon-fertilizer:before {
  content: "\e60b";
}

.icon-pesticide:before {
  content: "\e60c";
}

.icon-irrigation:before {
  content: "\e60d";
}

/* 农作物图标 */
.icon-crop:before {
  content: "\e60e";
}

.icon-wheat:before {
  content: "\e60f";
}

.icon-corn:before {
  content: "\e610";
}

.icon-rice:before {
  content: "\e611";
}

.icon-vegetable:before {
  content: "\e612";
}

.icon-fruit:before {
  content: "\e613";
}

/* 状态图标 */
.icon-online:before {
  content: "\e614";
}

.icon-offline:before {
  content: "\e615";
}

.icon-warning:before {
  content: "\e616";
}

.icon-error:before {
  content: "\e617";
}

.icon-success:before {
  content: "\e618";
}

.icon-alarm:before {
  content: "\e619";
}

/* 功能图标 */
.icon-chart:before {
  content: "\e61a";
}

.icon-dashboard:before {
  content: "\e61b";
}

.icon-map:before {
  content: "\e61c";
}

.icon-settings:before {
  content: "\e61d";
}

.icon-export:before {
  content: "\e61e";
}

.icon-history:before {
  content: "\e61f";
}

.icon-profile:before {
  content: "\e620";
}

.icon-help:before {
  content: "\e621";
}

.icon-monitor:before {
  content: "\e622";
}

.icon-home:before {
  content: "\e623";
}

.icon-search:before {
  content: "\e624";
}

.icon-filter:before {
  content: "\e625";
}

.icon-refresh:before {
  content: "\e626";
}

.icon-add:before {
  content: "\e627";
}

.icon-edit:before {
  content: "\e628";
}

.icon-delete:before {
  content: "\e629";
}

.icon-save:before {
  content: "\e62a";
}

.icon-cancel:before {
  content: "\e62b";
}

.icon-confirm:before {
  content: "\e62c";
}

/* 天气图标 */
.icon-sunny:before {
  content: "\e62d";
}

.icon-cloudy:before {
  content: "\e62e";
}

.icon-rainy:before {
  content: "\e62f";
}

.icon-windy:before {
  content: "\e630";
}

/* 图标尺寸类 */
.icon-xs {
  font-size: 20rpx;
}

.icon-sm {
  font-size: 24rpx;
}

.icon-md {
  font-size: 32rpx;
}

.icon-lg {
  font-size: 40rpx;
}

.icon-xl {
  font-size: 48rpx;
}

.icon-xxl {
  font-size: 56rpx;
}

/* 图标颜色类 */
.icon-primary {
  color: #2E7D32;
}

.icon-success {
  color: #4CAF50;
}

.icon-warning {
  color: #FF9800;
}

.icon-danger {
  color: #F44336;
}

.icon-info {
  color: #2196F3;
}

.icon-muted {
  color: #999;
}

.icon-white {
  color: #fff;
}

/* 图标动画效果 */
@keyframes icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.icon-spin {
  animation: icon-spin 1s linear infinite;
}

.icon-pulse {
  animation: icon-pulse 1.5s ease-in-out infinite;
}

@keyframes icon-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}