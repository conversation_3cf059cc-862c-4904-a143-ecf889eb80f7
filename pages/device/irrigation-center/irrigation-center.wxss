/* 水肥一体机控制中心样式 */

.page-container {
  background-color: #F5F5F5;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 设备状态网格 */
.device-status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: white;
  border-radius: 12rpx;
  border: 1px solid #E0E0E0;
}

.status-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  background-color: #E0E0E0;
}

.status-icon .icon-text {
  font-size: 32rpx;
  color: white;
}

.status-icon.online {
  background-color: #08C160;
}

.status-icon.status-online {
  background-color: #08C160;
}

.status-icon.status-warning {
  background-color: #FF9800;
}

.status-icon.status-offline {
  background-color: #F44336;
}

.status-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.status-name {
  font-size: 26rpx;
  color: #333333;
  font-weight: 600;
}

.status-value {
  font-size: 24rpx;
  color: #666666;
}

.status-value.status-online {
  color: #08C160;
}

.status-value.status-warning {
  color: #FF9800;
}

.status-value.status-offline {
  color: #F44336;
}

/* 快速控制面板 */
.quick-control-panel {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.control-row {
  display: flex;
  gap: 20rpx;
}

.control-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 20rpx;
  background-color: white;
  border: 2rpx solid #E0E0E0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.control-btn:active {
  transform: scale(0.95);
}

.control-btn.primary {
  border-color: #08C160;
  background-color: #E8F5E8;
}

.control-btn.primary.active {
  background-color: #08C160;
  color: white;
}

.control-btn.secondary {
  border-color: #1976D2;
  background-color: #E3F2FD;
}

.control-btn.secondary.active {
  background-color: #1976D2;
  color: white;
}

.control-btn.warning {
  border-color: #F44336;
  background-color: #FFEBEE;
}

.btn-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}

.btn-text {
  font-size: 26rpx;
  font-weight: 600;
}

/* 运行参数 */
.running-params {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.param-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.param-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.param-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

/* 进度信息 */
.progress-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.progress-label {
  font-size: 26rpx;
  color: #333333;
  min-width: 120rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background-color: #E0E0E0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #08C160;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666666;
  min-width: 80rpx;
  text-align: right;
}

/* 功能网格 */
.function-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: white;
  border-radius: 12rpx;
  border: 1px solid #E0E0E0;
  transition: all 0.3s ease;
}

.function-item:active {
  transform: scale(0.95);
  background-color: #F5F5F5;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  background-color: #E0E0E0;
}

.function-icon .icon-text {
  font-size: 40rpx;
  color: white;
}

.function-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.function-desc {
  font-size: 22rpx;
  color: #666666;
  text-align: center;
  line-height: 1.4;
}

/* 今日统计 */
.today-stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #08C160;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #666666;
  text-align: center;
  line-height: 1.3;
}

/* 背景颜色 */
.primary-bg {
  background-color: #08C160;
}

.secondary-bg {
  background-color: #1976D2;
}

.warning-bg {
  background-color: #FF9800;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background-color: white;
  padding: 40rpx;
  border-radius: 12rpx;
  color: #333333;
  font-size: 28rpx;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .device-status-grid {
    grid-template-columns: 1fr;
  }
  
  .function-grid {
    grid-template-columns: 1fr;
  }
  
  .today-stats {
    grid-template-columns: 1fr 1fr;
  }
  
  .control-row {
    flex-direction: column;
  }
  
  .running-params {
    grid-template-columns: 1fr;
  }
}