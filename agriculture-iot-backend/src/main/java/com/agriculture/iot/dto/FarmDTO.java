package com.agriculture.iot.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 农场数据传输对象
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
public class FarmDTO {
    private Long id;
    private String name;
    private String description;
    private BigDecimal area;
    private String address;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String contactPerson;
    private String contactPhone;
    private String contactEmail;
    private Integer status;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    // 统计信息
    private Integer plotCount;
    private Integer deviceCount;
    private Integer activeDeviceCount;
    private Integer alarmCount;
}