package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备类型实体类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("device_types")
public class DeviceType {

    /**
     * 设备类型ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备类型名称
     */
    @TableField("name")
    private String name;

    /**
     * 设备分类: irrigation-灌溉, fertilizer-施肥, sensor-传感器
     */
    @TableField("category")
    private String category;

    /**
     * 制造商
     */
    @TableField("manufacturer")
    private String manufacturer;

    /**
     * 型号
     */
    @TableField("model")
    private String model;

    /**
     * 设备规格参数
     */
    @TableField("specifications")
    private String specifications;

    /**
     * 控制协议: mqtt, http, modbus
     */
    @TableField("control_protocol")
    private String controlProtocol;

    /**
     * 状态: 0-禁用, 1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}