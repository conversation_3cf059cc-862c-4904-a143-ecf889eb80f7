<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriculture.iot.mapper.ScheduleMapper">

    <!-- Schedule.Detail结果映射 -->
    <resultMap id="ScheduleDetailResultMap" type="com.agriculture.iot.entity.Schedule$Detail">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="type" property="type"/>
        <result column="type_text" property="typeText"/>
        <result column="plot_id" property="plotId"/>
        <result column="plot_name" property="plotName"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_name" property="deviceName"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="execution_time" property="executionTime"/>
        <result column="repeat_pattern" property="repeatPattern"/>
        <result column="repeat_pattern_text" property="repeatPatternText"/>
        <result column="repeat_interval" property="repeatInterval"/>
        <result column="status" property="status"/>
        <result column="status_text" property="statusText"/>
        <result column="priority" property="priority"/>
        <result column="priority_text" property="priorityText"/>
        <result column="enabled" property="enabled"/>
        <result column="next_execution" property="nextExecution"/>
        <result column="last_execution" property="lastExecution"/>
        <result column="execution_count" property="executionCount"/>
        <result column="success_count" property="successCount"/>
        <result column="success_rate" property="successRate"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- Schedule.CalendarItem结果映射 -->
    <resultMap id="CalendarItemResultMap" type="com.agriculture.iot.entity.Schedule$CalendarItem">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="type_text" property="typeText"/>
        <result column="execution_time" property="time"/>
        <result column="status" property="status"/>
        <result column="status_text" property="statusText"/>
        <result column="priority" property="priority"/>
        <result column="priority_text" property="priorityText"/>
        <result column="can_execute" property="canExecute"/>
    </resultMap>

    <!-- 分页查询计划列表 -->
    <select id="selectScheduleDetailsPage" resultMap="ScheduleDetailResultMap">
        SELECT 
            s.id,
            s.name,
            s.description,
            s.type,
            CASE s.type 
                WHEN 'irrigation' THEN '灌溉'
                WHEN 'fertilizer' THEN '施肥'
                WHEN 'monitoring' THEN '监测'
                ELSE s.type
            END as type_text,
            s.plot_id,
            p.name as plot_name,
            s.device_id,
            d.name as device_name,
            s.start_date,
            s.end_date,
            s.execution_time,
            s.repeat_pattern,
            CASE s.repeat_pattern
                WHEN 'daily' THEN '每日'
                WHEN 'weekly' THEN '每周'
                WHEN 'monthly' THEN '每月'
                WHEN 'none' THEN '不重复'
                ELSE s.repeat_pattern
            END as repeat_pattern_text,
            s.repeat_interval,
            s.status,
            CASE s.status
                WHEN 'active' THEN '活跃'
                WHEN 'paused' THEN '暂停'
                WHEN 'completed' THEN '完成'
                WHEN 'cancelled' THEN '取消'
                ELSE s.status
            END as status_text,
            s.priority,
            CASE s.priority
                WHEN 'low' THEN '低'
                WHEN 'medium' THEN '中'
                WHEN 'high' THEN '高'
                WHEN 'urgent' THEN '紧急'
                ELSE s.priority
            END as priority_text,
            s.enabled,
            s.next_execution,
            s.last_execution,
            s.execution_count,
            s.success_count,
            CASE 
                WHEN s.execution_count > 0 THEN ROUND((s.success_count * 100.0 / s.execution_count), 2)
                ELSE 0
            END as success_rate,
            s.created_at,
            s.updated_at
        FROM schedules s
        LEFT JOIN plots p ON s.plot_id = p.id
        LEFT JOIN devices d ON s.device_id = d.id
        <where>
            <if test="type != null and type != ''">
                AND s.type = #{type}
            </if>
            <if test="status != null and status != ''">
                AND s.status = #{status}
            </if>
            <if test="plotId != null">
                AND s.plot_id = #{plotId}
            </if>
            <if test="deviceId != null">
                AND s.device_id = #{deviceId}
            </if>
        </where>
        ORDER BY s.created_at DESC
    </select>

    <!-- 根据ID获取计划详情 -->
    <select id="selectScheduleDetailById" resultMap="ScheduleDetailResultMap">
        SELECT 
            s.id,
            s.name,
            s.description,
            s.type,
            CASE s.type 
                WHEN 'irrigation' THEN '灌溉'
                WHEN 'fertilizer' THEN '施肥'
                WHEN 'monitoring' THEN '监测'
                ELSE s.type
            END as type_text,
            s.plot_id,
            p.name as plot_name,
            s.device_id,
            d.name as device_name,
            s.start_date,
            s.end_date,
            s.execution_time,
            s.repeat_pattern,
            s.repeat_interval,
            s.status,
            s.priority,
            s.enabled,
            s.next_execution,
            s.last_execution,
            s.execution_count,
            s.success_count,
            s.created_at,
            s.updated_at
        FROM schedules s
        LEFT JOIN plots p ON s.plot_id = p.id
        LEFT JOIN devices d ON s.device_id = d.id
        WHERE s.id = #{id}
    </select>

    <!-- 获取今日计划 -->
    <select id="selectTodaySchedules" resultMap="CalendarItemResultMap">
        SELECT 
            s.id,
            s.name,
            s.type,
            CASE s.type 
                WHEN 'irrigation' THEN '灌溉'
                WHEN 'fertilizer' THEN '施肥'
                WHEN 'monitoring' THEN '监测'
                ELSE s.type
            END as type_text,
            s.execution_time,
            s.status,
            CASE s.status
                WHEN 'active' THEN '活跃'
                WHEN 'paused' THEN '暂停'
                WHEN 'completed' THEN '完成'
                WHEN 'cancelled' THEN '取消'
                ELSE s.status
            END as status_text,
            s.priority,
            CASE s.priority
                WHEN 'low' THEN '低'
                WHEN 'medium' THEN '中'
                WHEN 'high' THEN '高'
                WHEN 'urgent' THEN '紧急'
                ELSE s.priority
            END as priority_text,
            CASE 
                WHEN s.status = 'active' AND s.enabled = 1 THEN 1
                ELSE 0
            END as can_execute
        FROM schedules s
        WHERE DATE(s.next_execution) = CURDATE()
        AND s.enabled = 1
        <if test="plotId != null">
            AND s.plot_id = #{plotId}
        </if>
        <if test="type != null and type != ''">
            AND s.type = #{type}
        </if>
        ORDER BY s.execution_time ASC
    </select>

    <!-- 获取即将到期的计划 -->
    <select id="selectUpcomingSchedules" resultMap="CalendarItemResultMap">
        SELECT 
            s.id,
            s.name,
            s.type,
            s.execution_time,
            s.status,
            s.priority,
            1 as can_execute
        FROM schedules s
        WHERE s.next_execution BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{hours} HOUR)
        AND s.enabled = 1
        AND s.status = 'active'
        <if test="plotId != null">
            AND s.plot_id = #{plotId}
        </if>
        <if test="type != null and type != ''">
            AND s.type = #{type}
        </if>
        ORDER BY s.next_execution ASC
    </select>

    <!-- 获取逾期计划 -->
    <select id="selectOverdueSchedules" resultMap="CalendarItemResultMap">
        SELECT 
            s.id,
            s.name,
            s.type,
            s.execution_time,
            s.status,
            s.priority,
            1 as can_execute
        FROM schedules s
        WHERE s.next_execution &lt; NOW()
        AND s.enabled = 1
        AND s.status = 'active'
        <if test="plotId != null">
            AND s.plot_id = #{plotId}
        </if>
        <if test="type != null and type != ''">
            AND s.type = #{type}
        </if>
        ORDER BY s.next_execution ASC
    </select>

    <!-- 更新下次执行时间 -->
    <update id="updateNextExecution">
        UPDATE schedules 
        SET next_execution = #{nextExecution}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新执行次数 -->
    <update id="updateExecutionCount">
        UPDATE schedules 
        SET execution_count = execution_count + 1, 
            last_execution = NOW(),
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新成功次数 -->
    <update id="updateSuccessCount">
        UPDATE schedules 
        SET success_count = success_count + 1, 
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 批量更新计划状态 -->
    <update id="batchUpdateStatus">
        UPDATE schedules 
        SET status = #{status}, updated_at = NOW()
        WHERE id IN
        <foreach collection="scheduleIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 获取计划模板列表 -->
    <select id="selectScheduleTemplates" resultType="com.agriculture.iot.entity.Schedule$Template">
        SELECT 
            id,
            name,
            description,
            type,
            category,
            repeat_pattern,
            repeat_interval,
            usage,
            precautions,
            created_at
        FROM schedule_templates
        <where>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 获取活跃计划数量 -->
    <select id="selectActiveScheduleCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM schedules 
        WHERE status = 'active' AND enabled = 1
        <if test="plotId != null">
            AND plot_id = #{plotId}
        </if>
    </select>

</mapper>