<!--配方编辑页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 配方基本信息 -->
    <view class="card">
      <view class="card-title">基本信息</view>
      <view class="form-list">
        <view class="form-item">
          <text class="form-label">配方名称</text>
          <input class="form-input" value="{{formulaData.name}}" 
                 bindinput="onNameInput" placeholder="请输入配方名称"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">适用作物</text>
          <picker range="{{cropOptions}}" value="{{formulaData.cropIndex}}" 
                  bindchange="onCropChange" class="form-picker">
            <view class="picker-view">{{cropOptions[formulaData.cropIndex]}}</view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">生长阶段</text>
          <picker range="{{stageOptions}}" value="{{formulaData.stageIndex}}" 
                  bindchange="onStageChange" class="form-picker">
            <view class="picker-view">{{stageOptions[formulaData.stageIndex]}}</view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">配方描述</text>
          <textarea class="form-textarea" value="{{formulaData.description}}" 
                    bindinput="onDescriptionInput" placeholder="请输入配方描述（可选）"/>
        </view>
      </view>
    </view>

    <!-- 营养成分配置 -->
    <view class="card">
      <view class="card-title">营养成分配置</view>
      
      <!-- NPK配比 -->
      <view class="nutrient-section">
        <view class="section-header">
          <text class="section-title">NPK配比</text>
          <text class="section-desc">单位：%</text>
        </view>
        
        <view class="nutrient-item">
          <view class="nutrient-header">
            <text class="nutrient-label">氮 (N)</text>
            <text class="nutrient-value">{{formulaData.nitrogen}}%</text>
          </view>
          <view class="custom-nutrient-slider nitrogen">
            <view class="slider-track">
              <view class="slider-fill" style="width: {{formulaData.nitrogen / 50 * 100}}%"></view>
              <view class="slider-thumb" 
                    style="left: {{formulaData.nitrogen / 50 * 100}}%" 
                    bindtouchstart="onNutrientTouchStart"
                    bindtouchmove="onNutrientTouchMove"
                    bindtouchend="onNutrientTouchEnd"
                    data-type="nitrogen"
                    data-max="50">
                <view class="thumb-inner"></view>
              </view>
            </view>
          </view>
          <view class="slider-range">
            <text class="range-min">0%</text>
            <text class="range-max">50%</text>
          </view>
        </view>
        
        <view class="nutrient-item">
          <view class="nutrient-header">
            <text class="nutrient-label">磷 (P)</text>
            <text class="nutrient-value">{{formulaData.phosphorus}}%</text>
          </view>
          <view class="custom-nutrient-slider phosphorus">
            <view class="slider-track">
              <view class="slider-fill" style="width: {{formulaData.phosphorus / 30 * 100}}%"></view>
              <view class="slider-thumb" 
                    style="left: {{formulaData.phosphorus / 30 * 100}}%" 
                    bindtouchstart="onNutrientTouchStart"
                    bindtouchmove="onNutrientTouchMove"
                    bindtouchend="onNutrientTouchEnd"
                    data-type="phosphorus"
                    data-max="30">
                <view class="thumb-inner"></view>
              </view>
            </view>
          </view>
          <view class="slider-range">
            <text class="range-min">0%</text>
            <text class="range-max">30%</text>
          </view>
        </view>
        
        <view class="nutrient-item">
          <view class="nutrient-header">
            <text class="nutrient-label">钾 (K)</text>
            <text class="nutrient-value">{{formulaData.potassium}}%</text>
          </view>
          <view class="custom-nutrient-slider potassium">
            <view class="slider-track">
              <view class="slider-fill" style="width: {{formulaData.potassium / 60 * 100}}%"></view>
              <view class="slider-thumb" 
                    style="left: {{formulaData.potassium / 60 * 100}}%" 
                    bindtouchstart="onNutrientTouchStart"
                    bindtouchmove="onNutrientTouchMove"
                    bindtouchend="onNutrientTouchEnd"
                    data-type="potassium"
                    data-max="60">
                <view class="thumb-inner"></view>
              </view>
            </view>
          </view>
          <view class="slider-range">
            <text class="range-min">0%</text>
            <text class="range-max">60%</text>
          </view>
        </view>
      </view>

      <!-- 微量元素 -->
      <view class="micro-section">
        <view class="section-header">
          <text class="section-title">微量元素</text>
          <text class="section-desc">单位：mg/L</text>
        </view>
        
        <view class="micro-grid">
          <view class="micro-item" wx:for="{{microElements}}" wx:key="name">
            <text class="micro-label">{{item.name}}</text>
            <input class="micro-input" type="digit" value="{{item.value}}" 
                   bindinput="onMicroElementInput" data-name="{{item.name}}"
                   placeholder="{{item.placeholder}}"/>
            <text class="micro-unit">mg/L</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 目标参数 -->
    <view class="card">
      <view class="card-title">目标参数</view>
      <view class="target-params">
        <view class="param-item">
          <text class="param-label">EC值</text>
          <view class="param-input-group">
            <input class="param-input" type="digit" value="{{formulaData.targetEC}}" 
                   bindinput="onTargetECInput" placeholder="1.5"/>
            <text class="param-unit">mS/cm</text>
          </view>
          <view class="param-range">
            <text class="range-text">推荐范围: 1.0 - 3.0</text>
          </view>
        </view>
        
        <view class="param-item">
          <text class="param-label">pH值</text>
          <view class="param-input-group">
            <input class="param-input" type="digit" value="{{formulaData.targetPH}}" 
                   bindinput="onTargetPHInput" placeholder="6.5"/>
            <text class="param-unit">pH</text>
          </view>
          <view class="param-range">
            <text class="range-text">推荐范围: 5.5 - 7.5</text>
          </view>
        </view>
        
        <view class="param-item">
          <text class="param-label">施肥浓度</text>
          <view class="param-input-group">
            <input class="param-input" type="number" value="{{formulaData.concentration}}" 
                   bindinput="onConcentrationInput" placeholder="80"/>
            <text class="param-unit">%</text>
          </view>
          <view class="param-range">
            <text class="range-text">推荐范围: 50% - 100%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="card">
      <view class="card-title">使用说明</view>
      <view class="usage-list">
        <view class="usage-item">
          <text class="usage-label">施肥频率</text>
          <picker range="{{frequencyOptions}}" value="{{formulaData.frequencyIndex}}" 
                  bindchange="onFrequencyChange" class="usage-picker">
            <view class="picker-view">{{frequencyOptions[formulaData.frequencyIndex]}}</view>
          </picker>
        </view>
        
        <view class="usage-item">
          <text class="usage-label">单次时长</text>
          <view class="input-group">
            <input class="usage-input" type="number" value="{{formulaData.duration}}" 
                   bindinput="onDurationInput" placeholder="15"/>
            <text class="input-unit">分钟</text>
          </view>
        </view>
        
        <view class="usage-item">
          <text class="usage-label">适用季节</text>
          <checkbox-group bindchange="onSeasonChange">
            <view class="season-group">
              <label class="season-item" wx:for="{{seasonOptions}}" wx:key="*this">
                <checkbox value="{{item}}" checked="{{formulaData.seasons.indexOf(item) !== -1}}"/>
                <text class="season-text">{{item}}</text>
              </label>
            </view>
          </checkbox-group>
        </view>
        
        <view class="usage-item">
          <text class="usage-label">注意事项</text>
          <textarea class="usage-textarea" value="{{formulaData.notes}}" 
                    bindinput="onNotesInput" placeholder="请输入使用注意事项"/>
        </view>
      </view>
    </view>

    <!-- 配方预览 -->
    <view class="card">
      <view class="card-title">配方预览</view>
      <view class="preview-content">
        <view class="preview-item">
          <text class="preview-label">配方名称:</text>
          <text class="preview-value">{{formulaData.name || '未设置'}}</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">适用作物:</text>
          <text class="preview-value">{{cropOptions[formulaData.cropIndex]}}</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">生长阶段:</text>
          <text class="preview-value">{{stageOptions[formulaData.stageIndex]}}</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">NPK配比:</text>
          <text class="preview-value">{{formulaData.nitrogen}}% - {{formulaData.phosphorus}}% - {{formulaData.potassium}}%</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">目标EC:</text>
          <text class="preview-value">{{formulaData.targetEC}} mS/cm</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">目标pH:</text>
          <text class="preview-value">{{formulaData.targetPH}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="btn btn-outline btn-full" bindtap="previewFormula">
        🔍 预览效果
      </button>
      <button class="btn btn-secondary btn-full" bindtap="testFormula">
        🧪 测试配方
      </button>
      <button class="btn btn-primary btn-full" bindtap="saveFormula">
        💾 保存配方
      </button>
    </view>

  </view>
</view>