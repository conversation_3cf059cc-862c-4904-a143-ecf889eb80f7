<!--智慧农业地块管理页面-->
<view class="page-container">
  <view class="container">
    
    <!-- 农场概览 -->
    <view class="card">
      <view class="farm-overview">
        <view class="farm-header">
          <view class="farm-basic">
            <text class="farm-name">{{farmInfo.name}}</text>
            <text class="farm-owner">{{farmInfo.owner}}</text>
            <view class="farm-location">
              <text class="location-icon">📍</text>
              <text class="location-text">{{farmInfo.location}}</text>
            </view>
          </view>
          <view class="farm-weather">
            <view class="weather-icon">{{weatherInfo.icon}}</view>
            <view class="weather-temp">{{weatherInfo.temperature}}°C</view>
            <view class="weather-desc">{{weatherInfo.description}}</view>
          </view>
        </view>
        
        <view class="farm-stats">
          <view class="stat-item">
            <text class="stat-icon">🌾</text>
            <text class="stat-value">{{farmInfo.totalArea}}</text>
            <text class="stat-label">总面积(亩)</text>
          </view>
          <view class="stat-item">
            <text class="stat-icon">📐</text>
            <text class="stat-value">{{farmInfo.plotCount}}</text>
            <text class="stat-label">地块数量</text>
          </view>
          <view class="stat-item">
            <text class="stat-icon">🌱</text>
            <text class="stat-value">{{farmInfo.cropTypes}}</text>
            <text class="stat-label">作物种类</text>
          </view>
          <view class="stat-item">
            <text class="stat-icon">🔧</text>
            <text class="stat-value">{{farmInfo.deviceCount}}</text>
            <text class="stat-label">设备数量</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能导航 -->
    <view class="card">
      <view class="card-title">地块管理</view>
      <view class="function-nav">
        <view class="nav-item {{currentTab === 'plots' ? 'active' : ''}}" bindtap="switchTab" data-tab="plots">
          <view class="nav-icon">🗺️</view>
          <text class="nav-text">地块信息</text>
        </view>
        <view class="nav-item {{currentTab === 'crops' ? 'active' : ''}}" bindtap="switchTab" data-tab="crops">
          <view class="nav-icon">🌿</view>
          <text class="nav-text">作物管理</text>
        </view>
        <view class="nav-item {{currentTab === 'devices' ? 'active' : ''}}" bindtap="switchTab" data-tab="devices">
          <view class="nav-icon">📱</view>
          <text class="nav-text">设备绑定</text>
        </view>
      </view>
    </view>

    <!-- 地块信息模块 -->
    <view wx:if="{{currentTab === 'plots'}}" class="content-section">
      <view class="card">
        <view class="card-title">
          <text>地块信息</text>
          <button class="btn btn-small btn-primary" bindtap="addPlot">
            ➕ 新增地块
          </button>
        </view>
        
        <!-- 地块地图视图 -->
        <view class="plot-map-container">
          <view class="map-view">
            <view class="farm-layout">
              <view class="plot-map-item {{plot.status}} {{selectedPlotId === plot.id ? 'selected' : ''}}" 
                    wx:for="{{plotMapData}}" wx:key="id" wx:for-item="plot"
                    style="left: {{plot.x}}%; top: {{plot.y}}%; width: {{plot.width}}%; height: {{plot.height}}%;"
                    bindtap="selectPlot" data-id="{{plot.id}}">
                <view class="plot-info-overlay">
                  <text class="plot-name">{{plot.name}}</text>
                  <text class="plot-area">{{plot.area}}亩</text>
                  <text class="selected-indicator" wx:if="{{selectedPlotId === plot.id}}">✓</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="map-controls">
            <button class="btn btn-mini btn-outline" bindtap="toggleMapView">
              {{showMap ? '列表视图' : '地图视图'}}
            </button>
            <button class="btn btn-mini btn-outline" bindtap="manageDevices">
              📱 设备管理
            </button>
          </view>
        </view>

        <!-- 地块列表视图 -->
        <view wx:if="{{!showMap}}" class="plot-list">
          <view class="plot-item" wx:for="{{plotList}}" wx:key="id" bindtap="viewPlotDetail" data-id="{{item.id}}">
            <view class="plot-header">
              <view class="plot-basic">
                <text class="plot-name">{{item.name}}</text>
                <text class="plot-area">{{item.area}}亩</text>
                <view class="plot-coordinates">
                  <text class="coord-label">坐标:</text>
                  <text class="coord-value">{{item.coordinates}}</text>
                </view>
              </view>
              <view class="plot-status {{item.status}}">
                <text class="status-dot"></text>
                <text class="status-text">{{item.statusText}}</text>
              </view>
            </view>
            
            <view class="plot-details">
              <view class="detail-section">
                <text class="section-title">基本信息</text>
                <view class="detail-grid">
                  <view class="detail-item">
                    <text class="detail-label">土壤类型</text>
                    <text class="detail-value">{{item.soilType}}</text>
                  </view>
                  <view class="detail-item">
                    <text class="detail-label">排水状况</text>
                    <text class="detail-value">{{item.drainage}}</text>
                  </view>
                  <view class="detail-item">
                    <text class="detail-label">灌溉方式</text>
                    <text class="detail-value">{{item.irrigationType}}</text>
                  </view>
                  <view class="detail-item">
                    <text class="detail-label">地形坡度</text>
                    <text class="detail-value">{{item.slope}}</text>
                  </view>
                </view>
              </view>
              
              <view class="detail-section">
                <text class="section-title">种植规划</text>
                <view class="planting-plan">
                  <view class="current-crop">
                    <text class="crop-name">{{item.currentCrop.name}}</text>
                    <text class="plant-date">种植: {{item.currentCrop.plantDate}}</text>
                    <text class="harvest-date">收获: {{item.currentCrop.harvestDate}}</text>
                  </view>
                  <view class="growth-progress">
                    <view class="progress-bar">
                      <view class="progress-fill" style="width: {{item.currentCrop.progress}}%"></view>
                    </view>
                    <text class="progress-text">生长进度 {{item.currentCrop.progress}}%</text>
                  </view>
                </view>
              </view>
            </view>
            
            <view class="plot-devices">
              <text class="devices-title">关联设备</text>
              <view class="device-list">
                <view class="device-tag {{device.status}}" wx:for="{{item.devices}}" wx:key="id" wx:for-item="device">
                  <text class="device-icon">{{device.icon}}</text>
                  <text class="device-name">{{device.name}}</text>
                  <text class="device-status">{{device.statusText}}</text>
                </view>
              </view>
            </view>

            <view class="plot-actions">
              <button class="btn btn-mini btn-outline" bindtap="editPlotInfo" data-id="{{item.id}}">
                ✏️ 编辑
              </button>
              <button class="btn btn-mini btn-info" bindtap="viewPlotMap" data-id="{{item.id}}">
                🗺️ 位置
              </button>
              <button class="btn btn-mini btn-success" bindtap="managePlotDevices" data-id="{{item.id}}">
                📱 设备
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 作物管理模块 -->
    <view wx:if="{{currentTab === 'crops'}}" class="content-section">
      <view class="card">
        <view class="card-title">
          <text>作物概览</text>
        </view>
        
        <view class="crop-summary">
          <view class="summary-text">
            <text>作物信息已整合到地块管理中，可在地块信息中查看和编辑当前种植的作物。</text>
          </view>
          <button class="btn btn-primary" bindtap="switchTab" data-tab="plots">
            🗺️ 查看地块信息
          </button>
        </view>
      </view>
    </view>

    <!-- 设备绑定模块 -->
    <view wx:if="{{currentTab === 'devices'}}" class="content-section">
      <view class="card">
        <view class="card-title">
          <text>设备关联</text>
          <button class="btn btn-small btn-primary" bindtap="bindDevice">
            🔗 绑定设备
          </button>
        </view>
        
        <view class="device-binding">
          <view class="plot-device-map">
            <view class="plot-device-item" wx:for="{{plotDeviceMap}}" wx:key="plotId">
              <view class="plot-device-header">
                <text class="plot-name">{{item.plotName}}</text>
                <text class="device-count">{{item.deviceCount}}台设备</text>
              </view>
              
              <view class="device-categories">
                <view class="device-category" wx:for="{{item.categories}}" wx:key="type" wx:for-item="category">
                  <view class="category-header">
                    <text class="category-icon">{{category.icon}}</text>
                    <text class="category-name">{{category.name}}</text>
                    <text class="category-count">{{category.devices.length}}台</text>
                  </view>
                  
                  <view class="device-list">
                    <view class="device-item {{device.status}}" wx:for="{{category.devices}}" wx:key="id" wx:for-item="device">
                      <view class="device-info">
                        <text class="device-name">{{device.name}}</text>
                        <text class="device-model">{{device.model}}</text>
                        <text class="device-location">{{device.location}}</text>
                      </view>
                      <view class="device-status">
                        <text class="status-dot"></text>
                        <text class="status-text">{{device.statusText}}</text>
                      </view>
                      <view class="device-actions">
                        <button class="btn btn-mini btn-outline" bindtap="configDevice" data-id="{{device.id}}">
                          ⚙️ 配置
                        </button>
                        <button class="btn btn-mini btn-warning" bindtap="unbindDevice" data-id="{{device.id}}">
                          🔓 解绑
                        </button>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
              
              <view class="device-binding">
                <text class="binding-title">设备绑定</text>
                <view class="device-binding-controls">
                  <button class="btn btn-mini btn-primary" bindtap="bindNewDevice" data-plot-id="{{item.plotId}}">
                    ➕ 绑定设备
                  </button>
                  <button class="btn btn-mini btn-outline" bindtap="optimizeDeviceLayout" data-plot-id="{{item.plotId}}">
                    🎯 优化布局
                  </button>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="permission-settings">
          <view class="permission-title">权限设置</view>
          <view class="permission-list">
            <view class="permission-item" wx:for="{{devicePermissions}}" wx:key="userId">
              <view class="user-info">
                <view class="user-avatar">{{item.avatar}}</view>
                <view class="user-details">
                  <text class="user-name">{{item.userName}}</text>
                  <text class="user-role">{{item.role}}</text>
                </view>
              </view>
              <view class="permission-controls">
                <view class="permission-group">
                  <text class="permission-label">设备控制</text>
                  <switch checked="{{item.permissions.control}}" bindchange="updatePermission" 
                         data-user-id="{{item.userId}}" data-type="control"/>
                </view>
                <view class="permission-group">
                  <text class="permission-label">数据查看</text>
                  <switch checked="{{item.permissions.view}}" bindchange="updatePermission" 
                         data-user-id="{{item.userId}}" data-type="view"/>
                </view>
                <view class="permission-group">
                  <text class="permission-label">配置修改</text>
                  <switch checked="{{item.permissions.config}}" bindchange="updatePermission" 
                         data-user-id="{{item.userId}}" data-type="config"/>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>


  </view>
</view>