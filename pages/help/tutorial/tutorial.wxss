/* pages/help/tutorial/tutorial.wxss */

.page-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 页面头部 */
.page-header {
  padding: 40rpx 32rpx 32rpx;
  text-align: center;
  background: linear-gradient(135deg, #2E7D32, #4CAF50);
  color: white;
  margin-bottom: 20rpx;
}

.page-title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.page-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 教程网格 */
.tutorial-grid {
  padding: 0 20rpx;
}

.tutorial-card {
  display: flex;
  align-items: center;
  background: #ffffff;
  margin-bottom: 16rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.tutorial-card:active {
  transform: scale(0.98);
  background: #f8f8f8;
}

.tutorial-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  width: 80rpx;
  text-align: center;
}

.tutorial-icon.large {
  font-size: 64rpx;
  margin-bottom: 16rpx;
}

.tutorial-info {
  flex: 1;
}

.tutorial-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.tutorial-desc {
  display: block;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.tutorial-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-count {
  font-size: 24rpx;
  color: #2E7D32;
  background: rgba(46, 125, 50, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.tutorial-arrow {
  font-size: 24rpx;
  color: #999999;
}

/* 帮助提示 */
.help-tip {
  display: flex;
  align-items: flex-start;
  background: #e8f5e8;
  margin: 20rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  border-left: 4rpx solid #2E7D32;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.tip-content {
  flex: 1;
}

.tip-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2E7D32;
  margin-bottom: 8rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #4a7c59;
  line-height: 1.5;
}

/* 教程详情 */
.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.back-btn {
  display: flex;
  align-items: center;
  color: #2E7D32;
  font-size: 28rpx;
}

.back-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.tutorial-progress {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #666666;
}

.progress-bar {
  width: 200rpx;
  height: 6rpx;
  background: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #2E7D32;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 教程内容 */
.tutorial-content {
  padding: 32rpx;
}

.tutorial-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.step-content {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  background: #2E7D32;
  color: #ffffff;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 16rpx;
}

.step-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.step-body {
  margin-left: 64rpx;
}

.step-text {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.step-image-placeholder {
  width: 100%;
  height: 300rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}

.image-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  opacity: 0.5;
}

.image-text {
  font-size: 24rpx;
  color: #999999;
}

/* 步骤导航点 */
.step-dots {
  display: flex;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 32rpx;
}

.step-dot {
  width: 16rpx;
  height: 16rpx;
  background: #e0e0e0;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.step-dot.active {
  background: #2E7D32;
  transform: scale(1.2);
}

/* 底部导航 */
.tutorial-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 16rpx;
  padding: 20rpx 32rpx;
  background: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 按钮样式 */
.btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.btn::after {
  border: none;
}

.btn-primary {
  background: #2E7D32;
  color: #ffffff;
}

.btn-primary:active {
  background: #1B5E20;
}

.btn-secondary {
  background: #f5f5f5;
  color: #666666;
}

.btn-secondary:active {
  background: #e0e0e0;
}

.btn-success {
  background: #4CAF50;
  color: #ffffff;
}

.btn-success:active {
  background: #388E3C;
}

.btn[disabled] {
  background: #f5f5f5 !important;
  color: #cccccc !important;
}

/* 响应式适配 */
@media screen and (max-width: 400px) {
  .tutorial-footer {
    padding: 16rpx 20rpx;
  }
  
  .btn {
    padding: 20rpx;
    font-size: 26rpx;
  }
}