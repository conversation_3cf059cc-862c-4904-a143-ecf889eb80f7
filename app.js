const api = require('./utils/api.js');

App({
  onLaunch: function () {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        traceUser: true,
      });
    }
    
    this.globalData = {
      userInfo: null,
      currentFarm: null,
      deviceList: [],
      sensorData: {}
    };
    
    this.checkLogin();
  },
  
  checkLogin: function() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.globalData.userInfo = userInfo;
      console.log('用户已登录:', userInfo);
    } else {
      console.log('用户未登录');
      this.globalData.userInfo = null;
    }
  },
  
  // 全局登录检查方法
  requireLogin: function() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (!token || !userInfo) {
      // 未登录，跳转到登录页
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },
  
  // 设置用户信息
  setUserInfo: function(userInfo) {
    this.globalData.userInfo = userInfo;
    wx.setStorageSync('userInfo', userInfo);
  },
  
  // 清除用户信息
  clearUserInfo: function() {
    this.globalData.userInfo = null;
    wx.removeStorageSync('token');
    wx.removeStorageSync('refreshToken');
    wx.removeStorageSync('userInfo');
  },
  
  globalData: {
    userInfo: null,
    currentFarm: null,
    deviceList: [],
    sensorData: {}
  }
});