Page({
  data: {
    // 编辑模式相关
    editMode: false,
    scheduleId: null,
    
    // 表单数据
    form: {
      name: '',
      customWeekdays: [
        { day: 1, label: '一', selected: true },
        { day: 2, label: '二', selected: true },
        { day: 3, label: '三', selected: true },
        { day: 4, label: '四', selected: true },
        { day: 5, label: '五', selected: true },
        { day: 6, label: '六', selected: false },
        { day: 0, label: '日', selected: false }
      ],
      weekdaysText: '工作日',
      timeSlots: [{
        tempId: Date.now(),
        startTime: '07:00',
        endTime: '07:30',
        duration: 30,
        flowRate: 60,
        zoneOptions: [
          { id: 1, name: '1区', selected: false },
          { id: 2, name: '2区', selected: false },
          { id: 3, name: '3区', selected: false },
          { id: 4, name: '4区', selected: false }
        ]
      }]
    },

    // 区域选项
    zoneOptions: [
      { id: 1, name: '1区' },
      { id: 2, name: '2区' },
      { id: 3, name: '3区' },
      { id: 4, name: '4区' }
    ]
  },

  onLoad(options) {
    // 检查是否是编辑模式
    if (options.mode === 'edit' && options.id) {
      this.setData({
        editMode: true,
        scheduleId: parseInt(options.id)
      });
      
      wx.setNavigationBarTitle({
        title: '编辑灌溉计划'
      });
      
      // 加载编辑的计划数据
      this.loadScheduleData(parseInt(options.id));
    } else if (options.template) {
      // 应用模板模式
      wx.setNavigationBarTitle({
        title: '应用模板 - 新增计划'
      });
      
      // 应用模板数据
      this.applyTemplate(options.template);
    } else {
      wx.setNavigationBarTitle({
        title: '新增灌溉计划'
      });
    }
  },

  // 加载编辑的计划数据
  loadScheduleData(scheduleId) {
    // 模拟从服务器或本地存储加载数据
    // 这里应该从上级页面传递过来或者从全局数据中获取
    const scheduleData = this.getScheduleById(scheduleId);
    
    if (scheduleData) {
      // 转换时间段数据格式以适配编辑页面
      const timeSlots = scheduleData.timeSlots.map(slot => ({
        tempId: slot.id || Date.now() + Math.random(),
        startTime: slot.startTime,
        endTime: slot.endTime,
        duration: slot.duration,
        flowRate: slot.flowRate,
        zoneOptions: this.data.zoneOptions.map(zone => ({
          ...zone,
          selected: slot.zones && slot.zones.includes(zone.name)
        }))
      }));
      
      this.setData({
        'form.name': scheduleData.name,
        'form.customWeekdays': scheduleData.customWeekdays,
        'form.weekdaysText': scheduleData.weekdaysText,
        'form.timeSlots': timeSlots
      });
    }
  },

  // 根据ID获取计划数据（模拟数据）
  getScheduleById(id) {
    // 这里使用模拟数据，实际应该从全局状态或服务器获取
    const allSchedules = [
      {
        id: 1,
        name: '早晨灌溉',
        enabled: true,
        customWeekdays: [
          { day: 1, label: '一', selected: true },
          { day: 2, label: '二', selected: true },
          { day: 3, label: '三', selected: true },
          { day: 4, label: '四', selected: true },
          { day: 5, label: '五', selected: true },
          { day: 6, label: '六', selected: true },
          { day: 0, label: '日', selected: true }
        ],
        weekdaysText: '每天',
        timeDisplay: '07:00-07:30',
        timeSlots: [
          {
            id: 1,
            startTime: '07:00',
            endTime: '07:30',
            duration: 30,
            flowRate: 60,
            zones: '1区, 2区',
            enabled: true
          }
        ],
      },
      {
        id: 2,
        name: '傍晚灌溉',
        enabled: true,
        customWeekdays: [
          { day: 1, label: '一', selected: true },
          { day: 2, label: '二', selected: true },
          { day: 3, label: '三', selected: true },
          { day: 4, label: '四', selected: true },
          { day: 5, label: '五', selected: true },
          { day: 6, label: '六', selected: true },
          { day: 0, label: '日', selected: true }
        ],
        weekdaysText: '每天',
        timeDisplay: '18:00-18:20, 19:00-19:15',
        timeSlots: [
          {
            id: 2,
            startTime: '18:00',
            endTime: '18:20',
            duration: 20,
            flowRate: 50,
            zones: '1区, 3区',
            enabled: true
          },
          {
            id: 3,
            startTime: '19:00',
            endTime: '19:15',
            duration: 15,
            flowRate: 40,
            zones: '2区',
            enabled: true
          }
        ],
      }
    ];
    
    return allSchedules.find(schedule => schedule.id === id);
  },

  // 应用模板
  applyTemplate(templateId) {
    const scheduleTemplates = [
      {
        id: 'morning',
        name: '晨间灌溉',
        icon: '🌅',
        description: '早晨7:00-7:30',
        timeSlots: [{
          startTime: '07:00',
          endTime: '07:30',
          duration: 30,
          flowRate: 60
        }]
      },
      {
        id: 'evening',
        name: '傍晚灌溉',
        icon: '🌇',
        description: '傍晚18:00-18:30',
        timeSlots: [{
          startTime: '18:00',
          endTime: '18:30',
          duration: 30,
          flowRate: 50
        }]
      },
      {
        id: 'multi',
        name: '多时段灌溉',
        icon: '⏰',
        description: '早晚各一次',
        timeSlots: [
          {
            startTime: '07:00',
            endTime: '07:20',
            duration: 20,
            flowRate: 60
          },
          {
            startTime: '18:00',
            endTime: '18:20',
            duration: 20,
            flowRate: 50
          }
        ]
      },
      {
        id: 'frequent',
        name: '高频灌溉',
        icon: '💧',
        description: '每4小时一次',
        timeSlots: [
          { startTime: '06:00', endTime: '06:10', duration: 10, flowRate: 40 },
          { startTime: '10:00', endTime: '10:10', duration: 10, flowRate: 40 },
          { startTime: '14:00', endTime: '14:10', duration: 10, flowRate: 40 },
          { startTime: '18:00', endTime: '18:10', duration: 10, flowRate: 40 }
        ]
      }
    ];
    
    const template = scheduleTemplates.find(t => t.id === templateId);
    
    if (template) {
      const timeSlots = template.timeSlots.map(slot => ({
        ...slot,
        tempId: Date.now() + Math.random(),
        zoneOptions: this.data.zoneOptions.map(zone => ({ ...zone, selected: false }))
      }));
      
      this.setData({
        'form.name': template.name,
        'form.timeSlots': timeSlots
      });
    }
  },

  // 表单输入
  onFormInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    console.log('输入字段:', field, '输入值:', value);
    
    this.setData({
      [`form.${field}`]: value
    });
  },

  // 切换工作日
  toggleWeekday(e) {
    const day = e.currentTarget.dataset.day;
    const customWeekdays = this.data.form.customWeekdays.map(item => {
      if (item.day === day) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });
    
    // 更新weekdaysText显示
    const weekdaysText = this.generateWeekdaysText(customWeekdays);
    
    this.setData({
      'form.customWeekdays': customWeekdays,
      'form.weekdaysText': weekdaysText
    });
  },

  // 生成星期显示文本
  generateWeekdaysText(customWeekdays) {
    const selectedDays = customWeekdays.filter(item => item.selected);
    
    if (selectedDays.length === 7) {
      return '每天';
    }
    
    if (selectedDays.length === 5 && 
        selectedDays.every(day => day.day >= 1 && day.day <= 5)) {
      return '工作日';
    }
    
    if (selectedDays.length === 2 && 
        selectedDays.some(day => day.day === 0) && 
        selectedDays.some(day => day.day === 6)) {
      return '周末';
    }
    
    // 其他情况显示具体星期
    return selectedDays.map(day => `周${day.label}`).join('、');
  },

  // 添加时间段
  addTimeSlot() {
    const newSlot = {
      tempId: Date.now(),
      startTime: '07:00',
      endTime: '07:30',
      duration: 30,
      flowRate: 60,
      zoneOptions: this.data.zoneOptions.map(zone => ({ ...zone, selected: false }))
    };
    
    const timeSlots = [...this.data.form.timeSlots, newSlot];
    this.setData({
      'form.timeSlots': timeSlots
    });
  },

  // 删除时间段
  removeTimeSlot(e) {
    const index = e.currentTarget.dataset.index;
    const timeSlots = this.data.form.timeSlots.filter((_, i) => i !== index);
    
    this.setData({
      'form.timeSlots': timeSlots
    });
  },

  // 时间段时间改变
  onTimeSlotChange(e) {
    const index = e.currentTarget.dataset.index;
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    const timeSlots = this.data.form.timeSlots.map((slot, i) => {
      if (i === index) {
        return { ...slot, [field]: value };
      }
      return slot;
    });
    
    this.setData({
      'form.timeSlots': timeSlots
    });
  },

  // 时间段参数输入
  onTimeSlotInput(e) {
    const index = e.currentTarget.dataset.index;
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    const timeSlots = this.data.form.timeSlots.map((slot, i) => {
      if (i === index) {
        return { ...slot, [field]: parseInt(value) || 0 };
      }
      return slot;
    });
    
    this.setData({
      'form.timeSlots': timeSlots
    });
  },

  // 切换时间段区域
  toggleSlotZone(e) {
    const slotIndex = e.currentTarget.dataset.slot;
    const zoneId = e.currentTarget.dataset.zone;
    
    const timeSlots = this.data.form.timeSlots.map((slot, i) => {
      if (i === slotIndex) {
        const zoneOptions = slot.zoneOptions.map(zone => {
          if (zone.id === zoneId) {
            return { ...zone, selected: !zone.selected };
          }
          return zone;
        });
        return { ...slot, zoneOptions };
      }
      return slot;
    });
    
    this.setData({
      'form.timeSlots': timeSlots
    });
  },

  // 取消操作
  cancelSchedule() {
    wx.showModal({
      title: '确认取消',
      content: '取消后当前编辑的内容将不会保存，确定要取消吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 保存计划
  saveSchedule() {
    const form = this.data.form;
    
    if (!form.name) {
      wx.showToast({
        title: '请输入计划名称',
        icon: 'none'
      });
      return;
    }
    
    if (form.timeSlots.length === 0) {
      wx.showToast({
        title: '请添加至少一个时间段',
        icon: 'none'
      });
      return;
    }
    
    // 验证时间段
    for (let slot of form.timeSlots) {
      if (!slot.startTime || !slot.endTime || !slot.duration || !slot.flowRate) {
        wx.showToast({
          title: '请完善时间段信息',
          icon: 'none'
        });
        return;
      }
    }
    
    // 模拟保存到服务器
    if (this.data.editMode) {
      console.log('更新计划:', this.data.scheduleId, form);
      wx.showToast({
        title: '更新成功',
        icon: 'success'
      });
    } else {
      console.log('新增计划:', form);
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    }
    
    // 返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  }
})