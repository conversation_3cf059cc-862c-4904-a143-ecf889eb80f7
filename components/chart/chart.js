// 图表组件
Component({
  properties: {
    title: {
      type: String,
      value: ''
    },
    type: {
      type: String,
      value: 'line' // line, bar, pie, area
    },
    data: {
      type: Array,
      value: []
    },
    options: {
      type: Object,
      value: {}
    },
    width: {
      type: Number,
      value: 350
    },
    height: {
      type: Number,
      value: 300
    },
    showActions: {
      type: Boolean,
      value: false
    },
    legend: {
      type: Array,
      value: []
    }
  },

  data: {
    canvasId: '',
    loading: false,
    isEmpty: false,
    chart: null
  },

  lifetimes: {
    attached() {
      this.setData({
        canvasId: `chart_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      });
    },

    ready() {
      this.initChart();
    },

    detached() {
      if (this.data.chart) {
        this.data.chart.dispose();
      }
    }
  },

  observers: {
    'data, type, options': function() {
      if (this.data.chart) {
        this.updateChart();
      }
    }
  },

  methods: {
    initChart() {
      const { canvasId, width, height } = this.data;
      
      this.setData({
        loading: true
      });

      // 导入 ECharts
      const echarts = require('../../libs/ec-canvas/echarts.min.js');
      
      // 获取Canvas对象
      const query = this.createSelectorQuery();
      query.select(`#${canvasId}`)
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res[0] && res[0].node) {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');
            
            // 设置Canvas尺寸
            const dpr = wx.getSystemInfoSync().pixelRatio;
            canvas.width = width * dpr;
            canvas.height = height * dpr;
            ctx.scale(dpr, dpr);
            
            // 创建 ECharts 实例
            const chart = echarts.init(canvas, null, {
              width: width,
              height: height,
              devicePixelRatio: dpr
            });

            this.setData({
              chart: chart,
              loading: false
            });

            this.updateChart();
          } else {
            // 兼容旧版本Canvas API
            this.initLegacyCanvas();
          }
        });
    },

    initLegacyCanvas() {
      const { canvasId, width, height } = this.data;
      const echarts = require('../../libs/ec-canvas/echarts.min.js');
      
      // 创建Canvas上下文
      const ctx = wx.createCanvasContext(canvasId, this);
      
      // 模拟Canvas对象
      const mockCanvas = {
        getContext: () => ctx,
        width: width,
        height: height
      };
      
      const chart = echarts.init(mockCanvas, null, {
        width: width,
        height: height
      });

      this.setData({
        chart: chart,
        loading: false
      });

      // 延迟更新图表，确保DOM渲染完成
      setTimeout(() => {
        this.updateChart();
      }, 100);
    },

    updateChart() {
      const { chart } = this.data;
      const { data, type, options } = this.properties;

      if (!chart || !data || data.length === 0) {
        this.setData({
          isEmpty: true
        });
        return;
      }

      this.setData({
        isEmpty: false
      });

      const chartOption = this.getChartOption(type, data, options);
      chart.setOption(chartOption);
    },

    getChartOption(type, data, customOptions) {
      const baseOption = {
        backgroundColor: '#ffffff',
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderWidth: 0,
          textStyle: {
            color: '#ffffff',
            fontSize: 12
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#E0E0E0'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666666',
            fontSize: 10
          },
          data: data.map(item => item.name || item.x)
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666666',
            fontSize: 10
          },
          splitLine: {
            lineStyle: {
              color: '#F0F0F0'
            }
          }
        }
      };

      // 根据图表类型设置不同的配置
      switch (type) {
        case 'line':
          baseOption.series = [{
            type: 'line',
            smooth: true,
            lineStyle: {
              width: 3,
              color: '#2E7D32'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(46, 125, 50, 0.3)'
                }, {
                  offset: 1,
                  color: 'rgba(46, 125, 50, 0.1)'
                }]
              }
            },
            itemStyle: {
              color: '#2E7D32'
            },
            data: data.map(item => item.value || item.y)
          }];
          break;

        case 'bar':
          baseOption.series = [{
            type: 'bar',
            barWidth: '50%',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: '#2E7D32'
                }, {
                  offset: 1,
                  color: '#4CAF50'
                }]
              },
              borderRadius: [4, 4, 0, 0]
            },
            data: data.map(item => item.value || item.y)
          }];
          break;

        case 'pie':
          baseOption.series = [{
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '50%'],
            itemStyle: {
              borderRadius: 8,
              borderColor: '#ffffff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}: {c}',
              fontSize: 10,
              color: '#666666'
            },
            labelLine: {
              show: true,
              length: 10,
              length2: 5
            },
            data: data.map((item, index) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: this.getColorByIndex(index)
              }
            }))
          }];
          delete baseOption.xAxis;
          delete baseOption.yAxis;
          break;

        case 'area':
          baseOption.series = [{
            type: 'line',
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#2E7D32'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(46, 125, 50, 0.5)'
                }, {
                  offset: 1,
                  color: 'rgba(46, 125, 50, 0.1)'
                }]
              }
            },
            itemStyle: {
              color: '#2E7D32'
            },
            data: data.map(item => item.value || item.y)
          }];
          break;
      }

      // 合并自定义配置
      return Object.assign(baseOption, customOptions);
    },

    getColorByIndex(index) {
      const colors = [
        '#2E7D32', '#1976D2', '#FF9800', '#F44336', 
        '#9C27B0', '#00BCD4', '#4CAF50', '#FFC107'
      ];
      return colors[index % colors.length];
    },

    onTouchStart(e) {
      if (this.data.chart) {
        this.data.chart.dispatchAction({
          type: 'showTip',
          x: e.touches[0].x,
          y: e.touches[0].y
        });
      }
    },

    onTouchMove(e) {
      if (this.data.chart) {
        this.data.chart.dispatchAction({
          type: 'showTip',
          x: e.touches[0].x,
          y: e.touches[0].y
        });
      }
    },

    onTouchEnd() {
      if (this.data.chart) {
        this.data.chart.dispatchAction({
          type: 'hideTip'
        });
      }
    },

    onExport() {
      this.triggerEvent('export', {
        chart: this.data.chart
      });
    },

    onFullscreen() {
      this.triggerEvent('fullscreen', {
        chart: this.data.chart
      });
    }
  }
});