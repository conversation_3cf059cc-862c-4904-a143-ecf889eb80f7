// 微信小程序 ECharts 简化版本
// 这是一个简化的ECharts封装，用于微信小程序环境

class WxChart {
  constructor(canvas, options = {}) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.width = options.width || 350;
    this.height = options.height || 300;
    this.option = {};
    this.disposed = false;
  }

  setOption(option) {
    this.option = { ...option };
    this.render();
  }

  render() {
    if (this.disposed) return;
    
    const { ctx, width, height } = this;
    
    // 清空画布
    ctx.clearRect(0, 0, width, height);
    
    // 绘制背景
    ctx.fillStyle = this.option.backgroundColor || '#ffffff';
    ctx.fillRect(0, 0, width, height);
    
    // 根据图表类型渲染
    if (this.option.series && this.option.series.length > 0) {
      const series = this.option.series[0];
      
      switch (series.type) {
        case 'line':
          this.renderLineChart(series);
          break;
        case 'bar':
          this.renderBarChart(series);
          break;
        case 'pie':
          this.renderPieChart(series);
          break;
        default:
          this.renderLineChart(series);
      }
    }
    
    // 渲染标题
    if (this.option.title && this.option.title.text && this.option.title.text.trim()) {
      this.renderTitle();
    }
    
    // 确保Canvas绘制完成
    if (ctx.draw) {
      ctx.draw();
    }
  }

  renderLineChart(series) {
    const { ctx, width, height } = this;
    const data = series.data || [];
    const xData = this.option.xAxis ? this.option.xAxis.data : [];
    
    if (data.length === 0) return;
    
    // 计算绘图区域
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    // 获取数据范围
    const maxValue = Math.max(...data);
    const minValue = Math.min(...data);
    const valueRange = maxValue - minValue || 1;
    
    // 绘制网格线
    ctx.strokeStyle = '#F0F0F0';
    ctx.lineWidth = 1;
    
    // 垂直网格线
    for (let i = 0; i <= 5; i++) {
      const x = padding + (chartWidth / 5) * i;
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, height - padding);
      ctx.stroke();
    }
    
    // 水平网格线
    for (let i = 0; i <= 4; i++) {
      const y = padding + (chartHeight / 4) * i;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }
    
    // 绘制数据线
    if (data.length > 1) {
      ctx.beginPath();
      ctx.strokeStyle = series.lineStyle?.color || '#2E7D32';
      ctx.lineWidth = series.lineStyle?.width || 2;
      
      for (let i = 0; i < data.length; i++) {
        const x = padding + (chartWidth / (data.length - 1)) * i;
        const y = height - padding - ((data[i] - minValue) / valueRange) * chartHeight;
        
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      ctx.stroke();
      
      // 绘制面积
      if (series.areaStyle) {
        ctx.lineTo(width - padding, height - padding);
        ctx.lineTo(padding, height - padding);
        ctx.closePath();
        
        const gradient = ctx.createLinearGradient(0, padding, 0, height - padding);
        gradient.addColorStop(0, 'rgba(46, 125, 50, 0.3)');
        gradient.addColorStop(1, 'rgba(46, 125, 50, 0.1)');
        ctx.fillStyle = gradient;
        ctx.fill();
      }
      
      // 绘制数据点
      ctx.fillStyle = series.itemStyle?.color || '#2E7D32';
      for (let i = 0; i < data.length; i++) {
        const x = padding + (chartWidth / (data.length - 1)) * i;
        const y = height - padding - ((data[i] - minValue) / valueRange) * chartHeight;
        
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, 2 * Math.PI);
        ctx.fill();
      }
    }
    
    // 绘制坐标轴标签
    ctx.fillStyle = '#666666';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    
    // X轴标签
    if (xData && xData.length > 0) {
      for (let i = 0; i < Math.min(xData.length, data.length); i++) {
        const x = padding + (chartWidth / (data.length - 1)) * i;
        const label = String(xData[i]).substring(0, 5); // 限制标签长度
        ctx.fillText(label, x, height - padding + 20);
      }
    }
    
    // Y轴标签
    ctx.textAlign = 'right';
    for (let i = 0; i <= 4; i++) {
      const value = minValue + (valueRange / 4) * (4 - i);
      const y = padding + (chartHeight / 4) * i + 4;
      ctx.fillText(value.toFixed(1), padding - 10, y);
    }
  }

  renderBarChart(series) {
    const { ctx, width, height } = this;
    const data = series.data || [];
    const xData = this.option.xAxis ? this.option.xAxis.data : [];
    
    if (data.length === 0) return;
    
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    const maxValue = Math.max(...data);
    const barWidth = chartWidth / data.length * 0.6;
    const barSpacing = chartWidth / data.length;
    
    // 绘制柱状图
    const gradient = ctx.createLinearGradient(0, padding, 0, height - padding);
    gradient.addColorStop(0, '#2E7D32');
    gradient.addColorStop(1, '#4CAF50');
    ctx.fillStyle = gradient;
    
    for (let i = 0; i < data.length; i++) {
      const barHeight = (data[i] / maxValue) * chartHeight;
      const x = padding + barSpacing * i + (barSpacing - barWidth) / 2;
      const y = height - padding - barHeight;
      
      ctx.fillRect(x, y, barWidth, barHeight);
    }
    
    // 绘制标签
    ctx.fillStyle = '#666666';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    
    if (xData && xData.length > 0) {
      for (let i = 0; i < Math.min(xData.length, data.length); i++) {
        const x = padding + barSpacing * i + barSpacing / 2;
        ctx.fillText(xData[i], x, height - padding + 20);
      }
    }
  }

  renderPieChart(series) {
    const { ctx, width, height } = this;
    const data = series.data || [];
    
    if (data.length === 0) return;
    
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 3;
    
    const total = data.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = -Math.PI / 2;
    
    const colors = ['#2E7D32', '#1976D2', '#FF9800', '#F44336', '#9C27B0', '#00BCD4'];
    
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      const sliceAngle = (item.value / total) * 2 * Math.PI;
      
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      ctx.closePath();
      
      ctx.fillStyle = item.itemStyle?.color || colors[i % colors.length];
      ctx.fill();
      
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // 绘制标签
      const labelAngle = currentAngle + sliceAngle / 2;
      const labelX = centerX + Math.cos(labelAngle) * (radius + 20);
      const labelY = centerY + Math.sin(labelAngle) * (radius + 20);
      
      ctx.fillStyle = '#666666';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(item.name, labelX, labelY);
      
      currentAngle += sliceAngle;
    }
  }

  renderTitle() {
    const { ctx, width } = this;
    const title = this.option.title;
    
    ctx.fillStyle = title.textStyle?.color || '#333333';
    ctx.font = `${title.textStyle?.fontSize || 16}px Arial`;
    ctx.textAlign = 'center';
    ctx.fillText(title.text, width / 2, 30);
  }

  dispose() {
    this.disposed = true;
    this.ctx = null;
    this.canvas = null;
  }

  dispatchAction(action) {
    // 简化的事件处理
    if (action.type === 'showTip') {
      // 可以在这里添加tooltip显示逻辑
    }
  }
}

// 模拟 ECharts API
const echarts = {
  init: function(canvas, theme, options = {}) {
    return new WxChart(canvas, options);
  }
};

module.exports = echarts;