package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 传感器类型实体类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sensor_types")
public class SensorType {

    /**
     * 传感器类型ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 传感器类型名称
     */
    @TableField("name")
    private String name;

    /**
     * 传感器代码
     */
    @TableField("code")
    private String code;

    /**
     * 数据单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 最小值
     */
    @TableField("min_value")
    private BigDecimal minValue;

    /**
     * 最大值
     */
    @TableField("max_value")
    private BigDecimal maxValue;

    /**
     * 正常范围最小值
     */
    @TableField("normal_min")
    private BigDecimal normalMin;

    /**
     * 正常范围最大值
     */
    @TableField("normal_max")
    private BigDecimal normalMax;

    /**
     * 精度
     */
    @TableField("precision")
    private Integer precision;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 状态: 0-禁用, 1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}