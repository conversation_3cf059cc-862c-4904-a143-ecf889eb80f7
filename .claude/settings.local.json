{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "Bash(node:*)", "Bash(find:*)", "Bash(rg:*)", "Bash(rm:*)", "Bash(ls:*)", "WebFetch(domain:tdesign.tencent.com)", "WebFetch(domain:github.com)", "Bash(npm install)", "Bash(/bin/rm:*)", "WebFetch(domain:lbs.qq.com)", "<PERSON><PERSON>(chmod:*)", "Bash(./batch_replace.sh:*)", "WebFetch(domain:dev.qweather.com)", "Bash(grep -n \"^};\" /Users/<USER>/develop/agriculture-mini/utils/mock/data.js)", "Bash(npm start)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(textutil:*)"], "deny": []}}