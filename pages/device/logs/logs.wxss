/* 运行记录页面样式 */

/* 标签导航 */
.tab-nav {
  display: flex;
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  padding: 25rpx;
  text-align: center;
  font-size: 26rpx;
  color: #666666;
  background-color: #F8F9FA;
  border-right: 1rpx solid #E0E0E0;
  transition: all 0.3s ease;
}

.tab-item:last-child {
  border-right: none;
}

.tab-item.active {
  background-color: #08C160;
  color: white;
  font-weight: 600;
}

/* 筛选器 */
.filter-bar {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: white;
  border-radius: 12rpx;
}

.date-picker {
  flex: 1;
}

.picker-btn {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  background-color: #F8F9FA;
}

.picker-icon {
  font-size: 24rpx;
}

.picker-text {
  font-size: 24rpx;
  color: #333333;
}

.date-separator {
  font-size: 24rpx;
  color: #666666;
}

.filter-btn {
  padding: 15rpx 25rpx;
  background-color: #08C160;
  color: white;
  border-radius: 8rpx;
  font-size: 24rpx;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  text-align: center;
  padding: 30rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #08C160;
  display: block;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
}

/* 操作筛选 */
.operation-filter {
  margin-bottom: 30rpx;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tags {
  display: flex;
  gap: 15rpx;
  padding: 20rpx;
}

.filter-tag {
  padding: 15rpx 25rpx;
  background-color: white;
  border: 1rpx solid #E0E0E0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666666;
  white-space: nowrap;
}

.filter-tag.active {
  background-color: #08C160;
  color: white;
  border-color: #08C160;
}

/* 日志列表 */
.log-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.log-item {
  background-color: white;
  border-radius: 12rpx;
  padding: 25rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.log-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 15rpx;
}

.log-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.log-icon.irrigation {
  background-color: #E3F2FD;
}

.log-icon.fertilizer {
  background-color: #E8F8EC;
}

.log-icon.maintenance {
  background-color: #FFF3E0;
}

.log-icon.config {
  background-color: #F3E5F5;
}

.log-info {
  flex: 1;
}

.log-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.log-time {
  font-size: 22rpx;
  color: #999999;
}

.log-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.log-status.success {
  background-color: #E8F8EC;
  color: #08C160;
}

.log-status.error {
  background-color: #FFEBEE;
  color: #F44336;
}

.log-status.warning {
  background-color: #FFF3E0;
  color: #FF9800;
}

.log-content {
  margin-bottom: 20rpx;
}

.log-description {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.log-details {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 20rpx;
}

.detail-item {
  display: flex;
  gap: 15rpx;
  margin-bottom: 10rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 24rpx;
  color: #666666;
  min-width: 120rpx;
}

.detail-value {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

.log-actions {
  display: flex;
  gap: 15rpx;
  justify-content: flex-end;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-top: 30rpx;
}

/* 报告类型选择 */
.report-types {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 30rpx;
  background-color: white;
  border: 2rpx solid #E0E0E0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.type-item.active {
  border-color: #08C160;
  background-color: rgba(46, 125, 50, 0.05);
}

.type-icon {
  font-size: 40rpx;
}

.type-text {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

/* 报告统计 */
.report-stats {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.stat-row:last-child {
  border-bottom: none;
}

.stat-name {
  font-size: 26rpx;
  color: #333333;
}

.stat-data {
  font-size: 26rpx;
  color: #08C160;
  font-weight: 600;
}

/* 图表容器 */
.chart-container {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #999999;
}

.chart-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}

.chart-text {
  font-size: 28rpx;
  display: block;
  margin-bottom: 10rpx;
}

.chart-note {
  font-size: 22rpx;
}

/* 效率分析 */
.efficiency-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.efficiency-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.efficiency-name {
  display: flex;
  align-items: center;
  gap: 10rpx;
  min-width: 160rpx;
}

.item-icon {
  font-size: 24rpx;
}

.item-text {
  font-size: 26rpx;
  color: #333333;
}

.efficiency-bar {
  flex: 1;
  height: 12rpx;
  background-color: #E0E0E0;
  border-radius: 6rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background-color: #2196F3;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.bar-fill.fertilizer {
  background-color: #08C160;
}

.bar-fill.system {
  background-color: #FF9800;
}

.efficiency-value {
  font-size: 24rpx;
  color: #666666;
  min-width: 60rpx;
  text-align: right;
}

/* 导出区域 */
.export-section {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

/* 异常统计 */
.alert-stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.alert-stat {
  text-align: center;
  padding: 30rpx;
  background-color: white;
  border-radius: 12rpx;
}

.alert-count {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 10rpx;
}

.alert-count.warning {
  color: #FF9800;
}

.alert-count.error {
  color: #F44336;
}

.alert-count.resolved {
  color: #08C160;
}

.alert-label {
  font-size: 22rpx;
  color: #666666;
}

/* 异常筛选 */
.alert-filter {
  display: flex;
  gap: 15rpx;
  margin-bottom: 30rpx;
  padding: 20rpx;
}

/* 异常列表 */
.alert-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.alert-item {
  background-color: white;
  border-radius: 12rpx;
  padding: 25rpx;
  border-left: 4rpx solid #E0E0E0;
}

.alert-item.error {
  border-left-color: #F44336;
}

.alert-item.warning {
  border-left-color: #FF9800;
}

.alert-item.resolved {
  border-left-color: #08C160;
}

.alert-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 15rpx;
}

.alert-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  background-color: #F0F0F0;
}

.alert-info {
  flex: 1;
}

.alert-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.alert-time {
  font-size: 22rpx;
  color: #999999;
}

.alert-level {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.alert-level.error {
  background-color: #FFEBEE;
  color: #F44336;
}

.alert-level.warning {
  background-color: #FFF3E0;
  color: #FF9800;
}

.alert-level.resolved {
  background-color: #E8F8EC;
  color: #08C160;
}

.alert-content {
  margin-bottom: 20rpx;
}

.alert-description {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.alert-solution {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 20rpx;
}

.solution-label {
  font-size: 24rpx;
  color: #666666;
  display: block;
  margin-bottom: 10rpx;
}

.solution-text {
  font-size: 24rpx;
  color: #333333;
  line-height: 1.5;
}

.alert-actions {
  display: flex;
  gap: 15rpx;
  justify-content: flex-end;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx;
  color: #999999;
}

.empty-icon {
  font-size: 100rpx;
  display: block;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  display: block;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 24rpx;
}