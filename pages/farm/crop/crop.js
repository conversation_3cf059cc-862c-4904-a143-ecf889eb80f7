Page({
  data: {
    currentTab: 'archive',
    
    // 作物档案数据
    cropArchives: [
      {
        id: 1,
        name: '番茄',
        variety: '红果番茄',
        plotName: '1号地块',
        plantDate: '2024-03-15',
        expectedHarvestDate: '2024-06-15',
        currentStage: 'flowering',
        area: 2.5,
        seedAmount: '500粒',
        status: 'growing',
        image: '/images/tomato.jpg',
        description: '品质优良的红果番茄，适合温室种植'
      },
      {
        id: 2,
        name: '黄瓜',
        variety: '荷兰黄瓜',
        plotName: '2号地块',
        plantDate: '2024-03-20',
        expectedHarvestDate: '2024-06-20',
        currentStage: 'fruiting',
        area: 1.8,
        seedAmount: '300粒',
        status: 'growing',
        image: '/images/cucumber.jpg',
        description: '高产优质荷兰黄瓜品种'
      },
      {
        id: 3,
        name: '草莓',
        variety: '奶油草莓',
        plotName: '3号地块',
        plantDate: '2024-02-10',
        expectedHarvestDate: '2024-05-10',
        currentStage: 'harvest',
        area: 1.2,
        seedAmount: '200株',
        status: 'harvesting',
        image: '/images/strawberry.jpg',
        description: '香甜可口的奶油草莓'
      }
    ],
    
    // 生长阶段选项
    stageOptions: [
      { value: 'seeding', label: '播种期', color: '#8BC34A' },
      { value: 'germination', label: '发芽期', color: '#4CAF50' },
      { value: 'growth', label: '生长期', color: '#2E7D32' },
      { value: 'flowering', label: '开花期', color: '#FF9800' },
      { value: 'fruiting', label: '结果期', color: '#F44336' },
      { value: 'harvest', label: '收获期', color: '#9C27B0' },
      { value: 'completed', label: '完成', color: '#757575' }
    ],
    
    // 作物状态选项
    statusOptions: [
      { value: 'growing', label: '生长中', color: '#4CAF50' },
      { value: 'harvesting', label: '收获中', color: '#FF9800' },
      { value: 'completed', label: '已完成', color: '#757575' },
      { value: 'failed', label: '种植失败', color: '#F44336' }
    ],
    
    // 生长记录数据
    growthRecords: [
      {
        id: 1,
        cropId: 1,
        cropName: '番茄',
        date: '2024-07-08',
        type: 'watering',
        content: '浇水灌溉，用水量约50升',
        weather: '晴天',
        temperature: 28,
        humidity: 65,
        operator: '张三',
        images: [],
        notes: '植株生长良好，叶片翠绿'
      },
      {
        id: 2,
        cropId: 1,
        cropName: '番茄',
        date: '2024-07-06',
        type: 'fertilizing',
        content: '施用复合肥，NPK 15-15-15',
        weather: '多云',
        temperature: 25,
        humidity: 70,
        operator: '李四',
        images: [],
        notes: '根据土壤检测结果调整施肥量'
      },
      {
        id: 3,
        cropId: 2,
        cropName: '黄瓜',
        date: '2024-07-07',
        type: 'pest_control',
        content: '喷施生物杀虫剂防治蚜虫',
        weather: '晴天',
        temperature: 30,
        humidity: 55,
        operator: '王五',
        images: [],
        notes: '发现少量蚜虫，及时处理'
      }
    ],
    
    // 记录类型选项
    recordTypes: [
      { value: 'watering', label: '浇水', icon: '💧' },
      { value: 'fertilizing', label: '施肥', icon: '🌱' },
      { value: 'pest_control', label: '病虫害防治', icon: '🐛' },
      { value: 'pruning', label: '修剪', icon: '✂️' },
      { value: 'harvest', label: '收获', icon: '🌾' },
      { value: 'other', label: '其他', icon: '📝' }
    ],
    
    // 收获管理数据
    harvestRecords: [
      {
        id: 1,
        cropId: 3,
        cropName: '草莓',
        date: '2024-07-08',
        quantity: 15.5,
        unit: 'kg',
        quality: 'A',
        price: 25.0,
        income: 387.5,
        notes: '品质优良，甜度高',
        buyer: '本地超市'
      },
      {
        id: 2,
        cropId: 3,
        cropName: '草莓',
        date: '2024-07-05',
        quantity: 12.8,
        unit: 'kg',
        price: 25.0,
        quality: 'A',
        income: 320.0,
        notes: '第一批收获，外观好',
        buyer: '农贸市场'
      }
    ],
    
    // 品质等级选项
    qualityOptions: [
      { value: 'A', label: 'A级（优）', color: '#4CAF50' },
      { value: 'B', label: 'B级（良）', color: '#FF9800' },
      { value: 'C', label: 'C级（中）', color: '#F44336' }
    ],
    
    // 表单相关
    showAddForm: false,
    showRecordForm: false,
    showHarvestForm: false,
    editingItem: null,
    formData: {},
    
    // 筛选和搜索
    searchKeyword: '',
    selectedPlot: 'all',
    selectedStatus: 'all'
  },

  onLoad(options) {
    // 从其他页面传递过来的地块ID
    if (options.plotId) {
      this.setData({
        selectedPlot: options.plotId
      });
    }
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  // 搜索功能
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 筛选功能
  filterByPlot(e) {
    const plot = e.currentTarget.dataset.plot;
    this.setData({
      selectedPlot: plot
    });
  },

  filterByStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      selectedStatus: status
    });
  },

  // 添加作物档案
  showAddCropForm() {
    this.setData({
      showAddForm: true,
      editingItem: null,
      formData: {
        name: '',
        variety: '',
        plotName: '',
        plantDate: '',
        expectedHarvestDate: '',
        area: '',
        seedAmount: '',
        description: ''
      }
    });
  },

  // 编辑作物档案
  editCrop(e) {
    const cropId = e.currentTarget.dataset.id;
    const crop = this.data.cropArchives.find(item => item.id === cropId);
    
    this.setData({
      showAddForm: true,
      editingItem: crop,
      formData: { ...crop }
    });
  },

  // 查看作物详情
  viewCropDetail(e) {
    const cropId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/farm/crop/detail/detail?id=${cropId}`
    });
  },

  // 更新生长阶段
  updateStage(e) {
    const cropId = e.currentTarget.dataset.id;
    const stages = this.data.stageOptions.map(stage => stage.label);
    
    wx.showActionSheet({
      itemList: stages,
      success: (res) => {
        const selectedStage = this.data.stageOptions[res.tapIndex];
        this.updateCropStage(cropId, selectedStage.value);
      }
    });
  },

  updateCropStage(cropId, stage) {
    const { cropArchives } = this.data;
    const cropIndex = cropArchives.findIndex(crop => crop.id === cropId);
    
    if (cropIndex > -1) {
      cropArchives[cropIndex].currentStage = stage;
      
      this.setData({
        cropArchives: [...cropArchives]
      });
      
      wx.showToast({
        title: '阶段已更新',
        icon: 'success'
      });
    }
  },

  // 添加生长记录
  showAddRecordForm() {
    this.setData({
      showRecordForm: true,
      formData: {
        cropId: '',
        date: new Date().toISOString().split('T')[0],
        type: 'watering',
        content: '',
        weather: '',
        temperature: '',
        humidity: '',
        notes: ''
      }
    });
  },

  // 添加收获记录
  showAddHarvestForm() {
    this.setData({
      showHarvestForm: true,
      formData: {
        cropId: '',
        date: new Date().toISOString().split('T')[0],
        quantity: '',
        unit: 'kg',
        quality: 'A',
        price: '',
        notes: '',
        buyer: ''
      }
    });
  },

  // 表单输入处理
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择作物
  selectCrop(e) {
    const cropId = parseInt(e.detail.value);
    const crop = this.data.cropArchives[cropId];
    
    this.setData({
      'formData.cropId': crop.id,
      'formData.cropName': crop.name
    });
  },

  // 选择记录类型
  selectRecordType(e) {
    const typeIndex = parseInt(e.detail.value);
    const recordType = this.data.recordTypes[typeIndex];
    
    this.setData({
      'formData.type': recordType.value
    });
  },

  // 选择品质等级
  selectQuality(e) {
    const qualityIndex = parseInt(e.detail.value);
    const quality = this.data.qualityOptions[qualityIndex];
    
    this.setData({
      'formData.quality': quality.value
    });
  },

  // 保存作物档案
  saveCrop() {
    const { formData, editingItem, cropArchives } = this.data;
    
    // 表单验证
    if (!formData.name || !formData.variety || !formData.plotName) {
      wx.showToast({
        title: '请填写必填字段',
        icon: 'none'
      });
      return;
    }
    
    if (editingItem) {
      // 编辑模式
      const cropIndex = cropArchives.findIndex(crop => crop.id === editingItem.id);
      cropArchives[cropIndex] = { ...formData };
    } else {
      // 新增模式
      const newCrop = {
        ...formData,
        id: Date.now(),
        currentStage: 'seeding',
        status: 'growing'
      };
      cropArchives.unshift(newCrop);
    }
    
    this.setData({
      cropArchives: [...cropArchives],
      showAddForm: false,
      formData: {}
    });
    
    wx.showToast({
      title: editingItem ? '更新成功' : '添加成功',
      icon: 'success'
    });
  },

  // 保存生长记录
  saveRecord() {
    const { formData, growthRecords } = this.data;
    
    if (!formData.cropId || !formData.content) {
      wx.showToast({
        title: '请填写必填字段',
        icon: 'none'
      });
      return;
    }
    
    const newRecord = {
      ...formData,
      id: Date.now(),
      operator: '当前用户'
    };
    
    growthRecords.unshift(newRecord);
    
    this.setData({
      growthRecords: [...growthRecords],
      showRecordForm: false,
      formData: {}
    });
    
    wx.showToast({
      title: '记录已添加',
      icon: 'success'
    });
  },

  // 保存收获记录
  saveHarvest() {
    const { formData, harvestRecords } = this.data;
    
    if (!formData.cropId || !formData.quantity || !formData.price) {
      wx.showToast({
        title: '请填写必填字段',
        icon: 'none'
      });
      return;
    }
    
    const newRecord = {
      ...formData,
      id: Date.now(),
      income: parseFloat(formData.quantity) * parseFloat(formData.price)
    };
    
    harvestRecords.unshift(newRecord);
    
    this.setData({
      harvestRecords: [...harvestRecords],
      showHarvestForm: false,
      formData: {}
    });
    
    wx.showToast({
      title: '收获记录已添加',
      icon: 'success'
    });
  },

  // 关闭表单
  closeForm() {
    this.setData({
      showAddForm: false,
      showRecordForm: false,
      showHarvestForm: false,
      formData: {}
    });
  },

  // 删除记录
  deleteRecord(e) {
    const { id, type } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条记录吗？',
      success: (res) => {
        if (res.confirm) {
          if (type === 'crop') {
            this.deleteCrop(id);
          } else if (type === 'growth') {
            this.deleteGrowthRecord(id);
          } else if (type === 'harvest') {
            this.deleteHarvestRecord(id);
          }
        }
      }
    });
  },

  deleteCrop(id) {
    const { cropArchives } = this.data;
    const newArchives = cropArchives.filter(crop => crop.id !== id);
    
    this.setData({
      cropArchives: newArchives
    });
    
    wx.showToast({
      title: '已删除',
      icon: 'success'
    });
  },

  deleteGrowthRecord(id) {
    const { growthRecords } = this.data;
    const newRecords = growthRecords.filter(record => record.id !== id);
    
    this.setData({
      growthRecords: newRecords
    });
    
    wx.showToast({
      title: '已删除',
      icon: 'success'
    });
  },

  deleteHarvestRecord(id) {
    const { harvestRecords } = this.data;
    const newRecords = harvestRecords.filter(record => record.id !== id);
    
    this.setData({
      harvestRecords: newRecords
    });
    
    wx.showToast({
      title: '已删除',
      icon: 'success'
    });
  },

  // 导出数据
  exportData(e) {
    const type = e.currentTarget.dataset.type;
    
    wx.showLoading({
      title: '导出中...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      });
    }, 2000);
  }
})