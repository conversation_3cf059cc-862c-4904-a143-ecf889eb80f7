package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 施肥计划实体
 *
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@TableName("fertilizer_schedule")
public class FertilizerSchedule {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("device_id")
    private Long deviceId;
    
    @TableField("plot_id")
    private Long plotId;
    
    @TableField("schedule_name")
    private String scheduleName;
    
    @TableField("start_time")
    private LocalDateTime startTime;
    
    @TableField("end_time")
    private LocalDateTime endTime;
    
    @TableField("min_nutrient")
    private BigDecimal minNutrient;
    
    @TableField("fertilizer_type_id")
    private Long fertilizerTypeId;
    
    @TableField("fertilizer_amount")
    private BigDecimal fertilizerAmount;
    
    @TableField("duration")
    private Integer duration;
    
    @TableField("status")
    private String status;
    
    @TableField("created_at")
    private LocalDateTime createdAt;
    
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}