<!--编辑资料页面-->
<view class="page-container">
  <view class="form-container">
    
    <!-- 头像设置 -->
    <view class="avatar-section">
      <view class="avatar-container" bindtap="changeAvatar">
        <view class="avatar-wrapper">
          <image 
            wx:if="{{avatarUrl}}"
            class="avatar-image" 
            src="{{avatarUrl}}" 
            mode="aspectFill"
          ></image>
          <view wx:else class="avatar-placeholder">👤</view>
          
          <view class="avatar-overlay">
            <text class="camera-icon">📷</text>
            <text class="change-text">更换</text>
          </view>
        </view>
      </view>
      <text class="avatar-tip">点击更换头像</text>
    </view>

    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">基本信息</text>
      </view>
      
      <view class="form-group">
        <view class="form-label">
          <text class="label-text">姓名</text>
          <text class="required">*</text>
        </view>
        <input 
          class="form-input"
          placeholder="请输入您的姓名"
          value="{{userInfo.name}}"
          bindinput="onNameInput"
          maxlength="20"
        />
      </view>
      
      <view class="form-group">
        <view class="form-label">
          <text class="label-text">手机号</text>
        </view>
        <input 
          class="form-input"
          placeholder="请输入手机号"
          value="{{userInfo.phone}}"
          bindinput="onPhoneInput"
          type="number"
          maxlength="11"
        />
      </view>
      
      <view class="form-group">
        <view class="form-label">
          <text class="label-text">邮箱</text>
        </view>
        <input 
          class="form-input"
          placeholder="请输入邮箱地址"
          value="{{userInfo.email}}"
          bindinput="onEmailInput"
          type="text"
        />
      </view>
      
      <view class="form-group">
        <view class="form-label">
          <text class="label-text">角色</text>
        </view>
        <picker 
          class="form-picker"
          bindchange="selectRole"
          value="{{userInfo.role}}"
          range="{{roleOptions}}"
          range-key="label"
        >
          <view class="picker-content">
            <text class="picker-text">
              {{currentRoleLabel}}
            </text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 农场信息 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">农场信息</text>
      </view>
      
      <view class="form-group">
        <view class="form-label">
          <text class="label-text">农场名称</text>
        </view>
        <input 
          class="form-input"
          placeholder="请输入农场名称"
          value="{{userInfo.farmName}}"
          bindinput="onFarmNameInput"
          maxlength="50"
        />
      </view>
      
      <view class="form-group">
        <view class="form-label">
          <text class="label-text">农场面积</text>
        </view>
        <input 
          class="form-input"
          placeholder="请输入农场面积（亩）"
          value="{{userInfo.farmArea}}"
          bindinput="onFarmAreaInput"
          type="digit"
        />
      </view>
      
      <view class="form-group">
        <view class="form-label">
          <text class="label-text">农场地址</text>
        </view>
        <view class="address-input" bindtap="selectLocation">
          <text class="address-text {{userInfo.address ? '' : 'placeholder'}}">
            {{userInfo.address || '点击选择位置'}}
          </text>
          <text class="location-icon">📍</text>
        </view>
      </view>
      
      <view class="form-group">
        <view class="form-label">
          <text class="label-text">农场介绍</text>
        </view>
        <textarea 
          class="form-textarea"
          placeholder="简单介绍一下您的农场..."
          value="{{userInfo.description}}"
          bindinput="onDescriptionInput"
          maxlength="200"
          auto-height
          show-confirm-bar="{{false}}"
        ></textarea>
        <view class="char-counter">{{userInfo.description.length}}/200</view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button 
        class="btn btn-secondary" 
        bindtap="resetForm"
        disabled="{{!hasChanges}}"
      >
        🔄 重置
      </button>
      
      <button 
        class="btn btn-primary" 
        bindtap="saveProfile"
        disabled="{{saving || !hasChanges}}"
      >
        {{saving ? '保存中...' : '💾 保存修改'}}
      </button>
    </view>

  </view>

  <!-- 保存提示 -->
  <view wx:if="{{hasChanges}}" class="save-tip">
    <view class="tip-content">
      <text class="tip-icon">⚠️</text>
      <text class="tip-text">您有未保存的修改</text>
    </view>
  </view>
</view>