package com.agriculture.iot.service;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.Schedule;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 调度服务接口
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
public interface ScheduleService extends IService<Schedule> {

    /**
     * 分页获取计划列表
     */
    IPage<Schedule.Detail> getSchedules(
            Integer current, Integer size, String type, String status, 
            Long plotId, Long deviceId);

    /**
     * 创建计划
     */
    Result<Schedule.Detail> createSchedule(Schedule.CreateRequest request);

    /**
     * 更新计划
     */
    Result<Schedule.Detail> updateSchedule(Long id, Schedule.UpdateRequest request);

    /**
     * 删除计划
     */
    Result<Void> deleteSchedule(Long id);

    /**
     * 获取计划详情
     */
    Result<Schedule.Detail> getScheduleDetail(Long id);

    /**
     * 启用/禁用计划
     */
    Result<Void> toggleSchedule(Long id, Boolean enabled);

    /**
     * 立即执行计划
     */
    Result<Schedule.ExecutionResult> executeSchedule(Long id);

    /**
     * 批量执行计划
     */
    Result<List<Schedule.ExecutionResult>> batchExecuteSchedules(List<Long> scheduleIds);

    /**
     * 获取计划执行记录
     */
    IPage<Schedule.ExecutionRecord> getExecutionRecords(
            Integer current, Integer size, Long scheduleId, String status,
            LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取计划统计
     */
    Result<Schedule.Statistics> getScheduleStatistics(
            LocalDate startDate, LocalDate endDate, String type, Long plotId);

    /**
     * 获取计划模板
     */
    Result<List<Schedule.Template>> getScheduleTemplates(String type, String category);

    /**
     * 创建计划模板
     */
    Result<Schedule.Template> createScheduleTemplate(Schedule.Template template);

    /**
     * 从模板创建计划
     */
    Result<Schedule.Detail> createScheduleFromTemplate(
            Long templateId, Long plotId, Long deviceId, LocalDate startDate);

    /**
     * 检测计划冲突
     */
    Result<Schedule.ConflictInfo> checkScheduleConflict(
            Long plotId, Long deviceId, LocalDateTime startTime, LocalDateTime endTime, Long excludeId);

    /**
     * 获取日历视图
     */
    Result<List<Schedule.CalendarView>> getCalendarView(
            Integer year, Integer month, Long plotId, String type);

    /**
     * 获取今日计划
     */
    Result<List<Schedule.CalendarItem>> getTodaySchedules(Long plotId, String type);

    /**
     * 获取即将到期的计划
     */
    Result<List<Schedule.CalendarItem>> getUpcomingSchedules(
            Integer hours, Long plotId, String type);

    /**
     * 获取逾期计划
     */
    Result<List<Schedule.CalendarItem>> getOverdueSchedules(Long plotId, String type);

    /**
     * 复制计划
     */
    Result<Schedule.Detail> duplicateSchedule(Long id, Schedule.CreateRequest modifications);

    /**
     * 暂停计划
     */
    Result<Void> pauseSchedule(Long id);

    /**
     * 恢复计划
     */
    Result<Void> resumeSchedule(Long id);

    /**
     * 完成计划
     */
    Result<Void> completeSchedule(Long id);

    /**
     * 取消计划
     */
    Result<Void> cancelSchedule(Long id);

    /**
     * 批量更新计划状态
     */
    Result<Void> batchUpdateScheduleStatus(List<Long> scheduleIds, String status);

    /**
     * 获取下次执行的计划
     */
    Result<List<Schedule.CalendarItem>> getNextExecutionSchedules(Integer limit);

    /**
     * 更新计划执行时间
     */
    Result<Void> updateNextExecution(Long id, LocalDateTime nextExecution);

    /**
     * 记录计划执行结果
     */
    Result<Void> recordExecutionResult(Long scheduleId, Schedule.ExecutionResult result);
}