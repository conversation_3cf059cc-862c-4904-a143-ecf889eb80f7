/* 个人中心页面 - 现代简约风格 */

/* 页面容器 - 渐变背景 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #08C160 0%, #06A651 20%, #E8F5E8 60%, #F0FDF4 100%);
  position: relative;
  overflow: hidden;
}

/* 添加背景装饰元素 */
.page-container::before {
  content: '';
  position: absolute;
  top: -100rpx;
  right: -100rpx;
  width: 400rpx;
  height: 400rpx;
  background: radial-gradient(circle, rgba(8, 193, 96, 0.08) 0%, rgba(8, 193, 96, 0.02) 40%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  animation: float 8s ease-in-out infinite;
}

.page-container::after {
  content: '';
  position: absolute;
  bottom: -150rpx;
  left: -150rpx;
  width: 500rpx;
  height: 500rpx;
  background: radial-gradient(circle, rgba(8, 193, 96, 0.06) 0%, rgba(8, 193, 96, 0.01) 50%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  animation: float 12s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(3deg); }
}

.container {
  padding: 40rpx 24rpx;
  padding-bottom: 120rpx;
  position: relative;
  z-index: 1;
}

/* 卡片通用样式 - 现代化设计 */
.card {
  background: white;
  border-radius: 32rpx;
  margin-bottom: 32rpx;
  box-shadow:
    0 16rpx 40rpx rgba(8, 193, 96, 0.08),
    0 4rpx 20rpx rgba(8, 193, 96, 0.04),
    0 1rpx 4rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(8, 193, 96, 0.06);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #08C160 0%, #06D174 50%, #08C160 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:active::before {
  opacity: 1;
}

.card:active {
  transform: translateY(-8rpx) scale(1.02);
  box-shadow:
    0 24rpx 60rpx rgba(8, 193, 96, 0.15),
    0 8rpx 32rpx rgba(8, 193, 96, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 用户信息区域 - 直接在渐变背景上 */
.user-profile-section {
  display: flex;
  align-items: center;
  gap: 32rpx;
  padding: 40rpx 24rpx;
  margin-bottom: 32rpx;
}

/* 头像区域 */
.user-avatar-wrapper {
  flex-shrink: 0;
}

.user-avatar {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #08C160 0%, #06A651 100%);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #ffffff;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow:
    0 12rpx 32rpx rgba(8, 193, 96, 0.3),
    0 4rpx 16rpx rgba(8, 193, 96, 0.15),
    0 0 0 2rpx rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.user-avatar:active .avatar-placeholder {
  transform: scale(0.95);
  box-shadow: 
    0 8rpx 24rpx rgba(8, 193, 96, 0.4),
    0 2rpx 12rpx rgba(8, 193, 96, 0.2);
}


/* 用户信息区域 */
.user-info-wrapper {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-main-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 1.2;
}


.user-role-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 8rpx;
}

.user-role {
  font-size: 24rpx;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-weight: 500;
}

/* 按钮样式 */
.btn {
  border: none;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn::after {
  border: none;
}

.btn-mini {
  padding: 8rpx 16rpx;
  font-size: 22rpx;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 24rpx 48rpx;
  width: 100%;
  font-size: 32rpx;
  font-weight: 600;
}

.btn-outline {
  background: rgba(255, 255, 255, 0.15);
  border: 2rpx solid rgba(255, 255, 255, 0.4);
  color: #ffffff;
}

.btn-outline:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.95);
}

/* 九宫格菜单卡片 */
.grid-menu-card {
  padding: 40rpx 32rpx;
}

.menu-grid-nine {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx 24rpx;
}

.menu-grid-simple {
  display: flex;
  justify-content: center;
  align-items: center;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32rpx 16rpx;
  transition: all 0.3s ease;
  position: relative;
  min-height: 140rpx;
}

.grid-item.active {
  cursor: pointer;
}

.grid-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.grid-item.active:active {
  transform: scale(0.95);
}

.grid-icon {
  width: 72rpx;
  height: 72rpx;
  background: linear-gradient(135deg, #08C160 0%, #06A651 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  box-shadow: 
    0 8rpx 20rpx rgba(8, 193, 96, 0.3),
    0 2rpx 8rpx rgba(8, 193, 96, 0.15);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.grid-icon.disabled {
  background: linear-gradient(135deg, #CCCCCC 0%, #BBBBBB 100%);
  box-shadow: 
    0 4rpx 12rpx rgba(0, 0, 0, 0.1),
    0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}

.grid-item.active:active .grid-icon {
  transform: scale(1.1);
  box-shadow: 
    0 12rpx 28rpx rgba(8, 193, 96, 0.4),
    0 4rpx 12rpx rgba(8, 193, 96, 0.2);
}

.icon-text {
  font-size: 32rpx;
  line-height: 1;
  color: white;
}

.grid-title {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.grid-item.disabled .grid-title {
  color: #999999;
}

/* 退出登录卡片 */
.logout-card {
  padding: 32rpx;
}

.logout-btn {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  color: #ffffff;
  font-weight: 600;
  box-shadow: 
    0 12rpx 32rpx rgba(255, 71, 87, 0.3),
    0 4rpx 16rpx rgba(255, 71, 87, 0.15);
}

.logout-btn:active {
  background: linear-gradient(135deg, #ff3742 0%, #ff2837 100%);
  transform: scale(0.98);
  box-shadow: 
    0 8rpx 24rpx rgba(255, 71, 87, 0.4),
    0 2rpx 12rpx rgba(255, 71, 87, 0.2);
}

/* 响应式适配 */
@media screen and (max-width: 400px) {
  .container {
    padding: 32rpx 20rpx;
  }
  
  .user-profile-section {
    gap: 24rpx;
    padding: 32rpx 20rpx;
  }
  
  .avatar-placeholder {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50rpx;
    font-size: 40rpx;
    border: 3rpx solid rgba(255, 255, 255, 0.8);
    box-shadow:
      0 8rpx 24rpx rgba(8, 193, 96, 0.3),
      0 2rpx 12rpx rgba(8, 193, 96, 0.15),
      0 0 0 1rpx rgba(255, 255, 255, 0.3);
  }
  
  .user-name {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 400;
  }
  
  .menu-grid-nine {
    gap: 24rpx 16rpx;
  }
  
  .grid-item {
    padding: 24rpx 12rpx;
    min-height: 120rpx;
  }
  
  .grid-icon {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 12rpx;
  }
  
  .icon-text {
    font-size: 28rpx;
  }
  
  .grid-title {
    font-size: 22rpx;
  }
}

/* 动画效果 */
.card {
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为不同的卡片添加延迟动画 */
.user-profile-section {
  animation: slideInUp 0.4s ease-out;
  animation-delay: 0.1s;
}

.grid-menu-card {
  animation-delay: 0.2s;
}

.logout-card {
  animation-delay: 0.3s;
}

/* 触摸反馈增强 */
.grid-item.active:active,
.btn:active {
  transition: all 0.1s ease;
}

/* 主色调变量 */
:root {
  --primary-color: #08C160;
  --primary-light: rgba(8, 193, 96, 0.1);
  --primary-shadow: rgba(8, 193, 96, 0.3);
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --border-color: rgba(8, 193, 96, 0.06);
  --background-gradient: linear-gradient(135deg, #F0FDF4 0%, #F7FEF9 50%, #FAFBFA 100%);
  --card-shadow: 0 16rpx 40rpx rgba(8, 193, 96, 0.08), 0 4rpx 20rpx rgba(8, 193, 96, 0.04), 0 1rpx 4rpx rgba(0, 0, 0, 0.04);
}