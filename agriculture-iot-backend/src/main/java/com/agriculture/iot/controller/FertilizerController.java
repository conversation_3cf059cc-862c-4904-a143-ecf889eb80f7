package com.agriculture.iot.controller;

import com.agriculture.iot.common.Result;
import com.agriculture.iot.entity.FertilizerRecord;
import com.agriculture.iot.entity.FertilizerSchedule;
import com.agriculture.iot.entity.FertilizerType;
import com.agriculture.iot.service.FertilizerService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 施肥控制器
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Tag(name = "施肥控制管理", description = "施肥设备的控制、配方管理和记录查询")
@RestController
@RequestMapping("/api/fertilizer")
@RequiredArgsConstructor
public class FertilizerController {

    private final FertilizerService fertilizerService;

    @Operation(summary = "执行施肥", description = "启动指定设备的施肥操作")
    @PostMapping("/apply")
    public Result<Map<String, Object>> applyFertilizer(@Valid @RequestBody FertilizerRecord.ApplyRequest applyRequest) {
        Map<String, Object> result = fertilizerService.applyFertilizer(applyRequest);
        if (result == null) {
            return Result.error("施肥启动失败");
        }
        return Result.success("施肥启动成功", result);
    }

    @Operation(summary = "停止施肥", description = "停止指定设备的施肥操作")
    @PostMapping("/stop")
    public Result<Void> stopFertilizer(@Valid @RequestBody FertilizerRecord.StopRequest stopRequest) {
        return fertilizerService.stopFertilizer(stopRequest);
    }

    @Operation(summary = "获取施肥配方列表", description = "获取所有可用的施肥配方")
    @GetMapping("/formulas")
    public Result<List<FertilizerRecord.Formula>> getFertilizerFormulas(
            @Parameter(description = "作物类型") @RequestParam(required = false) String cropType,
            @Parameter(description = "生长阶段") @RequestParam(required = false) String growthStage) {
        
        List<FertilizerRecord.Formula> formulas = fertilizerService.getFertilizerFormulas(cropType, growthStage);
        return Result.success(formulas);
    }

    @Operation(summary = "获取施肥记录列表", description = "分页获取施肥记录")
    @GetMapping("/records")
    public Result<IPage<FertilizerRecord>> getFertilizerRecords(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "开始时间") @RequestParam(required = false) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) LocalDateTime endTime) {
        
        Page<FertilizerRecord> page = new Page<>(current, size);
        IPage<FertilizerRecord> result = fertilizerService.getFertilizerRecords(page, deviceId, plotId, startTime, endTime);
        return Result.success(result);
    }

    @Operation(summary = "获取施肥计划列表", description = "分页获取施肥计划")
    @GetMapping("/schedules")
    public Result<IPage<FertilizerSchedule>> getFertilizerSchedules(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        Page<FertilizerSchedule> page = new Page<>(current, size);
        IPage<FertilizerSchedule> result = fertilizerService.getFertilizerSchedules(page, deviceId, plotId, status);
        return Result.success(result);
    }

    @Operation(summary = "创建施肥计划", description = "创建新的施肥计划")
    @PostMapping("/schedules")
    public Result<FertilizerSchedule> createFertilizerSchedule(@Valid @RequestBody FertilizerSchedule schedule) {
        FertilizerSchedule createdSchedule = fertilizerService.createFertilizerSchedule(schedule);
        if (createdSchedule == null) {
            return Result.error("施肥计划创建失败");
        }
        return Result.success("施肥计划创建成功", createdSchedule);
    }

    @Operation(summary = "更新施肥计划", description = "更新现有的施肥计划")
    @PutMapping("/schedules/{id}")
    public Result<FertilizerSchedule> updateFertilizerSchedule(
            @Parameter(description = "计划ID") @PathVariable Long id,
            @Valid @RequestBody FertilizerSchedule schedule) {
        
        schedule.setId(id);
        FertilizerSchedule updatedSchedule = fertilizerService.updateFertilizerSchedule(schedule);
        if (updatedSchedule == null) {
            return Result.notFound("施肥计划不存在");
        }
        return Result.success("施肥计划更新成功", updatedSchedule);
    }

    @Operation(summary = "删除施肥计划", description = "删除指定的施肥计划")
    @DeleteMapping("/schedules/{id}")
    public Result<Void> deleteFertilizerSchedule(
            @Parameter(description = "计划ID") @PathVariable Long id) {
        
        boolean deleted = fertilizerService.deleteFertilizerSchedule(id);
        if (!deleted) {
            return Result.notFound("施肥计划不存在");
        }
        return Result.success("施肥计划删除成功", null);
    }

    @Operation(summary = "获取肥料类型列表", description = "获取所有肥料类型")
    @GetMapping("/types")
    public Result<List<FertilizerType>> getFertilizerTypes() {
        List<FertilizerType> fertilizerTypes = fertilizerService.getFertilizerTypes();
        return Result.success(fertilizerTypes);
    }

    @Operation(summary = "获取施肥状态", description = "获取设备的实时施肥状态")
    @GetMapping("/status")
    public Result<FertilizerRecord.StatusInfo> getFertilizerStatus(
            @Parameter(description = "设备ID") @RequestParam Long deviceId) {
        
        return fertilizerService.getFertilizerStatus(deviceId);
    }

    @Operation(summary = "获取施肥统计信息", description = "获取施肥操作的统计数据")
    @GetMapping("/statistics")
    public Result<FertilizerRecord.Statistics> getFertilizerStatistics(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        
        return fertilizerService.getFertilizerStatistics(plotId, deviceId, days);
    }

    @Operation(summary = "获取施肥效果分析", description = "分析施肥效果和营养吸收情况")
    @GetMapping("/effectiveness")
    public Result<List<FertilizerRecord.EffectivenessData>> getFertilizerEffectiveness(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        
        List<FertilizerRecord.EffectivenessData> effectivenessData = fertilizerService.getFertilizerEffectiveness(plotId, deviceId, startTime, endTime);
        return Result.success(effectivenessData);
    }

    @Operation(summary = "创建自定义配方", description = "创建自定义的施肥配方")
    @PostMapping("/formulas")
    public Result<FertilizerRecord.Formula> createCustomFormula(@Valid @RequestBody FertilizerRecord.Formula formula) {
        return fertilizerService.createCustomFormula(formula);
    }

    @Operation(summary = "获取推荐配方", description = "根据作物类型和土壤条件推荐施肥配方")
    @GetMapping("/recommendations")
    public Result<List<FertilizerRecord.Formula>> getRecommendedFormulas(
            @Parameter(description = "作物类型") @RequestParam String cropType,
            @Parameter(description = "生长阶段") @RequestParam String growthStage,
            @Parameter(description = "土壤类型") @RequestParam(required = false) String soilType,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId) {
        
        List<FertilizerRecord.Formula> recommendations = fertilizerService.getRecommendedFormulas(cropType, growthStage, soilType, plotId);
        return Result.success(recommendations);
    }

    @Operation(summary = "获取施肥报表", description = "生成施肥操作报表")
    @GetMapping("/reports")
    public Result<FertilizerRecord.ReportData> getFertilizerReport(
            @Parameter(description = "报表类型") @RequestParam(defaultValue = "monthly") String reportType,
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        
        return fertilizerService.getFertilizerReport(reportType, deviceId, plotId, startTime, endTime);
    }

    @Operation(summary = "批量执行施肥", description = "批量对多个设备执行施肥操作")
    @PostMapping("/batch-apply")
    public Result<Map<String, Object>> batchApplyFertilizer(@Valid @RequestBody FertilizerRecord.BatchApplyRequest batchRequest) {
        Map<String, Object> result = fertilizerService.batchApplyFertilizer(batchRequest);
        return Result.success("批量施肥完成", result);
    }

    @Operation(summary = "获取营养元素分析", description = "分析土壤营养元素含量变化")
    @GetMapping("/nutrient-analysis")
    public Result<FertilizerRecord.NutrientAnalysis> getNutrientAnalysis(
            @Parameter(description = "地块ID") @RequestParam Long plotId,
            @Parameter(description = "分析周期（天）") @RequestParam(defaultValue = "30") Integer days) {
        
        FertilizerRecord.NutrientAnalysis analysis = fertilizerService.getNutrientAnalysis(plotId, days);
        return Result.success(analysis);
    }

    @Operation(summary = "获取施肥建议", description = "基于传感器数据提供施肥建议")
    @GetMapping("/suggestions")
    public Result<List<FertilizerRecord.Suggestion>> getFertilizerSuggestions(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId) {
        
        List<FertilizerRecord.Suggestion> suggestions = fertilizerService.getFertilizerSuggestions(deviceId, plotId);
        return Result.success(suggestions);
    }

    @Operation(summary = "设置施肥提醒", description = "设置定期施肥提醒")
    @PostMapping("/reminders")
    public Result<Void> setFertilizerReminder(@Valid @RequestBody FertilizerRecord.ReminderConfig reminderConfig) {
        boolean success = fertilizerService.setFertilizerReminder(reminderConfig);
        if (!success) {
            return Result.error("提醒设置失败");
        }
        return Result.success("提醒设置成功", null);
    }

    @Operation(summary = "获取施肥提醒配置", description = "获取施肥提醒配置")
    @GetMapping("/reminders")
    public Result<List<FertilizerRecord.ReminderConfig>> getFertilizerReminders(
            @Parameter(description = "设备ID") @RequestParam(required = false) Long deviceId,
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId) {
        
        List<FertilizerRecord.ReminderConfig> reminders = fertilizerService.getFertilizerReminders(deviceId, plotId);
        return Result.success(reminders);
    }

    @Operation(summary = "获取施肥成本分析", description = "分析施肥成本和投入产出比")
    @GetMapping("/cost-analysis")
    public Result<FertilizerRecord.CostAnalysis> getFertilizerCostAnalysis(
            @Parameter(description = "地块ID") @RequestParam(required = false) Long plotId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        
        FertilizerRecord.CostAnalysis costAnalysis = fertilizerService.getFertilizerCostAnalysis(plotId, startTime, endTime);
        return Result.success(costAnalysis);
    }
}