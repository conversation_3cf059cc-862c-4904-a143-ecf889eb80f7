package com.agriculture.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 肥料类型实体类
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("fertilizer_types")
public class FertilizerType {

    /**
     * 肥料类型ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 肥料名称
     */
    @TableField("name")
    private String name;

    /**
     * 肥料代码
     */
    @TableField("code")
    private String code;

    /**
     * 肥料分类: organic-有机肥, inorganic-无机肥, compound-复合肥
     */
    @TableField("category")
    private String category;

    /**
     * 氮含量(%)
     */
    @TableField("nitrogen_content")
    private String nitrogenContent;

    /**
     * 磷含量(%)
     */
    @TableField("phosphorus_content")
    private String phosphorusContent;

    /**
     * 钾含量(%)
     */
    @TableField("potassium_content")
    private String potassiumContent;

    /**
     * 推荐用量(kg/亩)
     */
    @TableField("recommended_dosage")
    private String recommendedDosage;

    /**
     * 适用作物
     */
    @TableField("suitable_crops")
    private String suitableCrops;

    /**
     * 使用说明
     */
    @TableField("usage_instructions")
    private String usageInstructions;

    /**
     * 注意事项
     */
    @TableField("precautions")
    private String precautions;

    /**
     * 状态: 0-禁用, 1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}