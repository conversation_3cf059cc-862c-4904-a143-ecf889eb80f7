package com.agriculture.iot.mapper;

import com.agriculture.iot.entity.Schedule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 调度计划数据访问层
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Mapper
public interface ScheduleMapper extends BaseMapper<Schedule> {

    /**
     * 分页查询计划列表（包含关联信息）
     */
    IPage<Schedule.Detail> selectScheduleDetailsPage(
            Page<Schedule.Detail> page, 
            @Param("type") String type,
            @Param("status") String status, 
            @Param("plotId") Long plotId,
            @Param("deviceId") Long deviceId);

    /**
     * 根据ID获取计划详情
     */
    Schedule.Detail selectScheduleDetailById(@Param("id") Long id);

    /**
     * 检查计划冲突
     */
    List<Schedule> selectConflictingSchedules(
            @Param("plotId") Long plotId,
            @Param("deviceId") Long deviceId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("excludeId") Long excludeId);

    /**
     * 获取日历视图数据
     */
    List<Schedule.CalendarView> selectCalendarView(
            @Param("year") Integer year,
            @Param("month") Integer month,
            @Param("plotId") Long plotId,
            @Param("type") String type);

    /**
     * 获取今日计划
     */
    List<Schedule.CalendarItem> selectTodaySchedules(
            @Param("plotId") Long plotId,
            @Param("type") String type);

    /**
     * 获取即将到期的计划
     */
    List<Schedule.CalendarItem> selectUpcomingSchedules(
            @Param("hours") Integer hours,
            @Param("plotId") Long plotId,
            @Param("type") String type);

    /**
     * 获取逾期计划
     */
    List<Schedule.CalendarItem> selectOverdueSchedules(
            @Param("plotId") Long plotId,
            @Param("type") String type);

    /**
     * 获取下次执行的计划
     */
    List<Schedule.CalendarItem> selectNextExecutionSchedules(@Param("limit") Integer limit);

    /**
     * 更新下次执行时间
     */
    int updateNextExecution(@Param("id") Long id, @Param("nextExecution") LocalDateTime nextExecution);

    /**
     * 更新执行次数
     */
    int updateExecutionCount(@Param("id") Long id);

    /**
     * 更新成功次数
     */
    int updateSuccessCount(@Param("id") Long id);

    /**
     * 批量更新计划状态
     */
    int batchUpdateStatus(@Param("scheduleIds") List<Long> scheduleIds, @Param("status") String status);

    /**
     * 获取计划统计数据
     */
    Schedule.Statistics selectScheduleStatistics(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("type") String type,
            @Param("plotId") Long plotId);

    /**
     * 获取计划模板列表
     */
    List<Schedule.Template> selectScheduleTemplates(
            @Param("type") String type,
            @Param("category") String category);

    /**
     * 根据模板ID获取模板详情
     */
    Schedule.Template selectTemplateById(@Param("templateId") Long templateId);

    /**
     * 创建计划模板
     */
    int insertScheduleTemplate(Schedule.Template template);

    /**
     * 获取活跃计划数量
     */
    Integer selectActiveScheduleCount(@Param("plotId") Long plotId);

    /**
     * 获取今日已执行计划数量
     */
    Integer selectTodayExecutedCount(@Param("plotId") Long plotId);

    /**
     * 根据类型获取计划数量
     */
    Integer selectScheduleCountByType(@Param("type") String type, @Param("plotId") Long plotId);
}