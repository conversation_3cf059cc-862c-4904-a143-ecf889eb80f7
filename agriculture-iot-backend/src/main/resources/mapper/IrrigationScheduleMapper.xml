<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriculture.iot.mapper.IrrigationScheduleMapper">

    <!-- 查找活跃的灌溉计划 -->
    <select id="findActiveSchedules" resultType="com.agriculture.iot.entity.IrrigationSchedule">
        SELECT 
            id,
            device_id as deviceId,
            plot_id as plotId,
            schedule_name as scheduleName,
            start_time as startTime,
            end_time as endTime,
            min_moisture as minMoisture,
            water_amount as waterAmount,
            duration,
            status,
            created_at as createdAt,
            updated_at as updatedAt
        FROM irrigation_schedule
        WHERE status = 'ACTIVE'
        AND start_time <= #{now}
        AND end_time >= #{now}
    </select>

</mapper>