package com.agriculture.iot.mapper;

import com.agriculture.iot.entity.Plot;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 地块数据访问层
 * 
 * <AUTHOR> IoT System
 * @since 2024-01-01
 */
@Mapper
public interface PlotMapper extends BaseMapper<Plot> {
    
    /**
     * 获取地块绑定的设备列表
     * 
     * @param plotId 地块ID
     * @return 设备列表
     */
    List<Object> getPlotDevices(@Param("plotId") Long plotId);
    
    /**
     * 为地块绑定设备
     * 
     * @param plotId 地块ID
     * @param deviceId 设备ID
     * @return 影响行数
     */
    int bindDeviceToPlot(@Param("plotId") Long plotId, @Param("deviceId") Long deviceId);
    
    /**
     * 解绑设备
     * 
     * @param plotId 地块ID
     * @param deviceId 设备ID
     * @return 影响行数
     */
    int unbindDeviceFromPlot(@Param("plotId") Long plotId, @Param("deviceId") Long deviceId);
    
    /**
     * 获取地块设备数量
     * 
     * @param plotId 地块ID
     * @return 设备数量
     */
    Integer getDeviceCountByPlotId(@Param("plotId") Long plotId);
    
    /**
     * 获取地块在线设备数量
     * 
     * @param plotId 地块ID
     * @return 在线设备数量
     */
    Integer getOnlineDeviceCountByPlotId(@Param("plotId") Long plotId);
    
    /**
     * 获取地块传感器数量
     * 
     * @param plotId 地块ID
     * @return 传感器数量
     */
    Integer getSensorCountByPlotId(@Param("plotId") Long plotId);
    
    /**
     * 获取地块本月灌溉次数
     * 
     * @param plotId 地块ID
     * @return 灌溉次数
     */
    Integer getIrrigationCountByPlotId(@Param("plotId") Long plotId);
    
    /**
     * 获取地块本月施肥次数
     * 
     * @param plotId 地块ID
     * @return 施肥次数
     */
    Integer getFertilizerCountByPlotId(@Param("plotId") Long plotId);
    
    /**
     * 获取地块本月报警次数
     * 
     * @param plotId 地块ID
     * @return 报警次数
     */
    Integer getAlarmCountByPlotId(@Param("plotId") Long plotId);
    
    /**
     * 获取地块最新土壤湿度
     * 
     * @param plotId 地块ID
     * @return 土壤湿度
     */
    BigDecimal getLatestSoilHumidity(@Param("plotId") Long plotId);
    
    /**
     * 获取地块最新土壤温度
     * 
     * @param plotId 地块ID
     * @return 土壤温度
     */
    BigDecimal getLatestSoilTemperature(@Param("plotId") Long plotId);
    
    /**
     * 获取地块最新pH值
     * 
     * @param plotId 地块ID
     * @return pH值
     */
    BigDecimal getLatestPhValue(@Param("plotId") Long plotId);
}