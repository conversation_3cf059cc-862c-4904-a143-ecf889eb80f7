/* 灌溉控制页面样式 */

/* 今日计划样式 */
.today-schedule {
  margin-bottom: 30rpx;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.today-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.next-schedule {
  font-size: 24rpx;
  color: #08C160;
  font-weight: 500;
}

.schedule-timeline {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #e0e0e0;
}

.timeline-item.completed {
  border-left-color: #08C160;
  background-color: #E8F8EC;
}

.timeline-item.pending {
  border-left-color: #FF9800;
  background-color: #FFF3E0;
}

.timeline-time {
  font-size: 24rpx;
  font-weight: 600;
  color: #666;
  min-width: 80rpx;
}

.timeline-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.timeline-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.timeline-details {
  font-size: 22rpx;
  color: #666;
}

.timeline-status {
  font-size: 22rpx;
}

.timeline-item.completed .status-text {
  color: #08C160;
}

.timeline-item.pending .status-text {
  color: #FF9800;
}

/* 功能入口 */
.schedule-features {
  margin-bottom: 30rpx;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 25rpx;
  background-color: white;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
  transition: all 0.2s;
  cursor: pointer;
}

.feature-item:active {
  transform: scale(0.95);
  background-color: #f5f5f5;
}

.feature-icon {
  font-size: 40rpx;
}

.feature-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.feature-desc {
  font-size: 22rpx;
  color: #666;
  text-align: center;
}

/* 快速操作 */
.quick-schedule {
  padding-top: 20rpx;
  border-top: 1rpx solid #e0e0e0;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.quick-actions {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.quick-btn {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  font-size: 24rpx;
  background-color: #E8F8EC;
  color: #08C160;
  border: 1rpx solid #C8F2D6;
  border-radius: 8rpx;
  text-align: center;
}

.quick-btn:active {
  background-color: #C8F2D6;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .quick-btn {
    min-width: auto;
  }
}

/* 设备状态面板 */
.device-status-panel {
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  color: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.device-name {
  font-size: 32rpx;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: white;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
}

.status-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-label {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 10rpx;
}

.info-value {
  font-size: 28rpx;
  font-weight: 600;
}

/* 控制面板 */
.control-panel {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.main-control {
  padding: 30rpx;
  background-color: #F8F9FA;
  border-radius: 16rpx;
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.control-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.main-switch {
  transform: scale(1.2);
}

.control-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx;
  background-color: rgba(8, 193, 96, 0.1);
  border-radius: 12rpx;
}

.running-text {
  font-size: 28rpx;
  color: #08C160;
  font-weight: 600;
}

.remaining-time {
  font-size: 24rpx;
  color: #666666;
}

/* 控制参数 */
.control-params {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.param-group {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.param-item {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.param-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.param-control {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.param-unit {
  font-size: 26rpx;
  color: #666666;
  min-width: 60rpx;
}

.duration-input {
  flex: 1;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 28rpx;
}

/* 区域选择 */
.plot-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.plot-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 12rpx;
  background-color: #F8F9FA;
  transition: all 0.3s ease;
}

.plot-item.selected {
  border-color: #08C160;
  background-color: rgba(8, 193, 96, 0.1);
}

.plot-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}

.plot-area {
  font-size: 24rpx;
  color: #666666;
}

.device-count {
  font-size: 22rpx;
  color: #08C160;
  background-color: rgba(8, 193, 96, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin-top: 6rpx;
}

/* 快捷设置 */
.quick-settings {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 25rpx;
  background-color: #F0F0F0;
  border-radius: 12rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.preset-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.preset-btn {
  padding: 20rpx;
  background-color: white;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #333333;
}

.preset-btn:active {
  background-color: #08C160;
  color: white;
}

/* 操作按钮 */
.control-actions {
  display: flex;
  gap: 15rpx;
  justify-content: center;
  margin-top: 20rpx;
}

/* 定时设置 */
.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.schedule-item {
  padding: 25rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  border-left: 4rpx solid #08C160;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.schedule-info {
  flex: 1;
}

.schedule-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.schedule-time {
  font-size: 24rpx;
  color: #666666;
}

.schedule-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 15rpx;
}

.detail-row {
  display: flex;
  gap: 10rpx;
}

.detail-label {
  font-size: 24rpx;
  color: #666666;
  min-width: 60rpx;
}

.detail-value {
  font-size: 24rpx;
  color: #333333;
}

.schedule-actions {
  display: flex;
  gap: 10rpx;
  justify-content: flex-end;
}

.no-schedule {
  text-align: center;
  padding: 60rpx;
  color: #999999;
}

/* 智能控制 */
.smart-control {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.smart-item {
  padding: 25rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
}

.smart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.smart-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.smart-params {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #E0E0E0;
}

.param-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.smart-input {
  flex: 1;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 26rpx;
}


.water-saving-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.6;
}

/* 运行记录 */
.record-count {
  font-size: 24rpx;
  color: #666666;
}

.record-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
}

.record-time {
  font-size: 24rpx;
  color: #666666;
  min-width: 80rpx;
}

.record-content {
  flex: 1;
  margin-left: 20rpx;
}

.record-action {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.record-details {
  font-size: 24rpx;
  color: #666666;
}

.record-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.record-status.success {
  background-color: #E8F8EC;
  color: #08C160;
}

.view-more {
  text-align: center;
  padding: 20rpx;
  color: #08C160;
  font-size: 26rpx;
}

/* ======= 手动控制面板改进样式 ======= */

/* 设备状态面板优化 */
.status-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 20rpx;
}

.info-row {
  display: flex;
  gap: 20rpx;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-bar {
  height: 8rpx;
  background-color: #E0E0E0;
  border-radius: 4rpx;
  overflow: hidden;
}

.info-fill {
  height: 100%;
  background-color: #08C160;
  transition: width 0.3s ease;
}

.info-fill.fertilizer {
  background-color: #FF9800;
}

.info-value.warning {
  color: #F44336;
}

/* 主控制按钮样式 */
.device-status-mini {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.main-buttons {
  margin: 30rpx 0;
  display: flex;
  justify-content: center;
}

.main-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200rpx;
  height: 120rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
}

.main-btn:active {
  transform: scale(0.95);
}

.main-btn.hidden {
  display: none;
}

.start-btn {
  background: linear-gradient(135deg, #08C160 0%, #08C160 100%);
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(8, 193, 96, 0.3);
}

.start-btn:disabled {
  background: #CCCCCC;
  color: #666666;
  box-shadow: none;
}

.stop-btn {
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(244, 67, 54, 0.4);
}

.stop-btn:active {
  box-shadow: 0 4rpx 12rpx rgba(244, 67, 54, 0.4);
}

.btn-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
}

.start-icon {
  position: relative;
}

.start-icon::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-40%, -50%);
  width: 0;
  height: 0;
  border-left: 20rpx solid white;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
}

.stop-icon {
  position: relative;
}

.stop-icon::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 20rpx;
  background-color: white;
  border-radius: 2rpx;
}

.btn-text {
  font-size: 26rpx;
  font-weight: 600;
}

/* 实时数据显示 */
.running-info {
  text-align: center;
  margin-bottom: 20rpx;
}

.realtime-data {
  display: flex;
  gap: 30rpx;
  justify-content: center;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.realtime-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.realtime-label {
  font-size: 22rpx;
  color: #666666;
}

.realtime-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #08C160;
}


/* ======= 自定义流量控制滑块样式 ======= */

.flow-control {
  background-color: #F8F9FA;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.param-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.flow-display {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  background-color: #08C160;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.flow-value {
  font-size: 32rpx;
  font-weight: 600;
}

.flow-unit {
  font-size: 20rpx;
  opacity: 0.9;
}

/* 自定义滑块 */
.custom-slider {
  margin: 30rpx 0 0;
}

.slider-track {
  position: relative;
  height: 8rpx;
  background-color: #E0E0E0;
  border-radius: 4rpx;
  margin: 0 20rpx;
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #08C160 0%, #08C160 100%);
  border-radius: 4rpx;
  transition: width 0.1s ease;
}

.slider-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 40rpx;
  height: 40rpx;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.thumb-inner {
  width: 20rpx;
  height: 20rpx;
  background-color: #08C160;
  border-radius: 50%;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin: 20rpx 20rpx 0;
}

.slider-min,
.slider-max {
  font-size: 22rpx;
  color: #666666;
}


/* 响应式调整 */
@media (max-width: 375px) {
  .info-row {
    flex-direction: column;
  }
  
  .realtime-data {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .main-btn {
    width: 180rpx;
    height: 100rpx;
  }
  
  .flow-control {
    padding: 20rpx;
  }
}