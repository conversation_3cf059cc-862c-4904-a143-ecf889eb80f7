/* 智慧农业首页样式 - 现代化设计 */

.index-container {
  background: linear-gradient(135deg, #F0FDF4 0%, #F7FEF9 50%, #FAFBFA 100%);
  min-height: 100vh;
  padding: 24rpx;
  padding-bottom: 120rpx;
}

/* 农场天气综合卡片 - 现代设计 */
.farm-weather-card {
  background: linear-gradient(135deg, #08C160 0%, #06A651 50%, #059142 100%);
  border-radius: 32rpx;
  padding: 48rpx 40rpx;
  margin-bottom: 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow:
    0 20rpx 40rpx rgba(8, 193, 96, 0.25),
    0 8rpx 16rpx rgba(8, 193, 96, 0.15),
    0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  color: white;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
}

.farm-weather-card::before {
  content: '';
  position: absolute;
  top: -60rpx;
  right: -60rpx;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.05) 40%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  animation: float 6s ease-in-out infinite;
}

.farm-weather-card::after {
  content: '';
  position: absolute;
  bottom: -40rpx;
  left: -40rpx;
  width: 160rpx;
  height: 160rpx;
  background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.03) 50%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  animation: float 8s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-8rpx) rotate(2deg); }
}

/* 农场信息部分 */
.farm-info {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
  z-index: 1;
}

.farm-main-info {
  display: flex;
  flex-direction: column;
}

.farm-name {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.farm-area {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 天气信息部分 - 新版本带动画 */
.weather-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 24rpx;
  padding: 20rpx;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 天气动画容器 */
.weather-animation-container {
  width: 120rpx;
  height: 80rpx;
  border-radius: 16rpx;
  overflow: hidden;
  /*background: rgba(255, 255, 255, 0.1);*/
  flex-shrink: 0;
  position: relative;
}

.weather-canvas {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

/* 天气详细信息 */
.weather-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.weather-main {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.weather-temp {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.weather-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.weather-location {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.location-icon {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

.location-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.agriculture-advice {
  margin-top: 8rpx;
  max-width: 200rpx;
}

.advice-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.3;
  text-align: right;
}

/* 卡片通用样式 - 现代化设计 */
.device-status-card,
.today-summary-card,
.env-data-card,
.task-card,
.quick-actions-card,
.notification-card {
  background: white;
  border-radius: 32rpx;
  margin-bottom: 40rpx;
  box-shadow:
    0 16rpx 40rpx rgba(8, 193, 96, 0.08),
    0 4rpx 20rpx rgba(8, 193, 96, 0.04),
    0 1rpx 4rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(8, 193, 96, 0.06);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.device-status-card::before,
.today-summary-card::before,
.env-data-card::before,
.task-card::before,
.quick-actions-card::before,
.notification-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #08C160 0%, #06D174 50%, #08C160 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.device-status-card:active::before,
.today-summary-card:active::before,
.env-data-card:active::before,
.task-card:active::before,
.quick-actions-card:active::before,
.notification-card:active::before {
  opacity: 1;
}

.device-status-card:active,
.today-summary-card:active,
.env-data-card:active,
.task-card:active,
.quick-actions-card:active,
.notification-card:active {
  transform: translateY(-8rpx) scale(1.02);
  box-shadow:
    0 24rpx 60rpx rgba(8, 193, 96, 0.15),
    0 8rpx 32rpx rgba(8, 193, 96, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  padding: 32rpx 32rpx 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #F0F0F0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.device-summary {
  font-size: 28rpx;
  color: #666;
}

/* 设备状态卡片 */
.device-grid {
  padding: 32rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.device-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 28rpx;
  background: linear-gradient(135deg, #F8F9FA 0%, #F5F6F7 100%);
  border-radius: 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.device-item:active {
  transform: scale(0.97);
  background: linear-gradient(135deg, #F0F1F2 0%, #EBEDEF 100%);
}

.device-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.device-icon::before {
  content: '💧';
  font-size: 24rpx;
  line-height: 1;
}

.water-icon {
  background: linear-gradient(135deg, #08C160 0%, #06A651 100%);
  box-shadow: 0 8rpx 20rpx rgba(8, 193, 96, 0.3);
}

.water-icon::before {
  content: '💧';
  color: white;
}

.sensor-icon {
  background: linear-gradient(135deg, #1890FF 0%, #0F7AE5 100%);
  box-shadow: 0 8rpx 20rpx rgba(24, 144, 255, 0.3);
}

.sensor-icon::before {
  content: '📊';
  color: white;
}

.warning-icon {
  background: linear-gradient(135deg, #FF8C00 0%, #FF7300 100%);
  box-shadow: 0 8rpx 20rpx rgba(255, 140, 0, 0.3);
}

.warning-icon::before {
  content: '⚠️';
  color: white;
}

.manage-icon {
  background: linear-gradient(135deg, #8C8C8C 0%, #737373 100%);
  box-shadow: 0 8rpx 20rpx rgba(140, 140, 140, 0.3);
}

.manage-icon::before {
  content: '⚙️';
  color: white;
}

.device-info {
  display: flex;
  flex-direction: column;
}

.device-type-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.device-count {
  font-size: 24rpx;
  color: #666;
}

/* 今日概览卡片 */
.summary-grid {
  padding: 32rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.summary-item {
  text-align: center;
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #F8F9FA 0%, #F5F6F7 100%);
  border-radius: 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.summary-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #4CAF50 0%, #2196F3 50%, #FF9800 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.summary-item:active::before {
  opacity: 1;
}

.summary-item:active {
  transform: scale(0.97);
  background: linear-gradient(135deg, #F0F1F2 0%, #EBEDEF 100%);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.summary-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.summary-value {
  font-size: 36rpx;
  font-weight: 600;
}

.summary-value.primary {
  color: #08C160;
  text-shadow: 0 2rpx 4rpx rgba(8, 193, 96, 0.2);
}

.summary-value.secondary {
  color: #1890FF;
  text-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.2);
}

.summary-value.warning {
  color: #FF8C00;
  text-shadow: 0 2rpx 4rpx rgba(255, 140, 0, 0.2);
}

.summary-value.normal {
  color: #2C3E50;
  text-shadow: 0 2rpx 4rpx rgba(44, 62, 80, 0.1);
}

/* 实时环境卡片 */
.env-data-grid {
  padding: 32rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.env-item {
  text-align: center;
  padding: 28rpx 20rpx;
  background: linear-gradient(135deg, #F8F9FA 0%, #F5F6F7 100%);
  border-radius: 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.env-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #2196F3 0%, #4CAF50 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.env-item:active::before {
  transform: scaleX(1);
}

.env-item:active {
  transform: scale(0.97);
  background: linear-gradient(135deg, #F0F1F2 0%, #EBEDEF 100%);
}

.env-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.env-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

/* 任务卡片 */
.task-list {
  padding: 32rpx;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.task-time {
  font-size: 24rpx;
  color: #666;
}

.task-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.task-status.completed {
  background: #E8F8EC;
  color: #08C160;
}

.task-status.pending {
  background: #FFF3E0;
  color: #FF9800;
}

/* 快捷操作卡片 */
.actions-grid {
  padding: 32rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 36rpx 20rpx;
  background: linear-gradient(135deg, #F8F9FA 0%, #F5F6F7 100%);
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-item::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(76, 175, 80, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.action-item:active::before {
  width: 200rpx;
  height: 200rpx;
}

.action-item:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #F0F1F2 0%, #EBEDEF 100%);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.action-icon-wrapper {
  width: 96rpx;
  height: 96rpx;
  background: linear-gradient(135deg, #08C160 0%, #06A651 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  box-shadow:
    0 12rpx 24rpx rgba(8, 193, 96, 0.3),
    0 4rpx 8rpx rgba(8, 193, 96, 0.15);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.action-item:active .action-icon-wrapper {
  transform: scale(1.1);
  box-shadow:
    0 16rpx 32rpx rgba(8, 193, 96, 0.4),
    0 6rpx 12rpx rgba(8, 193, 96, 0.2);
}

.action-icon-emoji {
  font-size: 36rpx;
  line-height: 1;
}

.action-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 消息中心卡片 */
.notification-badge {
  background: #F44336;
  color: white;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
}

.notification-list {
  padding: 32rpx;
}

.notification-item {
  display: flex;
  gap: 24rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 不同类型的消息卡片颜色 */
.notification-item.success {
  background: linear-gradient(135deg, #F0FDF4 0%, #E8F8EC 100%);
  border: 1rpx solid rgba(8, 193, 96, 0.15);
}

.notification-item.warning {
  background: linear-gradient(135deg, #FFF8E1 0%, #FFF3E0 100%);
  border: 1rpx solid rgba(255, 140, 0, 0.15);
}

.notification-item.error {
  background: linear-gradient(135deg, #FFEBEE 0%, #FFCDD2 100%);
  border: 1rpx solid rgba(244, 67, 54, 0.15);
}

.notification-item.info {
  background: linear-gradient(135deg, #E3F2FD 0%, #E1F5FE 100%);
  border: 1rpx solid rgba(24, 144, 255, 0.15);
}

.notification-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.notification-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  position: absolute;
  top: 20rpx;
  right: 20rpx;
}

.notification-item.success .notification-dot {
  background: #08C160;
  box-shadow: 0 2rpx 8rpx rgba(8, 193, 96, 0.3);
}

.notification-item.warning .notification-dot {
  background: #FF8C00;
  box-shadow: 0 2rpx 8rpx rgba(255, 140, 0, 0.3);
}

.notification-item.error .notification-dot {
  background: #F44336;
  box-shadow: 0 2rpx 8rpx rgba(244, 67, 54, 0.3);
}

.notification-item.info .notification-dot {
  background: #1890FF;
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.3);
}

.notification-icon-wrapper {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 不同类型的图标背景 */
.notification-item.success .notification-icon-wrapper {
  background: linear-gradient(135deg, #08C160 0%, #06A651 100%);
}

.notification-item.warning .notification-icon-wrapper {
  background: linear-gradient(135deg, #FF8C00 0%, #FF7300 100%);
}

.notification-item.error .notification-icon-wrapper {
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
}

.notification-item.info .notification-icon-wrapper {
  background: linear-gradient(135deg, #1890FF 0%, #0F7AE5 100%);
}

.notification-icon-emoji {
  font-size: 24rpx;
  color: white;
  line-height: 1;
}

.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.notification-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.notification-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.notification-time {
  font-size: 22rpx;
  color: #999;
}

.view-all-btn {
  text-align: center;
  padding: 24rpx 0;
  border-top: 1rpx solid #F0F0F0;
  margin-top: 16rpx;
}

.view-all-text {
  font-size: 28rpx;
  color: #2196F3;
}